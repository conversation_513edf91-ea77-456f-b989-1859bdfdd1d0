<shapes name="mxGraph.pid.pumps_-_iso">
<shape aspect="variable" h="100" name="<PERSON> Pump (Liquid)" strokewidth="inherit" w="100">
    <connections>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="E" perimeter="0" x="1" y="0.5"/>
    </connections>
    <background>
        <ellipse h="100" w="100" x="0" y="0"/>
    </background>
    <foreground>
        <fillstroke/>
        <path>
            <move x="50" y="0"/>
            <line x="100" y="50"/>
            <line x="50" y="100"/>
            <move x="7" y="25"/>
            <arc large-arc-flag="0" rx="50" ry="50" sweep-flag="0" x="75" x-axis-rotation="0" y="25"/>
            <move x="7" y="75"/>
            <arc large-arc-flag="0" rx="50" ry="50" sweep-flag="1" x="75" x-axis-rotation="0" y="75"/>
        </path>
        <stroke/>
    </foreground>
</shape>
<shape aspect="variable" h="100" name="Pump (Centrifugal)" strokewidth="inherit" w="100">
    <connections>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="E" perimeter="0" x="1" y="0.5"/>
    </connections>
    <background>
        <ellipse h="100" w="100" x="0" y="0"/>
    </background>
    <foreground>
        <fillstroke/>
        <path>
            <move x="0" y="50"/>
            <line x="100" y="50"/>
            <move x="50" y="0"/>
            <line x="100" y="50"/>
            <line x="50" y="100"/>
        </path>
        <stroke/>
    </foreground>
</shape>
<shape aspect="variable" h="100" name="Pump (Diaphragm)" strokewidth="inherit" w="100">
    <connections>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="E" perimeter="0" x="1" y="0.5"/>
    </connections>
    <background>
        <ellipse h="100" w="100" x="0" y="0"/>
    </background>
    <foreground>
        <fillstroke/>
        <path>
            <move x="100" y="50"/>
            <line x="50" y="100"/>
            <arc large-arc-flag="0" rx="65" ry="65" sweep-flag="1" x="50" x-axis-rotation="0" y="0"/>
            <close/>
        </path>
        <stroke/>
    </foreground>
</shape>
<shape aspect="variable" h="100" name="Pump (Gear)" strokewidth="inherit" w="100">
    <connections>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="E" perimeter="0" x="1" y="0.5"/>
    </connections>
    <background>
        <ellipse h="100" w="100" x="0" y="0"/>
    </background>
    <foreground>
        <fillstroke/>
        <path>
            <move x="50" y="0"/>
            <line x="100" y="50"/>
            <line x="50" y="100"/>
        </path>
        <stroke/>
        <ellipse h="30" w="30" x="35" y="45"/>
        <stroke/>
        <ellipse h="30" w="30" x="35" y="25"/>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="100" name="Pump (Liquid)" strokewidth="inherit" w="100">
    <connections>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="E" perimeter="0" x="1" y="0.5"/>
    </connections>
    <background>
        <ellipse h="100" w="100" x="0" y="0"/>
    </background>
    <foreground>
        <fillstroke/>
        <path>
            <move x="50" y="0"/>
            <line x="100" y="50"/>
            <line x="50" y="100"/>
        </path>
        <stroke/>
    </foreground>
</shape>
<shape aspect="variable" h="100" name="Pump (Positive Displacement)" strokewidth="inherit" w="100">
    <connections>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="E" perimeter="0" x="1" y="0.5"/>
    </connections>
    <background>
        <ellipse h="100" w="100" x="0" y="0"/>
    </background>
    <foreground>
        <fillstroke/>
        <path>
            <move x="50" y="0"/>
            <line x="100" y="50"/>
            <line x="50" y="100"/>
        </path>
        <stroke/>
        <rect h="30" w="30" x="35" y="35"/>
        <stroke/>
    </foreground>
</shape>
<shape aspect="variable" h="100" name="Pump (Progressive Cavity)" strokewidth="inherit" w="100">
    <connections>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="E" perimeter="0" x="1" y="0.5"/>
    </connections>
    <background>
        <ellipse h="100" w="100" x="0" y="0"/>
    </background>
    <foreground>
        <fillstroke/>
        <path>
            <move x="50" y="0"/>
            <line x="100" y="50"/>
            <line x="50" y="100"/>
            <move x="25" y="50"/>
            <arc large-arc-flag="1" rx="5" ry="5" sweep-flag="1" x="42.5" x-axis-rotation="0" y="50"/>
            <arc large-arc-flag="1" rx="5" ry="5" sweep-flag="0" x="57.5" x-axis-rotation="0" y="50"/>
            <arc large-arc-flag="1" rx="5" ry="5" sweep-flag="1" x="75" x-axis-rotation="0" y="50"/>
        </path>
        <stroke/>
    </foreground>
</shape>
<shape aspect="variable" h="100" name="Pump (Reciprocating Piston)" strokewidth="inherit" w="100">
    <connections>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="E" perimeter="0" x="1" y="0.5"/>
    </connections>
    <background>
        <ellipse h="100" w="100" x="0" y="0"/>
    </background>
    <foreground>
        <fillstroke/>
        <path>
            <move x="50" y="0"/>
            <line x="100" y="50"/>
            <line x="50" y="100"/>
            <move x="70" y="35"/>
            <line x="70" y="65"/>
            <move x="40" y="50"/>
            <line x="70" y="50"/>
        </path>
        <stroke/>
    </foreground>
</shape>
<shape aspect="variable" h="100" name="Pump (Screw)" strokewidth="inherit" w="100">
    <connections>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="E" perimeter="0" x="1" y="0.5"/>
    </connections>
    <background>
        <ellipse h="100" w="100" x="0" y="0"/>
    </background>
    <foreground>
        <fillstroke/>
        <path>
            <move x="50" y="0"/>
            <line x="100" y="50"/>
            <line x="50" y="100"/>
            <move x="25" y="45"/>
            <line x="30" y="40"/>
            <line x="40" y="50"/>
            <line x="50" y="40"/>
            <line x="60" y="50"/>
            <line x="70" y="40"/>
            <line x="75" y="45"/>
            <move x="25" y="55"/>
            <line x="30" y="50"/>
            <line x="40" y="60"/>
            <line x="50" y="50"/>
            <line x="60" y="60"/>
            <line x="70" y="50"/>
            <line x="75" y="55"/>
        </path>
        <stroke/>
    </foreground>
</shape>
</shapes>