{"name": "drawio-obsidian", "version": "1.5.0", "description": "This is a draw.io plugin for Obsidian (https://obsidian.md)", "scripts": {"automation": "mocha --exit --config ./test/automation/.mocharc.json", "build": "rollup --config rollup.config.js"}, "keywords": [], "author": "", "license": "MIT", "devDependencies": {"@rollup/plugin-typescript": "8.2.5", "@types/chai": "4.3.1", "@types/mocha": "9.1.1", "@types/node": "14.17.15", "@types/w3c-css-typed-object-model-level-1": "20180410.0.4", "chai": "4.3.6", "mocha": "9.2.2", "obsidian": "^0.16.3", "rollup": "^2.56.3", "rollup-plugin-clear": "2.0.7", "rollup-plugin-copy": "3.4.0", "rollup-plugin-html": "0.2.1", "rollup-plugin-terser": "7.0.2", "spectron": "^19.0.0", "ts-node": "10.7.0", "tslib": "2.3.1", "typescript": "4.4.2"}, "overrides": {"rollup": "2.79.2", "nanoid": "3.3.8", "serialize-javascript": "6.0.2", "ws": "8.17.1"}}