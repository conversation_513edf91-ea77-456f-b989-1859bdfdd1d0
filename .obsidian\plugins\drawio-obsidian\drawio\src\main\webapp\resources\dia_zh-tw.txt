# *DO NOT DIRECTLY EDIT THIS FILE, IT IS AUTOMATICALLY GENERATED AND IT IS BASED ON:*
# https://docs.google.com/spreadsheet/ccc?key=0AmQEO36liL4FdDJLWVNMaVV2UmRKSnpXU09MYkdGbEE
about=關於
aboutDrawio=關於 draw.io
accessDenied=存取遭拒
accounts=帳號
action=動作
actualSize=實際尺寸
add=新增
addAccount=新增帳號
addedFile=已新增 {1}
addImages=新增圖片
addImageUrl=新增圖片網址
addLayer=新增圖層
addProperty=新增屬性
address=地址
addToExistingDrawing=新增到現有圖紙
addToScratchpad=新增到便箋本
addWaypoint=新增航點
adjustTo=調至
advanced=進階
smartTemplate=Smart Template
align=對齊
alignment=對齊
allChangesLost=所有修改都將丟失！
allPages=所有頁面
allProjects=所有專案
allSpaces=所有部份
allTags=所有標籤
anchor=錨點
android=Android
angle=角度
arc=弧線
areYouSure=是否確定？
ensureDataSaved=關閉前請確認您的資料已儲存。
allChangesSaved=所有的修改已儲存
allChangesSavedInDrive=所有修改已儲存至雲端硬碟
allowPopups=允許彈出式視窗以阻止此對話框。
allowRelativeUrl=允許相對網址
alreadyConnected=節點已連接
appearance=外觀
apply=套用
archiMate21=ArchiMate 2.1
arrange=調整
arrow=箭頭
arrows=箭頭
asNew=作為新圖紙
atlas=輿圖
author=作者
authorizationRequired=需要授權
authorizeThisAppIn=在 {1} 中授權此應用程式：
authorize=授權
authorizing=授權中
automatic=自動
autosave=自動儲存
autosize=自動調整
attachments=附件
aws=AWS
aws3d=AWS 3D
azure=Azure
back=返回
background=背景
backgroundColor=背景顏色
backgroundImage=背景圖片
basic=基本
beta=測試版
blankDrawing=空白圖紙
blankDiagram=空白圖表
block=區塊
blockquote=區塊引言
blog=部落格
bold=粗體
bootstrap=Bootstrap
border=邊框
borderColor=邊框顏色
borderWidth=邊框寬度
bottom=下
bottomAlign=向下對齊
bottomLeft=左下
bottomRight=右下
bpmn=BPMN
bringForward=上移一層
browser=瀏覽器
bulletedList=項目符號列表
business=商務
busy=處理中
cabinets=機殼
cancel=取消
center=置中
cannotLoad=戴入失敗。請稍後重試。
cannotLogin=登入失敗。請稍後重試。
cannotOpenFile=無法開啟檔案
change=變更
changeOrientation=變更方向
changeUser=變更使用者
changeStorage=變更儲存格式
changesNotSaved=變更尚未儲存
classDiagram=Class Diagram
userJoined={1}已加入
userLeft={1}已離開
chatWindowTitle=交談
chooseAnOption=請選擇一項
chromeApp=Chrome App
collaborativeEditingNotice=協同編輯的重要通知
compare=比較
compressed=已壓縮
commitMessage=提交訊息
configLinkWarn=此連結將設定 draw.io。僅在您信任提供連結給你的人時才點擊確定。
configLinkConfirm=點擊確認進行組態並重新整理 draw.io
container=容器
csv=CSV
dark=暗色
diagramLanguage=Diagram Language
diagramType=圖表類型
diagramXmlDesc=XML 檔案
diagramHtmlDesc=HTML 檔案
diagramPngDesc=可編輯點陣圖檔
diagramSvgDesc=可編輯向量圖檔
didYouMeanToExportToPdf=是否匯出為 PDF？
disabled=已停用
draftFound=發現現有的「{1}」的草圖。要載入它進行編輯或是丟棄以繼續?
draftRevisionMismatch=本頁面的共享草稿中的此圖表有不同的版本。請從草稿編輯該圖表以確保您使用的是最新版本。
selectDraft=選擇一個草稿以繼續編輯：
dragAndDropNotSupported=不支援圖片拖放。是否使用匯入功能?
dropboxCharsNotAllowed=不得使用以下字元：\ / : ? * " |
check=檢查
checksum=校驗碼
circle=圓形
cisco=Cisco
classic=經典
clearDefaultStyle=清除預設樣式
clearWaypoints=清除航點
clipart=剪貼畫
close=關閉
closingFile=關閉檔案中
realtimeCollaboration=即時協作
collaborate=協作
collaborator=協作者
collaborators=協作者
collapse=收起
collapseExpand=收起/展開
collapse-expand=點選以收起／展開\nShift+點選以移動周邊 \nAlt+點選以保護組大小
collapsible=可收展
comic=手繪
comment=評論
commentsNotes=評論/備註
compress=Compress
configuration=組態
connect=連線
connecting=連線中
connectWithDrive=連結至 Google 雲端硬碟
connection=連接線
connectionArrows=連接箭頭
connectionPoints=連接點
constrainProportions=限制寬高比
containsValidationErrors=含有驗證錯誤
copiedToClipboard=已複製到剪貼簿
copy=複製
copyConnect=連接時複製
copyCreated=A copy of the file was created.
copyData=複製資料
copyOf={1} 的副本
copyOfDrawing=圖紙副本
copySize=複製大小
copyStyle=複製樣式
create=新增
createBlankDiagram=Create Blank Diagram
createNewDiagram=新增圖表
createRevision=新增修訂版本
createShape=新增圖形
crop=匯出單頁
curved=曲線
custom=自訂
current=目前
currentPage=目前頁面
cut=剪下
dashed=虛線
decideLater=稍後再決定
default=預設
delete=刪除
deleteColumn=刪除欄
deleteLibrary401=沒有足夠許可權刪除此圖庫
deleteLibrary404=未找到所選圖庫
deleteLibrary500=刪除圖庫時出錯
deleteLibraryConfirm=即將永久刪除此圖庫。您確定要這樣做嗎？
deleteRow=刪除列
description=描述
describeYourDiagram=描述您的圖表
device=裝置
diagram=圖表
diagramContent=圖表内容
diagramLocked=圖表已鎖住，以避免進一步的資料損失。
diagramLockedBySince=The diagram is locked by {1} since {2} ago
diagramName=圖表名稱
diagramIsPublic=圖表是公開的
diagramIsNotPublic=圖表未公開
diamond=菱形
diamondThin=菱形(細)
didYouKnow=您可知道...
direction=方向
discard=放棄
discardChangesAndReconnect=放棄所作變更並重新連線
googleDriveMissingClickHere=找不到 Google 雲端硬碟？請按此！
discardChanges=取消修改
disconnected=連線中斷
distribute=等距分佈
done=完成
doNotShowAgain=不再顯示
dotted=點線的
doubleClickOrientation=雙擊改變方向
doubleClickTooltip=雙撃插入文字
doubleClickChangeProperty=雙撃更改屬性名
download=下載
downloadDesktop=取得桌面應用程式
downloadAs=下載為
clickHereToSave=點擊此處存檔。
dpi=DPI
draftDiscarded=草稿已丟棄
draftSaved=草槁已儲存
dragElementsHere=把元件拖到這裏
dragImagesHere=把圖片或連結拖到這裏
dragUrlsHere=把連結拖到這裏
draw.io=draw.io
drawing=圖紙{1}
drawingEmpty=圖紙空白
drawingTooLarge=圖紙過大
drawioForWork=Draw.io for GSuite
dropbox=Dropbox
duplicate=建立副本
duplicateIt=建立{1}的副本
divider=分隔線
dx=Dx
dy=Dy
east=向右漸層
edit=編輯
editData=編輯資料
editDiagram=編輯圖表
editGeometry=編輯幾何圖形
editImage=編輯圖片
editImageUrl=編輯圖片超連結
editLink=編輯連結
editShape=編輯圖形
editStyle=編輯樣式
editText=編輯文字
editTooltip=編輯提示
glass=玻璃
googleImages=Google 圖片
imageSearch=圖片搜尋
eip=EIP
embed=嵌入
embedFonts=嵌入字型
embedImages=嵌入圖片
mainEmbedNotice=將此貼入頁面
electrical=電路
ellipse=橢圓形
embedNotice=貼上至本頁頁尾處
enterGroup=進入組進行編輯
enterName=輸入名稱
enterPropertyName=輸入屬性名
enterValue=輸入值
entityRelation=實體關係
entityRelationshipDiagram=E-R 圖
error=錯誤
errorDeletingFile=刪除檔案時發生錯誤
errorLoadingFile=載入檔案時發生錯誤
errorRenamingFile=檔案更名時發生錯誤
errorRenamingFileNotFound=檔案更名時發生錯誤。找不到該檔案。
errorRenamingFileForbidden=檔案更名錯誤。沒有足夠的存取權。
errorSavingDraft=儲存草稿時發生錯誤
errorSavingFile=儲存檔案時發生錯誤
errorSavingFileUnknown=Google服務授權失敗。請重新載入頁面然後再試。
errorSavingFileForbidden=儲存檔案時發生錯誤。沒有足夠的存取權。
errorSavingFileNameConflict=無法儲存此圖表。目前頁面已存在名為「{1}」的檔案。
errorSavingFileNotFound=儲存檔案時發生錯誤。找不到檔案。
errorSavingFileReadOnlyMode=唯讀模式啟動時無法儲存圖表。
errorSavingFileSessionTimeout=你的會話已經結束。請 <a target='_blank' href='{1}'>{2}</a>，然後返回此選項卡並再次儲存。
errorSendingFeedback=傳送回饋意見時發生錯誤。
errorUpdatingPreview=更新預覽畫面出錯。
exit=退出
exitGroup=退出編輯組
expand=展開
export=匯出
exporting=匯出中
exportAs=匯出為
exportOptionsDisabled=已停用匯出
exportOptionsDisabledDetails=擁有者已禁止留言者和瀏覽者下載，列印或複製此檔案。
externalChanges=外部變更
extras=其他
facebook=Facebook
failedToSaveTryReconnect=存檔失敗，正嘗試重新連線
featureRequest=新功能建議
feedback=意見回饋
feedbackSent=回饋傳送成功。
floorplans=平面圖
file=檔案
fileChangedOverwriteDialog=檔案已更改。是否存檔並覆寫改動內容？
fileChangedSyncDialog=The file has been modified.
fileChangedSync=檔案已修改。按一下此處以進行同步。
overwrite=覆蓋
synchronize=同步
filename=檔案名稱
fileExists=檔案已存在
fileMovedToTrash=檔案已移到資源回收桶
fileNearlyFullSeeFaq=檔案容量即將達到上限，請參閱常見問題集
fileNotFound=找不到檔案
repositoryNotFound=未找到儲存庫
fileNotFoundOrDenied=找不到該檔案。該檔案不存在或您沒有存取權限。
fileNotLoaded=檔案無法載入
fileNotSaved=檔案無法儲存
fileOpenLocation=您想如何開啟這些檔案 ?
filetypeHtml=.html causes file to save as HTML with redirect to cloud URL
filetypePng=.png causes file to save as PNG with embedded data
filetypeSvg=.svg causes file to save as SVG with embedded data
fileWillBeSavedInAppFolder={1}將會儲存在應用資料夾。
fill=填滿
fillColor=填滿顏色
filterCards=Filter Cards
find=搜尋
fit=適應大小
fitContainer=調整容器大小
fitIntoContainer=適應容器大小
fitPage=整頁顯示
fitPageWidth=與頁面等寬
fitTo=適應
fitToSheetsAcross=橫向頁面
fitToBy=及
fitToSheetsDown=縱向頁面
fitTwoPages=雙頁
fitWindow=適應視窗大小
flip=翻轉
flipH=水平鏡像
flipV=垂直鏡像
flowchart=流程圖
folder=資料夾
font=字型
fontColor=文字顏色
fontFamily=字型
fontSize=文字大小
forbidden=您沒有該檔案的存取權限
format=格式
formatPanel=格式面板
formatted=格式化
formattedText=格式化文字
formatPng=PNG
formatGif=GIF
formatJpg=JPEG
formatPdf=PDF
formatSql=SQL
formatSvg=SVG
formatHtmlEmbedded=HTML
formatSvgEmbedded=SVG (XML)
formatVsdx=VSDX
formatVssx=VSSX
formatWebp=WebP
formatXmlPlain=XML (純文字檔)
formatXml=XML
forum=討論區幫助論壇
freehand=Freehand
fromTemplate=從樣板開啟
fromTemplateUrl=從樣板URL開啟
fromText=從文字
fromUrl=從URL
fromThisPage=從此頁
fullscreen=全螢幕
gap=Gap
gcp=GCP
general=一般
getNotionChromeExtension=取得 Notion 的 Chrome 擴充功能
github=GitHub
gitlab=GitLab
gliffy=Gliffy
global=全域
googleDocs=Google 文件
googleDrive=Google 雲端硬碟
googleGadget=Google Gadget
googleSharingNotAvailable=分享功能只在使用 Google Drive 時可用。請按下方的「開啟」從功能表中分享。
googleSlides=Google 簡報
googleSites=Google 協作平台
googleSheets=Google 試算表
gradient=漸層
gradientColor=顏色
grid=網格
gridColor=網格線顏色
gridSize=網格大小
group=群組
guides=參考線
hateApp=我討厭 draw.io
heading=標題
height=高
help=說明
helpTranslate=協助我們翻譯此應用程式
hide=隱藏
hideIt=隱藏 {1}
hidden=已隱藏
home=最上層的組
horizontal=水平
horizontalFlow=水平流線
horizontalTree=水平樹狀
howTranslate=您所選語言的翻譯品質如何？
html=HTML
htmlText=HTML文字
id=ID
iframe=IFrame
ignore=忽略
image=圖片
imageUrl=圖片URL
images=圖片
imagePreviewError=無法載入此圖片進行預覽。請核對URL。
imageTooBig=圖片太大
imgur=Imgur
import=匯入
importFrom=從...匯入
improveContrast=Improve Contrast
includeCopyOfMyDiagram=包含我的圖表的副本
increaseIndent=增加內縮
decreaseIndent=減少內縮
insert=插入
insertColumnBefore=插入左方欄
insertColumnAfter=插入右方欄
insertEllipse=插入橢圓
insertImage=插入圖片
insertHorizontalRule=插入水平尺標
insertLink=插入連結
insertPage=插入頁
insertRectangle=插入矩形
insertRhombus=插入菱形
insertRowBefore=插入上方列
insertRowAfter=插入下方列
insertText=插入文字
inserting=正在插入
installApp=安裝 App
invalidFilename=圖表名稱不得包含以下字元：\ / | : ; { } < > & + ? = "
invalidLicenseSeeThisPage=您的授權無效，請參閱此<a target="_blank" href="https://www.drawio.com/doc/faq/license-drawio-confluence-jira-cloud">頁面</a>。
invalidInput=無效輸入
invalidName=無效名稱
invalidOrMissingFile=無效或遺失的檔案
invalidPublicUrl=無效的公開URL
isometric=等尺寸的
ios=iOS
italic=斜體
kennedy=Kennedy
keyboardShortcuts=快速鍵
labels=Labels
layers=圖層
landscape=橫向
language=語言
leanMapping=精實流圖
lastChange=最後一次修改：{1}之前
lessThanAMinute=一分鐘內
licensingError=授權出錯
licenseHasExpired={1}的授權已在{2}過期。請點擊這裡。
licenseRequired=This feature requires draw.io to be licensed.
licenseWillExpire={1}的授權將在{2}過期。請點擊這裡。
light=Light
lineJumps=Line jumps
linkAccountRequired=如果圖表非公開，則需要Google帳戶才能查看該連結。
linkText=連結文字
list=列表
minute=分鐘
minutes=分鐘
hours=小時
days=天
months=月
years=年
restartForChangeRequired=變更將在頁面重新載入後生效。
laneColor=泳道底色
languageCode=Language Code
lastModified=最近修改
layout=配置
left=左
leftAlign=向左對齊
leftToRight=向右對齊
libraryTooltip=將圖形拖放至此或按一下加號以插入。按兩下來編輯。
lightbox=光箱特效
line=邊線
lineend=邊線終點
lineheight=邊線高度
linestart=邊線起點
linewidth=邊線寬度
link=連結
links=連結
loading=載入中
lockUnlock=鎖定/解鎖
loggedOut=已登出
logIn=登入
loveIt=我愛{1}
lucidchart=Lucidchart
maps=地圖
mathematicalTypesetting=數學排版
makeCopy=建立副本
manual=手冊
merge=合併
mermaid=Mermaid
microsoftOffice=Microsoft Office
microsoftExcel=Microsoft Excel
microsoftPowerPoint=Microsoft PowerPoint
microsoftWord=Microsoft Word
middle=垂直置中
minimal=Minimal
misc=其他
mockups=實物模型
modern=Modern
modificationDate=修改日期
modifiedBy=修改者
more=更多
moreResults=更多結果
moreShapes=更多圖形
move=移動
moveToFolder=移至目錄
moving=移動中
moveSelectionTo=將所選移至圖層 {1}
myDrive=My Drive
myFiles=My Files
name=名稱
navigation=導航
network=網路
networking=網路
new=新增
newLibrary=新增圖庫
nextPage=下一頁
no=否
noPickFolder=不，選擇資料夾
noAttachments=未找到附件
noColor=無顏色
noFiles=無檔案
noFileSelected=未選擇檔案
noLibraries=未找到圖庫
noMoreResults=無更多結果
none=無
noOtherViewers=無其他檢視使用者
noPlugins=無外掛
noPreview=無預覽畫面
noResponse=伺服器無回應
noResultsFor=未找到「{1}」的相關結果
noRevisions=無修訂
noSearchResults=無任何搜尋結果
noPageContentOrNotSaved=此頁面上找不到任何錨點，或錨點尚未儲存
normal=正常
north=向上漸層
notADiagramFile=非圖表檔案
notALibraryFile=非圖庫檔案
notAvailable=無法使用
notAUtf8File=非 UTF-8 檔案
notConnected=未連線
note=備註
notion=Notion
notSatisfiedWithImport=Not satisfied with the import?
notUsingService=未使用 {1} ?
numberedList=編號列表
offline=離線
ok=確定
oneDrive=OneDrive
online=線上
opacity=不透明度
open=開啟
openArrow=開放的箭頭
openExistingDiagram=開啟現有圖表
openFile=開啟檔案
openFrom=從...開啟
openLibrary=開啟圖庫
openLibraryFrom=從...開啟圖庫
openLink=開啟連結
openInNewWindow=在新視窗開啟
openInThisWindow=在本視窗開啟
openIt=開啟{1}
openRecent=開啟最近使用檔案
openSupported=本軟體支援的檔案格式為本軟體產生之(.xml)，.vsdx和.gliffy
options=選項
organic=力導向配置圖
orgChart=Org Chart
orthogonal=正交
otherViewer=其他檢視之使用者
otherViewers=其他檢視之使用者
outline=畫略圖
oval=橢圓
page=頁面
pageContent=頁面內容
pageNotFound=未找到頁面
pageWithNumber=第{1}頁
pages=頁面
pageTabs=Page Tabs
pageView=頁面檢視
pageSetup=頁面設定
pageScale=頁面縮放
pan=移動畫布
panTooltip=按住空格鍵並拖曳以移動畫布
paperSize=頁面尺寸
pattern=圖案樣式
parallels=Parallels
paste=貼上
pasteData=貼上資料
pasteHere=貼在此處
pasteSize=貼上大小
pasteStyle=貼上樣式
perimeter=周長
permissionAnyone=任何人都可編輯
permissionAuthor=只有本人可編輯
pickFolder=選擇目錄
pickLibraryDialogTitle=選擇圖庫
publicDiagramUrl=圖表的公共URL
placeholders=佔位字元
plantUml=PlantUML
plugins=外掛
pluginUrl=外掛URL
pluginWarning=該頁面已請求載入下列外掛：\n \n {1}\n \n 你想要即刻載入這些外掛嗎？ \n 注：請在充分瞭解這樣做的安全性及影響後，方才允許外掛的運行。/n
plusTooltip=單擊以連接和複製（ctrl +單擊複製，shift +單擊連接）。拖動以連接（ctrl + 拖動以複製）。
portrait=直向
position=位置
posterPrint=海報列印
preferences=偏好
preview=預覽
previousPage=上一頁
presentationMode=Presentation Mode
print=列印
printAllPages=列印所有頁
procEng=工藝過程
project=專案
priority=優先級
processForHiringNewEmployee=Process for hiring a new employee
properties=屬性
publish=發佈
quickStart=快速入門教程
rack=機架
radial=Radial
radialTree=徑向圖
readOnly=唯讀
reconnecting=正在重新連線
recentlyUpdated=最近更新
recentlyViewed=最近閱覽
rectangle=矩形
redirectToNewApp=此檔案是由該應用的新版本創建或修改的，將重新定向。
realtimeTimeout=似乎您在離線時作了一些更改。很遺憾，這些更改無法儲存。
redo=重作
refresh=更新
regularExpression=正規表示式
relative=Relative
relativeUrlNotAllowed=Relative URL not allowed
rememberMe=記住我
rememberThisSetting=記住設定
removeFormat=清除格式
removeFromGroup=移出群組
removeIt=刪除{1}
removeWaypoint=刪除航點
rename=重新命名
renamed=已重新命名
renameIt=重新命名 {1}
renaming=正在重新命名
replace=取代
replaceIt={1}已經存在。是否要取代它？
replaceExistingDrawing=取代現有圖紙
required=必填
requirementDiagram=Requirement Diagram
reset=重設
resetView=重設檢視
resize=調整大小
resizeLargeImages=您是否要縮小大圖尺寸以使程式執行地更快？
retina=Retina
responsive=迴響式
restore=復原
restoring=復原中
retryingIn={1}秒後重試
retryingLoad=載入失敗。重試中...
retryingLogin=登入逾時。重試中...
reverse=逆轉
revision=修訂
revisionHistory=修訂歷史
rhombus=菱形
right=右
rightAlign=向右對齊
rightToLeft=由右至左
rotate=旋轉
rotateTooltip=點選並拖曳旋轉，或單擊旋轉90度
rotation=旋轉
rounded=圓角
save=儲存
saveAndExit=儲存並退出
saveAs=另存新檔
saveAsXmlFile=儲存為 XML 檔？
saved=已儲存
saveDiagramFirst=請先儲存圖表
saveDiagramsTo=將圖表儲存到
saveLibrary403=沒有足夠的權限編輯此圖庫
saveLibrary500=儲存圖庫時發生錯誤
saveLibraryReadOnly=Could not save library while read-only mode is active
saving=儲存中
scratchpad=便箋本
scrollbars=捲軸
search=搜尋
searchShapes=搜尋圖形
selectAll=全選
selectionOnly=僅所選範圍
selectCard=Select Card
selectEdges=選擇邊
selectFile=選擇檔案
selectFolder=選擇目錄
selectFont=選擇字型
selectNone=全不選
selectTemplate=選擇樣板
selectVertices=選擇頂點
sendBackward=下移一層
sendMessage=傳送
sendYourFeedback=傳送您的回饋意見
sequenceDiagram=Sequence Diagram
serviceUnavailableOrBlocked=服務無法使用或已被阻擋
sessionExpired=工作階段已過期。請重新載入瀏覽器畫面。
sessionTimeoutOnSave=您的工作階段已逾時，且與 Google Drive 斷線。按下確定以登入並儲存。
setAsDefaultStyle=設為預設樣式
settings=設定
shadow=陰影
shape=圖形
shapes=圖形
share=共用
shareCursor=共用滑鼠游標
shareLink=分享編輯連結
sharingAvailable=Google Drive 及 OneDrive 檔案可以共用。
saveItToGoogleDriveToCollaborate=You'll need to save "{1}" to Google Drive before you can collaborate.
saveToGoogleDrive=儲存到 Google Drive
sharp=銳角
show=顯示
showRemoteCursors=顯示遠端滑鼠游標
showStartScreen=顯示開始畫面
sidebarTooltip=點擊展開。將圖形拖曳至圖表中。Shift+單擊以更改選擇。Alt+單擊以插入和連接。
signs=標誌
signOut=登出
simple=簡單
simpleArrow=簡單箭頭
simpleViewer=簡單檢視器
size=大小
sketch=Sketch
snapToGrid=Snap to Grid
solid=實線
sourceSpacing=來源間距
south=向下漸層
software=軟體
space=空間
spacing=間距
specialLink=特殊連結
stateDiagram=狀態圖
standard=標準
startDrawing=開始繪圖
stopDrawing=停止繪圖
starting=啟動中
straight=直線
strikethrough=刪除線
strokeColor=邊線顏色
style=物件樣式
subscript=下標
summary=總結
superscript=上標
support=支援
swap=Swap
swimlaneDiagram=泳道圖
sysml=SysML
tags=標籤
table=表格
tables=表格
takeOver=Take Over
targetSpacing=目標間距
template=樣板
templates=樣板
text=文字
textAlignment=文字對齊
textOpacity=文字不透明度
theme=佈景主題
timeout=逾時
title=標題
to=至
toBack=移動到最下層
toFront=移動到最上層
tooLargeUseDownload=檔案過大，請改用下載。
toolbar=工具列
tooltips=秘訣
top=上
topAlign=向上對齊
topLeft=左上
topRight=右上
transparent=透明
transparentBackground=透明背景
trello=Trello
tryAgain=再試一次
tryOpeningViaThisPage=嘗試透過此頁面開啟
turn=旋轉90°
type=類型
twitter=Twitter
uml=UML
unassigned=Unassigned
underline=底線
undo=還原
ungroup=取消群組
unmerge=取消合併
unsavedChanges=未儲存的修改
unsavedChangesClickHereToSave=修改未儲存。點此以儲存。
untitled=未命名
untitledDiagram=未命名圖表
untitledLayer=未命名圖層
untitledLibrary=未命名圖庫
unknownError=未知錯誤
updateFile=更新{1}
updatingDocument=文件更新中。請稍候...
updatingPreview=預覽更新中。請稍候...
updatingSelection=選擇更新中。請稍候...
upload=上傳
url=URL
useOffline=離線使用
useRootFolder=Use root folder?
userManual=使用手冊
vertical=垂直
verticalFlow=垂直流線
verticalTree=垂直樹狀
view=檢視
viewerSettings=檢視器設定
viewUrl=用於檢視的連結：{1}
voiceAssistant=語音助理（測試版）
warning=警告
waypoints=航點
west=向左漸層
where=Where
width=寬
wiki=Wiki
wordWrap=自動換行
writingDirection=書寫方向
yes=是
yourEmailAddress=您的電子信箱
zoom=縮放
zoomIn=放大
zoomOut=縮小
basic=基本圖形
businessprocess=業務流程圖
charts=圖表
engineering=工程圖
flowcharts=流程圖
gmdl=材料設計
mindmaps=心智圖
mockups=模型圖
networkdiagrams=網路結構圖
nothingIsSelected=未選擇
other=其他
softwaredesign=軟體設計
venndiagrams=文氏圖
webEmailOrOther=網址、電子信箱或任何其他網路地址
webLink=網頁連結
wireframes=線框圖
property=內容
value=數值
showMore=顯示更多
showLess=顯示更少
myDiagrams=我的圖表
allDiagrams=全部圖表
recentlyUsed=最近使用
listView=列表檢視
gridView=網格檢視
resultsFor=「{1}」的結果
oneDriveCharsNotAllowed=不允許這些字元：~ " # %  * : < > ? / \ { | }
oneDriveInvalidDeviceName=裝置名稱無效
officeNotLoggedOD=您尚未登入 OneDrive。請開啟 draw.io 工作列並登入。
officeSelectSingleDiag=Please select a single draw.io diagram only without other contents.
officeSelectDiag=請選擇 draw.io 圖表
officeCannotFindDiagram=Cannot find a draw.io diagram in the selection
noDiagrams=未找到圖表
authFailed=認證失敗
officeFailedAuthMsg=Unable to successfully authenticate user or authorize application.
convertingDiagramFailed=轉換圖表失敗
officeCopyImgErrMsg=Due to some limitations in the host application, the image could not be inserted. Please manually copy the image then paste it to the document.
insertingImageFailed=插入圖片失敗
officeCopyImgInst=Instructions: Right-click the image below. Select "Copy image" from the context menu. Then, in the document, right-click and select "Paste" from the context menu.
folderEmpty=資料夾為空
recent=最近使用
sharedWithMe=與我共用
sharepointSites=Sharepoint Sites
errorFetchingFolder=取得資料夾項目時發生錯誤
errorAuthOD=OneDrive 認證失敗
officeMainHeader=將 draw.io 圖表加入您的文件
officeStepsHeader=This add-in performs the following steps:
officeStep1=連線到 Microsoft OneDrive、Google Drive 或您的裝置
officeStep2=選擇 draw.io 圖表
officeStep3=插入圖表至文件
officeAuthPopupInfo=請於彈出視窗中完成認證
officeSelDiag=選擇 draw.io 圖表
files=檔案
shared=已分享
sharepoint=Sharepoint
officeManualUpdateInst=Instructions: Copy draw.io diagram from the document. Then, in the box below, right-click and select "Paste" from the context menu.
officeClickToEdit=按一下圖示以開始編輯
pasteDiagram=Paste draw.io diagram here
connectOD=連線到 OneDrive
selectChildren=Select Children
selectSiblings=Select Siblings
selectParent=Select Parent
selectDescendants=Select Descendants
lastSaved=上次儲存於 {1} 前
resolve=Resolve
reopen=重新開啟
showResolved=Show Resolved
reply=回覆
objectNotFound=找不到物件
reOpened=Re-opened
markedAsResolved=標示為已解決
noCommentsFound=找不到留言
comments=留言
timeAgo={1} 前
confluenceCloud=Confluence 雲端
libraries=圖庫
confAnchor=Confluence 頁面錨點
confTimeout=連線逾時
confSrvTakeTooLong={1} 伺服器太久無回應
confCannotInsertNew=無法在新的 Confluence 頁面中插入 draw.io 圖表
confSaveTry=請儲存頁面並重試
confCannotGetID=無法確認頁面 ID
confContactAdmin=請聯絡您的 Confluence 管理員。
readErr=讀取錯誤
editingErr=編輯錯誤
confExtEditNotPossible=圖表無法在外部編輯。請嘗試在編輯頁面時編輯它。
confEditedExt=圖表/頁面已在外部編輯
diagNotFound=找不到圖表
confEditedExtRefresh=圖表/頁面已在外部編輯。請重新整理頁面。
confCannotEditDraftDelOrExt=Cannot edit diagrams in a draft page, diagram is deleted from the page, or diagram is edited externally. Please check the page.
retBack=Return back
confDiagNotPublished=該圖表不屬於已發布的頁面
createdByDraw=使用 draw.io 製作
filenameShort=檔名太短
invalidChars=無效字元
alreadyExst={1} 已存在
draftReadErr=草稿讀取錯誤
diagCantLoad=無法載入圖表
draftWriteErr=草稿寫入錯誤
draftCantCreate=無法建立草稿
confDuplName=偵測到重複的圖表名稱。請取其它名稱。
confSessionExpired=工作階段似乎過期了。重新登入以繼續工作。
login=登入
drawPrev=draw.io 預覽
drawDiag=draw.io 圖表
invalidCallFnNotFound=無效呼叫：{1} 不存在
invalidCallErrOccured=無效呼叫：發生錯誤, {1}
anonymous=Anonymous
confGotoPage=Go to containing page
showComments=顯示留言
confError=錯誤：{1}
gliffyImport=匯入 Gliffy
gliffyImportInst1=Click the "Start Import" button to import all Gliffy diagrams to draw.io.
gliffyImportInst2=Please note that the import procedure will take some time and the browser window must remain open until the import is completed.
startImport=開始匯入
drawConfig=draw.io 組態
customLib=自訂圖庫
customTemp=自訂樣板
pageIdsExp=匯出頁面 ID
drawReindex=draw.io 重新索引中 (測試)
working=Working
drawConfigNotFoundInst=draw.io 組態空間 (DRAWIOCONFIG) 不存在。儲存 draw.io 組態檔和自訂圖庫/樣板時需要該空間。
createConfSp=建立組態空間
unexpErrRefresh=發生意外錯誤，請重新整理頁面，然後重試。
configJSONInst=Write draw.io JSON configuration in the editor below then click save. If you need help, please refer to
thisPage=this page
curCustLib=當前自訂圖庫
libName=圖庫名稱
action=動作
drawConfID=draw.io 組態 ID
addLibInst=點擊「新增圖庫」按鈕並上傳一份新圖庫。
addLib=新增圖庫
customTempInst1=Custom templates are draw.io diagrams saved in children pages of
customTempInst2=更多詳細資訊，請參考
tempsPage=樣板頁面
pageIdsExpInst1=選擇匯出目標，然後點擊「開始匯出」按鈕並匯出所有頁面 ID。
pageIdsExpInst2=Please note that the export procedure will take some time and the browser window must remain open until the export is completed.
startExp=開始匯出
refreshDrawIndex=重新整理 draw.io 圖表索引
reindexInst1=點擊「開始索引」按鈕並重新整理 draw.io 圖表索引。
reindexInst2=Please note that the indexing procedure will take some time and the browser window must remain open until the indexing is completed.
startIndexing=開始索引
confAPageFoundFetch=找到頁面「{1}」。獲取中
confAAllDiagDone=All {1} diagrams processed. Process finished.
confAStartedProcessing=開始處理頁面「{1}」
confAAllDiagInPageDone=All {1} diagrams in page "{2}" processed successfully.
confAPartialDiagDone={1} out of {2} {3} diagrams in page "{4}" processed successfully.
confAUpdatePageFailed=更新頁面「{1}」失敗。
confANoDiagFoundInPage=No {1} diagrams found in page "{2}".
confAFetchPageFailed=獲取頁面「{1}」失敗。
confANoDiagFound=No {1} diagrams found. Process finished.
confASearchFailed=Searching for {1} diagrams failed. Please try again later.
confAGliffyDiagFound={2} diagram "{1}" found. Importing
confAGliffyDiagImported={2} diagram "{1}" imported successfully.
confASavingImpGliffyFailed=Saving imported {2} diagram "{1}" failed.
confAImportedFromByDraw=Imported from "{1}" by draw.io
confAImportGliffyFailed=Importing {2} diagram "{1}" failed.
confAFetchGliffyFailed=Fetching {2} diagram "{1}" failed.
confACheckBrokenDiagLnk=Checking for broken diagrams links.
confADelDiagLinkOf=Deleting diagram link of "{1}"
confADupLnk=(duplicate link)
confADelDiagLnkFailed=Deleting diagram link of "{1}" failed.
confAUnexpErrProcessPage=Unexpected error during processing the page with id: {1}
confADiagFoundIndex=Diagram "{1}" found. Indexing
confADiagIndexSucc=Diagram "{1}" indexed successfully.
confAIndexDiagFailed=索引圖表「{1}」失敗。
confASkipDiagOtherPage=Skipped "{1}" as it belongs to another page!
confADiagUptoDate=Diagram "{1}" is up to date.
confACheckPagesWDraw=Checking pages having draw.io diagrams.
confAErrOccured=發生錯誤！
savedSucc=儲存成功
confASaveFailedErr=儲存失敗（預期外的錯誤）
character=字元
confAConfPageDesc=This page contains draw.io configuration file (configuration.json) as attachment
confALibPageDesc=This page contains draw.io custom libraries as attachments
confATempPageDesc=This page contains draw.io custom templates as attachments
working=Working
confAConfSpaceDesc=This space is used to store draw.io configuration files and custom libraries/templates
confANoCustLib=No Custom Libraries
delFailed=刪除失敗！
showID=顯示 ID
confAIncorrectLibFileType=無效的檔案類型。圖庫應該是 XML 檔案。
uploading=上傳中
confALibExist=圖庫已存在
confAUploadSucc=上傳成功
confAUploadFailErr=上傳失敗（意外錯誤）
hiResPreview=高解析度預覽
officeNotLoggedGD=您尚未登入 Google Drive。請開啟 draw.io 工作列並登入。
officePopupInfo=Please complete the process in the pop-up window.
pickODFile=選擇 OneDrive 檔案
createODFile=建立 OneDrive 檔案
pickGDriveFile=選擇 Google Drive 檔案
createGDriveFile=建立 Google Drive 檔案
pickDeviceFile=選擇裝置檔案
vsdNoConfig="vsdurl" is not configured
ruler=尺規
units=單位
points=點
inches=英吋
millimeters=毫米
confEditDraftDelOrExt=This diagram is in a draft page, is deleted from the page, or is edited externally. It will be saved as a new attachment version and may not be reflected in the page.
confDiagEditedExt=Diagram is edited in another session. It will be saved as a new attachment version but the page will show other session's modifications.
macroNotFound=找不到巨集
confAInvalidPageIdsFormat=Incorrect Page IDs file format
confACollectingCurPages=Collecting current pages
confABuildingPagesMap=Building pages mapping
confAProcessDrawDiag=已開始處理匯入的 draw.io 圖表
confAProcessDrawDiagDone=完成處理匯入的 draw.io 圖表
confAProcessImpPages=已開始處理匯入的頁面
confAErrPrcsDiagInPage=Error processing draw.io diagrams in page "{1}"
confAPrcsDiagInPage=Processing draw.io diagrams in page "{1}"
confAImpDiagram=匯入圖表「{1}」中
confAImpDiagramFailed=Importing diagram "{1}" failed. Cannot find its new page ID. Maybe it points to a page that is not imported.
confAImpDiagramError=Error importing diagram "{1}". Cannot fetch or save the diagram. Cannot fix this diagram links.
confAUpdateDgrmCCFailed=Updating link to diagram "{1}" failed.
confImpDiagramSuccess=Updating diagram "{1}" done successfully.
confANoLnksInDrgm=No links to update in: {1}
confAUpdateLnkToPg=Updated link to page: "{1}" in diagram: "{2}"
confAUpdateLBLnkToPg=Updated lightbox link to page: "{1}" in diagram: "{2}"
confAUpdateLnkBase=Updated base URL from: "{1}" to: "{2}" in diagram: "{3}"
confAPageIdsImpDone=頁面 ID 匯入已完成
confAPrcsMacrosInPage=正在處理頁面「{1}」中的 draw.io 巨集
confAErrFetchPage=Error fetching page "{1}"
confAFixingMacro=Fixing macro of diagram "{1}"
confAErrReadingExpFile=讀取匯出的檔案時發生錯誤
confAPrcsDiagInPageDone=Processing draw.io diagrams in page "{1}" finished
confAFixingMacroSkipped=Fixing macro of diagram "{1}" failed. Cannot find its new page ID. Maybe it points to a page that is not imported.
pageIdsExpTrg=匯出目標
confALucidDiagImgImported={2} diagram "{1}" image extracted successfully
confASavingLucidDiagImgFailed=Extracting {2} diagram "{1}" image failed
confGetInfoFailed=Fetching file info from {1} failed.
confCheckCacheFailed=Cannot get cached file info.
confReadFileErr=Cannot read "{1}" file from {2}.
confSaveCacheFailed=意外錯誤。無法儲存快取檔案。
orgChartType=Org Chart Type
linear=Linear
hanger2=Hanger 2
hanger4=Hanger 4
fishbone1=Fishbone 1
fishbone2=Fishbone 2
1ColumnLeft=Single Column Left
1ColumnRight=Single Column Right
smart=智慧
parentChildSpacing=父子間距
siblingSpacing=兄弟間距
confNoPermErr=抱歉，您沒有檢視頁面 {1} 中嵌入圖表的權限
copyAsImage=複製為圖片
lucidImport=匯入 Lucidchart
lucidImportInst1=點擊「開始匯入」按鈕並匯入所有 Lucidchart 圖表。
installFirst=請先安裝 {1}
drawioChromeExt=draw.io Chrome 擴充功能
loginFirstThen=請先登入 {1}，然後{2}
errFetchDocList=錯誤：無法獲取文件列表
builtinPlugins=內建外掛
extPlugins=外部外掛
backupFound=找到備份檔
chromeOnly=此功能僅可於 Google Chrome 運作
msgDeleted=訊息已刪除
confAErrFetchDrawList=無法獲取圖表列表。部分圖表已跳過。
confAErrCheckDrawDiag=無法檢查圖表 {1}
confAErrFetchPageList=取得頁面列表時發生錯誤
confADiagImportIncom={1} diagram "{2}" is imported partially and may have missing shapes
invalidSel=選取無效
diagNameEmptyErr=圖表名稱不得為空
openDiagram=開啟圖表
newDiagram=新增圖表
editable=可編輯
confAReimportStarted=重新匯入 {1} 圖表已開始...
spaceFilter=Filter by spaces
curViewState=當前檢視器狀態
pageLayers=頁面和圖層
customize=自訂
firstPage=First Page (All Layers)
curEditorState=當前編輯器狀態
noAnchorsFound=未找到錨點
attachment=附件
curDiagram=目前圖表
recentDiags=最近圖表
csvImport=匯入 CSV
chooseFile=選擇檔案…
choose=選擇
gdriveFname=Google Drive 檔案名稱
widthOfViewer=檢視器寬度 (px)
heightOfViewer=檢視器高度 (px)
autoSetViewerSize=自動設定檢視器的大小
thumbnail=縮圖
prevInDraw=在 draw.io 中預覽
onedriveFname=Onedrive 檔案名稱
diagFname=圖表檔案名稱
diagUrl=圖表網址
showDiag=顯示圖表
diagPreview=圖表預覽
csvFileUrl=CSV 檔案網址
generate=產生
selectDiag2Insert=請選擇圖表並插入。
errShowingDiag=意外錯誤。無法顯示圖表。
noRecentDiags=No recent diagrams found
fetchingRecentFailed=無法取得最近的圖表
useSrch2FindDiags=使用搜尋框來尋找 draw.io 圖表
cantReadChckPerms=無法讀取圖表，請確認您擁有該檔案的讀取權限。
cantFetchChckPerms=無法取得圖表資訊，請確認您具有該檔案的讀取權限。
searchFailed=搜尋失敗，請稍後再試。
plsTypeStr=請輸入一個搜尋字串。
unsupportedFileChckUrl=不支援的檔案，請檢查網址。
diagNotFoundChckUrl=圖表不存在或無法被存取，請檢查網址。
csvNotFoundChckUrl=CSV 檔不存在或無法被存取，請檢查網址。
cantReadUpload=無法讀取上傳的圖表
select=選擇
errCantGetIdType=Unexpected Error: Cannot get content id or type.
errGAuthWinBlocked=錯誤：Google Authentication 視窗被封鎖
authDrawAccess=驗證 {1} 以賦予 draw.io 存取權限
connTimeout=連線逾時
errAuthSrvc=無法與 {1} 認證
plsSelectFile=請選擇檔案
mustBgtZ={1} 須為大於 0 的值
cantLoadPrev=無法載入檔案預覽
errAccessFile=錯誤：存取被拒。您沒有存取「{1}」的權限。
noPrevAvail=預覽尚不可用。
personalAccNotSup=不支援個人帳戶。
errSavingTryLater=儲存時發生錯誤，請稍後再試
plsEnterFld=請輸入 {1}
invalidDiagUrl=無效的圖表網址
unsupportedVsdx=不支援的 vsdx 檔案
unsupportedImg=不支援的圖檔
unsupportedFormat=不支援的檔案格式
plsSelectSingleFile=請只選擇一個檔案
attCorrupt=附件檔「{1}」損毀
loadAttFailed=無法載入附件「{1}」
embedDrawDiag=嵌入 draw.io 圖表
addDiagram=新增圖表
embedDiagram=嵌入圖表
editOwningPg=編輯擁有的頁面
deepIndexing=Deep Indexing (Index diagrams that aren't used in any page also)
confADeepIndexStarted=深度索引已開始
confADeepIndexDone=深度索引已完成
officeNoDiagramsSelected=No diagrams found in the selection
officeNoDiagramsInDoc=No diagrams found in the document
officeNotSupported=This feature is not supported in this host application
someImagesFailed={1} out of {2} failed due to the following errors
importingNoUsedDiagrams=Importing {1} Diagrams not used in pages
importingDrafts=Importing {1} Diagrams in drafts
processingDrafts=處理草稿中
updatingDrafts=更新草稿中
updateDrafts=更新草稿
notifications=通知
drawioImp=draw.io 匯入
confALibsImp=匯入 draw.io 圖庫
confALibsImpFailed=匯入 {1} 圖庫失敗
contributors=貢獻者
drawDiagrams=draw.io 圖表
errFileNotFoundOrNoPer=錯誤：存取被拒。{2} 上的「{1}」不存在或缺少存取權限。
confACheckPagesWEmbed=正在檢查含有嵌入 draw.io 圖表的頁面
confADelBrokenEmbedDiagLnk=正在移除失效的圖表連結
replaceWith=取代為
replaceAll=全部取代
confASkipDiagModified=Skipped "{1}" as it was modified after initial import
replFind=取代/尋找
matchesRepl=已替換 {1} 次
draftErrDataLoss=An error occurred while reading the draft file. The diagram cannot be edited now to prevent any possible data loss. Please try again later or contact support.
ibm=IBM
linkToDiagramHint=Add a link to this diagram. The diagram can only be edited from the page that owns it.
linkToDiagram=Link to Diagram
changedBy=Changed By
lastModifiedOn=最後修改於
searchResults=搜尋結果
showAllTemps=顯示所有樣板
notionToken=Notion Token
selectDB=選擇資料庫
noDBs=無資料庫
diagramEdited={1} diagram "{2}" edited
confDraftPermissionErr=Draft cannot be written. Do you have attachment write/read permission on this page?
confFileTooBigErr=File size is too large. Pease check "Attachment Maximum Size" of "Attachment Settings" in Confluence Configuration.
owner=擁有者
repository=儲存庫
branch=分支
meters=公尺
teamsNoEditingMsg=Editor functionality is only available in Desktop environment (in MS Teams App or a web browser)
contactOwner=聯絡擁有者
viewerOnlyMsg=You cannot edit the diagrams in the mobile platform, please use the desktop client or a web browser.
website=網站
check4Updates=檢查更新
attWriteFailedRetry={1}: 附件寫入失敗，請在 {2} 秒後重試...
confPartialPageList=We couldn't fetch all pages due to an error in Confluence. Continuing using {1} pages only.
spellCheck=拼寫檢查
noChange=No Change
lblToSvg=Convert labels to SVG
txtSettings=文字設定
LinksLost=Links will be lost
arcSize=Arc Size
editConnectionPoints=編輯連接點
notInOffline=離線時不支援
notInDesktop=桌面應用程式中不支援
confConfigSpaceArchived=draw.io 組態空間 (DRAWIOCONFIG) 已封存。請先還原。
confACleanOldVerStarted=Cleaning old diagram draft versions started
confACleanOldVerDone=Cleaning old diagram draft versions finished
confACleaningFile=Cleaning diagram draft "{1}" old versions
confAFileCleaned=Cleaning diagram draft "{1}" done
confAFileCleanFailed=Cleaning diagram draft "{1}" failed
confACleanOnly=Clean Diagram Drafts Only
brush=筆刷
openDevTools=開啟開發人員工具
autoBkp=自動備份
confAIgnoreCollectErr=Ignore collecting current pages errors
drafts=草稿
draftSaveInt=Draft save interval [sec] (0 to disable)
pluginsDisabled=外部外掛已停用
extExpNotConfigured=External image service is not configured
pathFilename=路徑/檔名
confAHugeInstances=Very Large Instances
confAHugeInstancesDesc=If this instance includes 100,000+ pages, it is faster to request the current instance pages list from Atlassian. Please contact our support for more details.
choosePageIDsFile=Choose current page IDs csv file
chooseDrawioPsgesFile=Choose pages with draw.io diagrams csv file
private=私人
diagramTooLarge=The diagram is too large, please reduce its size and try again.
selectAdminUsers=Select Admin Users
xyzTeam={1} Team
addTeamTitle=新增一個 draw.io 團隊
addTeamInst1=To create a new draw.io Team, you need to create a new Atlassian group with "drawio-" prefix (e.g, a group named "drawio-marketing").
addTeamInst2=Then, configure which team member can edit/add configuration, templates, and libraries from this page.
drawioTeams=draw.io 團隊
members=成員
adminEditors=管理員/編輯者
allowAll=Allow all
noTeams=找不到團隊
errorLoadingTeams=團隊載入錯誤
noTeamMembers=找不到團隊成員
errLoadTMembers=團隊成員載入錯誤
errCreateTeamPage=Error creating team "{1}" page in "draw.io Configuration" space, please check you have the required permissions.
gotoConfigPage=Please create the space from draw.io "Configuration" page.
noAdminsSelected=No admins/editors selected
errCreateConfigFile=Error creating "configuration.json" file, please check you have the required permissions.
errSetPageRestr=Error setting page restrictions
notAdmin4Team=你不是該團隊的管理員
configUpdated=Configuration updated, restart the editor if you want to work with last configuration.
outOfDateRevisionAlert=You are editing a historical revision of the diagram, please review the revision and open it to replace the latest version. Or close and overwrite/merge later.
confAErrFaqs=There are {1} error(s), the following instructions may help fixing most of the cases. (Please download the log for future references)
confA403ErrFaq=There are ({1}) 403 error(s). The current users must have add (write) permissions on all pages and attachments. Even admins sometimes are not allowed to write to some pages via page restrictions
confA404ErrFaq=There are ({1}) 404 error(s). The attachment/page is not found. This is due to improper migration or the diagram file (an attachment of the page) is deleted.
confA500ErrFaq=There are ({1}) 500 error(s). An internal server error in Confluence Cloud. Such errors are due to overloading the server and usually fixed by retrying the process.
confAOtherErrFaq=There are ({1}) other error(s). Please check the error description. If the description is not clear, please contact our support.
confAReplaceBaseUrl=Replace Base URL in diagram links when no page ID mapping is found
drawSvgPrev=draw.io SVG preview
googleFonts=Google Fonts
diagDupl=Duplicate Diagram Detected
diagDuplMsg=This diagram is used in multiple places, which can result in unexpected results when edited. We've created an independent copy. Please open the editor again.
diagDuplNoEditMsg=This diagram is used in multiple places. Please edit it within its own page.
confCloudMigConfirm=Warning: This process will edit many pages and diagrams, so it is recommended to stop the Synchrony service during the process. Do you want to proceed?
confCloudMigNotice=In the Cloud instance, please add linkAdjustments to draw.io configuration as follows {1}. Without this configuation, links in diagrams pointing to Confluence pages will not work.
