@keyframes geBellAnim {
  0%,
  20%,
  80%,
  to {
    transform: rotate(0);
  }
  10% {
    transform: rotate(30deg);
  }
  90% {
    transform: rotate(-30deg);
  }
}
@keyframes geRadAnim {
  0%,
  20%,
  80%,
  to {
    transform: translateX(0);
  }
  10% {
    transform: translateX(5px);
  }
  90% {
    transform: translateX(-5px);
  }
}
@keyframes geZoomAnim {
  0% {
    opacity: 0;
    transform: scale(0);
  }
  50% {
    opacity: 1;
    transform: scale(1);
  }
  to {
    opacity: 1;
  }
}
@-webkit-keyframes geHere-am-i {
  0% {
    transform: translate3d(0, 50px, 0);
    opacity: 0;
  }
  to {
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}
@keyframes geHere-am-i {
  0% {
    transform: translate3d(0, 50px, 0);
    opacity: 0;
  }
  to {
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}
.mxCellEditor,
div.mxRubberband,
div.mxWindow {
  position: absolute;
  overflow: hidden;
}
div.mxRubberband {
  border-style: solid;
  border-width: 1px;
  border-color: #00f;
  background: #07f;
}
.mxCellEditor {
  background: url(data:image/gif;base64,R0lGODlhMAAwAIAAAP///wAAACH5BAEAAAAALAAAAAAwADAAAAIxhI+py+0Po5y02ouz3rz7D4biSJbmiabqyrbuC8fyTNf2jef6zvf+DwwKh8Si8egpAAA7);
  border-color: transparent;
  border-style: solid;
  display: inline-block;
  overflow: visible;
  word-wrap: normal;
  border-width: 0;
  min-width: 1px;
  resize: none;
}
.mxCellEditor,
.mxPlainTextEditor * {
  padding: 0;
  margin: 0;
}
div.mxWindow {
  box-shadow: 3px 3px 12px silver;
  background: url(data:image/gif;base64,R0lGODlhGgAUAIAAAOzs7PDw8CH5BAAAAAAALAAAAAAaABQAAAIijI+py70Ao5y02lud3lzhD4ZUR5aPiKajyZbqq7YyB9dhAQA7);
  border: 1px solid #c3c3c3;
  z-index: 1;
}
table.mxWindow {
  border-collapse: collapse;
  table-layout: fixed;
  font-family: inherit;
  font-size: 8pt;
}
td.mxWindowTitle {
  background: url(data:image/gif;base64,R0lGODlhFwAXAMQAANfX18rKyuHh4c7OzsDAwMHBwc/Pz+Li4uTk5NHR0dvb2+jo6O/v79/f3/n5+dnZ2dbW1uPj44yMjNPT0+Dg4N3d3ebm5szMzAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACH5BAAAAAAALAAAAAAXABcAAAWQICESxWiW5Ck6bOu+MMvMdG3f86LvfO/rlqBwSCwaj8ikUohoOp/QaDNCrVqvWKpgezhsv+AwmEIum89ocmPNbrvf64p8Tq/b5Yq8fs/v5x+AgYKDhIAAh4iJiouHEI6PkJGSjhOVlpeYmZUJnJ2en6CcBqMDpaanqKgXq6ytrq+rAbKztLW2shK5uru8vbkhADs=)
    repeat-x;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-align: center;
  font-weight: 700;
  overflow: hidden;
  height: 13px;
  padding: 4px 2px 6px;
  color: #000;
}
td.mxWindowPane {
  vertical-align: top;
  padding: 0;
}
div.mxWindowPane {
  overflow: hidden;
  position: relative;
}
td.mxPopupMenuItem,
td.mxWindowPane button,
td.mxWindowPane td {
  font-family: inherit;
  font-size: 8pt;
}
td.mxWindowPane input,
td.mxWindowPane radio,
td.mxWindowPane select,
td.mxWindowPane textarea {
  border-color: #8c8c8c;
  border-style: solid;
  border-width: 1px;
  font-family: inherit;
  font-size: 8pt;
  padding: 1px;
}
td.mxWindowPane button {
  background: url(data:image/gif;base64,R0lGODlhCgATALMAAP7+/t7e3vj4+Ojo6OXl5e/v7/n5+fb29vPz8/39/e3t7fHx8e7u7v///wAAAAAAACH5BAAAAAAALAAAAAAKABMAAAQ2MMlJhb0Y6c2X/2AhjiRjnqiirizqMkEsz0Rt30Ou7y8K/ouDcEg0GI9IgHLJbDif0Kh06owAADs=)
    repeat-x;
  padding: 2px;
  float: left;
}
img.mxToolbarItem {
  margin-right: 6px;
  margin-bottom: 6px;
  border-width: 1px;
}
select.mxToolbarCombo {
  vertical-align: top;
  border-style: inset;
  border-width: 2px;
}
div.mxToolbarComboContainer {
  padding: 2px;
}
img.mxToolbarMode {
  margin: 2px 4px 4px 2px;
  border-width: 0;
}
img.mxToolbarModeSelected {
  margin: 0 2px 2px 0;
  border-width: 2px;
  border-style: inset;
}
div.mxPopupMenu,
div.mxTooltip {
  box-shadow: 3px 3px 12px silver;
  position: absolute;
  border-style: solid;
  border-width: 1px;
  border-color: #000;
}
div.mxTooltip {
  background: #ffc;
  font-family: inherit;
  font-size: 8pt;
  cursor: default;
  padding: 4px;
  color: #000;
}
div.mxPopupMenu {
  background-color: #fff;
}
table.mxPopupMenu {
  border-collapse: collapse;
  margin-top: 1px;
  margin-bottom: 1px;
}
tr.mxPopupMenuItem {
  color: #000;
  cursor: pointer;
}
tr.mxPopupMenuItemHover {
  background-color: #006;
  color: #fff;
  cursor: pointer;
}
td.mxPopupMenuItem {
  padding: 2px 30px 2px 10px;
  white-space: nowrap;
}
td.mxPopupMenuIcon {
  padding: 2px 4px;
}
.mxDisabled {
  opacity: 0.2 !important;
  cursor: default !important;
}
.geEditor {
  font-family: inherit;
  font-size: 14px;
  border: 0;
  margin: 0;
}
.geEditor input,
button,
select,
textarea {
  font-size: inherit;
}
.geEditor input {
  border-width: 2px;
}
.geEditor select {
  border-width: 1px;
}
.geEditor div.mxTooltip {
  background: #f5f5f5;
  border-color: #d3d3d3;
  font-size: 11px;
  color: #000;
  padding: 6px;
}
.geDragPreview {
  border: 1px dashed #000;
}
.geMenubarContainer .geItem,
.geToolbar .geButton,
.geToolbar .geLabel {
  cursor: pointer !important;
}
.geSidebarContainer .geTitle {
  cursor: default !important;
}
.geBackgroundPage {
  box-shadow: 0 0 2px 1px #d1d1d1;
}
.geMenubarContainer a,
.geSidebarContainer a,
.geToolbar a {
  color: #000;
  text-decoration: none;
}
.geDiagramContainer,
.geMenubarContainer,
.geToolbarContainer {
  overflow: hidden;
  position: absolute;
  cursor: default;
}
.geFooterContainer,
.geSidebarContainer {
  cursor: default;
}
.geFooterContainer,
.geHsplit,
.geVsplit {
  overflow: hidden;
  position: absolute;
}
.geFormatContainer {
  overflow-x: hidden !important;
  overflow-y: auto !important;
  font-size: 12px;
  border-left: 1px solid #dadce0;
}
.geSidebarFooter {
  border-top: 1px solid #dadce0;
}
.geFormatSection {
  border-bottom: 1px solid #dadce0;
  border-color: #dadce0;
}
.geDiagramContainer {
  background-color: #fff;
  font-size: 0;
  outline: 0;
}
.geMenubar,
.geToolbar {
  white-space: nowrap;
  display: block;
  width: 100%;
}
.geMenubarContainer .geItem,
.geSidebar,
.geSidebar .geItem,
.geSidebarContainer .geTitle,
.geToolbar .geButton,
.geToolbar .geLabel,
.mxPopupMenuItem {
  transition: all 0.1s ease-in-out;
}
.geHint {
  background-color: #fff;
  border: 1px solid gray;
  padding: 4px 16px;
  border-radius: 3px;
  box-shadow: 1px 1px 2px 0 #ddd;
  opacity: 0.8;
  font-size: 9pt;
}
.geStatusAlert,
.geStatusAlertOrange {
  margin-top: -5px;
  font-size: 12px;
  padding: 4px 6px;
}
.geStatusAlert {
  white-space: nowrap;
  background-color: #f2dede;
  border: 1px solid #ebccd1;
  color: #a94442 !important;
  border-radius: 3px;
}
.geStatusAlertOrange {
  background-color: #f2931e;
  border: #f08705;
  color: #000 !important;
  opacity: 0.8;
}
.geStatusAlertOrange:hover {
  opacity: 1;
}
.geAlert,
.geStatusAlertOrange,
.geStatusMessage {
  white-space: nowrap;
  border-radius: 3px;
}
.geStatusMessage {
  margin-top: -5px;
  padding: 4px 6px;
  font-size: 12px;
  background: linear-gradient(to bottom, #dff0d8 0, #c8e5bc 100%);
  background-repeat: repeat-x;
  border: 1px solid #b2dba1;
  color: #3c763d !important;
}
.geAlert {
  position: absolute;
  padding: 14px;
  background-color: #f2dede;
  border: 1px solid #ebccd1;
  color: #a94442;
  box-shadow: 2px 2px 3px 0 #ddd;
}
.geBtn,
.mxWindow .geBtn {
  background-image: none;
  background-color: #f5f5f5;
  border-radius: 2px;
  border: 1px solid #d8d8d8;
  color: #333;
  font-size: 13px;
  font-weight: 500;
  letter-spacing: 0.25px;
  height: 29px;
  line-height: 27px;
  margin: 0 0 0 8px;
  min-width: 72px;
  outline: 0;
  padding: 0 8px;
  cursor: pointer;
}
.geBtn:focus,
.geBtn:hover {
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
  border: 1px solid #c6c6c6;
  background-color: #f8f8f8;
  background-image: linear-gradient(#f8f8f8 0, #f1f1f1 100%);
  color: #111;
}
.geBtn:active,
.geStatus:active {
  opacity: 0.7;
}
.geBtn:disabled {
  opacity: 0.5;
}
.geToolbarContainer > .geToolbar > div > a:active {
  opacity: 0.5;
}
.geBtnDown,
.geBtnUp {
  background-image: url(data:image/gif;base64,R0lGODlhCgAGAJECAGZmZtXV1f///wAAACH/C1hNUCBEYXRhWE1QPD94cGFja2V0IGJlZ2luPSLvu78iIGlkPSJXNU0wTXBDZWhpSHpyZVN6TlRjemtjOWQiPz4gPHg6eG1wbWV0YSB4bWxuczp4PSJhZG9iZTpuczptZXRhLyIgeDp4bXB0az0iQWRvYmUgWE1QIENvcmUgNS4wLWMwNjAgNjEuMTM0Nzc3LCAyMDEwLzAyLzEyLTE3OjMyOjAwICAgICAgICAiPiA8cmRmOlJERiB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmRmLXN5bnRheC1ucyMiPiA8cmRmOkRlc2NyaXB0aW9uIHJkZjphYm91dD0iIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8iIHhtbG5zOnhtcE1NPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvbW0vIiB4bWxuczpzdFJlZj0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL3NUeXBlL1Jlc291cmNlUmVmIyIgeG1wOkNyZWF0b3JUb29sPSJBZG9iZSBQaG90b3Nob3AgQ1M1IE1hY2ludG9zaCIgeG1wTU06SW5zdGFuY2VJRD0ieG1wLmlpZDo0QzM3ODJERjg4NUQxMUU0OTFEQ0E2MzRGQzcwNUY3NCIgeG1wTU06RG9jdW1lbnRJRD0ieG1wLmRpZDo0QzM3ODJFMDg4NUQxMUU0OTFEQ0E2MzRGQzcwNUY3NCI+IDx4bXBNTTpEZXJpdmVkRnJvbSBzdFJlZjppbnN0YW5jZUlEPSJ4bXAuaWlkOjRDMzc4MkREODg1RDExRTQ5MURDQTYzNEZDNzA1Rjc0IiBzdFJlZjpkb2N1bWVudElEPSJ4bXAuZGlkOjRDMzc4MkRFODg1RDExRTQ5MURDQTYzNEZDNzA1Rjc0Ii8+IDwvcmRmOkRlc2NyaXB0aW9uPiA8L3JkZjpSREY+IDwveDp4bXBtZXRhPiA8P3hwYWNrZXQgZW5kPSJyIj8+Af/+/fz7+vn49/b19PPy8fDv7u3s6+rp6Ofm5eTj4uHg397d3Nva2djX1tXU09LR0M/OzczLysnIx8bFxMPCwcC/vr28u7q5uLe2tbSzsrGwr66trKuqqainpqWko6KhoJ+enZybmpmYl5aVlJOSkZCPjo2Mi4qJiIeGhYSDgoGAf359fHt6eXh3dnV0c3JxcG9ubWxramloZ2ZlZGNiYWBfXl1cW1pZWFdWVVRTUlFQT05NTEtKSUhHRkVEQ0JBQD8+PTw7Ojk4NzY1NDMyMTAvLi0sKyopKCcmJSQjIiEgHx4dHBsaGRgXFhUUExIREA8ODQwLCgkIBwYFBAMCAQAAIfkEAQAAAgAsAAAAAAoABgAAAg6UjwiQBhGYglCKhXFLBQA7);
  background-position: center center;
  background-repeat: no-repeat;
}
.geBtnDown:active,
.geBtnUp:active {
  background-color: #4d90fe;
  background-image: linear-gradient(#4d90fe 0, #357ae8 100%);
}
.geBtnDown {
  background-image: url(data:image/gif;base64,R0lGODlhCgAGAJECANXV1WZmZv///wAAACH/C1hNUCBEYXRhWE1QPD94cGFja2V0IGJlZ2luPSLvu78iIGlkPSJXNU0wTXBDZWhpSHpyZVN6TlRjemtjOWQiPz4gPHg6eG1wbWV0YSB4bWxuczp4PSJhZG9iZTpuczptZXRhLyIgeDp4bXB0az0iQWRvYmUgWE1QIENvcmUgNS4wLWMwNjAgNjEuMTM0Nzc3LCAyMDEwLzAyLzEyLTE3OjMyOjAwICAgICAgICAiPiA8cmRmOlJERiB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmRmLXN5bnRheC1ucyMiPiA8cmRmOkRlc2NyaXB0aW9uIHJkZjphYm91dD0iIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8iIHhtbG5zOnhtcE1NPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvbW0vIiB4bWxuczpzdFJlZj0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL3NUeXBlL1Jlc291cmNlUmVmIyIgeG1wOkNyZWF0b3JUb29sPSJBZG9iZSBQaG90b3Nob3AgQ1M1IE1hY2ludG9zaCIgeG1wTU06SW5zdGFuY2VJRD0ieG1wLmlpZDo0QzM3ODJEQjg4NUQxMUU0OTFEQ0E2MzRGQzcwNUY3NCIgeG1wTU06RG9jdW1lbnRJRD0ieG1wLmRpZDo0QzM3ODJEQzg4NUQxMUU0OTFEQ0E2MzRGQzcwNUY3NCI+IDx4bXBNTTpEZXJpdmVkRnJvbSBzdFJlZjppbnN0YW5jZUlEPSJ4bXAuaWlkOjRDMzc4MkQ5ODg1RDExRTQ5MURDQTYzNEZDNzA1Rjc0IiBzdFJlZjpkb2N1bWVudElEPSJ4bXAuZGlkOjRDMzc4MkRBODg1RDExRTQ5MURDQTYzNEZDNzA1Rjc0Ii8+IDwvcmRmOkRlc2NyaXB0aW9uPiA8L3JkZjpSREY+IDwveDp4bXBtZXRhPiA8P3hwYWNrZXQgZW5kPSJyIj8+Af/+/fz7+vn49/b19PPy8fDv7u3s6+rp6Ofm5eTj4uHg397d3Nva2djX1tXU09LR0M/OzczLysnIx8bFxMPCwcC/vr28u7q5uLe2tbSzsrGwr66trKuqqainpqWko6KhoJ+enZybmpmYl5aVlJOSkZCPjo2Mi4qJiIeGhYSDgoGAf359fHt6eXh3dnV0c3JxcG9ubWxramloZ2ZlZGNiYWBfXl1cW1pZWFdWVVRTUlFQT05NTEtKSUhHRkVEQ0JBQD8+PTw7Ojk4NzY1NDMyMTAvLi0sKyopKCcmJSQjIiEgHx4dHBsaGRgXFhUUExIREA8ODQwLCgkIBwYFBAMCAQAAIfkEAQAAAgAsAAAAAAoABgAAAg6UjxLLewEiCAnOZBzeBQA7);
}
.geColorBtn {
  background-color: #f5f5f5;
  background-image: linear-gradient(#f5f5f5 0, #e1e1e1 100%);
  border-radius: 4px;
  border: 1px solid rgba(0, 0, 0, 0.5);
  color: #333;
  margin: 0;
  outline: 0;
  padding: 0;
  cursor: pointer;
}
.geColorBtn:hover {
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.7);
}
.geColorBtn:active {
  background-color: #4d90fe;
  background-image: linear-gradient(#4d90fe 0, #357ae8 100%);
  border: 1px solid #2f5bb7;
  color: #fff;
}
.geColorBtn:disabled {
  opacity: 0.5;
}
.gePrimaryBtn,
.mxWindow .gePrimaryBtn {
  background-color: #4d90fe;
  background-image: linear-gradient(#4d90fe 0, #4787ed 100%);
  border: 1px solid #3079ed;
  color: #fff;
}
.gePrimaryBtn:focus,
.gePrimaryBtn:hover {
  background-color: #357ae8;
  background-image: linear-gradient(#4d90fe 0, #357ae8 100%);
  border: 1px solid #2f5bb7;
  color: #fff;
}
.gePrimaryBtn:disabled {
  opacity: 0.5;
}
.geAlertLink,
.geFooterContainer a {
  color: #843534;
  font-weight: 700;
  text-decoration: none;
}
.geActivePage {
  font-weight: 700;
  color: #188038 !important;
}
.geHsplit,
.geMenubarContainer,
.geToolbarContainer,
.geVsplit {
  background-color: #fbfbfb;
}
.geMenubar {
  padding: 0 2px;
  vertical-align: middle;
}
.geMenubarContainer .geItem,
.geToolbar .geItem {
  padding: 6px 6px 6px 9px;
}
.geToolbar .geItem {
  cursor: default;
}
.geMenubarContainer .geItem:hover {
  background: #eee;
  border-radius: 2px;
}
.geMenubarContainer .geItem:active {
  background: #f8c382;
}
.geToolbarContainer .geButton:hover {
  opacity: 1;
  background: #eee;
  border-radius: 2px;
}
.geToolbarContainer .geButton:active,
.geToolbarContainer .geLabel:active {
  background: #f8c382;
}
.geToolbarContainer .geLabel:hover {
  background: #eee;
  border-radius: 2px;
}
.geActiveButton:active {
  opacity: 0.3;
}
.geToolbarButton {
  opacity: 0.6;
}
.geToolbarButton:active {
  opacity: 0.2;
}
.mxDisabled:hover {
  background: inherit !important;
}
.geMenubar a.geStatus {
  color: #888;
  padding-left: 12px;
  display: inline-block;
  cursor: default !important;
}
.geMenubar a.geStatus:hover {
  background: 0 0;
}
.geToolbarContainer {
  border-bottom: 1px solid #dadce0;
}
.geSidebarContainer .geToolbarContainer {
  background: 0 0;
  border-bottom: none;
}
.geSidebarContainer button {
  text-overflow: ellipsis;
  overflow: hidden;
}
.geToolbar {
  border-top: 1px solid #dadce0;
  box-shadow: inset 0 1px 0 0 #fff;
  padding: 5px 0 0 6px;
}
.geToolbarContainer .geSeparator {
  float: left;
  width: 1px;
  height: 20px;
  background: #e5e5e5;
  margin-left: 6px;
  margin-right: 6px;
  margin-top: 4px;
}
.geToolbarContainer .geButton,
.geToolbarContainer .geLabel {
  float: left;
  margin: 2px;
  cursor: pointer;
  border: 1px solid transparent;
}
.geToolbarContainer .geButton {
  width: 20px;
  height: 20px;
  padding: 0 2px 4px;
  opacity: 0.6;
}
div.mxWindow .geButton {
  margin: -1px 2px 2px;
  padding: 1px 2px 2px 1px;
}
.geToolbarContainer .geLabel {
  padding: 3px 5px;
}
.geToolbarContainer .mxDisabled:hover {
  border: 1px solid transparent !important;
  opacity: 0.2 !important;
}
.geDiagramBackdrop {
  background-color: #f8f9fa;
}
.geSidebarContainer {
  background: #fbfbfb;
  position: absolute;
  overflow: auto;
}
.geTabContainer {
  border-top: 1px solid #e5e5e5;
  background-color: #f1f3f4;
}
.geSidebar,
.geSidebarContainer .geTitle {
  border-bottom: 1px solid #e5e5e5;
  overflow: hidden;
}
.geSidebar {
  padding: 6px 6px 6px 10px;
}
.geSidebarContainer .geTitle {
  display: block;
  font-size: 13px;
  font-weight: 500;
  padding: 8px 0 8px 14px;
  margin: 0;
  white-space: nowrap;
  text-overflow: ellipsis;
  line-height: 1.4em;
}
.geSidebarContainer .geTitle:hover {
  background: #eee;
  border-radius: 2px;
}
.geSidebarContainer .geTitle:active {
  background-color: #f8c382;
}
.geSidebarContainer .geDropTarget {
  border-radius: 10px;
  border: 2px dotted #b0b0b0;
  text-align: center;
  padding: 6px;
  margin: 6px;
  color: #a0a0a0;
  font-size: 13px;
}
.geTitle img {
  opacity: 0.5;
}
.geTitle img:hover {
  opacity: 1;
}
.geTitle .geButton {
  border: 1px solid transparent;
  padding: 3px;
  border-radius: 2px;
}
.geTitle .geButton:hover {
  border: 1px solid gray;
}
.geSidebar .geItem {
  display: inline-block;
  background-repeat: no-repeat;
  background-position: 50% 50%;
  border-radius: 8px;
}
.geHsplit:hover,
.geSidebar .geItem:hover,
.geVsplit:hover {
  background-color: #e0e0e0;
}
.geItem {
  vertical-align: top;
  display: inline-block;
}
.geSidebarTooltip {
  position: absolute;
  background: #fbfbfb;
  overflow: hidden;
  box-shadow: 0 2px 6px 2px rgba(60, 64, 67, 0.15);
  border-radius: 6px;
}
.geFooterContainer {
  background: #e5e5e5;
  border-top: 1px solid silver;
}
.geFooterContainer a {
  display: inline-block;
  box-sizing: border-box;
  width: 100%;
  white-space: nowrap;
  font-size: 14px;
  color: #235695;
}
.geFooterContainer table,
html table.mxPopupMenu {
  border-collapse: collapse;
  margin: 0 auto;
}
.geFooterContainer td {
  border-left: 1px solid silver;
  border-right: 1px solid silver;
}
.geFooterContainer td:hover {
  background-color: #b3b3b3;
}
.geHsplit,
.geVsplit {
  cursor: col-resize;
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAQAAAAHBAMAAADdS/HjAAAAGFBMVEUzMzP///9tbW1QUFCKiopBQUF8fHxfX1/IXlmXAAAAHUlEQVQImWMQEGAQFWUQFmYQF2cQEmIQE2MQEQEACy4BF67hpEwAAAAASUVORK5CYII=);
  background-repeat: no-repeat;
  background-position: center center;
}
.geVsplit {
  font-size: 1pt;
  cursor: row-resize;
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAcAAAAEBAMAAACw6DhOAAAAGFBMVEUzMzP///9tbW1QUFCKiopBQUF8fHxfX1/IXlmXAAAAFElEQVQImWNgNVdzYBAUFBRggLMAEzYBy29kEPgAAAAASUVORK5CYII=);
}
.geHsplit {
  border-left: 1px solid #e5e5e5;
  border-right: 1px solid #e5e5e5;
}
.geVSplit {
  border-top: 1px solid #e5e5e5;
  border-bottom: 1px solid #e5e5e5;
}
.geDialog {
  background: #fff;
  line-height: 1em;
  padding: 30px;
  border: 1px solid #acacac;
  box-shadow: 0 0 2px 2px #d5d5d5;
}
.geDialog,
.geTransDialog {
  position: absolute;
  overflow: hidden;
  z-index: 2;
}
.geDialogClose {
  position: absolute;
  width: 9px;
  height: 9px;
  opacity: 0.5;
  cursor: pointer;
}
.geDialogClose:hover {
  opacity: 1;
}
.geDialogFooter,
.geDialogTitle {
  white-space: nowrap;
  box-sizing: border-box;
}
.geDialogTitle {
  background: #e5e5e5;
  border-bottom: 1px solid silver;
  font-size: 15px;
  font-weight: 700;
  text-align: center;
  color: #235695;
}
.geDialogFooter {
  background: #f5f5f5;
  text-align: right;
  border-top: 1px solid #e5e5e5;
  color: #a9a9a9;
}
.geSprite {
  background: url(data:image/png;base64,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)
    no-repeat;
  width: 21px;
  height: 21px;
}
.geBaseButton {
  padding: 10px;
  border-radius: 6px;
  border: 1px solid silver;
  cursor: pointer;
  background-color: #ececec;
  background-image: linear-gradient(#ececec 0, #fcfcfc 100%);
}
.geBaseButton:hover {
  background: #ececec;
}
.geBigButton {
  color: #fff;
  border: 0;
  padding: 4px 10px;
  font-size: 14px;
  white-space: nowrap;
  border-radius: 3px;
  background-color: #0052cc;
  cursor: pointer;
  transition: background-color 0.1s ease-out;
  overflow: hidden;
  text-overflow: ellipsis;
}
.geBigButton:hover {
  background-color: #0065ff;
}
.geBigButton:active {
  background-color: #0747a6;
}
html body .geBigStandardButton {
  color: #344563;
  background-color: rgba(9, 30, 66, 0.08);
}
html body .geBigStandardButton:hover {
  background-color: rgba(9, 30, 66, 0.13);
}
html body .geBigStandardButton:active {
  background-color: #f8c382;
  color: #600000;
}
@media print {
  div.geNoPrint {
    display: none !important;
  }
}
.geSprite-actualsize {
  background-position: 0 0;
}
.geSprite-bold {
  background-position: 0 -46px;
}
.geSprite-bottom {
  background-position: 0 -92px;
}
.geSprite-center {
  background-position: 0 -138px;
}
.geSprite-delete {
  background-position: 0 -184px;
}
.geSprite-fillcolor {
  background-position: 0 -229px;
}
.geSprite-fit {
  background-position: 0 -277px;
}
.geSprite-fontcolor {
  background-position: 0 -322px;
}
.geSprite-gradientcolor {
  background-position: 0 -368px;
}
.geSprite-image {
  background-position: 0 -414px;
}
.geSprite-italic {
  background-position: 0 -460px;
}
.geSprite-left {
  background-position: 0 -505px;
}
.geSprite-middle {
  background-position: 0 -552px;
}
.geSprite-print {
  background-position: 0 -598px;
}
.geSprite-redo {
  background-position: 0 -644px;
}
.geSprite-right {
  background-position: 0 -689px;
}
.geSprite-shadow {
  background-position: 0 -735px;
}
.geSprite-strokecolor {
  background-position: 0 -782px;
}
.geSprite-top {
  background-position: 0 -828px;
}
.geSprite-underline {
  background-position: 0 -874px;
}
.geSprite-undo {
  background-position: 0 -920px;
}
.geSprite-zoomin {
  background-position: 0 -966px;
}
.geSprite-zoomout {
  background-position: 0 -1012px;
}
.geSprite-arrow {
  background-position: 0 -1059px;
}
.geSprite-linkedge {
  background-position: 0 -1105px;
}
.geSprite-straight {
  background-position: 0 -1150px;
}
.geSprite-entity {
  background-position: 0 -1196px;
}
.geSprite-orthogonal {
  background-position: 0 -1242px;
}
.geSprite-curved {
  background-position: 0 -1288px;
}
.geSprite-noarrow {
  background-position: 0 -1334px;
}
.geSprite-endclassic {
  background-position: 0 -1380px;
}
.geSprite-endopen {
  background-position: 0 -1426px;
}
.geSprite-endblock {
  background-position: 0 -1472px;
}
.geSprite-endoval {
  background-position: 0 -1518px;
}
.geSprite-enddiamond {
  background-position: 0 -1564px;
}
.geSprite-endthindiamond {
  background-position: 0 -1610px;
}
.geSprite-endclassictrans {
  background-position: 0 -1656px;
}
.geSprite-endblocktrans {
  background-position: 0 -1702px;
}
.geSprite-endovaltrans {
  background-position: 0 -1748px;
}
.geSprite-enddiamondtrans {
  background-position: 0 -1794px;
}
.geSprite-endthindiamondtrans {
  background-position: 0 -1840px;
}
.geSprite-startclassic {
  background-position: 0 -1886px;
}
.geSprite-startopen {
  background-position: 0 -1932px;
}
.geSprite-startblock {
  background-position: 0 -1978px;
}
.geSprite-startoval {
  background-position: 0 -2024px;
}
.geSprite-startdiamond {
  background-position: 0 -2070px;
}
.geSprite-startthindiamond {
  background-position: 0 -2116px;
}
.geSprite-startclassictrans {
  background-position: 0 -2162px;
}
.geSprite-startblocktrans {
  background-position: 0 -2208px;
}
.geSprite-startovaltrans {
  background-position: 0 -2254px;
}
.geSprite-startdiamondtrans {
  background-position: 0 -2300px;
}
.geSprite-startthindiamondtrans {
  background-position: 0 -2346px;
}
.geSprite-globe {
  background-position: 0 -2392px;
}
.geSprite-orderedlist {
  background-position: 0 -2438px;
}
.geSprite-unorderedlist {
  background-position: 0 -2484px;
}
.geSprite-horizontalrule {
  background-position: 0 -2530px;
}
.geSprite-link {
  background-position: 0 -2576px;
}
.geSprite-indent {
  background-position: 0 -2622px;
}
.geSprite-outdent {
  background-position: 0 -2668px;
}
.geSprite-code {
  background-position: 0 -2714px;
}
.geSprite-fontbackground {
  background-position: 0 -2760px;
}
.geSprite-removeformat {
  background-position: 0 -2806px;
}
.geSprite-superscript {
  background-position: 0 -2852px;
}
.geSprite-subscript {
  background-position: 0 -2898px;
}
.geSprite-table {
  background-position: 0 -2944px;
}
.geSprite-deletecolumn {
  background-position: 0 -2990px;
}
.geSprite-deleterow {
  background-position: 0 -3036px;
}
.geSprite-insertcolumnafter {
  background-position: 0 -3082px;
}
.geSprite-insertcolumnbefore {
  background-position: 0 -3128px;
}
.geSprite-insertrowafter {
  background-position: 0 -3174px;
}
.geSprite-insertrowbefore {
  background-position: 0 -3220px;
}
.geSprite-grid {
  background-position: 0 -3272px;
}
.geSprite-guides {
  background-position: 0 -3324px;
}
.geSprite-dots {
  background-position: 0 -3370px;
}
.geSprite-alignleft {
  background-position: 0 -3416px;
}
.geSprite-alignright {
  background-position: 0 -3462px;
}
.geSprite-aligncenter {
  background-position: 0 -3508px;
}
.geSprite-aligntop {
  background-position: 0 -3554px;
}
.geSprite-alignbottom {
  background-position: 0 -3600px;
}
.geSprite-alignmiddle {
  background-position: 0 -3646px;
}
.geSprite-justifyfull {
  background-position: 0 -3692px;
}
.geSprite-formatpanel {
  background-position: 0 -3738px;
}
.geSprite-connection {
  background-position: 0 -3784px;
}
.geSprite-vertical {
  background-position: 0 -3830px;
}
.geSprite-simplearrow {
  background-position: 0 -3876px;
}
.geSprite-plus {
  background-position: 0 -3922px;
}
.geSprite-rounded {
  background-position: 0 -3968px;
}
.geSprite-toback {
  background-position: 0 -4014px;
}
.geSprite-tofront {
  background-position: 0 -4060px;
}
.geSprite-duplicate {
  background-position: 0 -4106px;
}
.geSprite-insert {
  background-position: 0 -4152px;
}
.geSprite-endblockthin {
  background-position: 0 -4201px;
}
.geSprite-endblockthintrans {
  background-position: 0 -4247px;
}
.geSprite-enderone {
  background-position: 0 -4293px;
}
.geSprite-enderonetoone {
  background-position: 0 -4339px;
}
.geSprite-enderonetomany {
  background-position: 0 -4385px;
}
.geSprite-endermany {
  background-position: 0 -4431px;
}
.geSprite-enderoneopt {
  background-position: 0 -4477px;
}
.geSprite-endermanyopt {
  background-position: 0 -4523px;
}
.geSprite-endclassicthin {
  background-position: 0 -4938px;
}
.geSprite-endclassicthintrans {
  background-position: 0 -4984px;
}
.geSprite-enddash {
  background-position: 0 -5029px;
}
.geSprite-endcircleplus {
  background-position: 0 -5075px;
}
.geSprite-endcircle {
  background-position: 0 -5121px;
}
.geSprite-endasync {
  background-position: 0 -5167px;
}
.geSprite-endasynctrans {
  background-position: 0 -5213px;
}
.geSprite-startblockthin {
  background-position: 0 -4569px;
}
.geSprite-startblockthintrans {
  background-position: 0 -4615px;
}
.geSprite-starterone {
  background-position: 0 -4661px;
}
.geSprite-starteronetoone {
  background-position: 0 -4707px;
}
.geSprite-starteronetomany {
  background-position: 0 -4753px;
}
.geSprite-startermany {
  background-position: 0 -4799px;
}
.geSprite-starteroneopt {
  background-position: 0 -4845px;
}
.geSprite-startermanyopt {
  background-position: 0 -4891px;
}
.geSprite-startclassicthin {
  background-position: 0 -5259px;
}
.geSprite-startclassicthintrans {
  background-position: 0 -5305px;
}
.geSprite-startdash {
  background-position: 0 -5351px;
}
.geSprite-startcircleplus {
  background-position: 0 -5397px;
}
.geSprite-startcircle {
  background-position: 0 -5443px;
}
.geSprite-startasync {
  background-position: 0 -5489px;
}
.geSprite-startasynctrans {
  background-position: 0 -5535px;
}
.geSprite-startcross {
  background-position: 0 -5581px;
}
.geSprite-startopenthin {
  background-position: 0 -5627px;
}
.geSprite-startopenasync {
  background-position: 0 -5673px;
}
.geSprite-endcross {
  background-position: 0 -5719px;
}
.geSprite-endopenthin {
  background-position: 0 -5765px;
}
.geSprite-endopenasync {
  background-position: 0 -5811px;
}
.geSprite-verticalelbow {
  background-position: 0 -5857px;
}
.geSprite-horizontalelbow {
  background-position: 0 -5903px;
}
.geSprite-horizontalisometric {
  background-position: 0 -5949px;
}
.geSprite-verticalisometric {
  background-position: 0 -5995px;
}
.geSvgSprite {
  background-position: center center;
}
.geFlipSprite {
  transform: scaleX(-1);
}
.geSprite-box {
  background-image: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='20' height='10' transform='translate(0.5,0.5)'><rect stroke='black' fill='none' x='2' y='2' width='6' height='6'/><path stroke='black' d='M8 5 L 18 5'/></svg>");
}
.geSprite-halfCircle {
  background-image: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='20' height='10' transform='translate(0.5,0.5)'><path stroke='black' fill='none' d='M 2 2 Q 6 2 6 5 Q 6 8 2 8 M 6 5 L 18 5'/></svg>");
}
html div.mxRubberband {
  border-color: #00d;
  background: #9cf;
}
td.mxPopupMenuIcon div {
  width: 16px;
  height: 16px;
}
.geEditor div.mxPopupMenu {
  box-shadow: 0 2px 6px 2px rgba(60, 64, 67, 0.15);
  background: #fff;
  border-radius: 4px;
  border: 0;
  padding: 3px;
}
html table.mxPopupMenu {
  margin: 0;
}
html td.mxPopupMenuItem {
  padding: 7px 30px;
  font-family: inherit;
  font-size: 10pt;
}
html td.mxPopupMenuIcon {
  padding: 0;
}
td.mxPopupMenuIcon .geIcon {
  padding: 2px 2px 4px;
  margin: 2px;
  border: 1px solid transparent;
  opacity: 0.5;
}
td.mxPopupMenuIcon .geIcon:hover {
  border: 1px solid gray;
  border-radius: 2px;
  opacity: 1;
}
html tr.mxPopupMenuItemHover {
  background-color: #eee;
  color: #000;
}
table.mxPopupMenu hr {
  color: #ccc;
  background-color: #ccc;
  border: 0;
  height: 1px;
}
table.mxPopupMenu tr {
  font-size: 4pt;
}
html td.mxWindowTitle {
  font-family: inherit;
  text-align: left;
  font-size: 12px;
  color: #707070;
  padding: 4px;
}
table.geProperties tr td {
  height: 21px;
}
.gePropHeader,
.gePropRow {
  border: 1px solid #e9e9e9;
}
.gePropRowDark {
  border: 1px solid #4472c4;
}
.gePropHeader > .gePropHeaderCell {
  border-top: 0;
  border-bottom: 0;
  text-align: left;
  width: 50%;
}
.gePropHeader > .gePropHeaderCell:first-child {
  border-left: none;
}
.gePropHeader > .gePropHeaderCell:last-child {
  border-right: none;
}
.gePropHeader {
  background: #e5e5e5;
  color: #000;
}
.gePropRowCell {
  border-left: 1px solid #f3f3f3;
  width: 50%;
}
.gePropRow > .gePropRowCell {
  background: #fff;
}
.gePropRowAlt > .gePropRowCell {
  background: #fcfcfc;
}
.gePropRowDark > .gePropRowCell {
  background: #fff;
  color: #305496;
  font-weight: 700;
}
.gePropRowDarkAlt > .gePropRowCell {
  background: #d9e1f2;
  color: #305496;
  font-weight: 700;
}
.gePropEditor input:invalid {
  border: 1px solid red;
}
.geTemplateDlg {
  width: 100%;
  height: 100%;
}
.geTemplateDlg ::-webkit-scrollbar {
  width: 12px;
  height: 12px;
}
.geTemplateDlg ::-webkit-scrollbar-track {
  background: #f5f5f5;
  box-shadow: inset 0 0 4px rgba(0, 0, 0, 0.1);
}
.geTemplateDlg ::-webkit-scrollbar-thumb {
  background: #c5c5c5;
  border-radius: 10px;
  border: #f5f5f5 solid 3px;
}
.geTemplateDlg ::-webkit-scrollbar-thumb:hover {
  background: #b5b5b5;
}
.geTempDlgHeader {
  box-sizing: border-box;
  height: 62px;
  width: 100%;
  border: 1px solid #ccc;
  border-radius: 5px 5px 0 0;
  background-color: #f5f5f5;
}
.geTempDlgHeaderLogo {
  height: 34px;
  margin: 14px 14px 14px 20px;
}
.geTempDlgSearchBox {
  color: #888;
  background: url(/images/icon-search.svg) no-repeat;
  background-color: #fff;
  background-position: 15px;
  height: 40px;
  width: 40%;
  max-width: 400px;
  border: 1px solid #ccc;
  border-radius: 3px;
  float: right;
  font-size: 15px;
  line-height: 36px;
  margin: 11px 36px 0 0;
  outline: medium;
  padding: 0 0 0 36px;
  text-shadow: 1px 1px 0 #fff;
}
.geTemplatesList {
  float: left;
  height: calc(100% - 118px);
  width: 20%;
  background-color: #fff;
  display: inline-block;
  overflow-x: hidden;
  overflow-y: auto;
}
.geTempDlgContent,
.geTempDlgFooter,
.geTemplatesList {
  box-sizing: border-box;
  border: 1px solid #ccc;
}
.geTempDlgContent {
  float: right;
  height: calc(100% - 118px);
  width: 80%;
  background-color: #fff;
  display: inline-block;
  overflow-x: hidden;
  overflow-y: auto;
  position: relative;
}
.geTempDlgFooter {
  height: 52px;
  width: 100%;
  border-radius: 0 0 5px 5px;
  background-color: #f5f5f5;
  text-align: right;
  font-size: 14px;
  line-height: 17px;
  padding-top: 11px;
}
.geTempDlgCancelBtn,
.geTempDlgCreateBtn,
.geTempDlgOpenBtn {
  display: inline-block;
  width: 67px;
  padding: 6px;
  text-align: center;
  cursor: pointer;
}
.geTempDlgCreateBtn,
.geTempDlgOpenBtn {
  border-radius: 3px;
  background-color: #3d72ad;
  color: #fff;
  margin-left: 5px;
}
.geTempDlgCancelBtn {
  color: #3d72ad;
}
.geTempDlgCancelBtn:active,
.geTempDlgCreateBtn:active,
.geTempDlgOpenBtn:active,
.geTempDlgShowAllBtn:active {
  transform: translateY(2px);
}
.geTempDlgBtnDisabled {
  background-color: #9fbddd;
}
.geTempDlgBtnDisabled:active {
  transform: translateY(0);
}
.geTempDlgBtnBusy {
  background-image: url(/images/aui-wait.gif);
  background-repeat: no-repeat;
  background-position: 62px 7px;
}
.geTempDlgBack {
  height: 17px;
  color: #333;
  font-size: 14px;
  font-weight: 700;
  line-height: 17px;
  padding: 25px 0 0 20px;
  cursor: pointer;
}
.geTempDlgHLine {
  height: 1px;
  width: calc(100% - 22px);
  background-color: #ccc;
  margin: 20px 0 0 11px;
}
.geTemplateCatLink,
.geTemplatesLbl {
  height: 17px;
  font-size: 14px;
  line-height: 17px;
}
.geTemplatesLbl {
  color: #6d6d6d;
  font-weight: 700;
  text-transform: uppercase;
  margin: 20px 0 3px 20px;
}
.geTemplateCatLink {
  color: #3d72ad;
  margin: 12px 0 0 20px;
  cursor: pointer;
}
.geTempDlgNewDiagramCat {
  height: 280px;
  width: 100%;
  background-color: #555;
}
.geTempDlgNewDiagramCatLbl {
  height: 17px;
  color: #fff;
  font-size: 14px;
  font-weight: 700;
  line-height: 17px;
  padding: 25px 0 0 20px;
  text-transform: uppercase;
}
.geTempDlgNewDiagramCatList {
  width: 100%;
  height: 190px;
  padding-left: 9px;
  box-sizing: border-box;
  overflow-y: auto;
  overflow-x: hidden;
}
.geTempDlgNewDiagramCatFooter {
  width: 100%;
}
.geTempDlgShowAllBtn {
  width: 78px;
  border: 1px solid #777;
  border-radius: 3px;
  cursor: pointer;
  text-align: center;
  color: #ddd;
  font-size: 14px;
  line-height: 17px;
  padding: 4px;
  float: right;
  margin-right: 30px;
}
.geTempDlgNewDiagramCatItem {
  height: 155px;
  width: 134px;
  padding: 18px 6px 0 9px;
  display: inline-block;
}
.geTempDlgNewDiagramCatItemImg {
  box-sizing: border-box;
  height: 134px;
  width: 134px;
  border: 1px solid #ccc;
  border-radius: 3px;
  background-color: #fff;
  display: table-cell;
  vertical-align: middle;
  text-align: center;
  cursor: pointer;
}
.geTempDlgDiagramTileActive > .geTempDlgDiagramTileImg,
.geTempDlgNewDiagramCatItemActive > .geTempDlgNewDiagramCatItemImg {
  border: 4px solid #3d72ad;
}
.geTempDlgNewDiagramCatItemLbl {
  height: 17px;
  width: 100%;
  color: #fff;
  font-size: 14px;
  line-height: 17px;
  text-align: center;
  padding-top: 4px;
  cursor: pointer;
}
.geTempDlgDiagramsList {
  width: 100%;
  min-height: calc(100% - 280px);
  padding-left: 9px;
  box-sizing: border-box;
  background-color: #e5e5e5;
}
.geTempDlgDiagramsListHeader {
  width: 100%;
  height: 45px;
  padding: 18px 20px 0 11px;
  box-sizing: border-box;
}
.geTempDlgDiagramsListTitle {
  box-sizing: border-box;
  height: 17px;
  color: #666;
  font-size: 14px;
  font-weight: 700;
  line-height: 17px;
  text-transform: uppercase;
  padding-top: 5px;
  display: inline-block;
}
.geTempDlgDiagramsListBtns {
  float: right;
  margin-top: -9px;
}
.geTempDlgRadioBtn {
  box-sizing: border-box;
  border: 1px solid #ccc;
  border-radius: 3px;
  background-color: #fff;
  color: #333;
  display: inline-block;
  font-size: 14px;
  line-height: 17px;
  text-align: center;
  padding: 4px;
  cursor: pointer;
}
.geTempDlgRadioBtnActive {
  background-color: #555;
  color: #fff;
}
.geTempDlgRadioBtnLarge {
  height: 27px;
  width: 120px;
}
.geTempDlgRadioBtnSmall {
  position: relative;
  top: 9px;
  height: 27px;
  width: 27px;
}
.geTempDlgRadioBtnSmall img {
  position: absolute;
  top: 6px;
  left: 6px;
  height: 13px;
  width: 13px;
}
.geTempDlgSpacer {
  display: inline-block;
  width: 10px;
}
.geTempDlgDiagramsListGrid {
  width: 100%;
  white-space: nowrap;
  font-size: 13px;
  padding: 0 20px 20px 10px;
  box-sizing: border-box;
  border-spacing: 0;
}
.geTempDlgDiagramsListGrid tr {
  height: 40px;
}
.geTempDlgDiagramsListGrid th {
  background-color: #e5e5e5;
  color: #8e8e8e;
  font-weight: 700;
  text-align: left;
  padding: 5px;
  border-bottom: 1px solid #ccc;
  font-size: 14px;
}
.geTempDlgDiagramsListGrid td {
  background-color: #fff;
  color: #888;
  padding: 5px;
  border-bottom: 1px solid #ccc;
  overflow: hidden;
}
.geTempDlgDiagramsListGridActive td {
  border-bottom: 2px solid #3d72ad;
  border-top: 2px solid #3d72ad;
}
.geTempDlgDiagramsListGridActive td:first-child {
  border-left: 2px solid #3d72ad;
}
.geTempDlgDiagramsListGridActive td:last-child {
  border-right: 2px solid #3d72ad;
}
.geTempDlgDiagramTitle {
  font-weight: 700;
  color: #666 !important;
}
.geTempDlgDiagramsTiles {
  position: relative;
  min-height: 100px;
}
.geTempDlgDiagramTile {
  height: 152px;
  width: 130px;
  padding: 20px 7px 0 10px;
  display: inline-block;
  position: relative;
}
.geTempDlgDiagramTileImg {
  box-sizing: border-box;
  height: 130px;
  width: 130px;
  border: 1px solid #ccc;
  border-radius: 3px;
  background-color: #fff;
  display: table-cell;
  vertical-align: middle;
  text-align: center;
}
.geTempDlgDiagramTileImgError,
.geTempDlgDiagramTileImgLoading {
  background-image: url(/images/aui-wait.gif);
  background-repeat: no-repeat;
  background-position: center;
}
.geTempDlgDiagramTileImgError {
  background-image: url(/images/broken.png);
  background-color: #be3730;
}
.geTempDlgDiagramTileImg img {
  max-width: 117px;
  max-height: 117px;
  cursor: pointer;
}
.geTempDlgDiagramTileLbl {
  height: 17px;
  width: 100%;
  color: #333;
  font-size: 14px;
  line-height: 17px;
  text-align: center;
  padding-top: 5px;
  cursor: pointer;
}
.geTempDlgDiagramPreviewBtn {
  position: absolute;
  top: 28px;
  right: 15px;
  cursor: pointer;
}
.geTempDlgDiagramListPreviewBtn {
  cursor: pointer;
  padding-left: 5px;
  padding-right: 15px;
}
.geTempDlgDiagramPreviewBox {
  position: absolute;
  top: 3%;
  left: 10%;
  width: 80%;
  height: 94%;
  background: #fff;
  border: 4px solid #3d72ad;
  border-radius: 6px;
  box-sizing: border-box;
  display: table-cell;
  vertical-align: middle;
  text-align: center;
  z-index: 2;
}
.geTempDlgDialogMask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}
.geTempDlgDiagramPreviewBox img {
  max-width: 95%;
  max-height: 95%;
  vertical-align: middle;
}
.geTempDlgPreviewCloseBtn {
  position: absolute;
  top: 5px;
  right: 5px;
  cursor: pointer;
}
.geTempDlgLinkToDiagramHint {
  color: #555;
}
.geTempDlgLinkToDiagramBtn {
  color: #555;
  margin: 0 10px;
  height: 27px;
  font-size: 14px;
}
.geTempDlgErrMsg {
  display: none;
  color: red;
  position: absolute;
  width: 100%;
  text-align: center;
}
.geTempDlgImportCat {
  font-weight: 700;
  background: #f9f9f9;
  padding: 10px;
  margin: 10px 10px 0 0;
}
.geCommentsWin {
  user-select: none;
  border: 1px solid #f5f5f5;
  height: 100%;
  margin-bottom: 10px;
  overflow: auto;
}
.geCommentsToolbar {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  overflow: hidden;
  border-width: 1px 0 0;
  border-color: #c3c3c3;
  border-style: solid;
  display: block;
  white-space: nowrap;
}
.geCommentsList {
  position: absolute;
  overflow: auto;
  left: 0;
  right: 0;
  top: 0;
}
.geCommentContainer {
  position: relative;
  padding: 12px;
  margin: 5px;
  min-height: 50px;
  display: block;
  background-color: #fff;
  border-width: 0 0 1px;
  border-color: #c3c3c3;
  border-style: solid;
  border-radius: 10px;
  white-space: nowrap;
  box-shadow: 2px 2px 6px rgba(60, 64, 67, 0.15);
  color: #3c4043;
}
.geCommentHeader {
  width: 100%;
  height: 32px;
}
.geCommentUserImg {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  float: left;
  background-color: #f5f5f5;
}
.geCommentHeaderTxt {
  overflow: hidden;
  height: 32px;
  padding-left: 5px;
}
.geCommentDate,
.geCommentUsername {
  overflow: hidden;
  text-overflow: ellipsis;
}
.geCommentUsername {
  height: 18px;
  font-size: 15px;
  font-weight: 700;
}
.geCommentDate {
  color: #707070;
  height: 14px;
  font-size: 11px;
}
.geCommentDate::first-letter {
  text-transform: uppercase;
}
.geCommentTxt {
  font-size: 14px;
  padding-top: 5px;
  white-space: normal;
  min-height: 12px;
}
.geCommentEditTxtArea {
  margin-top: 5px;
  font-size: 14px !important;
  min-height: 12px;
  max-width: 100%;
  min-width: 100%;
  width: 100%;
  box-sizing: border-box;
}
.geCommentEditBtns {
  width: 100%;
  box-sizing: border-box;
  padding-top: 5px;
  height: 20px;
}
.geCommentEditBtn {
  padding: 3px 8px !important;
  float: right !important;
  margin-left: 5px;
}
.geCommentActions {
  color: #707070;
  font-size: 12px;
}
.geCommentActionsList {
  list-style-type: disc;
  margin: 0;
  padding: 10px 0 0;
}
.geCommentAction {
  display: inline-block;
  padding: 0;
}
.geCommentAction:before {
  content: "\2022";
  padding: 5px;
}
.geCommentAction:first-child:before {
  content: "";
  padding: 0;
}
.geCommentActionLnk {
  cursor: pointer;
  color: #707070;
  text-decoration: none;
}
.geCommentActionLnk:hover {
  text-decoration: underline;
}
.geCheckedBtn {
  background-color: #ccc;
  border-top: 1px solid #000 !important;
  border-left: 1px solid #000 !important;
}
.geCommentBusyImg {
  position: absolute;
  top: 5px;
  right: 5px;
}
.geAspectDlgListItem {
  width: 120px;
  height: 120px;
  display: inline-block;
  border: 3px solid #f0f0f0;
  border-radius: 5px;
  padding: 5px;
  margin: 2px 2px 20px;
}
.geAspectDlgListItem:hover {
  border: 3px solid #c5c5c5;
}
.geAspectDlgListItemSelected {
  border: 3px solid #3b73af;
}
.geAspectDlgListItemSelected:hover {
  border: 3px solid #405a86;
}
.geAspectDlgListItemText {
  text-overflow: ellipsis;
  max-width: 100%;
  min-height: 2em;
  overflow: hidden;
  text-align: center;
  margin-top: 10px;
}
.geAspectDlgList {
  min-height: 184px;
  white-space: nowrap;
}
.geStripedTable {
  border-collapse: collapse;
  width: 100%;
  table-layout: fixed;
}
.geStripedTable td {
  padding: 2px;
}
.geStripedTable td,
.geStripedTable th {
  border: 1px solid #ddd;
  text-align: left;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.geStripedTable tr:nth-child(odd) {
  background-color: #f2f2f2;
}
.geStripedTable tr:hover {
  background-color: #ddd;
}
.geStripedTable th {
  padding: 4px 2px;
  background-color: #bbb;
}
.geNotification-box {
  width: 50px;
  height: 30px;
  text-align: center;
  float: left;
  position: relative;
  top: -2px;
  cursor: pointer;
}
.geNotification-bell {
  animation: geBellAnim 1s 1s both;
}
.geNotification-bell * {
  display: block;
  margin: 0 auto;
  background-color: #656565;
  box-shadow: 0 0 10px #656565;
}
.geNotification-bellOff * {
  box-shadow: none !important;
}
.geBell-top {
  width: 2px;
  height: 2px;
  border-radius: 1px 1px 0 0;
}
.geBell-middle {
  width: 12px;
  height: 12px;
  margin-top: -1px;
  border-radius: 7px 7px 0 0;
}
.geBell-bottom {
  position: relative;
  z-index: 0;
  width: 16px;
  height: 1px;
}
.geBell-bottom::after,
.geBell-bottom::before {
  content: "";
  position: absolute;
  top: -4px;
}
.geBell-bottom::before {
  left: 1px;
  border-bottom-width: 4px;
  border-right: 0 solid transparent;
  border-left: 4px solid transparent;
}
.geBell-bottom::after {
  right: 1px;
  border-bottom-width: 4px;
  border-right: 4px solid transparent;
  border-left: 0 solid transparent;
}
.geBell-rad {
  width: 3px;
  height: 2px;
  margin-top: 0.5px;
  border-radius: 0 0 2px 2px;
  animation: geRadAnim 1s 2s both;
}
.geNotification-count {
  position: absolute;
  z-index: 1;
  top: -5px;
  right: 7px;
  width: 15px;
  height: 15px;
  line-height: 15px;
  font-size: 10px;
  border-radius: 50%;
  background-color: #ff4927;
  color: #fff;
  animation: geZoomAnim 1s 1s both;
}
.geNotifPanel {
  height: 300px;
  width: 300px;
  background: #fff;
  border-radius: 3px;
  overflow: hidden;
  box-shadow: 10px 10px 15px 0 rgba(0, 0, 0, 0.3);
  transition: all 0.5s ease-in-out;
  position: absolute;
  right: 100px;
  top: 42px;
  z-index: 150;
}
.geNotifPanel .header {
  height: 30px;
  width: 100%;
  background: #cecece;
  color: #707070;
  font-size: 15px;
}
.geNotifPanel .header .title {
  display: block;
  text-align: center;
  line-height: 30px;
  font-weight: 600;
}
.geNotifPanel .header .closeBtn {
  position: absolute;
  line-height: 30px;
  cursor: pointer;
  right: 15px;
  top: 0;
}
.geNotifPanel .notifications {
  position: relative;
  height: 270px;
  overflow-x: hidden;
  overflow-y: auto;
}
.geNotifPanel .notifications .line {
  position: absolute;
  top: 0;
  left: 27px;
  height: 100%;
  width: 3px;
  background: #ebebeb;
}
.geNotifPanel .notifications .notification {
  position: relative;
  z-index: 2;
  margin: 25px 20px 25px 43px;
}
.geNotifPanel .notifications .notification:nth-child(n + 1) {
  animation: geHere-am-i 0.5s ease-out 0.4s;
  animation-fill-mode: both;
}
.geNotifPanel .notifications .notification:hover {
  color: #1b95e0;
  cursor: pointer;
}
.geNotifPanel .notifications .notification .circle {
  box-sizing: border-box;
  position: absolute;
  height: 11px;
  width: 11px;
  background: #fff;
  border: 2px solid #1b95e0;
  box-shadow: 0 0 0 3px #fff;
  border-radius: 6px;
  top: 0;
  left: -20px;
}
.geNotifPanel .notifications .notification .circle.active {
  background: #1b95e0;
}
.geNotifPanel .notifications .notification .time {
  display: block;
  font-size: 11px;
  line-height: 11px;
  margin-bottom: 2px;
}
.geNotifPanel .notifications .notification p {
  font-size: 15px;
  line-height: 20px;
  margin: 0;
}
.geNotifPanel .notifications .notification p b {
  font-weight: 600;
}
.geTempTree {
  margin: 0;
  padding: 0;
}
.geTempTree,
.geTempTreeActive,
.geTempTreeNested {
  list-style-type: none;
  transition: all 0.5s;
}
.geTempTreeActive > li,
.geTempTreeCaret,
.geTempTreeNested > li {
  box-sizing: border-box;
  cursor: pointer;
  user-select: none;
  padding: 6px;
  width: 100%;
  transition: all 0.5s;
}
.geTempTreeCaret::before {
  content: "\25B6";
  display: inline-block;
  font-size: 10px;
  margin-right: 6px;
}
.geTempTreeCaret-down::before {
  transform: rotate(90deg);
}
.geTempTreeNested {
  height: 0;
  opacity: 0;
}
.geTempTreeActive {
  height: 100%;
  opacity: 1;
  padding-left: 15px;
}
.geTempTreeNested {
  padding-left: 15px;
}
.geTempTreeActive > li,
.geTempTreeNested > li {
  padding: 3px;
}

/* custom styles for obsidian */

.theme-light ::-webkit-scrollbar-thumb {
  background-color: #eee;
}
.theme-light ::-webkit-scrollbar {
  background-color: #fbfbfb;
}
.theme-light ::-webkit-scrollbar-thumb:active {
  background-color: #e0e0e0;
}

.theme-dark ::-webkit-scrollbar-thumb {
  background-color: #000000;
}
.theme-dark ::-webkit-scrollbar {
  background-color: #2a2a2a;
}
.theme-dark ::-webkit-scrollbar-thumb:active {
  background-color: #000000;
}

::-webkit-scrollbar {
  width: 10px;
  height: 10px;
  -webkit-border-radius: 100px;
}
::-webkit-scrollbar-thumb {
  -webkit-border-radius: 100px;
}
::-webkit-scrollbar-thumb:active {
  -webkit-border-radius: 100px;
}
::-webkit-scrollbar-corner {
  background: transparent;
}
.theme-light * {
  scrollbar-width: thin;
  scrollbar-color: #eee #fbfbfb;
}
.theme-dark * {
  scrollbar-width: thin;
  scrollbar-color: #000 #2a2a2a;
}
