/*
  @license
	Rollup.js v2.56.3
	Mon, 23 Aug 2021 05:06:39 GMT - commit c41d17ceedfa6c1d7430da70c6c80d86a91e9434


	https://github.com/rollup/rollup

	Released under the MIT License.
*/
'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var rollup = require('./shared/rollup.js');
require('path');
require('crypto');
require('fs');
require('events');



exports.VERSION = rollup.version;
exports.defineConfig = rollup.defineConfig;
exports.rollup = rollup.rollup;
exports.watch = rollup.watch;
//# sourceMappingURL=rollup.js.map
