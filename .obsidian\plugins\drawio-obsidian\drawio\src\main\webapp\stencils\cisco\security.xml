<shapes name="mxgraph.cisco.security">
<shape name="Centri Firewall" h="39.34" w="27" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
<constraint x="0.12" y="0.08" perimeter="0" name="NW"/>
<constraint x="0" y="1" perimeter="0" name="SW"/>
<constraint x="1" y="0" perimeter="0" name="NE"/>
<constraint x="0.87" y="0.92" perimeter="0" name="SE"/>
</connections>
<background>
<save/>
<path>
<move x="20.34" y="6.67"/>
<line x="0" y="6.67"/>
<line x="0" y="39.34"/>
<line x="20.34" y="39.34"/>
<close/>
<move x="20.34" y="39.34"/>
<line x="27" y="32.67"/>
<line x="27" y="0"/>
<line x="6.34" y="0"/>
<line x="0" y="6.67"/>
<line x="20.34" y="6.67"/>
<close/>
<move x="20.34" y="6.67"/>
<line x="27" y="0"/>
<move x="0" y="16"/>
<line x="20.34" y="16"/>
</path>
</background>
<foreground>
<linejoin join="round"/>
<fillstroke/>
<restore/>
<rect/>
<stroke/>
<path>
<move x="5" y="11.67"/>
<line x="10.67" y="11.67"/>
<move x="13" y="13.67"/>
<line x="13" y="9.67"/>
<line x="2.67" y="9.67"/>
<line x="2.67" y="13.67"/>
<close/>
</path>
<stroke/>
<fillcolor color="#ffffff"/>
<path>
<move x="4" y="31.34"/>
<line x="4" y="23.34"/>
<line x="11.67" y="27.34"/>
<line x="4" y="31.34"/>
<close/>
<move x="15" y="31"/>
<line x="15" y="23.67"/>
<line x="12.67" y="23.67"/>
<line x="12.67" y="31"/>
<close/>
</path>
<fill/>
<strokecolor color="#ffffff"/>
<strokewidth width="1"/>
<path>
<move x="9.67" y="35.67"/>
<curve x1="14.34" y1="35.67" x2="18.34" y2="32" x3="18.34" y3="27.34"/>
<curve x1="18.34" y1="22.67" x2="14.34" y2="19" x3="9.67" y3="19"/>
<curve x1="5" y1="19" x2="1.34" y2="22.67" x3="1.34" y3="27.34"/>
<curve x1="1.34" y1="32" x2="5" y2="35.67" x3="9.67" y3="35.67"/>
<close/>
</path>
<stroke/>
</foreground>
</shape>
<shape name="Cisco Security" h="44" w="49" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
<constraint x="0" y="0" perimeter="0" name="NW"/>
<constraint x="0" y="1" perimeter="0" name="SW"/>
<constraint x="1" y="0" perimeter="0" name="NE"/>
<constraint x="1" y="1" perimeter="0" name="SE"/>
</connections>
<background>
<save/>
<path>
<move x="42" y="37.67"/>
<line x="42" y="29.67"/>
<line x="0" y="29.67"/>
<line x="0" y="37.67"/>
<close/>
</path>
</background>
<foreground>
<linejoin join="round"/>
<fillstroke/>
<strokewidth width="0.67"/>
<path>
<move x="29" y="33.67"/>
<line x="39.67" y="33.67"/>
</path>
<stroke/>
<path>
<move x="49" y="30"/>
<line x="49" y="23"/>
<line x="42" y="29.67"/>
<line x="42" y="37.67"/>
<close/>
<move x="34.33" y="44"/>
<line x="34.33" y="42.34"/>
<line x="38.33" y="37"/>
<line x="38.33" y="40.34"/>
<close/>
<move x="34.33" y="42.34"/>
<line x="34.33" y="44"/>
<line x="0.67" y="44"/>
<line x="0.67" y="42.34"/>
<close/>
<move x="34.33" y="42.34"/>
<line x="1" y="42.34"/>
<line x="4.67" y="37"/>
<line x="38.33" y="37"/>
<close/>
<move x="49" y="23"/>
<line x="7" y="23"/>
<line x="0" y="29.67"/>
<line x="42" y="29.67"/>
<close/>
<move x="41" y="23"/>
<line x="13" y="23"/>
<line x="8.33" y="27.67"/>
<line x="36.33" y="27.67"/>
<close/>
</path>
<fillstroke/>
<path>
<move x="36.33" y="25.67"/>
<line x="36.33" y="4.67"/>
<line x="7.33" y="4.67"/>
<line x="7.33" y="25.67"/>
<close/>
</path>
<fillstroke/>
<path>
<move x="10" y="9.34"/>
<curve x1="10" y1="7" x2="12" y2="6.67" x3="12" y3="6.67"/>
<curve x1="12" y1="6.67" x2="29" y2="6.67" x3="31" y3="6.67"/>
<curve x1="34" y1="6.67" x2="33.67" y2="9.34" x3="33.67" y3="9.34"/>
<curve x1="33.67" y1="9.34" x2="33.67" y2="19.67" x3="33.67" y3="21.34"/>
<curve x1="33.67" y1="22.67" x2="31.67" y2="23.34" x3="31.67" y3="23.34"/>
<curve x1="31.67" y1="23.34" x2="14.33" y2="23.34" x3="12.33" y3="23.34"/>
<curve x1="10.67" y1="23.34" x2="10" y2="21.34" x3="10" y3="21.34"/>
<close/>
</path>
<stroke/>
<path>
<move x="41.33" y="0"/>
<line x="12.33" y="0"/>
<line x="7.33" y="4.67"/>
<line x="36.33" y="4.67"/>
<close/>
</path>
<fillstroke/>
<path>
<move x="41.33" y="21.34"/>
<line x="41.33" y="0"/>
<line x="36.33" y="4.67"/>
<line x="36.33" y="25.67"/>
<close/>
</path>
<fillstroke/>
<restore/>
<strokewidth width="0.67"/>
<path>
<move x="26.33" y="10.34"/>
<line x="26.33" y="20"/>
</path>
<stroke/>
<linejoin join="round"/>
<path>
<move x="17" y="20"/>
<curve x1="19" y1="24.67" x2="20.67" y2="19.34" x3="20.67" y3="19.34"/>
<curve x1="29.67" y1="19.34" x2="29.67" y2="19.34" x3="29.67" y3="19.34"/>
<curve x1="28.67" y1="21.67" x2="27.33" y2="22" x3="27.33" y3="22"/>
<curve x1="19.33" y1="22" x2="19.33" y2="22" x3="19.33" y3="22"/>
<curve x1="17.67" y1="21.67" x2="17" y2="20" x3="17" y3="20"/>
<close/>
</path>
<fillstroke/>
<path>
<move x="19.33" y="14.34"/>
<line x="24.33" y="14.34"/>
<move x="19.33" y="15.34"/>
<line x="24.33" y="15.34"/>
<move x="19.33" y="17"/>
<line x="24.33" y="17"/>
<move x="19.33" y="18"/>
<line x="24.33" y="18"/>
</path>
<stroke/>
<path>
<move x="26.33" y="10"/>
<curve x1="25.67" y1="8.34" x2="25.67" y2="8.34" x3="25.67" y3="8.34"/>
<curve x1="17" y1="8.34" x2="17" y2="8.34" x3="17" y3="8.34"/>
<curve x1="17" y1="8.34" x2="15.67" y2="8.34" x3="15.33" y3="10.67"/>
<curve x1="15.33" y1="12.67" x2="15.33" y2="12.67" x3="15.33" y3="12.67"/>
<curve x1="17" y1="12.67" x2="17" y2="12.67" x3="17" y3="12.67"/>
<curve x1="17" y1="10" x2="17" y2="10" x3="17" y3="10"/>
</path>
<fillstroke/>
<path>
<move x="19.33" y="11.67"/>
<line x="24.33" y="11.67"/>
<move x="19.33" y="10.67"/>
<line x="24.33" y="10.67"/>
<move x="17.33" y="20"/>
<line x="17.33" y="9.67"/>
</path>
<fillstroke/>
</foreground>
</shape>
<shape name="Firewall" h="42.67" w="18.67" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
<constraint x="0.25" y="0.09" perimeter="0" name="NW"/>
<constraint x="0" y="1" perimeter="0" name="SW"/>
<constraint x="1" y="0" perimeter="0" name="NE"/>
<constraint x="0.75" y="0.91" perimeter="0" name="SE"/>
</connections>
<background>
<path>
<move x="0" y="42.67"/>
<line x="0" y="7.34"/>
<line x="10.67" y="7.34"/>
<line x="10.67" y="42.67"/>
<close/>
<move x="10.67" y="7.34"/>
<line x="0" y="7.34"/>
<line x="9.67" y="0"/>
<line x="18.67" y="0"/>
<close/>
<move x="18.67" y="0"/>
<line x="18.67" y="33.67"/>
<line x="10.67" y="42.67"/>
<line x="10.67" y="7.34"/>
<close/>
</path>
</background>
<foreground>
<linejoin join="round"/>
<fillstroke/>
<path>
<move x="16.33" y="7.67"/>
<line x="16.33" y="2"/>
<move x="16.33" y="19.34"/>
<line x="16.33" y="13.67"/>
<move x="16.33" y="30.67"/>
<line x="16.33" y="25"/>
<move x="18.67" y="28"/>
<line x="10.67" y="37"/>
<move x="18.67" y="22.34"/>
<line x="10.67" y="31"/>
<move x="18.67" y="16.67"/>
<line x="10.67" y="25"/>
<move x="18.67" y="11"/>
<line x="10.67" y="19.34"/>
<move x="18.67" y="5.67"/>
<line x="10.67" y="13.34"/>
<move x="10.67" y="7.34"/>
<line x="18.67" y="0"/>
<move x="10.67" y="37"/>
<line x="0" y="37"/>
<move x="10.67" y="31"/>
<line x="0" y="31"/>
<move x="10.67" y="25"/>
<line x="0" y="25"/>
<move x="10.67" y="19"/>
<line x="0" y="19"/>
<move x="10.67" y="13.34"/>
<line x="0" y="13.34"/>
<move x="13.67" y="39.33"/>
<line x="13.67" y="33.67"/>
<move x="13.67" y="28"/>
<line x="13.67" y="22.34"/>
<move x="13.67" y="16.34"/>
<line x="13.67" y="10.67"/>
</path>
<stroke/>
</foreground>
</shape>
<shape name="Gatekeeper" h="37.34" w="53.67" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0.04" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0.04" y="0.46" perimeter="0" name="W"/>
<constraint x="0.98" y="0.46" perimeter="0" name="E"/>
<constraint x="0.9" y="1" perimeter="0" name="SE"/>
</connections>
<background>
<path>
<move x="19.67" y="4.67"/>
<curve x1="11" y1="3" x2="6.34" y2="7.34" x3="7" y3="11.34"/>
<curve x1="7" y1="11.67" x2="7" y2="11.67" x3="7" y3="11.67"/>
<curve x1="0" y1="12.67" x2="2" y2="21" x3="5.34" y3="21"/>
<curve x1="5.67" y1="21" x2="5.67" y2="21" x3="5.67" y3="21"/>
<curve x1="4.67" y1="24.67" x2="12.67" y2="28.67" x3="17.34" y3="26.67"/>
<curve x1="17.67" y1="26.34" x2="17.67" y2="26.34" x3="17.67" y3="26.34"/>
<curve x1="19.34" y1="29.67" x2="25" y2="30.34" x3="30.67" y3="30.67"/>
<curve x1="35.34" y1="30.67" x2="38.34" y2="30.34" x3="40.34" y3="28"/>
<curve x1="40.67" y1="28.34" x2="40.67" y2="28.34" x3="40.67" y3="28.34"/>
<curve x1="48.34" y1="29" x2="52" y2="23.34" x3="50.34" y3="19.67"/>
<curve x1="50.67" y1="19.34" x2="50.67" y2="19.34" x3="50.67" y3="19.34"/>
<curve x1="53.34" y1="18.67" x2="53.67" y2="12.67" x3="49" y3="11.67"/>
<curve x1="49.34" y1="11.34" x2="49.34" y2="11.34" x3="49.34" y3="11.34"/>
<curve x1="51.34" y1="7.34" x2="46.67" y2="3.67" x3="40" y3="4.34"/>
<curve x1="39.67" y1="4" x2="39.67" y2="4" x3="39.67" y3="4"/>
<curve x1="35" y1="0" x2="21.67" y2="0.67" x3="20" y3="4.67"/>
<close/>
</path>
</background>
<foreground>
<fillstroke/>
<strokewidth width="2.67"/>
<path>
<move x="23.67" y="11"/>
<line x="23.67" y="33.67"/>
<line x="45" y="33.67"/>
<line x="45" y="11"/>
<close/>
<move x="23.67" y="11"/>
<line x="45" y="33.67"/>
<move x="23.67" y="33.67"/>
<line x="45" y="11"/>
</path>
<stroke/>
<strokewidth width="0.67"/>
<path>
<move x="27" y="14.34"/>
<line x="27" y="7.34"/>
<line x="20.34" y="7.34"/>
<line x="20.34" y="14.34"/>
<close/>
<move x="48.34" y="14.34"/>
<line x="48.34" y="7.34"/>
<line x="41.67" y="7.34"/>
<line x="41.67" y="14.34"/>
<close/>
<move x="27" y="37.34"/>
<line x="27" y="30.34"/>
<line x="20.34" y="30.34"/>
<line x="20.34" y="37.34"/>
<close/>
<move x="48.34" y="37.34"/>
<line x="48.34" y="30.34"/>
<line x="41.67" y="30.34"/>
<line x="41.67" y="37.34"/>
<close/>
<move x="37.67" y="25.67"/>
<line x="37.67" y="18.67"/>
<line x="30.67" y="18.67"/>
<line x="30.67" y="25.67"/>
<close/>
</path>
<fillstroke/>
</foreground>
</shape>
<shape name="Guard" h="34.33" w="55.33" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
<constraint x="0" y="0" perimeter="0" name="NW"/>
<constraint x="0" y="1" perimeter="0" name="SW"/>
<constraint x="1" y="0" perimeter="0" name="NE"/>
<constraint x="1" y="1" perimeter="0" name="SE"/>
</connections>
<background>
<save/>
<path>
<move x="47.66" y="34.33"/>
<line x="47.66" y="6.33"/>
<line x="7.66" y="6.33"/>
<line x="7.66" y="34.33"/>
<close/>
<move x="47.66" y="6.33"/>
<line x="55.33" y="0"/>
<line x="18" y="0"/>
<line x="7.66" y="6.33"/>
<close/>
<move x="55.33" y="28"/>
<line x="55.33" y="0"/>
<line x="47.66" y="6.33"/>
<line x="47.66" y="34.33"/>
<close/>
</path>
</background>
<foreground>
<linejoin join="round"/>
<fillstroke/>
<strokecolor color="#ffffff"/>
<strokewidth width="1"/>
<linecap cap="square"/>
<dashed dashed="1"/>
<dashpattern dash="8 8"/>
<path>
<move x="45" y="15"/>
<line x="8" y="15"/>
</path>
<stroke/>
<restore/>
<rect/>
<stroke/>
<strokewidth width="1"/>
<fillcolor color="#ffffff"/>
<path>
<move x="47" y="15"/>
<line x="41.33" y="17.66"/>
<line x="42.66" y="15"/>
<line x="41.33" y="12.66"/>
<close/>
</path>
<fill/>
<save/>
<strokecolor color="#d9d9d9"/>
<strokewidth width="1"/>
<linejoin join="round"/>
<linecap cap="square"/>
<dashed dashed="1"/>
<dashpattern dash="8 8"/>
<path>
<move x="34" y="29"/>
<line x="39.33" y="24.66"/>
<line x="8" y="24.66"/>
</path>
<stroke/>
<restore/>
<fillcolor color="#d9d9d9"/>
<path>
<move x="30.66" y="31.66"/>
<line x="33.33" y="26.33"/>
<line x="34" y="29"/>
<line x="36.33" y="30"/>
<close/>
</path>
<fill/>
<strokecolor color="#ffffff"/>
<strokewidth width="0.85"/>
<path>
<move x="42.33" y="27.33"/>
<line x="42.33" y="9.33"/>
<line x="37.33" y="13.33"/>
<line x="37.33" y="31.33"/>
<close/>
<move x="16.66" y="27.33"/>
<line x="16.66" y="9.33"/>
<line x="11.66" y="13.33"/>
<line x="11.66" y="31.33"/>
<close/>
<move x="29.33" y="27.33"/>
<line x="29.33" y="9.33"/>
<line x="24.66" y="13.33"/>
<line x="24.66" y="31.33"/>
<close/>
</path>
<stroke/>
<strokecolor color="#000000"/>
<strokewidth width="1"/>
<linecap cap="square"/>
<dashed dashed="1"/>
<dashpattern dash="8 8"/>
<path>
<move x="7.66" y="15"/>
<line x="0" y="15"/>
</path>
<stroke/>
<strokecolor color="#8c8c8c"/>
<strokewidth width="1"/>
<dashed dashed="1"/>
<dashpattern dash="8 8"/>
<path>
<move x="7.66" y="24.66"/>
<line x="0" y="24.66"/>
</path>
<stroke/>
</foreground>
</shape>
<shape name="IOS Firewall" h="41" w="25" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
<constraint x="0.09" y="0.04" perimeter="0" name="NW"/>
<constraint x="0" y="1" perimeter="0" name="SW"/>
<constraint x="1" y="0" perimeter="0" name="NE"/>
<constraint x="0.93" y="0.95" perimeter="0" name="SE"/>
</connections>
<background>
<save/>
<path>
<move x="21.67" y="41"/>
<line x="21.67" y="17.67"/>
<line x="0" y="17.67"/>
<line x="0" y="41"/>
<close/>
<move x="21.67" y="17.67"/>
<line x="21.67" y="3.67"/>
<line x="0" y="3.67"/>
<line x="0" y="17.67"/>
<close/>
<move x="0" y="3.67"/>
<line x="4" y="0.33"/>
<line x="25" y="0.33"/>
<line x="21.67" y="3.67"/>
<close/>
<move x="21.67" y="41"/>
<line x="25" y="37"/>
<line x="25" y="14.33"/>
<line x="21.67" y="17.67"/>
<close/>
<move x="21.67" y="17.67"/>
<line x="25" y="14"/>
<line x="25" y="0.33"/>
<line x="21.67" y="3.67"/>
<close/>
</path>
</background>
<foreground>
<linejoin join="round"/>
<fillstroke/>
<path>
<move x="0" y="38.33"/>
<line x="21.67" y="38.33"/>
<move x="0" y="17.67"/>
<line x="21.33" y="17.67"/>
</path>
<stroke/>
<fillcolor color="#ffffff"/>
<path>
<move x="9" y="30.33"/>
<line x="9" y="33.33"/>
<line x="8" y="33.33"/>
<line x="10.33" y="36.33"/>
<line x="13" y="33.33"/>
<line x="11.67" y="33.33"/>
<line x="11.67" y="30.33"/>
<close/>
<move x="9" y="27.67"/>
<line x="9" y="24.67"/>
<line x="8" y="24.67"/>
<line x="10.33" y="21.67"/>
<line x="13" y="24.67"/>
<line x="11.67" y="24.67"/>
<line x="11.67" y="27.67"/>
<close/>
<move x="3" y="28"/>
<line x="6" y="28"/>
<line x="6" y="26.67"/>
<line x="9" y="29"/>
<line x="6" y="31.67"/>
<line x="6" y="30.33"/>
<line x="3" y="30.33"/>
<close/>
<move x="18" y="28"/>
<line x="14.67" y="28"/>
<line x="14.67" y="26.67"/>
<line x="11.67" y="29"/>
<line x="14.67" y="31.67"/>
<line x="14.67" y="30.33"/>
<line x="18" y="30.33"/>
<close/>
</path>
<fill/>
<restore/>
<rect/>
<stroke/>
<path>
<move x="21.67" y="8.33"/>
<line x="0" y="8.33"/>
<move x="23.67" y="1.67"/>
<line x="2" y="1.67"/>
<move x="21.67" y="13"/>
<line x="0" y="13"/>
<move x="25" y="5"/>
<line x="21.67" y="8.33"/>
<move x="25" y="9.33"/>
<line x="21.67" y="13"/>
<move x="23.33" y="2"/>
<line x="23.33" y="15.33"/>
<move x="6.33" y="3.67"/>
<line x="6.33" y="8.33"/>
<move x="14.67" y="3.67"/>
<line x="14.67" y="8.33"/>
<move x="6.33" y="13.33"/>
<line x="6.33" y="17.67"/>
<move x="14.67" y="13.33"/>
<line x="14.67" y="17.67"/>
<move x="10.67" y="8.33"/>
<line x="10.67" y="13"/>
<move x="2.33" y="8.33"/>
<line x="2.33" y="13"/>
<move x="19" y="8.33"/>
<line x="19" y="13"/>
<move x="10.67" y="0.33"/>
<line x="6.33" y="3.67"/>
<move x="19" y="0.33"/>
<line x="14.67" y="3.67"/>
<move x="25" y="34.67"/>
<line x="21.67" y="38.33"/>
</path>
<stroke/>
</foreground>
</shape>
<shape name="Lock" h="38.67" w="33.34" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
<constraint x="0.01" y="0.99" perimeter="0" name="SW"/>
<constraint x="0.92" y="0.93" perimeter="0" name="SE"/>
</connections>
<background>
<save/>
<path>
<move x="26.67" y="38.67"/>
<curve x1="27.34" y1="38.67" x2="28" y2="38" x3="28" y3="37.34"/>
<curve x1="28" y1="21" x2="28" y2="21" x3="28" y3="21"/>
<curve x1="28" y1="20" x2="27.34" y2="19.67" x3="26.67" y3="19.67"/>
<curve x1="1.34" y1="19.67" x2="1.34" y2="19.67" x3="1.34" y3="19.67"/>
<curve x1="0.34" y1="19.67" x2="0" y2="20" x3="0" y3="21"/>
<curve x1="0" y1="37.34" x2="0" y2="37.34" x3="0" y3="37.34"/>
<curve x1="0" y1="38" x2="0.34" y2="38.67" x3="1.34" y3="38.67"/>
<close/>
</path>
</background>
<foreground>
<fillstroke/>
<linejoin join="round"/>
<path>
<move x="28" y="19.67"/>
<line x="33.34" y="14.34"/>
<line x="5.34" y="14.34"/>
<line x="0" y="19.67"/>
<close/>
</path>
<fillstroke/>
<path>
<move x="33.34" y="33.34"/>
<line x="33.34" y="14.34"/>
<line x="28" y="19.67"/>
<line x="28" y="38.67"/>
<close/>
</path>
<fillstroke/>
<path>
<move x="16.67" y="2.34"/>
<curve x1="12.67" y1="2.34" x2="9.34" y2="5.67" x3="9.34" y3="9.67"/>
<curve x1="9.34" y1="16.34" x2="9.34" y2="16.34" x3="9.34" y3="16.34"/>
<curve x1="9.34" y1="16.67" x2="9" y2="17.34" x3="8.34" y3="17.34"/>
<curve x1="7.34" y1="17.34" x2="6.67" y2="17" x3="6.67" y3="16.67"/>
<curve x1="6.67" y1="10" x2="6.67" y2="10" x3="6.67" y3="10"/>
<curve x1="6.67" y1="4.67" x2="11" y2="0" x3="16.67" y3="0"/>
<curve x1="22" y1="0" x2="26.67" y2="4.67" x3="26.67" y3="10"/>
<curve x1="26.67" y1="17" x2="26.67" y2="17" x3="26.67" y3="17"/>
<curve x1="26.34" y1="17.67" x2="26" y2="18" x3="25" y3="18"/>
<curve x1="24.34" y1="18" x2="23.67" y2="17" x3="24" y3="16.67"/>
<curve x1="24" y1="9.67" x2="24" y2="9.67" x3="24" y3="9.67"/>
<curve x1="24" y1="5.67" x2="20.67" y2="2.34" x3="16.67" y3="2.34"/>
<close/>
</path>
<fillstroke/>
<restore/>
<rect/>
<stroke/>
<fillcolor color="#ffffff"/>
<path>
<move x="14" y="30.67"/>
<curve x1="15" y1="30.67" x2="16" y2="29.34" x3="16" y3="27.67"/>
<curve x1="16" y1="26" x2="15" y2="24.67" x3="14" y3="24.67"/>
<curve x1="12.67" y1="24.67" x2="11.67" y2="26" x3="11.67" y3="27.67"/>
<curve x1="11.67" y1="29.34" x2="12.67" y2="30.67" x3="14" y3="30.67"/>
<close/>
</path>
<fill/>
<path>
<move x="14.67" y="33.67"/>
<line x="14.67" y="28"/>
<line x="13" y="29"/>
<line x="13" y="34.67"/>
<close/>
</path>
<fill/>
</foreground>
</shape>
<shape name="Network Security" h="36.67" w="28" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
<constraint x="0" y="0" perimeter="0" name="NW"/>
<constraint x="1" y="0" perimeter="0" name="NE"/>
</connections>
<background>
</background>
<foreground>
<rect/>
<stroke/>
<save/>
<linejoin join="round"/>
<strokecolor color="#000000"/>
<strokewidth width="1.33"/>
<fillcolor color="#ffffff"/>
<path>
<move x="14" y="14.34"/>
<line x="0" y="14.34"/>
<line x="0" y="0"/>
<line x="14" y="0"/>
<close/>
</path>
<fillstroke/>
<restore/>
<strokecolor color="#000000"/>
<strokewidth width="1.33"/>
<linejoin join="round"/>
<path>
<move x="28" y="14"/>
<line x="14" y="14"/>
<line x="14" y="0"/>
<line x="28" y="0"/>
<close/>
<move x="14" y="36.67"/>
<line x="0" y="28.34"/>
<line x="0" y="14"/>
<line x="14" y="14"/>
<close/>
</path>
<fillstroke/>
<fillcolor color="#ffffff"/>
<path>
<move x="28" y="28.34"/>
<line x="14" y="36.67"/>
<line x="14" y="14"/>
<line x="28" y="14"/>
<close/>
</path>
<fillstroke/>
</foreground>
</shape>
<shape name="PIX Firewall" h="32.67" w="48" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
<constraint x="0.12" y="0.1" perimeter="0" name="NW"/>
<constraint x="0.12" y="0.9" perimeter="0" name="SW"/>
<constraint x="0.88" y="0.1" perimeter="0" name="NE"/>
<constraint x="0.88" y="0.9" perimeter="0" name="SE"/>
</connections>
<background>
<path>
<move x="48" y="9.33"/>
<curve x1="48" y1="14.67" x2="37.34" y2="18.67" x3="24" y3="18.67"/>
<curve x1="10.67" y1="18.67" x2="0" y2="14.67" x3="0" y3="9.33"/>
<curve x1="0" y1="23" x2="0" y2="23" x3="0" y3="23"/>
<curve x1="0" y1="28.33" x2="10.67" y2="32.67" x3="24" y3="32.67"/>
<curve x1="37.34" y1="32.67" x2="48" y2="28.33" x3="48" y3="23"/>
<close/>
<move x="24" y="18.67"/>
<curve x1="37.34" y1="18.67" x2="48" y2="14.67" x3="48" y3="9.33"/>
<curve x1="48" y1="4.33" x2="37.34" y2="0" x3="24" y3="0"/>
<curve x1="10.67" y1="0" x2="0" y2="4.33" x3="0" y3="9.33"/>
<curve x1="0" y1="14.67" x2="10.67" y2="18.67" x3="24" y3="18.67"/>
<close/>
</path>
</background>
<foreground>
<linejoin join="round"/>
<fillstroke/>
<fillcolor color="#ffffff"/>
<path>
<move x="17" y="14.67"/>
<line x="17" y="2.33"/>
<line x="28.34" y="8.33"/>
<close/>
</path>
<fill/>
<rect x="28.67" y="3" w="3.67" h="11"/>
<fill/>
</foreground>
</shape>
<shape name="Router Firewall" h="39.33" w="49.33" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
<constraint x="0.12" y="0.09" perimeter="0" name="NW"/>
<constraint x="0.12" y="0.91" perimeter="0" name="SW"/>
<constraint x="0.88" y="0.09" perimeter="0" name="NE"/>
<constraint x="0.88" y="0.91" perimeter="0" name="SE"/>
</connections>
<background>
<save/>
<path>
<move x="49.33" y="15.66"/>
<curve x1="49.33" y1="21" x2="38.33" y2="25.33" x3="24.66" y3="25.33"/>
<curve x1="11" y1="25.33" x2="0" y2="21" x3="0" y3="15.66"/>
<curve x1="0" y1="29.66" x2="0" y2="29.66" x3="0" y3="29.66"/>
<curve x1="0" y1="35" x2="11" y2="39.33" x3="24.66" y3="39.33"/>
<curve x1="38.33" y1="39.33" x2="49.33" y2="35" x3="49.33" y3="29.66"/>
<close/>
</path>
</background>
<foreground>
<linejoin join="round"/>
<fillstroke/>
<path>
<move x="24.66" y="19"/>
<curve x1="38" y1="19" x2="49" y2="14.66" x3="49" y3="9.66"/>
<curve x1="49" y1="4.33" x2="38" y2="0" x3="24.66" y3="0"/>
<curve x1="11.33" y1="0" x2="0.33" y2="4.33" x3="0.33" y3="9.66"/>
<curve x1="0.33" y1="14.66" x2="11.33" y2="19" x3="24.66" y3="19"/>
<close/>
</path>
<fillstroke/>
<path>
<move x="49" y="9.66"/>
<curve x1="49" y1="14.66" x2="38" y2="19" x3="24.66" y3="19"/>
<curve x1="11.33" y1="19" x2="0.33" y2="14.66" x3="0.33" y3="9.66"/>
<curve x1="0.33" y1="16.33" x2="0.33" y2="16.33" x3="0.33" y3="16.33"/>
<curve x1="0.33" y1="21.66" x2="11.33" y2="25.66" x3="24.66" y3="25.66"/>
<curve x1="38" y1="25.66" x2="49" y2="21.66" x3="49" y3="16.33"/>
<close/>
</path>
<fillstroke/>
<restore/>
<rect/>
<stroke/>
<strokewidth width="1"/>
<fillcolor color="#ffffff"/>
<path>
<move x="19" y="10"/>
<line x="25.66" y="10.33"/>
<move x="5.33" y="9"/>
<line x="12" y="9.66"/>
<move x="2.33" y="6"/>
<line x="7.66" y="6.66"/>
<move x="8.33" y="6.33"/>
<line x="1.66" y="12"/>
<move x="15" y="7"/>
<line x="5.66" y="15"/>
<move x="22.33" y="7.33"/>
<line x="11" y="17"/>
<move x="8.33" y="12.66"/>
<line x="15.33" y="13"/>
<move x="38" y="13.33"/>
<line x="44.66" y="13.66"/>
<move x="24.33" y="12.66"/>
<line x="31" y="13"/>
<move x="27.33" y="9.66"/>
<line x="17" y="18.33"/>
<move x="34.66" y="10"/>
<line x="24.33" y="18.66"/>
<move x="41.33" y="10.66"/>
<line x="32" y="18.66"/>
<move x="49" y="9.66"/>
<line x="42.66" y="16"/>
<move x="13.66" y="15"/>
<line x="20.33" y="15.66"/>
<move x="27.66" y="16"/>
<line x="34.33" y="16.33"/>
<move x="14.33" y="1"/>
<line x="8" y="6.66"/>
<move x="23.33" y="0"/>
<line x="15" y="7.33"/>
<move x="41" y="4.33"/>
<line x="45.66" y="5"/>
<move x="31" y="0.33"/>
<line x="19.66" y="10"/>
<move x="37.33" y="1.33"/>
<line x="26.33" y="10.33"/>
<move x="42.33" y="3.33"/>
<line x="33.66" y="10.66"/>
<move x="47" y="6"/>
<line x="41" y="11.33"/>
<move x="16.66" y="6.33"/>
<line x="23.66" y="6.66"/>
<move x="30.66" y="7"/>
<line x="37.33" y="7.66"/>
<move x="44.33" y="8"/>
<line x="48.33" y="8.33"/>
</path>
<stroke/>
<path>
<move x="19" y="5.33"/>
<line x="21" y="8.33"/>
<line x="13.33" y="10.33"/>
<line x="15" y="8.66"/>
<line x="3.33" y="6.66"/>
<line x="6.33" y="4.66"/>
<line x="17.66" y="6.33"/>
<close/>
<move x="30" y="12.66"/>
<line x="28.33" y="9.66"/>
<line x="35.33" y="8.33"/>
<line x="34.33" y="9.33"/>
<line x="45.66" y="11.33"/>
<line x="43" y="13.66"/>
<line x="31.33" y="11.33"/>
<close/>
<move x="26" y="3.66"/>
<line x="33.66" y="1.66"/>
<line x="34" y="5"/>
<line x="32" y="4.66"/>
<line x="28.33" y="7.66"/>
<line x="24.66" y="7.33"/>
<line x="28.33" y="4"/>
<close/>
<move x="22.66" y="15.66"/>
<line x="15.33" y="17"/>
<line x="15" y="13.66"/>
<line x="17.33" y="14"/>
<line x="21.33" y="10.66"/>
<line x="25" y="11.33"/>
<line x="20.66" y="15"/>
<close/>
</path>
<fill/>
</foreground>
</shape>
</shapes>