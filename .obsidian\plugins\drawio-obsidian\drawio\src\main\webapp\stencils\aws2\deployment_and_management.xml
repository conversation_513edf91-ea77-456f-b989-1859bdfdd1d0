<shapes name="mxgraph.aws2.deployment_and_management">
<shape aspect="variable" h="72" name="CloudFormation" strokewidth="inherit" w="59.14">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0"/>
        <constraint name="S" perimeter="0" x="0.5" y="1"/>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="E" perimeter="0" x="1" y="0.5"/>
        <constraint name="NW" perimeter="0" x="0" y="0.21"/>
        <constraint name="NE" perimeter="0" x="1" y="0.21"/>
        <constraint name="SW" perimeter="0" x="0" y="0.79"/>
        <constraint name="SE" perimeter="0" x="1" y="0.79"/>
    </connections>
    <foreground>
        <fillcolor color="#B7CA9D"/>
        <path>
            <move x="3.81" y="49.8"/>
            <line x="29.71" y="57.56"/>
            <line x="29.82" y="53.1"/>
            <line x="12.04" y="48.64"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#3C4929"/>
        <path>
            <move x="29.82" y="19.21"/>
            <line x="12.04" y="23.36"/>
            <line x="3.81" y="22.2"/>
            <line x="29.71" y="14.43"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#B7CA9D"/>
        <path>
            <move x="55.33" y="49.8"/>
            <line x="29.44" y="57.56"/>
            <line x="29.32" y="53.1"/>
            <line x="47.11" y="48.64"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#3C4929"/>
        <path>
            <move x="29.32" y="19.21"/>
            <line x="47.11" y="23.36"/>
            <line x="55.33" y="22.2"/>
            <line x="29.44" y="14.43"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#759C3E"/>
        <path>
            <move x="12.04" y="48.64"/>
            <line x="3.81" y="49.8"/>
            <line x="3.81" y="22.2"/>
            <line x="12.04" y="23.36"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#4B612C"/>
        <path>
            <move x="29.54" y="0"/>
            <line x="0" y="14.91"/>
            <line x="0.01" y="57.1"/>
            <line x="29.54" y="72"/>
            <line x="29.54" y="0"/>
            <close/>
            <move x="20.9" y="54.92"/>
            <line x="3.81" y="49.8"/>
            <line x="3.81" y="22.2"/>
            <line x="20.9" y="17.08"/>
            <line x="20.9" y="54.92"/>
            <close/>
        </path>
        <fillstroke/>
        <path>
            <move x="47.11" y="48.64"/>
            <line x="55.33" y="49.8"/>
            <line x="55.33" y="22.2"/>
            <line x="47.11" y="23.36"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#759C3E"/>
        <path>
            <move x="29.54" y="72"/>
            <line x="59.13" y="57.1"/>
            <line x="59.15" y="14.91"/>
            <line x="29.54" y="0"/>
            <line x="29.54" y="72"/>
            <close/>
            <move x="38.24" y="17.08"/>
            <line x="55.33" y="22.2"/>
            <line x="55.33" y="49.8"/>
            <line x="38.24" y="54.92"/>
            <line x="38.24" y="17.08"/>
            <close/>
        </path>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="49.1" name="CloudFormation Stack" strokewidth="inherit" w="64.44">
    <connections>
        <constraint name="N1" perimeter="0" x="0.25" y="0"/>
        <constraint name="N" perimeter="0" x="0.5" y="0"/>
        <constraint name="N2" perimeter="0" x="0.75" y="0"/>
        <constraint name="S1" perimeter="0" x="0.25" y="1"/>
        <constraint name="S" perimeter="0" x="0.5" y="1"/>
        <constraint name="S2" perimeter="0" x="0.75" y="1"/>
        <constraint name="W1" perimeter="0" x="0" y="0.25"/>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="W2" perimeter="0" x="0" y="0.75"/>
        <constraint name="E1" perimeter="0" x="1" y="0.25"/>
        <constraint name="E" perimeter="0" x="1" y="0.5"/>
        <constraint name="E2" perimeter="0" x="1" y="0.75"/>
        <constraint name="NW" perimeter="0" x="0" y="0"/>
        <constraint name="NE" perimeter="0" x="1" y="0"/>
        <constraint name="SW" perimeter="0" x="0" y="1"/>
        <constraint name="SE" perimeter="0" x="1" y="1"/>
    </connections>
    <foreground>
        <fillcolor color="#769B3F"/>
        <rect h="14.45" w="19.74" x="22.83" y="32.18"/>
        <fillstroke/>
        <fillcolor color="#3C492A"/>
        <rect h="2.48" w="19.74" x="22.83" y="46.62"/>
        <fillstroke/>
        <fillcolor color="#769B3F"/>
        <rect h="14.45" w="18.7" x="45.69" y="32.18"/>
        <fillstroke/>
        <fillcolor color="#3C492A"/>
        <rect h="2.48" w="18.7" x="45.69" y="46.62"/>
        <fillstroke/>
        <fillcolor color="#769B3F"/>
        <rect h="11.35" w="30.1" x="34.28" y="16.3"/>
        <fillstroke/>
        <fillcolor color="#3C492A"/>
        <rect h="2.48" w="30.1" x="34.28" y="27.65"/>
        <fillstroke/>
        <fillcolor color="#769B3F"/>
        <rect h="14.42" w="19.65" x="0" y="32.2"/>
        <fillstroke/>
        <fillcolor color="#3C492A"/>
        <rect h="2.48" w="19.65" x="0" y="46.62"/>
        <fillstroke/>
        <fillcolor color="#769B3F"/>
        <rect h="11.24" w="31.07" x="0.09" y="16.3"/>
        <fillstroke/>
        <fillcolor color="#3C492A"/>
        <rect h="2.47" w="31.07" x="0.09" y="27.54"/>
        <fillstroke/>
        <fillcolor color="#769B3F"/>
        <rect h="11.63" w="64.44" x="0" y="0"/>
        <fillstroke/>
        <fillcolor color="#3C492A"/>
        <rect h="2.48" w="64.44" x="0" y="11.63"/>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="56.76" name="CloudFormation Template" strokewidth="inherit" w="48.83">
    <connections>
        <constraint name="N1" perimeter="0" x="0.25" y="0"/>
        <constraint name="N" perimeter="0" x="0.5" y="0"/>
        <constraint name="N2" perimeter="0" x="0.75" y="0"/>
        <constraint name="S1" perimeter="0" x="0.25" y="1"/>
        <constraint name="S" perimeter="0" x="0.5" y="1"/>
        <constraint name="S2" perimeter="0" x="0.75" y="1"/>
        <constraint name="W1" perimeter="0" x="0" y="0.25"/>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="W2" perimeter="0" x="0" y="0.75"/>
        <constraint name="E1" perimeter="0" x="1" y="0.25"/>
        <constraint name="E" perimeter="0" x="1" y="0.5"/>
        <constraint name="E2" perimeter="0" x="1" y="0.75"/>
        <constraint name="NW" perimeter="0" x="0" y="0"/>
        <constraint name="NE" perimeter="0" x="1" y="0"/>
        <constraint name="SW" perimeter="0" x="0" y="1"/>
        <constraint name="SE" perimeter="0" x="1" y="1"/>
    </connections>
    <foreground>
        <fillcolor color="#3A4728"/>
        <rect h="2.38" w="48.83" x="0" y="54.38"/>
        <fillstroke/>
        <fillcolor color="#769B3F"/>
        <path>
            <move x="0" y="0"/>
            <line x="0" y="54.38"/>
            <line x="48.83" y="54.38"/>
            <line x="48.83" y="0"/>
            <line x="0" y="0"/>
            <close/>
            <move x="16.27" y="46.98"/>
            <line x="7.18" y="46.98"/>
            <line x="7.18" y="37.95"/>
            <line x="16.27" y="37.95"/>
            <line x="16.27" y="46.98"/>
            <close/>
            <move x="16.27" y="32.31"/>
            <line x="7.18" y="32.31"/>
            <line x="7.18" y="24.41"/>
            <line x="16.27" y="24.41"/>
            <line x="16.27" y="32.31"/>
            <close/>
            <move x="16.27" y="18.77"/>
            <line x="7.18" y="18.77"/>
            <line x="7.18" y="9.75"/>
            <line x="16.27" y="9.75"/>
            <line x="16.27" y="18.77"/>
            <close/>
            <move x="41.26" y="43.6"/>
            <line x="19.67" y="43.6"/>
            <line x="19.67" y="41.34"/>
            <line x="41.26" y="41.34"/>
            <line x="41.26" y="43.6"/>
            <close/>
            <move x="41.26" y="30.05"/>
            <line x="19.67" y="30.05"/>
            <line x="19.67" y="27.8"/>
            <line x="41.26" y="27.8"/>
            <line x="41.26" y="30.05"/>
            <close/>
            <move x="41.26" y="15.39"/>
            <line x="19.67" y="15.39"/>
            <line x="19.67" y="13.13"/>
            <line x="41.26" y="13.13"/>
            <line x="41.26" y="15.39"/>
            <close/>
        </path>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="72" name="CloudTrail" strokewidth="inherit" w="59.63">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0"/>
        <constraint name="S" perimeter="0" x="0.5" y="1"/>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="E" perimeter="0" x="1" y="0.5"/>
        <constraint name="NE" perimeter="0" x="1" y="0.21"/>
        <constraint name="SE" perimeter="0" x="1" y="0.79"/>
    </connections>
    <foreground>
        <fillcolor color="#4B612C"/>
        <path>
            <move x="1.81" y="31.66"/>
            <line x="0" y="31.83"/>
            <line x="0" y="23.34"/>
            <line x="1.81" y="22.8"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#759C3E"/>
        <path>
            <move x="8.48" y="23.67"/>
            <line x="1.81" y="22.78"/>
            <line x="1.81" y="31.65"/>
            <line x="8.48" y="31.95"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#3C4929"/>
        <path>
            <move x="8.48" y="31.95"/>
            <line x="6.54" y="32.1"/>
            <line x="0" y="31.83"/>
            <line x="1.81" y="31.66"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#4B612C"/>
        <path>
            <move x="1.81" y="40.63"/>
            <line x="0" y="40.45"/>
            <line x="0" y="48.95"/>
            <line x="1.81" y="49.49"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#759C3E"/>
        <path>
            <move x="8.48" y="48.62"/>
            <line x="1.81" y="49.51"/>
            <line x="1.81" y="40.64"/>
            <line x="8.48" y="40.34"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#B7CA9D"/>
        <path>
            <move x="8.48" y="40.34"/>
            <line x="6.54" y="40.19"/>
            <line x="0" y="40.45"/>
            <line x="1.81" y="40.63"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#4B612C"/>
        <path>
            <move x="5.97" y="16.76"/>
            <line x="11" y="14.88"/>
            <line x="11" y="30.81"/>
            <line x="5.97" y="31.18"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#759C3E"/>
        <path>
            <move x="20.74" y="17.16"/>
            <line x="11" y="15.05"/>
            <line x="11" y="30.83"/>
            <line x="20.74" y="31.25"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#3C4929"/>
        <path>
            <move x="20.74" y="31.25"/>
            <line x="15.37" y="31.66"/>
            <line x="5.97" y="31.18"/>
            <line x="11" y="30.68"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#4B612C"/>
        <path>
            <move x="5.97" y="55.4"/>
            <line x="11" y="57.27"/>
            <line x="11" y="41.48"/>
            <line x="5.97" y="41.11"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#759C3E"/>
        <path>
            <move x="20.74" y="55.25"/>
            <line x="11" y="57.39"/>
            <line x="11" y="41.46"/>
            <line x="20.74" y="41.04"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#B7CA9D"/>
        <path>
            <move x="20.74" y="41.04"/>
            <line x="15.37" y="40.63"/>
            <line x="5.97" y="41.11"/>
            <line x="11" y="41.61"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#759C3E"/>
        <path>
            <move x="59.63" y="57.09"/>
            <line x="29.81" y="72"/>
            <line x="29.81" y="0"/>
            <line x="59.63" y="14.91"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#4B612C"/>
        <path>
            <move x="17.21" y="65.7"/>
            <line x="29.81" y="72"/>
            <line x="29.81" y="0"/>
            <line x="17.21" y="6.3"/>
            <close/>
        </path>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="72" name="CloudWatch" strokewidth="inherit" w="63.64">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0.05"/>
        <constraint name="S" perimeter="0" x="0.5" y="1"/>
        <constraint name="W" perimeter="0" x="0.1" y="0.5"/>
        <constraint name="E" perimeter="0" x="0.75" y="0.5"/>
        <constraint name="NW" perimeter="0" x="0.35" y="0"/>
        <constraint name="NE" perimeter="0" x="0.75" y="0.14"/>
        <constraint name="SW" perimeter="0" x="0" y="0.78"/>
        <constraint name="SE" perimeter="0" x="1" y="0.78"/>
    </connections>
    <foreground>
        <fillcolor color="#B7CA9D"/>
        <path>
            <move x="63.64" y="49.54"/>
            <line x="31.83" y="44.87"/>
            <line x="0" y="49.55"/>
            <line x="31.81" y="60.83"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#759C3E"/>
        <path>
            <move x="47.72" y="47.95"/>
            <line x="22.3" y="53.92"/>
            <line x="22.3" y="0"/>
            <line x="47.72" y="9.91"/>
            <close/>
        </path>
        <fillstroke/>
        <path>
            <move x="30.78" y="47.34"/>
            <line x="11.75" y="50.68"/>
            <line x="11.75" y="11.86"/>
            <line x="30.78" y="16.14"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#4B612C"/>
        <path>
            <move x="25" y="54.67"/>
            <line x="31.82" y="56.71"/>
            <line x="31.81" y="40.94"/>
            <line x="25" y="40.2"/>
            <close/>
        </path>
        <fillstroke/>
        <path>
            <move x="22.3" y="53.86"/>
            <line x="14.88" y="51.63"/>
            <line x="14.88" y="3.71"/>
            <line x="22.3" y="0"/>
            <close/>
        </path>
        <fillstroke/>
        <path>
            <move x="31.81" y="60.83"/>
            <line x="0" y="49.55"/>
            <line x="0" y="56.1"/>
            <line x="31.81" y="72"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#759C3E"/>
        <path>
            <move x="63.64" y="56.09"/>
            <line x="31.81" y="72"/>
            <line x="31.81" y="60.83"/>
            <line x="63.64" y="49.54"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#4B612C"/>
        <path>
            <move x="6.37" y="49"/>
            <line x="11.75" y="50.61"/>
            <line x="11.75" y="11.86"/>
            <line x="6.37" y="13.91"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#759C3E"/>
        <path>
            <move x="54.7" y="38.7"/>
            <line x="31.81" y="40.94"/>
            <line x="31.82" y="56.71"/>
            <line x="54.7" y="49.82"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#B7CA9D"/>
        <path>
            <move x="54.7" y="38.7"/>
            <line x="46.99" y="38.7"/>
            <line x="25" y="40.2"/>
            <line x="31.81" y="40.94"/>
            <close/>
        </path>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="58.38" name="CloudWatch Alarm" strokewidth="inherit" w="48.45">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0.42"/>
        <constraint name="S" perimeter="0" x="0.5" y="1"/>
        <constraint name="W" perimeter="0" x="0.045" y="0.5"/>
        <constraint name="E" perimeter="0" x="0.955" y="0.5"/>
        <constraint name="NW" perimeter="0" x="0.045" y="0.335"/>
        <constraint name="NE" perimeter="0" x="0.94" y="0.03"/>
        <constraint name="SW" perimeter="0" x="0" y="1"/>
        <constraint name="SE" perimeter="0" x="1" y="1"/>
    </connections>
    <foreground>
        <fillcolor color="#769B3F"/>
        <rect h="26.44" w="10.71" x="2.11" y="19.47"/>
        <fillstroke/>
        <fillcolor color="#4D622C"/>
        <rect h="2.62" w="10.71" x="2.11" y="45.91"/>
        <fillstroke/>
        <fillcolor color="#769B3F"/>
        <rect h="21.29" w="11.12" x="19.01" y="24.62"/>
        <fillstroke/>
        <fillcolor color="#4D622C"/>
        <rect h="2.62" w="11.12" x="19.01" y="45.91"/>
        <fillstroke/>
        <fillcolor color="#769B3F"/>
        <path>
            <move x="46.22" y="16.4"/>
            <line x="46.22" y="45.91"/>
            <line x="36.39" y="45.91"/>
            <line x="36.39" y="16.48"/>
            <curve x1="36.49" x2="38.34" x3="41.21" y1="16.55" y2="17.76" y3="17.76"/>
            <curve x1="42.68" x2="44.4" x3="46.22" y1="17.76" y2="17.42" y3="16.4"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#4D622C"/>
        <rect h="2.62" w="9.83" x="36.39" y="45.91"/>
        <fillstroke/>
        <fillcolor color="#769B3F"/>
        <rect h="5.66" w="48.44" x="0" y="50.11"/>
        <fillstroke/>
        <fillcolor color="#4D622C"/>
        <rect h="2.61" w="48.44" x="0" y="55.77"/>
        <fillstroke/>
        <fillcolor color="#EA2227"/>
        <path>
            <move x="41.03" y="0"/>
            <curve x1="44.62" x2="47.54" x3="47.54" y1="0" y2="2.9" y3="6.46"/>
            <curve x1="47.54" x2="44.62" x3="41.03" y1="10.03" y2="12.93" y3="12.93"/>
            <curve x1="37.43" x2="34.51" x3="34.51" y1="12.93" y2="10.03" y3="6.46"/>
            <curve x1="34.51" x2="37.43" x3="41.03" y1="2.9" y2="0" y3="0"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#8D090A"/>
        <path>
            <move x="47.54" y="6.46"/>
            <line x="47.54" y="9.07"/>
            <curve x1="47.54" x2="47.51" x3="47.47" y1="9.39" y2="9.69" y3="9.99"/>
            <line x="47.47" y="7.38"/>
            <curve x1="47.51" x2="47.54" x3="47.54" y1="7.08" y2="6.77" y3="6.46"/>
        </path>
        <fillstroke/>
        <path>
            <move x="47.47" y="7.38"/>
            <line x="47.47" y="9.99"/>
            <curve x1="47.42" x2="47.34" x3="47.23" y1="10.35" y2="10.7" y3="11.04"/>
            <line x="46.56" y="8.4"/>
            <curve x1="46.66" x2="47.42" x3="47.47" y1="8.06" y2="7.74" y3="7.38"/>
        </path>
        <fillstroke/>
        <path>
            <move x="47.23" y="8.42"/>
            <line x="47.23" y="11.04"/>
            <curve x1="47.1" x2="46.94" x3="46.74" y1="11.44" y2="11.82" y3="12.17"/>
            <line x="46.74" y="9.56"/>
            <curve x1="46.94" x2="47.1" x3="47.23" y1="9.2" y2="8.82" y3="8.42"/>
        </path>
        <fillstroke/>
        <path>
            <move x="46.74" y="9.56"/>
            <line x="46.74" y="12.17"/>
            <curve x1="45.63" x2="43.49" x3="41.03" y1="14.18" y2="15.54" y3="15.54"/>
            <curve x1="37.43" x2="34.51" x3="34.51" y1="15.54" y2="12.64" y3="9.07"/>
            <line x="34.51" y="6.45"/>
            <curve x1="34.51" x2="37.43" x3="41.03" y1="10.03" y2="12.93" y3="12.93"/>
            <curve x1="43.49" x2="45.63" x3="46.74" y1="12.93" y2="11.57" y3="9.56"/>
        </path>
        <fillstroke/>
        <path>
            <move x="47.54" y="6.46"/>
            <line x="47.54" y="9.07"/>
            <curve x1="47.54" x2="44.62" x3="41.03" y1="12.64" y2="15.54" y3="15.54"/>
            <curve x1="37.43" x2="34.51" x3="34.51" y1="15.54" y2="12.64" y3="9.07"/>
            <line x="34.51" y="6.46"/>
            <curve x1="34.51" x2="37.43" x3="41.03" y1="10.03" y2="12.93" y3="12.93"/>
            <curve x1="44.62" x2="47.54" x3="47.54" y1="12.93" y2="10.03" y3="6.46"/>
            <close/>
        </path>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="72" name="CodeDeploy" strokewidth="inherit" w="59.6">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0"/>
        <constraint name="S" perimeter="0" x="0.5" y="1"/>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="E" perimeter="0" x="1" y="0.5"/>
        <constraint name="NW" perimeter="0" x="0" y="0.21"/>
        <constraint name="NE" perimeter="0" x="1" y="0.21"/>
        <constraint name="SW" perimeter="0" x="0" y="0.79"/>
        <constraint name="SE" perimeter="0" x="1" y="0.79"/>
    </connections>
    <foreground>
        <fillcolor color="#759C3E"/>
        <path>
            <move x="39.2" y="52.7"/>
            <line x="8.4" y="61"/>
            <line x="8.4" y="10.7"/>
            <line x="39.2" y="19.1"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#4B612C"/>
        <path>
            <move x="8.4" y="61.2"/>
            <line x="0" y="57"/>
            <line x="0" y="14.9"/>
            <line x="8.4" y="10.7"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#759C3E"/>
        <path>
            <move x="59.6" y="25.5"/>
            <line x="29.8" y="18"/>
            <line x="29.8" y="0"/>
            <line x="59.6" y="14.9"/>
            <close/>
        </path>
        <fillstroke/>
        <path>
            <move x="59.6" y="41.2"/>
            <line x="29.8" y="44.9"/>
            <line x="29.8" y="26.9"/>
            <line x="59.6" y="30.7"/>
            <close/>
        </path>
        <fillstroke/>
        <path>
            <move x="59.6" y="57.1"/>
            <line x="29.8" y="72"/>
            <line x="29.8" y="54"/>
            <line x="59.6" y="46.5"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#4B612C"/>
        <path>
            <move x="13.9" y="7.9"/>
            <line x="29.8" y="0"/>
            <line x="29.8" y="18"/>
            <line x="13.9" y="22"/>
            <close/>
        </path>
        <fillstroke/>
        <path>
            <move x="13.9" y="64"/>
            <line x="29.8" y="71.9"/>
            <line x="29.8" y="54"/>
            <line x="13.9" y="50"/>
            <close/>
        </path>
        <fillstroke/>
        <path>
            <move x="13.9" y="28.9"/>
            <line x="29.8" y="26.9"/>
            <line x="29.8" y="44.9"/>
            <line x="13.9" y="43"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#3C4929"/>
        <path>
            <move x="59.6" y="25.5"/>
            <line x="45.1" y="27"/>
            <line x="13.9" y="22"/>
            <line x="29.8" y="18"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#B7CA9D"/>
        <path>
            <move x="59.6" y="46.5"/>
            <line x="45.1" y="45"/>
            <line x="13.9" y="50"/>
            <line x="29.8" y="54"/>
            <close/>
        </path>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="72" name="Data Pipeline" strokewidth="inherit" w="59.65">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0"/>
        <constraint name="S" perimeter="0" x="0.5" y="1"/>
        <constraint name="W" perimeter="0" x="0.1" y="0.5"/>
        <constraint name="E" perimeter="0" x="0.9" y="0.5"/>
        <constraint name="NW" perimeter="0" x="0" y="0.21"/>
        <constraint name="NE" perimeter="0" x="1" y="0.21"/>
        <constraint name="SW" perimeter="0" x="0.235" y="0.89"/>
        <constraint name="SE" perimeter="0" x="0.765" y="0.89"/>
    </connections>
    <foreground>
        <fillcolor color="#759C3E"/>
        <path>
            <move x="29.82" y="14.78"/>
            <line x="59.65" y="23.57"/>
            <line x="59.65" y="14.91"/>
            <line x="29.82" y="0"/>
            <close/>
        </path>
        <fillstroke/>
        <path>
            <move x="53.66" y="33.64"/>
            <line x="29.82" y="32.47"/>
            <line x="29.82" y="46.87"/>
            <line x="53.66" y="43.27"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#4B612C"/>
        <path>
            <move x="5.98" y="33.64"/>
            <line x="29.82" y="32.47"/>
            <line x="29.82" y="46.87"/>
            <line x="5.98" y="43.27"/>
            <close/>
        </path>
        <fillstroke/>
        <path>
            <move x="29.82" y="14.78"/>
            <line x="0" y="23.57"/>
            <line x="0" y="14.91"/>
            <line x="29.82" y="0"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#3C4929"/>
        <path>
            <move x="0" y="23.57"/>
            <line x="29.82" y="14.78"/>
            <line x="59.65" y="23.57"/>
            <line x="29.82" y="27.21"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#759C3E"/>
        <path>
            <move x="45.7" y="55.44"/>
            <line x="29.82" y="60.94"/>
            <line x="29.82" y="72"/>
            <line x="45.7" y="64.06"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#4B612C"/>
        <path>
            <move x="13.95" y="55.44"/>
            <line x="29.82" y="60.94"/>
            <line x="29.82" y="72"/>
            <line x="13.95" y="64.06"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#B7CA9D"/>
        <path>
            <move x="45.7" y="55.44"/>
            <line x="29.82" y="51.93"/>
            <line x="13.95" y="55.44"/>
            <line x="29.82" y="60.94"/>
            <close/>
        </path>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="72" name="Elastic Beanstalk" strokewidth="inherit" w="52.03">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0"/>
        <constraint name="S" perimeter="0" x="0.5" y="1"/>
        <constraint name="W" perimeter="0" x="0.26" y="0.5"/>
        <constraint name="E" perimeter="0" x="0.74" y="0.5"/>
        <constraint name="NW" perimeter="0" x="0.33" y="0.06"/>
        <constraint name="NE" perimeter="0" x="1" y="0.18"/>
        <constraint name="SW" perimeter="0" x="0.26" y="0.91"/>
        <constraint name="SE" perimeter="0" x="0.67" y="0.94"/>
    </connections>
    <foreground>
        <fillcolor color="#759C3E"/>
        <path>
            <move x="34.93" y="36"/>
            <line x="26.01" y="36"/>
            <line x="26.01" y="72"/>
            <line x="34.93" y="67.54"/>
            <close/>
        </path>
        <fillstroke/>
        <path>
            <move x="47.46" y="10.72"/>
            <line x="52.03" y="13.01"/>
            <line x="52.03" y="26.8"/>
            <line x="47.46" y="25.89"/>
            <close/>
        </path>
        <fillstroke/>
        <path>
            <move x="38.61" y="6.3"/>
            <line x="26.01" y="0"/>
            <line x="26.01" y="36"/>
            <line x="38.61" y="36"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#4B612C"/>
        <path>
            <move x="30.84" y="27.65"/>
            <line x="47.46" y="25.84"/>
            <line x="47.46" y="10.72"/>
            <line x="30.84" y="15.22"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#3C4929"/>
        <path>
            <move x="52.03" y="26.8"/>
            <line x="36.08" y="28.3"/>
            <line x="30.84" y="27.65"/>
            <line x="47.3" y="25.86"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#4B612C"/>
        <path>
            <move x="17.09" y="36"/>
            <line x="26.01" y="36"/>
            <line x="26.01" y="0"/>
            <line x="17.09" y="4.46"/>
            <close/>
        </path>
        <fillstroke/>
        <path>
            <move x="4.57" y="61.28"/>
            <line x="0" y="58.99"/>
            <line x="0" y="45.2"/>
            <line x="4.57" y="46.11"/>
            <close/>
        </path>
        <fillstroke/>
        <path>
            <move x="13.41" y="65.7"/>
            <line x="26.01" y="72"/>
            <line x="26.01" y="36"/>
            <line x="13.41" y="36"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#759C3E"/>
        <path>
            <move x="21.19" y="44.35"/>
            <line x="4.57" y="46.16"/>
            <line x="4.57" y="61.28"/>
            <line x="21.19" y="56.78"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#B7CA9D"/>
        <path>
            <move x="0" y="45.2"/>
            <line x="15.95" y="43.7"/>
            <line x="21.19" y="44.35"/>
            <line x="4.72" y="46.14"/>
            <close/>
        </path>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="57.14" name="Elastic Beanstalk Application" strokewidth="inherit" w="30.53">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0.06"/>
        <constraint name="S" perimeter="0" x="0.5" y="0.47"/>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="E" perimeter="0" x="0.28" y="0.5"/>
        <constraint name="NW" perimeter="0" x="0.15" y="0.18"/>
        <constraint name="NE" perimeter="0" x="1" y="0.06"/>
        <constraint name="SW" perimeter="0" x="0" y="1"/>
        <constraint name="SE" perimeter="0" x="0.28" y="1"/>
    </connections>
    <foreground>
        <fillcolor color="#4D622C"/>
        <path>
            <move x="29.19" y="0"/>
            <line x="29.19" y="2.67"/>
            <curve x1="20.36" x2="3.75" x3="3.59" y1="16.84" y2="22.88" y3="22.94"/>
            <curve x1="3.47" x2="3.34" x3="3.24" y1="22.99" y2="23.12" y3="23.3"/>
            <line x="3.24" y="20.64"/>
            <curve x1="3.34" x2="3.47" x3="3.59" y1="20.46" y2="20.32" y3="20.28"/>
            <curve x1="3.75" x2="20.36" x3="29.19" y1="20.22" y2="14.17" y3="0"/>
        </path>
        <fillstroke/>
        <path>
            <move x="3.24" y="20.64"/>
            <line x="3.24" y="23.3"/>
            <curve x1="3.15" x2="3.07" x3="3" y1="23.46" y2="23.65" y3="23.86"/>
            <line x="3" y="21.2"/>
            <curve x1="3.07" x2="3.15" x3="3.24" y1="20.99" y2="20.8" y3="20.64"/>
        </path>
        <fillstroke/>
        <path>
            <move x="3" y="21.2"/>
            <line x="3" y="23.86"/>
            <curve x1="2.91" x2="2.83" x3="2.79" y1="24.14" y2="24.44" y3="24.71"/>
            <line x="2.79" y="22.05"/>
            <curve x1="2.83" x2="2.91" x3="3" y1="21.77" y2="21.47" y3="21.2"/>
        </path>
        <fillstroke/>
        <path>
            <move x="2.79" y="22.05"/>
            <line x="2.79" y="24.71"/>
            <curve x1="2.77" x2="2.75" x3="2.75" y1="24.9" y2="25.07" y3="25.22"/>
            <line x="2.75" y="22.56"/>
            <curve x1="2.75" x2="2.77" x3="2.79" y1="22.41" y2="22.23" y3="22.05"/>
        </path>
        <fillstroke/>
        <path>
            <move x="2.75" y="25.22"/>
            <line x="2.75" y="22.56"/>
            <curve x1="2.75" x2="3.14" x3="3.59" y1="21.81" y2="20.43" y3="20.28"/>
            <curve x1="3.75" x2="20.36" x3="29.19" y1="20.22" y2="14.17" y3="0"/>
            <line x="29.19" y="2.67"/>
            <curve x1="20.36" x2="3.75" x3="3.59" y1="16.84" y2="22.88" y3="22.94"/>
            <curve x1="3.14" x2="2.75" x3="2.75" y1="23.09" y2="24.47" y3="25.22"/>
            <close/>
        </path>
        <fillstroke/>
        <path>
            <move x="30.53" y="3.17"/>
            <line x="30.53" y="5.84"/>
            <curve x1="30.39" x2="30.19" x3="29.94" y1="7.36" y2="9.04" y3="10.75"/>
            <line x="29.94" y="8.08"/>
            <curve x1="30.19" x2="30.39" x3="30.53" y1="6.36" y2="4.69" y3="3.17"/>
        </path>
        <fillstroke/>
        <path>
            <move x="29.94" y="8.08"/>
            <line x="29.94" y="10.75"/>
            <curve x1="29.53" x2="28.96" x3="28.18" y1="13.46" y2="16.25" y3="18.57"/>
            <line x="28.18" y="15.91"/>
            <curve x1="28.95" x2="29.53" x3="29.94" y1="13.59" y2="10.79" y3="8.08"/>
        </path>
        <fillstroke/>
        <path>
            <move x="28.18" y="15.91"/>
            <line x="28.18" y="18.57"/>
            <curve x1="27.79" x2="27.36" x3="26.86" y1="19.73" y2="20.77" y3="21.63"/>
            <line x="26.86" y="18.97"/>
            <curve x1="27.36" x2="27.79" x3="28.18" y1="18.1" y2="17.06" y3="15.91"/>
        </path>
        <fillstroke/>
        <path>
            <move x="26.86" y="18.97"/>
            <line x="26.86" y="21.63"/>
            <curve x1="26.51" x2="26.13" x3="25.72" y1="22.25" y2="22.77" y3="23.19"/>
            <curve x1="23.05" x2="16.02" x3="8.45" y1="25.82" y2="27.25" y3="27.88"/>
            <line x="8.45" y="25.21"/>
            <curve x1="16.02" x2="23.05" x3="25.72" y1="24.59" y2="23.16" y3="20.51"/>
            <curve x1="26.13" x2="26.51" x3="26.86" y1="20.11" y2="19.58" y3="18.97"/>
        </path>
        <fillstroke/>
        <path>
            <move x="30.53" y="3.17"/>
            <line x="30.53" y="5.84"/>
            <curve x1="29.97" x2="28.6" x3="25.72" y1="11.81" y2="20.31" y3="23.19"/>
            <curve x1="23.05" x2="16.02" x3="8.45" y1="25.82" y2="27.26" y3="27.88"/>
            <line x="8.45" y="25.21"/>
            <curve x1="16.02" x2="23.05" x3="25.72" y1="24.6" y2="23.16" y3="20.51"/>
            <curve x1="28.6" x2="29.97" x3="30.53" y1="17.65" y2="9.15" y3="3.17"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#769B3F"/>
        <path>
            <move x="4.56" y="24.04"/>
            <curve x1="5.26" x2="21.27" x3="30.53" y1="23.81" y2="18.22" y3="3.17"/>
            <curve x1="29.97" x2="28.6" x3="25.72" y1="9.15" y2="17.65" y3="20.51"/>
            <curve x1="23.05" x2="16.02" x3="8.45" y1="23.16" y2="24.6" y3="25.21"/>
            <line x="8.45" y="54.47"/>
            <line x="0" y="54.47"/>
            <line x="0" y="25.25"/>
            <curve x1="0.64" x2="1.11" x3="6.94" y1="18.55" y2="13.12" y3="7.94"/>
            <curve x1="11.05" x2="22.23" x3="29.19" y1="4.26" y2="0.9" y3="0"/>
            <curve x1="20.36" x2="3.75" x3="3.59" y1="14.17" y2="20.22" y3="20.28"/>
            <curve x1="3.05" x2="2.61" x3="2.79" y1="20.46" y2="22.36" y3="22.9"/>
            <curve x1="2.94" x2="3.8" x3="4.23" y1="23.33" y2="24.1" y3="24.1"/>
            <curve x1="4.33" x2="4.45" x3="4.56" y1="24.1" y2="24.09" y3="24.04"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#4D622C"/>
        <rect h="2.67" w="8.45" x="0" y="54.47"/>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="65.47" name="Elastic Beanstalk Deployment" strokewidth="inherit" w="49.4">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0.12"/>
        <constraint name="S" perimeter="0" x="0.5" y="1"/>
        <constraint name="W" perimeter="0" x="0.38" y="0.5"/>
        <constraint name="E" perimeter="0" x="0.85" y="0.5"/>
        <constraint name="NW" perimeter="0" x="0.07" y="0.06"/>
        <constraint name="NE" perimeter="0" x="1" y="0.17"/>
        <constraint name="SW" perimeter="0" x="0.375" y="1"/>
        <constraint name="SE" perimeter="0" x="0.555" y="1"/>
    </connections>
    <foreground>
        <fillcolor color="#4D622C"/>
        <path>
            <move x="47.94" y="7.97"/>
            <line x="47.94" y="10.64"/>
            <curve x1="39.11" x2="22.5" x3="22.33" y1="24.81" y2="30.86" y3="30.91"/>
            <curve x1="22.21" x2="22.09" x3="21.98" y1="30.96" y2="31.09" y3="31.27"/>
            <line x="21.98" y="28.62"/>
            <curve x1="22.09" x2="22.21" x3="22.33" y1="28.43" y2="28.29" y3="28.25"/>
            <curve x1="22.5" x2="39.11" x3="47.94" y1="28.2" y2="22.15" y3="7.97"/>
        </path>
        <fillstroke/>
        <path>
            <move x="21.98" y="28.62"/>
            <line x="21.98" y="31.27"/>
            <curve x1="21.9" x2="21.81" x3="21.75" y1="31.43" y2="31.62" y3="31.83"/>
            <line x="21.75" y="29.17"/>
            <curve x1="21.81" x2="21.9" x3="21.98" y1="28.96" y2="28.77" y3="28.62"/>
        </path>
        <fillstroke/>
        <path>
            <move x="21.75" y="29.17"/>
            <line x="21.75" y="31.83"/>
            <curve x1="21.65" x2="21.58" x3="21.54" y1="32.11" y2="32.41" y3="32.68"/>
            <line x="21.54" y="30.02"/>
            <curve x1="21.58" x2="21.65" x3="21.75" y1="29.75" y2="29.44" y3="29.17"/>
        </path>
        <fillstroke/>
        <path>
            <move x="21.54" y="30.02"/>
            <line x="21.54" y="32.68"/>
            <curve x1="21.51" x2="21.5" x3="21.5" y1="32.87" y2="33.04" y3="33.19"/>
            <line x="21.5" y="30.53"/>
            <curve x1="21.5" x2="21.51" x3="21.54" y1="30.38" y2="30.21" y3="30.02"/>
        </path>
        <fillstroke/>
        <path>
            <move x="21.5" y="33.19"/>
            <line x="21.5" y="30.53"/>
            <curve x1="21.5" x2="21.88" x3="22.33" y1="29.78" y2="28.4" y3="28.25"/>
            <curve x1="22.5" x2="39.11" x3="47.94" y1="28.2" y2="22.15" y3="7.97"/>
            <line x="47.94" y="10.64"/>
            <curve x1="39.11" x2="22.5" x3="22.33" y1="24.81" y2="30.86" y3="30.91"/>
            <curve x1="21.88" x2="21.5" x3="21.5" y1="31.07" y2="32.44" y3="33.19"/>
            <close/>
        </path>
        <fillstroke/>
        <path>
            <move x="49.33" y="11.1"/>
            <line x="49.33" y="13.77"/>
            <curve x1="49.19" x2="49" x3="48.74" y1="15.29" y2="16.97" y3="18.68"/>
            <line x="48.74" y="16.01"/>
            <curve x1="49" x2="49.19" x3="49.33" y1="14.3" y2="12.62" y3="11.1"/>
        </path>
        <fillstroke/>
        <path>
            <move x="48.74" y="16.01"/>
            <line x="48.74" y="18.68"/>
            <curve x1="48.33" x2="47.76" x3="46.98" y1="21.39" y2="24.18" y3="26.5"/>
            <line x="46.98" y="23.84"/>
            <curve x1="47.76" x2="48.33" x3="48.74" y1="21.52" y2="18.73" y3="16.01"/>
        </path>
        <fillstroke/>
        <path>
            <move x="46.98" y="23.84"/>
            <line x="46.98" y="26.5"/>
            <curve x1="46.6" x2="46.16" x3="45.67" y1="27.66" y2="28.7" y3="29.56"/>
            <line x="45.67" y="26.91"/>
            <curve x1="46.16" x2="46.6" x3="46.98" y1="26.04" y2="24.99" y3="23.84"/>
        </path>
        <fillstroke/>
        <path>
            <move x="45.67" y="26.91"/>
            <line x="45.67" y="29.56"/>
            <curve x1="45.32" x2="44.94" x3="44.52" y1="30.18" y2="30.71" y3="31.12"/>
            <curve x1="41.86" x2="34.83" x3="27.25" y1="33.75" y2="35.19" y3="35.81"/>
            <line x="27.25" y="33.14"/>
            <curve x1="34.83" x2="41.86" x3="44.52" y1="32.53" y2="31.09" y3="28.44"/>
            <curve x1="44.94" x2="45.32" x3="45.67" y1="28.04" y2="27.51" y3="26.91"/>
        </path>
        <fillstroke/>
        <path>
            <move x="49.33" y="11.1"/>
            <line x="49.33" y="13.77"/>
            <curve x1="48.77" x2="47.4" x3="44.52" y1="19.75" y2="28.25" y3="31.12"/>
            <curve x1="41.86" x2="34.83" x3="27.25" y1="33.75" y2="35.19" y3="35.81"/>
            <line x="27.25" y="33.14"/>
            <curve x1="34.83" x2="41.86" x3="44.52" y1="32.53" y2="31.09" y3="28.44"/>
            <curve x1="47.4" x2="48.77" x3="49.33" y1="25.59" y2="17.08" y3="11.1"/>
            <close/>
        </path>
        <fillstroke/>
        <rect h="2.67" w="9" x="18.38" y="62.81"/>
        <fillstroke/>
        <path>
            <move x="25.52" y="12.66"/>
            <line x="25.52" y="13.8"/>
            <curve x1="25.52" x2="25.48" x3="25.38" y1="14.45" y2="15.08" y3="15.69"/>
            <line x="25.38" y="14.55"/>
            <curve x1="25.48" x2="25.52" x3="25.52" y1="13.93" y2="13.3" y3="12.66"/>
        </path>
        <fillstroke/>
        <path>
            <move x="25.38" y="14.55"/>
            <line x="25.38" y="15.69"/>
            <curve x1="25.28" x2="25.11" x3="24.88" y1="16.41" y2="17.11" y3="17.79"/>
            <line x="24.88" y="16.64"/>
            <curve x1="25.11" x2="25.28" x3="25.38" y1="15.97" y2="15.27" y3="14.55"/>
        </path>
        <fillstroke/>
        <path>
            <move x="24.88" y="16.64"/>
            <line x="24.88" y="17.79"/>
            <curve x1="24.61" x2="24.27" x3="23.86" y1="18.58" y2="19.34" y3="20.06"/>
            <line x="23.86" y="18.92"/>
            <curve x1="24.27" x2="24.61" x3="24.88" y1="18.2" y2="17.44" y3="16.64"/>
        </path>
        <fillstroke/>
        <path>
            <move x="23.86" y="18.92"/>
            <line x="23.86" y="20.06"/>
            <curve x1="21.66" x2="17.52" x3="12.76" y1="23.88" y2="26.46" y3="26.46"/>
            <curve x1="5.71" x2="0" x3="0" y1="26.46" y2="20.79" y3="13.8"/>
            <line x="0" y="12.66"/>
            <curve x1="0" x2="5.71" x3="12.76" y1="19.65" y2="25.32" y3="25.32"/>
            <curve x1="17.52" x2="21.66" x3="23.86" y1="25.32" y2="22.74" y3="18.92"/>
        </path>
        <fillstroke/>
        <path>
            <move x="25.52" y="12.66"/>
            <line x="25.52" y="13.8"/>
            <curve x1="25.52" x2="19.81" x3="12.76" y1="20.79" y2="26.46" y3="26.46"/>
            <curve x1="5.71" x2="0" x3="0" y1="26.46" y2="20.79" y3="13.8"/>
            <line x="0" y="12.66"/>
            <curve x1="0" x2="5.71" x3="12.76" y1="19.65" y2="25.32" y3="25.32"/>
            <curve x1="19.81" x2="25.52" x3="25.52" y1="25.32" y2="19.65" y3="12.66"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#769B3F"/>
        <path>
            <move x="12.76" y="25.32"/>
            <curve x1="5.72" x2="0" x3="0" y1="25.32" y2="19.65" y3="12.66"/>
            <curve x1="0" x2="5.72" x3="12.76" y1="5.67" y2="0" y3="0"/>
            <curve x1="19.81" x2="25.53" x3="25.53" y1="0" y2="5.67" y3="12.66"/>
            <curve x1="25.52" x2="19.81" x3="12.76" y1="19.65" y2="25.32" y3="25.32"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#FFFFFF"/>
        <path>
            <move x="9.45" y="22.48"/>
            <line x="16.08" y="22.48"/>
            <line x="16.08" y="9.84"/>
            <line x="20.81" y="9.84"/>
            <line x="12.76" y="1.98"/>
            <line x="4.72" y="9.88"/>
            <line x="9.45" y="9.88"/>
            <line x="9.45" y="22.48"/>
            <close/>
            <move x="6.94" y="8.97"/>
            <line x="12.77" y="3.26"/>
            <line x="18.58" y="8.94"/>
            <line x="15.16" y="8.94"/>
            <line x="15.16" y="21.57"/>
            <line x="10.36" y="21.57"/>
            <line x="10.36" y="8.97"/>
            <line x="6.94" y="8.97"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#769B3F"/>
        <path>
            <move x="23.32" y="32.01"/>
            <curve x1="23.21" x2="23.1" x3="22.99" y1="32.06" y2="32.07" y3="32.07"/>
            <curve x1="22.56" x2="21.71" x3="21.56" y1="32.07" y2="31.3" y3="30.87"/>
            <curve x1="21.37" x2="21.82" x3="22.35" y1="30.34" y2="28.43" y3="28.25"/>
            <curve x1="22.52" x2="39.12" x3="47.95" y1="28.19" y2="22.15" y3="7.97"/>
            <curve x1="41.95" x2="32.81" x3="27.77" y1="8.75" y2="11.36" y3="14.42"/>
            <curve x1="27.79" x2="27.8" x3="27.8" y1="14.7" y2="14.99" y3="15.28"/>
            <curve x1="27.8" x2="24.3" x3="19.46" y1="20.71" y2="25.34" y3="27.14"/>
            <curve x1="19.11" x2="19.67" x3="18.67" y1="29.06" y2="31.07" y3="33.22"/>
            <line x="18.67" y="62.74"/>
            <line x="27.67" y="62.74"/>
            <line x="27.67" y="33.18"/>
            <curve x1="34.67" x2="42.04" x3="44.71" y1="32.57" y2="31.13" y3="28.48"/>
            <curve x1="47.59" x2="48.85" x3="49.4" y1="25.62" y2="17.12" y3="11.14"/>
            <curve x1="40.15" x2="24.02" x3="23.32" y1="26.19" y2="31.78" y3="32.01"/>
            <close/>
        </path>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="72" name="IAM" strokewidth="inherit" w="37.61">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0"/>
        <constraint name="S" perimeter="0" x="0.5" y="1"/>
        <constraint name="W" perimeter="0" x="0.26" y="0.5"/>
        <constraint name="E" perimeter="0" x="0.92" y="0.5"/>
        <constraint name="NW" perimeter="0" x="0.08" y="0.11"/>
        <constraint name="NE" perimeter="0" x="0.92" y="0.11"/>
        <constraint name="SW" perimeter="0" x="0.26" y="0.85"/>
        <constraint name="SE" perimeter="0" x="0.835" y="0.83"/>
    </connections>
    <foreground>
        <fillcolor color="#3C4929"/>
        <path>
            <move x="6.57" y="25.17"/>
            <line x="3.14" y="24.73"/>
            <line x="0" y="25.36"/>
            <line x="3.36" y="25.75"/>
            <close/>
        </path>
        <fillstroke/>
        <path>
            <move x="2.93" y="30.39"/>
            <line x="18.81" y="31.4"/>
            <line x="34.68" y="30.39"/>
            <line x="18.81" y="28.8"/>
            <close/>
        </path>
        <fillstroke/>
        <path>
            <move x="29.53" y="25.17"/>
            <line x="33.4" y="25.85"/>
            <line x="37.61" y="25.36"/>
            <line x="33.85" y="24.61"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#B7CA9D"/>
        <path>
            <move x="13.6" y="53.91"/>
            <line x="22.68" y="51.78"/>
            <line x="31.44" y="53.82"/>
            <line x="22.36" y="56.55"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#759C3E"/>
        <path>
            <move x="14.61" y="24.37"/>
            <line x="9.88" y="23.56"/>
            <line x="9.88" y="10.77"/>
            <line x="14.61" y="12.24"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#4B612C"/>
        <path>
            <move x="2.93" y="13.55"/>
            <line x="2.93" y="7.94"/>
            <line x="18.81" y="0"/>
            <line x="18.81" y="7.2"/>
            <line x="9.88" y="10.77"/>
            <line x="9.88" y="23.38"/>
            <line x="18.81" y="21.6"/>
            <line x="18.81" y="72"/>
            <line x="14.05" y="69.62"/>
            <line x="14.05" y="62.9"/>
            <line x="9.88" y="61.23"/>
            <line x="9.88" y="29.69"/>
            <line x="2.93" y="30.39"/>
            <line x="2.93" y="24.77"/>
            <line x="0" y="25.36"/>
            <line x="0" y="14.72"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#759C3E"/>
        <path>
            <move x="27.02" y="4.11"/>
            <line x="30.37" y="5.78"/>
            <line x="34.68" y="7.94"/>
            <line x="34.68" y="13.55"/>
            <line x="37.61" y="14.72"/>
            <line x="37.61" y="25.36"/>
            <line x="34.68" y="24.77"/>
            <line x="34.68" y="30.39"/>
            <line x="30.94" y="30.01"/>
            <line x="27.73" y="29.69"/>
            <line x="27.73" y="35.93"/>
            <line x="27.73" y="35.96"/>
            <line x="27.73" y="36"/>
            <line x="34.68" y="36"/>
            <line x="34.68" y="41.77"/>
            <line x="31.45" y="42.06"/>
            <line x="31.45" y="47.88"/>
            <line x="23.56" y="49.45"/>
            <line x="23.56" y="56.17"/>
            <line x="31.43" y="53.84"/>
            <line x="31.45" y="59.77"/>
            <line x="23.56" y="62.9"/>
            <line x="23.56" y="69.62"/>
            <line x="18.81" y="72"/>
            <line x="18.81" y="50.4"/>
            <line x="18.81" y="43.2"/>
            <line x="18.81" y="35.96"/>
            <line x="18.81" y="35.92"/>
            <line x="18.81" y="28.8"/>
            <line x="18.81" y="21.6"/>
            <line x="23" y="22.44"/>
            <line x="23" y="12.24"/>
            <line x="18.81" y="10.77"/>
            <line x="18.81" y="7.2"/>
            <line x="18.81" y="0"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#3C4929"/>
        <path>
            <move x="23.56" y="9.1"/>
            <line x="18.81" y="7.2"/>
            <line x="9.88" y="10.77"/>
            <line x="14.61" y="12.24"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#4B612C"/>
        <path>
            <move x="23.01" y="12.24"/>
            <line x="27.73" y="10.77"/>
            <line x="27.73" y="23.38"/>
            <line x="23.01" y="22.44"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#3C4929"/>
        <path>
            <move x="9.88" y="10.77"/>
            <line x="18.81" y="7.2"/>
            <line x="20.68" y="7.95"/>
            <line x="23.56" y="9.1"/>
            <line x="27.73" y="10.77"/>
            <line x="23.01" y="12.24"/>
            <line x="18.81" y="10.77"/>
            <line x="14.61" y="12.24"/>
            <close/>
        </path>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="24.14" name="IAM Add-on" strokewidth="inherit" w="44.4">
    <connections>
        <constraint name="N" perimeter="0" x="0.27" y="0"/>
        <constraint name="S" perimeter="0" x="0.27" y="1"/>
        <constraint name="W" perimeter="0" x="0.03" y="0.5"/>
        <constraint name="E" perimeter="0" x="1" y="0.46"/>
        <constraint name="NW" perimeter="0" x="0.08" y="0.18"/>
        <constraint name="NE" perimeter="0" x="0.62" y="0.21"/>
        <constraint name="SW" perimeter="0" x="0.08" y="0.82"/>
        <constraint name="SE" perimeter="0" x="0.96" y="0.7"/>
    </connections>
    <foreground>
        <fillcolor color="#4D622C"/>
        <path>
            <move x="44.4" y="11.1"/>
            <line x="44.4" y="13.72"/>
            <line x="42.62" y="17.02"/>
            <line x="42.62" y="14.4"/>
            <line x="43.4" y="13.64"/>
            <line x="42.62" y="17.02"/>
            <line x="22.25" y="17.3"/>
            <line x="21.83" y="14.52"/>
            <close/>
        </path>
        <fillstroke/>
        <path>
            <move x="22.25" y="14.7"/>
            <line x="22.25" y="17.31"/>
            <curve x1="22.2" x2="21.62" x3="21.27" y1="17.46" y2="18.6" y3="19.23"/>
            <line x="21.02" y="16.02"/>
            <curve x1="21.37" x2="22.2" x3="22.25" y1="15.38" y2="14.84" y3="14.7"/>
            <move x="22.25" y="14.69"/>
            <line x="22.25" y="17.3"/>
            <curve x1="22.25" x2="22.25" x3="22.25" y1="17.3" y2="17.31" y3="17.31"/>
            <line x="22.25" y="14.7"/>
            <curve x1="22.25" x2="22.25" x3="22.25" y1="14.69" y2="14.69" y3="14.69"/>
            <move x="22.02" y="15.2"/>
            <line x="21.68" y="18.47"/>
            <curve x1="21.4" x2="20.75" x3="20.36" y1="18.97" y2="19.98" y3="20.42"/>
            <line x="20.4" y="17.7"/>
            <curve x1="20.78" x2="21.81" x3="22.08" y1="17.25" y2="15.64" y3="15.14"/>
            <move x="20.72" y="17.28"/>
            <line x="20.58" y="20.14"/>
            <curve x1="20.17" x2="19.62" x3="19.05" y1="20.65" y2="21.26" y3="21.71"/>
            <line x="18.52" y="18.89"/>
            <curve x1="19.09" x2="20.32" x3="20.72" y1="18.45" y2="17.81" y3="17.3"/>
        </path>
        <fillstroke/>
        <path>
            <move x="19.52" y="18.27"/>
            <curve x1="17.51" x2="14.61" x3="12.2" y1="19.9" y2="21.53" y3="21.53"/>
            <curve x1="9.45" x2="6.73" x3="4.66" y1="21.53" y2="20.49" y3="18.5"/>
            <curve x1="4.36" x2="4.07" x3="3.8" y1="18.22" y2="17.91" y3="17.59"/>
            <curve x1="2.15" x2="1.34" x3="1.34" y1="15.58" y2="13.16" y3="10.76"/>
            <line x="1.34" y="13.38"/>
            <curve x1="1.34" x2="2.15" x3="3.8" y1="15.78" y2="18.2" y3="20.2"/>
            <curve x1="4.07" x2="4.36" x3="4.66" y1="20.53" y2="20.83" y3="21.12"/>
            <curve x1="6.73" x2="9.45" x3="12.2" y1="23.11" y2="24.14" y3="24.14"/>
            <curve x1="14.61" x2="17.04" x3="19.05" y1="24.14" y2="23.34" y3="21.71"/>
            <line x="19.52" y="18.27"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#769B3F"/>
        <path>
            <move x="40.47" y="7.44"/>
            <line x="44.4" y="11.1"/>
            <line x="42.62" y="14.4"/>
            <line x="22.25" y="14.69"/>
            <curve x1="22.21" x2="21.99" x3="21.63" y1="14.83" y2="15.32" y3="15.98"/>
            <curve x1="21.33" x2="20.96" x3="20.51" y1="16.56" y2="17.13" y3="17.64"/>
            <curve x1="20.11" x2="19.62" x3="19.05" y1="18.15" y2="18.65" y3="19.09"/>
            <curve x1="17.04" x2="14.62" x3="12.2" y1="20.73" y2="21.53" y3="21.53"/>
            <curve x1="9.46" x2="6.73" x3="4.66" y1="21.53" y2="20.49" y3="18.5"/>
            <curve x1="4.36" x2="4.07" x3="3.8" y1="18.22" y2="17.91" y3="17.59"/>
            <curve x1="0" x2="0.68" x3="5.32" y1="12.98" y2="6.2" y3="2.44"/>
            <curve x1="7.33" x2="9.76" x3="12.18" y1="0.8" y2="0" y3="0"/>
            <curve x1="14.61" x2="17.02" x3="18.98" y1="0" y2="0.8" y3="2.37"/>
            <curve x1="20.46" x2="21.42" x3="21.46" y1="3.46" y2="5.04" y3="5.12"/>
            <line x="22.6" y="5.17"/>
            <line x="27.57" y="5.11"/>
            <line x="29.32" y="8.16"/>
            <line x="33.54" y="7.76"/>
            <line x="35.15" y="9.42"/>
            <line x="36.63" y="9.74"/>
            <line x="40.47" y="7.44"/>
            <close/>
            <move x="7.43" y="13.42"/>
            <curve x1="9" x2="10.25" x3="10.23" y1="13.41" y2="12.11" y3="10.57"/>
            <curve x1="10.21" x2="8.93" x3="7.37" y1="9.01" y2="7.77" y3="7.77"/>
            <curve x1="7.36" x2="7.35" x3="7.34" y1="7.77" y2="7.77" y3="7.78"/>
            <curve x1="5.79" x2="4.52" x3="4.55" y1="7.8" y2="9.08" y3="10.65"/>
            <curve x1="4.56" x2="5.82" x3="7.38" y1="12.18" y2="13.42" y3="13.42"/>
            <curve x1="7.39" x2="7.41" x3="7.43" y1="13.42" y2="13.42" y3="13.42"/>
        </path>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="39.83" name="IAM Credentials" strokewidth="inherit" w="53">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0.04"/>
        <constraint name="S" perimeter="0" x="0.5" y="1"/>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="E" perimeter="0" x="1" y="0.5"/>
        <constraint name="NW" perimeter="0" x="0.04" y="0.08"/>
        <constraint name="NE" perimeter="0" x="0.96" y="0.08"/>
        <constraint name="SW" perimeter="0" x="0.04" y="0.96"/>
        <constraint name="SE" perimeter="0" x="0.96" y="0.96"/>
    </connections>
    <foreground>
        <fillcolor color="#737678"/>
        <path>
            <move x="6.09" y="39.83"/>
            <curve x1="2.85" x2="0" x3="0" y1="39.83" y2="36.81" y3="33.5"/>
            <line x="0" y="10.5"/>
            <curve x1="0" x2="2.85" x3="6.09" y1="7.19" y2="4.83" y3="4.83"/>
            <line x="47.14" y="4.83"/>
            <curve x1="50.37" x2="53" x3="53" y1="4.83" y2="7.19" y3="10.5"/>
            <line x="53" y="33.5"/>
            <curve x1="53" x2="50.37" x3="47.14" y1="36.81" y2="39.83" y3="39.83"/>
            <line x="6.09" y="39.83"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#FFFFFF"/>
        <path>
            <move x="6.09" y="36.83"/>
            <curve x1="2.85" x2="0" x3="0" y1="36.83" y2="33.81" y3="30.5"/>
            <line x="0" y="7.5"/>
            <curve x1="0" x2="2.85" x3="6.09" y1="4.19" y2="1.83" y3="1.83"/>
            <line x="47.14" y="1.83"/>
            <curve x1="50.37" x2="53" x3="53" y1="1.83" y2="4.19" y3="7.5"/>
            <line x="53" y="30.5"/>
            <curve x1="53" x2="50.37" x3="47.14" y1="33.81" y2="36.83" y3="36.83"/>
            <line x="6.09" y="36.83"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#737678"/>
        <path>
            <move x="40.6" y="21.34"/>
            <line x="44.09" y="24.6"/>
            <line x="42.51" y="27.54"/>
            <line x="24.37" y="27.8"/>
            <curve x1="24.34" x2="24.14" x3="23.82" y1="27.92" y2="28.36" y3="28.95"/>
            <curve x1="23.55" x2="23.22" x3="22.82" y1="29.46" y2="29.97" y3="30.43"/>
            <curve x1="22.46" x2="22.03" x3="21.52" y1="30.88" y2="31.32" y3="31.72"/>
            <curve x1="19.73" x2="17.57" x3="15.42" y1="33.17" y2="33.89" y3="33.89"/>
            <curve x1="12.98" x2="10.55" x3="8.7" y1="33.89" y2="32.97" y3="31.19"/>
            <curve x1="8.44" x2="8.18" x3="7.94" y1="30.94" y2="30.67" y3="30.38"/>
            <curve x1="4.55" x2="5.16" x3="9.29" y1="26.27" y2="20.23" y3="16.89"/>
            <curve x1="11.09" x2="13.25" x3="15.4" y1="15.43" y2="14.72" y3="14.72"/>
            <curve x1="17.56" x2="19.71" x3="21.46" y1="14.72" y2="15.43" y3="16.83"/>
            <curve x1="22.78" x2="23.63" x3="23.67" y1="17.8" y2="19.2" y3="19.28"/>
            <line x="24.68" y="19.32"/>
            <line x="29.11" y="19.26"/>
            <line x="30.67" y="21.98"/>
            <line x="34.42" y="21.63"/>
            <line x="35.86" y="23.1"/>
            <line x="37.18" y="23.39"/>
            <line x="40.6" y="21.34"/>
            <close/>
            <move x="11.17" y="26.67"/>
            <curve x1="12.57" x2="13.69" x3="13.67" y1="26.66" y2="25.5" y3="24.12"/>
            <curve x1="13.65" x2="12.51" x3="11.12" y1="22.74" y2="21.64" y3="21.64"/>
            <curve x1="11.11" x2="11.1" x3="11.1" y1="21.64" y2="21.64" y3="21.64"/>
            <curve x1="9.71" x2="8.58" x3="8.61" y1="21.66" y2="22.8" y3="24.2"/>
            <curve x1="8.62" x2="9.74" x3="11.13" y1="25.56" y2="26.67" y3="26.67"/>
            <curve x1="11.14" x2="11.16" x3="11.17" y1="26.67" y2="26.67" y3="26.67"/>
        </path>
        <fillstroke/>
        <fillcolor color="#769B3F"/>
        <path>
            <move x="16.57" y="12.54"/>
            <line x="13.07" y="9.28"/>
            <line x="14.66" y="6.34"/>
            <line x="32.8" y="6.09"/>
            <curve x1="32.83" x2="33.02" x3="33.35" y1="5.96" y2="5.53" y3="4.94"/>
            <curve x1="33.62" x2="33.94" x3="34.34" y1="4.42" y2="3.91" y3="3.46"/>
            <curve x1="34.71" x2="35.14" x3="35.65" y1="3" y2="2.56" y3="2.17"/>
            <curve x1="37.44" x2="39.6" x3="41.75" y1="0.71" y2="0" y3="0"/>
            <curve x1="44.19" x2="46.62" x3="48.47" y1="0" y2="0.92" y3="2.69"/>
            <curve x1="48.73" x2="48.99" x3="49.23" y1="2.94" y2="3.22" y3="3.51"/>
            <curve x1="52.61" x2="52.01" x3="47.87" y1="7.61" y2="13.65" y3="17"/>
            <curve x1="46.08" x2="43.92" x3="41.77" y1="18.46" y2="19.17" y3="19.17"/>
            <curve x1="39.6" x2="37.46" x3="35.71" y1="19.17" y2="18.45" y3="17.06"/>
            <curve x1="34.39" x2="33.54" x3="33.5" y1="16.09" y2="14.68" y3="14.61"/>
            <line x="32.48" y="14.56"/>
            <line x="28.06" y="14.62"/>
            <line x="26.5" y="11.91"/>
            <line x="22.75" y="12.26"/>
            <line x="21.31" y="10.78"/>
            <line x="19.99" y="10.49"/>
            <line x="16.57" y="12.54"/>
            <close/>
            <move x="46" y="7.22"/>
            <curve x1="44.6" x2="43.48" x3="43.5" y1="7.23" y2="8.38" y3="9.76"/>
            <curve x1="43.52" x2="44.66" x3="46.05" y1="11.15" y2="12.25" y3="12.25"/>
            <curve x1="46.06" x2="46.06" x3="46.07" y1="12.25" y2="12.25" y3="12.24"/>
            <curve x1="47.46" x2="48.59" x3="48.56" y1="12.22" y2="11.08" y3="9.69"/>
            <curve x1="48.55" x2="47.43" x3="46.04" y1="8.32" y2="7.21" y3="7.21"/>
            <curve x1="46.03" x2="46.01" x3="46" y1="7.22" y2="7.22" y3="7.22"/>
        </path>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="53.34" name="IAM Data Encryption Key" strokewidth="inherit" w="40.77">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0"/>
        <constraint name="S" perimeter="0" x="0.5" y="1"/>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="E" perimeter="0" x="1" y="0.5"/>
        <constraint name="NW" perimeter="0" x="0.02" y="0.02"/>
        <constraint name="NE" perimeter="0" x="0.98" y="0.02"/>
        <constraint name="SW" perimeter="0" x="0.02" y="0.98"/>
        <constraint name="SE" perimeter="0" x="0.98" y="0.98"/>
    </connections>
    <foreground>
        <fillcolor color="#C5C6C7"/>
        <path>
            <move x="37.77" y="0"/>
            <curve x1="39.43" x2="40.77" x3="40.77" y1="0" y2="1.33" y3="2.97"/>
            <line x="40.77" y="50.36"/>
            <curve x1="40.77" x2="39.43" x3="37.77" y1="52" y2="53.34" y3="53.34"/>
            <line x="3" y="53.34"/>
            <curve x1="1.34" x2="0" x3="0" y1="53.34" y2="52" y3="50.36"/>
            <line x="0" y="2.97"/>
            <curve x1="0" x2="1.34" x3="3" y1="1.33" y2="0" y3="0"/>
            <line x="37.77" y="0"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#737678"/>
        <path>
            <move x="13.89" y="33.18"/>
            <line x="10.99" y="36.29"/>
            <line x="8.38" y="34.88"/>
            <line x="8.15" y="18.76"/>
            <curve x1="8.04" x2="7.65" x3="7.13" y1="18.72" y2="18.55" y3="18.26"/>
            <curve x1="6.67" x2="6.22" x3="5.82" y1="18.03" y2="17.74" y3="17.38"/>
            <curve x1="5.41" x2="5.02" x3="4.67" y1="17.06" y2="16.67" y3="16.22"/>
            <curve x1="3.37" x2="2.74" x3="2.74" y1="14.63" y2="12.71" y3="10.8"/>
            <curve x1="2.74" x2="3.56" x3="5.14" y1="8.63" y2="6.47" y3="4.83"/>
            <curve x1="5.36" x2="5.6" x3="5.86" y1="4.59" y2="4.37" y3="4.15"/>
            <curve x1="9.51" x2="14.88" x3="17.85" y1="1.14" y2="1.68" y3="5.35"/>
            <curve x1="19.15" x2="19.78" x3="19.78" y1="6.95" y2="8.87" y3="10.78"/>
            <curve x1="19.78" x2="19.15" x3="17.9" y1="12.71" y2="14.62" y3="16.17"/>
            <curve x1="17.04" x2="15.8" x3="15.73" y1="17.34" y2="18.09" y3="18.14"/>
            <line x="15.69" y="19.04"/>
            <line x="15.74" y="22.97"/>
            <line x="13.33" y="24.36"/>
            <line x="13.64" y="27.69"/>
            <line x="12.33" y="28.97"/>
            <line x="12.07" y="30.14"/>
            <line x="13.89" y="33.18"/>
            <close/>
            <move x="9.15" y="7.02"/>
            <curve x1="9.16" x2="10.19" x3="11.42" y1="8.27" y2="9.26" y3="9.24"/>
            <curve x1="12.65" x2="13.63" x3="13.63" y1="9.22" y2="8.21" y3="6.97"/>
            <curve x1="13.63" x2="13.63" x3="13.63" y1="6.97" y2="6.96" y3="6.95"/>
            <curve x1="13.61" x2="12.59" x3="11.35" y1="5.72" y2="4.72" y3="4.74"/>
            <curve x1="10.14" x2="9.16" x3="9.16" y1="4.75" y2="5.75" y3="6.98"/>
            <curve x1="9.15" x2="9.15" x3="9.15" y1="6.99" y2="7.01" y3="7.02"/>
        </path>
        <fillstroke/>
        <fillcolor color="#3A3A3A"/>
        <path>
            <move x="33.63" y="36.42"/>
            <line x="33.63" y="30.28"/>
            <curve x1="33.2" x2="29.71" x3="25.53" y1="26.24" y2="23.09" y3="23.09"/>
            <curve x1="21.33" x2="17.84" x3="17.43" y1="23.09" y2="26.22" y3="30.28"/>
            <line x="17.43" y="36.42"/>
            <line x="13.24" y="36.42"/>
            <line x="13.24" y="50.03"/>
            <line x="37.7" y="50.03"/>
            <line x="37.7" y="36.42"/>
            <line x="33.63" y="36.42"/>
            <close/>
            <move x="29.67" y="36.42"/>
            <line x="21.33" y="36.42"/>
            <line x="21.36" y="30.28"/>
            <curve x1="21.36" x2="21.36" x3="25.53" y1="30.28" y2="26.52" y3="26.53"/>
            <curve x1="29.52" x2="29.7" x3="29.7" y1="26.54" y2="30.28" y3="30.28"/>
            <line x="29.67" y="36.42"/>
            <close/>
        </path>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="49" name="IAM Encrypted Data" strokewidth="inherit" w="38">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0"/>
        <constraint name="S" perimeter="0" x="0.5" y="1"/>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="E" perimeter="0" x="1" y="0.5"/>
        <constraint name="NW" perimeter="0" x="0" y="0"/>
        <constraint name="NE" perimeter="0" x="0.86" y="0.14"/>
        <constraint name="SW" perimeter="0" x="0" y="1"/>
        <constraint name="SE" perimeter="0" x="1" y="1"/>
    </connections>
    <foreground>
        <fillcolor color="#C5C6C7"/>
        <path>
            <move x="38" y="49"/>
            <line x="0" y="49"/>
            <line x="0" y="0"/>
            <line x="26.51" y="0"/>
            <line x="38" y="12.37"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#3A3A3A"/>
        <path>
            <move x="27.16" y="28.02"/>
            <line x="27.16" y="21.88"/>
            <curve x1="26.73" x2="23.24" x3="19.06" y1="17.84" y2="14.7" y3="14.7"/>
            <curve x1="14.86" x2="11.37" x3="10.96" y1="14.7" y2="17.82" y3="21.88"/>
            <line x="10.96" y="28.02"/>
            <line x="6.77" y="28.02"/>
            <line x="6.77" y="41.64"/>
            <line x="31.23" y="41.64"/>
            <line x="31.23" y="28.02"/>
            <line x="27.16" y="28.02"/>
            <close/>
            <move x="23.2" y="28.02"/>
            <line x="14.86" y="28.02"/>
            <line x="14.89" y="21.88"/>
            <curve x1="14.89" x2="14.89" x3="19.06" y1="21.88" y2="18.12" y3="18.13"/>
            <curve x1="23.05" x2="23.23" x3="23.23" y1="18.15" y2="21.88" y3="21.88"/>
            <line x="23.2" y="28.02"/>
            <close/>
        </path>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="53" name="IAM MFA Token" strokewidth="inherit" w="53">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0"/>
        <constraint name="S" perimeter="0" x="0.5" y="1"/>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="E" perimeter="0" x="1" y="0.5"/>
        <constraint name="NW" perimeter="0" x="0.145" y="0.145"/>
        <constraint name="NE" perimeter="0" x="0.855" y="0.145"/>
        <constraint name="SW" perimeter="0" x="0.145" y="0.855"/>
        <constraint name="SE" perimeter="0" x="0.855" y="0.855"/>
    </connections>
    <foreground>
        <save/>
        <strokecolor color="#808080"/>
        <fillcolor color="#C5C6C7"/>
        <strokewidth width="2"/>
        <miterlimit limit="10"/>
        <ellipse h="53" w="53" x="0" y="0"/>
        <fillstroke/>
        <restore/>
        <rect/>
        <stroke/>
        <fontcolor color="#333333"/>
        <fontsize size="18.54"/>
        <fontfamily family="'Helvetica-Bold'"/>
        <text str="M" valign="bottom" x="6.67" y="32.88"/>
        <text str="F" valign="bottom" x="22.11" y="32.88"/>
        <text str="A" valign="bottom" x="32.42" y="32.88"/>
    </foreground>
</shape>
<shape aspect="variable" h="57" name="IAM Permissions" strokewidth="inherit" w="42">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0"/>
        <constraint name="S" perimeter="0" x="0.5" y="1"/>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="E" perimeter="0" x="1" y="0.5"/>
        <constraint name="NW" perimeter="0" x="0.04" y="0.04"/>
        <constraint name="NE" perimeter="0" x="0.96" y="0.04"/>
        <constraint name="SW" perimeter="0" x="0.04" y="0.96"/>
        <constraint name="SE" perimeter="0" x="0.96" y="0.96"/>
    </connections>
    <foreground>
        <fillcolor color="#737678"/>
        <path>
            <move x="42" y="50.67"/>
            <curve x1="42" x2="38.78" x3="34.8" y1="53.98" y2="57" y3="57"/>
            <line x="7.2" y="57"/>
            <curve x1="3.22" x2="0" x3="0" y1="57" y2="53.98" y3="50.67"/>
            <line x="0" y="8.67"/>
            <curve x1="0" x2="3.22" x3="7.2" y1="5.35" y2="3" y3="3"/>
            <line x="34.8" y="3"/>
            <curve x1="38.78" x2="42" x3="42" y1="3" y2="5.35" y3="8.67"/>
            <line x="42" y="50.67"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#C5C6C7"/>
        <path>
            <move x="42" y="47.67"/>
            <curve x1="42" x2="38.54" x3="34.59" y1="50.98" y2="54" y3="54"/>
            <line x="7.16" y="54"/>
            <curve x1="3.2" x2="0" x3="0" y1="54" y2="50.98" y3="47.67"/>
            <line x="0" y="5.67"/>
            <curve x1="0" x2="3.2" x3="7.16" y1="2.35" y2="0" y3="0"/>
            <line x="34.59" y="0"/>
            <curve x1="38.54" x2="42" x3="42" y1="0" y2="2.35" y3="5.67"/>
            <line x="42" y="47.67"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#769B3F"/>
        <path>
            <move x="9.39" y="10.41"/>
            <line x="6.6" y="7.38"/>
            <line x="3.51" y="10.46"/>
            <line x="3.51" y="10.46"/>
            <line x="9.33" y="16.28"/>
            <line x="19.16" y="6.45"/>
            <line x="16.92" y="4.17"/>
            <close/>
        </path>
        <fillstroke/>
        <path>
            <move x="9.39" y="24.36"/>
            <line x="6.6" y="21.32"/>
            <line x="3.51" y="24.41"/>
            <line x="3.51" y="24.41"/>
            <line x="9.33" y="30.23"/>
            <line x="19.16" y="20.4"/>
            <line x="16.92" y="18.11"/>
            <close/>
        </path>
        <fillstroke/>
        <fontcolor color="#E15343"/>
        <fontsize size="15.568"/>
        <fontfamily family="'HelveticaNeue-BlackCond'"/>
        <text str="X" valign="bottom" x="3.36" y="45.03"/>
    </foreground>
</shape>
<shape aspect="variable" h="72.96" name="IAM Roles" strokewidth="inherit" w="84.35">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0.01"/>
        <constraint name="S" perimeter="0" x="0.5" y="0.95"/>
        <constraint name="W" perimeter="0" x="0.13" y="0.5"/>
        <constraint name="E" perimeter="0" x="0.96" y="0.5"/>
        <constraint name="NW" perimeter="0" x="0.22" y="0.22"/>
        <constraint name="NE" perimeter="0" x="0.83" y="0.17"/>
        <constraint name="SW" perimeter="0" x="0.1" y="0.91"/>
        <constraint name="SE" perimeter="0" x="0.78" y="0.78"/>
    </connections>
    <foreground>
        <fillcolor color="#4D632F"/>
        <path>
            <move x="9.17" y="66.74"/>
            <curve x1="8.55" x2="7.93" x3="7.33" y1="66.48" y2="66.21" y3="65.9"/>
            <curve x1="1.45" x2="0" x3="2.63" y1="62.88" y2="59.37" y3="53.3"/>
            <curve x1="3.71" x2="5.62" x3="7.12" y1="50.82" y2="48.71" y3="46.41"/>
            <curve x1="7.69" x2="8.28" x3="8.71" y1="45.54" y2="44.66" y3="43.72"/>
            <curve x1="10.88" x2="10.67" x3="12.38" y1="39.04" y2="33.76" y3="28.94"/>
            <curve x1="14.25" x2="16.6" x3="19.74" y1="23.65" y2="18.64" y3="13.98"/>
            <curve x1="25.31" x2="33.39" x3="42.94" y1="5.72" y2="2.16" y3="0.93"/>
            <curve x1="50.16" x2="56.29" x3="62.25" y1="0" y2="2.4" y3="6.09"/>
            <curve x1="70.7" x2="76.12" x3="78.2" y1="11.33" y2="18.71" y3="28.37"/>
            <curve x1="78.81" x2="79.45" x3="80.86" y1="31.2" y2="33.91" y3="36.42"/>
            <curve x1="81.1" x2="81.36" x3="81.69" y1="36.86" y2="37.3" y3="37.67"/>
            <curve x1="84.28" x2="84.35" x3="81.8" y1="40.58" y2="43.33" y3="46.28"/>
            <curve x1="79.15" x2="75.81" x3="72.28" y1="49.36" y2="51.57" y3="53.5"/>
            <curve x1="64.43" x2="56.56" x3="48.66" y1="57.79" y2="62.08" y3="66.28"/>
            <curve x1="46.53" x2="44.3" x3="42" y1="67.41" y2="68.48" y3="69.13"/>
            <curve x1="32.05" x2="16.04" x3="9.17" y1="72.96" y2="69.52" y3="66.74"/>
            <close/>
            <move x="78.25" y="36.16"/>
            <curve x1="77.98" x2="77.71" x3="77.44" y1="35.48" y2="34.81" y3="34.14"/>
            <curve x1="77.15" x2="76.7" x3="76.59" y1="33.63" y2="33.15" y3="32.6"/>
            <curve x1="76.33" x2="76.24" x3="76.08" y1="31.33" y2="30.02" y3="28.73"/>
            <curve x1="75.99" x2="75.97" x3="75.8" y1="28.07" y2="27.38" y3="26.75"/>
            <curve x1="74.58" x2="72.15" x3="69.4" y1="22.26" y2="18.38" y3="14.73"/>
            <curve x1="68.32" x2="66.66" x3="65.26" y1="13.3" y2="12.3" y3="11.1"/>
            <curve x1="61.79" x2="58.1" x3="53.77" y1="8.23" y2="5.73" y3="4.34"/>
            <curve x1="53.09" x2="52.41" x3="51.73" y1="4.1" y2="3.86" y3="3.62"/>
            <curve x1="51.53" x2="51.33" x3="51.13" y1="3.53" y2="3.35" y3="3.35"/>
            <curve x1="47.61" x2="44.06" x3="40.57" y1="3.38" y2="3.22" y3="3.55"/>
            <curve x1="37.63" x2="34.62" x3="32.29" y1="3.82" y2="4.53" y3="6.51"/>
            <curve x1="30.51" x2="28.96" x3="27.52" y1="8.02" y2="9.86" y3="11.72"/>
            <curve x1="23.65" x2="21.21" x3="18.88" y1="16.71" y2="22.5" y3="28.3"/>
            <curve x1="17.62" x2="17" x3="16.07" y1="31.45" y2="34.85" y3="38.13"/>
            <curve x1="15.55" x2="15.09" x3="14.41" y1="39.96" y2="41.82" y3="43.59"/>
            <curve x1="14.15" x2="13.4" x3="12.78" y1="44.28" y2="45.15" y3="45.22"/>
            <curve x1="12.28" x2="11.64" x3="11.06" y1="45.29" y2="44.31" y3="43.79"/>
            <curve x1="10.34" x2="10.91" x3="11.84" y1="45.31" y2="46.9" y3="48.3"/>
            <curve x1="14.27" x2="17.73" x3="21.93" y1="51.91" y2="54.12" y3="55.17"/>
            <curve x1="27.11" x2="32.27" x3="37.72" y1="56.46" y2="57.65" y3="56.93"/>
            <curve x1="42.11" x2="46.56" x3="50.98" y1="56.35" y2="56.21" y3="55.88"/>
            <curve x1="52.57" x2="54.16" x3="55.75" y1="55.76" y2="55.66" y3="55.56"/>
            <curve x1="50.53" x2="45.13" x3="39.52" y1="57.73" y2="59.49" y3="59.44"/>
            <curve x1="34.57" x2="29.62" x3="24.67" y1="59.4" y2="58.33" y3="57.73"/>
            <curve x1="20.22" x2="15.77" x3="11.32" y1="57.2" y2="56.66" y3="56.19"/>
            <curve x1="9.96" x2="8.81" x3="8.39" y1="56.05" y2="56.66" y3="58"/>
            <curve x1="7.59" x2="9.66" x3="12.26" y1="60.56" y2="64.65" y3="65.57"/>
            <curve x1="18.01" x2="23.97" x3="30.06" y1="67.62" y2="68.8" y3="69"/>
            <curve x1="35.36" x2="40.48" x3="45.2" y1="69.18" y2="67.95" y3="65.51"/>
            <curve x1="51.6" x2="57.92" x3="64.27" y1="62.21" y2="58.72" y3="55.31"/>
            <curve x1="66.34" x2="68.43" x3="70.8" y1="54.21" y2="53.12" y3="51.86"/>
            <curve x1="68.28" x2="67.69" x3="68.67" y1="50.66" y2="48.9" y3="46.62"/>
            <curve x1="69.56" x2="70.45" x3="71.33" y1="46.87" y2="47.13" y3="47.38"/>
            <curve x1="71.21" x2="71.08" x3="70.92" y1="48.68" y2="49.97" y3="51.65"/>
            <curve x1="74.98" x2="78.29" x3="80.8" y1="49.54" y2="47.27" y3="44"/>
            <curve x1="81.94" x2="81.55" x3="80.54" y1="42.53" y2="41.06" y3="39.64"/>
            <curve x1="79.73" x2="79.01" x3="78.25" y1="38.51" y2="37.32" y3="36.16"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#759B3D"/>
        <path>
            <move x="53.77" y="4.34"/>
            <curve x1="58.11" x2="61.79" x3="65.26" y1="5.73" y2="8.23" y3="11.1"/>
            <curve x1="64.04" x2="62.79" x3="61.6" y1="11.05" y2="10.81" y3="10.98"/>
            <curve x1="57.05" x2="53.93" x3="51.3" y1="11.64" y2="14.5" y3="18.01"/>
            <curve x1="47.63" x2="46.79" x3="47.07" y1="22.91" y2="28.61" y3="34.5"/>
            <curve x1="47.19" x2="47.63" x3="49.33" y1="37.21" y2="39.97" y3="42.8"/>
            <curve x1="49.59" x2="49.77" x3="50" y1="40.92" y2="39.45" y3="37.99"/>
            <curve x1="50.79" x2="52.14" x3="55.06" y1="33.11" y2="28.44" y3="24.35"/>
            <curve x1="59.71" x2="69.28" x3="73.75" y1="17.85" y2="17.79" y3="24.3"/>
            <curve x1="74.69" x2="75.31" x3="76.08" y1="25.66" y2="27.25" y3="28.73"/>
            <curve x1="76.24" x2="76.34" x3="76.59" y1="30.02" y2="31.33" y3="32.6"/>
            <curve x1="76.7" x2="77.15" x3="77.44" y1="33.15" y2="33.63" y3="34.14"/>
            <curve x1="77.09" x2="74.52" x3="71.74" y1="37.75" y2="39.73" y3="41.29"/>
            <curve x1="68.21" x2="64.59" x3="60.83" y1="43.26" y2="45.14" y3="46.6"/>
            <curve x1="54.36" x2="47.56" x3="40.68" y1="49.11" y2="50.54" y3="51.32"/>
            <curve x1="37.02" x2="33.26" x3="29.59" y1="51.74" y2="51.7" y3="51.34"/>
            <curve x1="25.58" x2="21.64" x3="17.66" y1="50.94" y2="49.91" y3="49.22"/>
            <curve x1="16.78" x2="15.86" x3="14.96" y1="49.07" y2="49.2" y3="49.2"/>
            <curve x1="14.93" x2="14.91" x3="14.89" y1="49.42" y2="49.63" y3="49.85"/>
            <curve x1="16.91" x2="18.92" x3="20.95" y1="50.44" y2="51.09" y3="51.62"/>
            <curve x1="25.9" x2="30.79" x3="36.01" y1="52.91" y2="54.72" y3="54.1"/>
            <curve x1="40.3" x2="44.58" x3="48.83" y1="53.58" y2="52.89" y3="52.1"/>
            <curve x1="58.42" x2="67.4" x3="75.13" y1="50.33" y2="47.07" y3="40.89"/>
            <curve x1="76.66" x2="78.3" x3="78.25" y1="39.66" y2="38.45" y3="36.16"/>
            <curve x1="79.01" x2="79.73" x3="80.54" y1="37.32" y2="38.51" y3="39.64"/>
            <curve x1="81.55" x2="81.94" x3="80.81" y1="41.06" y2="42.53" y3="44"/>
            <curve x1="78.3" x2="74.99" x3="70.93" y1="47.27" y2="49.55" y3="51.65"/>
            <curve x1="71.09" x2="71.21" x3="71.34" y1="49.97" y2="48.68" y3="47.38"/>
            <curve x1="73.33" x2="75.31" x3="77.3" y1="46.03" y2="44.67" y3="43.32"/>
            <curve x1="77.24" x2="77.18" x3="77.11" y1="43.16" y2="43.01" y3="42.85"/>
            <curve x1="76.22" x2="75.2" x3="74.45" y1="42.95" y2="42.82" y3="43.21"/>
            <curve x1="72.46" x2="70.59" x3="68.68" y1="44.23" y2="45.47" y3="46.62"/>
            <curve x1="67.69" x2="68.28" x3="70.81" y1="48.9" y2="50.66" y3="51.86"/>
            <curve x1="68.43" x2="66.35" x3="64.28" y1="53.12" y2="54.21" y3="55.32"/>
            <curve x1="57.92" x2="51.61" x3="45.2" y1="58.72" y2="62.21" y3="65.52"/>
            <curve x1="40.48" x2="35.37" x3="30.06" y1="67.95" y2="69.18" y3="69"/>
            <curve x1="23.98" x2="18.02" x3="12.26" y1="68.8" y2="67.62" y3="65.57"/>
            <curve x1="9.66" x2="7.59" x3="8.39" y1="64.65" y2="60.56" y3="58"/>
            <curve x1="8.81" x2="9.96" x3="11.32" y1="56.66" y2="56.05" y3="56.19"/>
            <curve x1="15.78" x2="20.23" x3="24.68" y1="56.66" y2="57.2" y3="57.73"/>
            <curve x1="29.63" x2="34.57" x3="39.52" y1="58.33" y2="59.4" y3="59.44"/>
            <curve x1="45.13" x2="50.53" x3="55.76" y1="59.49" y2="57.73" y3="55.56"/>
            <curve x1="54.17" x2="52.58" x3="50.99" y1="55.66" y2="55.76" y3="55.88"/>
            <curve x1="46.56" x2="42.12" x3="37.73" y1="56.21" y2="56.35" y3="56.93"/>
            <curve x1="32.28" x2="27.12" x3="21.94" y1="57.65" y2="56.46" y3="55.17"/>
            <curve x1="17.74" x2="14.27" x3="11.84" y1="54.12" y2="51.92" y3="48.3"/>
            <curve x1="10.91" x2="10.35" x3="11.06" y1="46.91" y2="45.32" y3="43.79"/>
            <curve x1="11.64" x2="12.28" x3="12.79" y1="44.31" y2="45.29" y3="45.23"/>
            <curve x1="13.4" x2="14.15" x3="14.42" y1="45.15" y2="44.28" y3="43.6"/>
            <curve x1="15.1" x2="15.55" x3="16.07" y1="41.82" y2="39.96" y3="38.13"/>
            <curve x1="17" x2="17.63" x3="18.89" y1="34.85" y2="31.45" y3="28.3"/>
            <curve x1="21.21" x2="23.66" x3="27.53" y1="22.5" y2="16.71" y3="11.72"/>
            <curve x1="28.97" x2="30.52" x3="32.3" y1="9.86" y2="8.02" y3="6.51"/>
            <curve x1="34.62" x2="37.63" x3="40.57" y1="4.53" y2="3.83" y3="3.55"/>
            <curve x1="44.07" x2="47.61" x3="51.13" y1="3.22" y2="3.38" y3="3.36"/>
            <curve x1="51.33" x2="51.53" x3="51.73" y1="3.35" y2="3.53" y3="3.62"/>
            <curve x1="49.54" x2="47.57" x3="45.97" y1="3.53" y2="4.19" y3="5.63"/>
            <curve x1="44.42" x2="42.9" x3="41.59" y1="7.03" y2="8.49" y3="10.1"/>
            <curve x1="35.53" x2="33.71" x3="33.5" y1="17.62" y2="26.55" y3="35.88"/>
            <curve x1="33.42" x2="34.16" x3="36.12" y1="39.07" y2="42.28" y3="45"/>
            <curve x1="37.18" x2="38.54" x3="40.62" y1="46.46" y2="47.35" y3="47.31"/>
            <curve x1="40.38" x2="40.24" x3="40.06" y1="46.65" y2="46.22" y3="45.82"/>
            <curve x1="38.36" x2="37.54" x3="37.49" y1="42.01" y2="38" y3="33.85"/>
            <curve x1="37.4" x2="39.9" x3="43.35" y1="26.59" y2="20.05" y3="13.82"/>
            <curve x1="45.75" x2="48.7" x3="53.77" y1="9.5" y2="5.74" y3="4.34"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#B5C79A"/>
        <path>
            <move x="76.08" y="28.73"/>
            <curve x1="75.31" x2="74.69" x3="73.75" y1="27.25" y2="25.66" y3="24.3"/>
            <curve x1="69.28" x2="59.71" x3="55.06" y1="17.79" y2="17.85" y3="24.35"/>
            <curve x1="52.14" x2="50.79" x3="50" y1="28.44" y2="33.11" y3="37.99"/>
            <curve x1="49.77" x2="49.59" x3="49.33" y1="39.45" y2="40.92" y3="42.8"/>
            <curve x1="47.63" x2="47.19" x3="47.06" y1="39.97" y2="37.21" y3="34.5"/>
            <curve x1="46.79" x2="47.63" x3="51.3" y1="28.61" y2="22.91" y3="18.01"/>
            <curve x1="53.93" x2="57.05" x3="61.6" y1="14.5" y2="11.64" y3="10.98"/>
            <curve x1="62.79" x2="64.04" x3="65.26" y1="10.81" y2="11.05" y3="11.1"/>
            <curve x1="66.66" x2="68.32" x3="69.4" y1="12.3" y2="13.3" y3="14.73"/>
            <curve x1="72.15" x2="74.58" x3="75.8" y1="18.38" y2="22.26" y3="26.75"/>
            <curve x1="75.97" x2="75.99" x3="76.08" y1="27.38" y2="28.07" y3="28.73"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#B4C79A"/>
        <path>
            <move x="53.77" y="4.34"/>
            <curve x1="48.7" x2="45.75" x3="43.35" y1="5.74" y2="9.49" y3="13.82"/>
            <curve x1="39.89" x2="37.39" x3="37.49" y1="20.05" y2="26.59" y3="33.85"/>
            <curve x1="37.54" x2="38.36" x3="40.06" y1="38" y2="42.01" y3="45.82"/>
            <curve x1="40.24" x2="40.37" x3="40.62" y1="46.22" y2="46.64" y3="47.3"/>
            <curve x1="38.54" x2="37.18" x3="36.12" y1="47.35" y2="46.46" y3="44.99"/>
            <curve x1="34.16" x2="33.42" x3="33.49" y1="42.27" y2="39.07" y3="35.87"/>
            <curve x1="33.71" x2="35.52" x3="41.59" y1="26.54" y2="17.62" y3="10.1"/>
            <curve x1="42.9" x2="44.42" x3="45.96" y1="8.49" y2="7.03" y3="5.63"/>
            <curve x1="47.57" x2="49.54" x3="51.73" y1="4.19" y2="3.53" y3="3.62"/>
            <curve x1="52.41" x2="53.09" x3="53.77" y1="3.86" y2="4.1" y3="4.34"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#B3C697"/>
        <path>
            <move x="78.25" y="36.16"/>
            <curve x1="78.3" x2="76.66" x3="75.12" y1="38.45" y2="39.66" y3="40.89"/>
            <curve x1="67.4" x2="58.42" x3="48.83" y1="47.07" y2="50.33" y3="52.1"/>
            <curve x1="44.57" x2="40.3" x3="36.01" y1="52.88" y2="53.58" y3="54.09"/>
            <curve x1="30.78" x2="25.89" x3="20.95" y1="54.72" y2="52.91" y3="51.62"/>
            <curve x1="18.91" x2="16.91" x3="14.88" y1="51.09" y2="50.44" y3="49.85"/>
            <curve x1="14.91" x2="14.93" x3="14.95" y1="49.63" y2="49.42" y3="49.2"/>
            <curve x1="15.85" x2="16.78" x3="17.65" y1="49.2" y2="49.07" y3="49.22"/>
            <curve x1="21.64" x2="25.58" x3="29.59" y1="49.91" y2="50.94" y3="51.33"/>
            <curve x1="33.25" x2="37.02" x3="40.67" y1="51.69" y2="51.74" y3="51.32"/>
            <curve x1="47.55" x2="54.35" x3="60.82" y1="50.54" y2="49.11" y3="46.6"/>
            <curve x1="64.59" x2="68.21" x3="71.74" y1="45.14" y2="43.26" y3="41.28"/>
            <curve x1="74.52" x2="77.09" x3="77.44" y1="39.73" y2="37.75" y3="34.14"/>
            <curve x1="77.71" x2="77.98" x3="78.25" y1="34.81" y2="35.48" y3="36.16"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#B2C597"/>
        <path>
            <move x="68.67" y="46.62"/>
            <curve x1="70.59" x2="72.46" x3="74.44" y1="45.46" y2="44.22" y3="43.21"/>
            <curve x1="75.2" x2="76.21" x3="77.11" y1="42.82" y2="42.95" y3="42.85"/>
            <curve x1="77.17" x2="77.23" x3="77.3" y1="43" y2="43.16" y3="43.32"/>
            <curve x1="75.31" x2="73.32" x3="71.34" y1="44.67" y2="46.03" y3="47.38"/>
            <curve x1="70.45" x2="69.56" x3="68.67" y1="47.12" y2="46.87" y3="46.62"/>
            <close/>
        </path>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="55.84" name="IAM Security Token Service" strokewidth="inherit" w="40.76">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0"/>
        <constraint name="S" perimeter="0" x="0.5" y="1"/>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="E" perimeter="0" x="1" y="0.5"/>
        <constraint name="NW" perimeter="0" x="0.02" y="0.02"/>
        <constraint name="NE" perimeter="0" x="0.98" y="0.02"/>
        <constraint name="SW" perimeter="0" x="0.02" y="0.98"/>
        <constraint name="SE" perimeter="0" x="0.98" y="0.98"/>
    </connections>
    <foreground>
        <save/>
        <save/>
        <fontcolor color="#FFFFFF"/>
        <fontsize size="10.54"/>
        <fontfamily family="'Helvetica-Bold'"/>
        <text str="ROOT" valign="bottom" x="5.28" y="49.89"/>
        <restore/>
        <rect/>
        <stroke/>
        <fillcolor color="#769B3F"/>
        <path>
            <move x="40.76" y="50.36"/>
            <line x="40.76" y="52.86"/>
            <curve x1="40.76" x2="40.75" x3="40.73" y1="53.01" y2="53.15" y3="53.29"/>
            <line x="40.73" y="50.78"/>
            <curve x1="40.75" x2="40.76" x3="40.76" y1="50.65" y2="50.5" y3="50.36"/>
        </path>
        <fillstroke/>
        <path>
            <move x="40.73" y="50.78"/>
            <line x="40.73" y="53.29"/>
            <curve x1="40.71" x2="40.67" x3="40.62" y1="53.45" y2="53.61" y3="53.77"/>
            <line x="40.62" y="51.27"/>
            <curve x1="40.67" x2="40.71" x3="40.73" y1="51.11" y2="50.95" y3="50.78"/>
        </path>
        <fillstroke/>
        <path>
            <move x="40.62" y="51.26"/>
            <line x="40.62" y="53.77"/>
            <curve x1="40.56" x2="40.49" x3="40.4" y1="53.95" y2="54.13" y3="54.29"/>
            <line x="40.4" y="51.79"/>
            <curve x1="40.49" x2="40.57" x3="40.62" y1="51.62" y2="51.45" y3="51.26"/>
        </path>
        <fillstroke/>
        <path>
            <move x="40.4" y="51.79"/>
            <line x="40.4" y="54.29"/>
            <curve x1="39.89" x2="38.9" x3="37.76" y1="55.21" y2="55.84" y3="55.84"/>
            <line x="37.76" y="53.34"/>
            <curve x1="38.9" x2="39.89" x3="40.4" y1="53.34" y2="52.71" y3="51.79"/>
        </path>
        <fillstroke/>
        <path>
            <move x="40.76" y="50.36"/>
            <line x="40.76" y="52.86"/>
            <curve x1="40.76" x2="39.43" x3="37.76" y1="54.5" y2="55.84" y3="55.84"/>
            <line x="37.76" y="53.34"/>
            <curve x1="39.43" x2="40.76" x3="40.76" y1="53.34" y2="52" y3="50.36"/>
            <close/>
        </path>
        <fillstroke/>
        <path>
            <move x="39.05" y="52.83"/>
            <line x="37.76" y="55.84"/>
            <line x="3" y="55.84"/>
            <line x="2.18" y="52.7"/>
            <close/>
        </path>
        <fillstroke/>
        <path>
            <move x="3" y="53.34"/>
            <line x="3" y="55.84"/>
            <curve x1="1.34" x2="0" x3="0" y1="55.84" y2="54.5" y3="52.86"/>
            <line x="0" y="50.36"/>
            <curve x1="0" x2="1.34" x3="3" y1="52" y2="53.34" y3="53.34"/>
        </path>
        <fillstroke/>
        <path>
            <move x="3" y="53.34"/>
            <line x="3" y="55.84"/>
            <curve x1="1.34" x2="0" x3="0" y1="55.84" y2="54.5" y3="52.86"/>
            <line x="0" y="50.36"/>
            <curve x1="0" x2="1.34" x3="3" y1="52" y2="53.34" y3="53.34"/>
            <close/>
        </path>
        <fillstroke/>
        <path>
            <move x="37.76" y="0"/>
            <curve x1="39.43" x2="40.76" x3="40.76" y1="0" y2="1.33" y3="2.97"/>
            <line x="40.76" y="50.36"/>
            <curve x1="40.76" x2="39.43" x3="37.76" y1="52" y2="53.34" y3="53.34"/>
            <line x="3" y="53.34"/>
            <curve x1="1.34" x2="0" x3="0" y1="53.34" y2="52" y3="50.36"/>
            <line x="0" y="2.97"/>
            <curve x1="0" x2="1.34" x3="3" y1="1.33" y2="0" y3="0"/>
            <line x="37.76" y="0"/>
            <close/>
        </path>
        <fillstroke/>
        <fontcolor color="#FFFFFF"/>
        <fontsize size="13.141"/>
        <fontfamily family="'Helvetica-Bold'"/>
        <text str="STS" valign="bottom" x="6.6" y="50.36"/>
        <restore/>
        <rect/>
        <stroke/>
        <strokecolor color="#444444"/>
        <fillcolor color="#FFFFFF"/>
        <strokewidth width="3"/>
        <miterlimit limit="10"/>
        <ellipse h="26.04" w="26.04" x="6.72" y="4.61"/>
        <fillstroke/>
        <path>
            <move x="20.81" y="9.46"/>
            <line x="20.74" y="20.83"/>
        </path>
        <fillstroke/>
        <path>
            <move x="14.31" y="19.7"/>
            <line x="21.31" y="19.7"/>
        </path>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="49.39" name="IAM Short Term Credentials" strokewidth="inherit" w="61.45">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0.04"/>
        <constraint name="S" perimeter="0" x="0.5" y="0.81"/>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="E" perimeter="0" x="0.93" y="0.5"/>
        <constraint name="NW" perimeter="0" x="0.04" y="0.06"/>
        <constraint name="NE" perimeter="0" x="0.83" y="0.06"/>
        <constraint name="SW" perimeter="0" x="0.04" y="0.78"/>
        <constraint name="SE" perimeter="0" x="0.95" y="0.95"/>
    </connections>
    <foreground>
        <fillcolor color="#737678"/>
        <path>
            <move x="6.09" y="39.94"/>
            <curve x1="2.85" x2="0" x3="0" y1="39.94" y2="36.92" y3="33.6"/>
            <line x="0" y="10.6"/>
            <curve x1="0" x2="2.85" x3="6.09" y1="7.29" y2="4.94" y3="4.94"/>
            <line x="47.14" y="4.94"/>
            <curve x1="50.37" x2="53" x3="53" y1="4.94" y2="7.29" y3="10.6"/>
            <line x="53" y="33.6"/>
            <curve x1="53" x2="50.37" x3="47.14" y1="36.92" y2="39.94" y3="39.94"/>
            <line x="6.09" y="39.94"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#FFFFFF"/>
        <path>
            <move x="6.09" y="36.94"/>
            <curve x1="2.85" x2="0" x3="0" y1="36.94" y2="33.92" y3="30.6"/>
            <line x="0" y="7.6"/>
            <curve x1="0" x2="2.85" x3="6.09" y1="4.29" y2="1.94" y3="1.94"/>
            <line x="47.14" y="1.94"/>
            <curve x1="50.37" x2="53" x3="53" y1="1.94" y2="4.29" y3="7.6"/>
            <line x="53" y="30.6"/>
            <curve x1="53" x2="50.37" x3="47.14" y1="33.92" y2="36.94" y3="36.94"/>
            <line x="6.09" y="36.94"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#737678"/>
        <path>
            <move x="38.51" y="21.34"/>
            <line x="42.01" y="24.6"/>
            <line x="40.43" y="27.54"/>
            <line x="22.29" y="27.8"/>
            <curve x1="22.25" x2="22.06" x3="21.73" y1="27.92" y2="28.36" y3="28.95"/>
            <curve x1="21.47" x2="21.14" x3="20.74" y1="29.46" y2="29.97" y3="30.43"/>
            <curve x1="20.38" x2="19.94" x3="19.44" y1="30.88" y2="31.32" y3="31.72"/>
            <curve x1="17.65" x2="15.49" x3="13.34" y1="33.17" y2="33.89" y3="33.89"/>
            <curve x1="10.89" x2="8.46" x3="6.62" y1="33.89" y2="32.97" y3="31.19"/>
            <curve x1="6.35" x2="6.1" x3="5.86" y1="30.94" y2="30.67" y3="30.38"/>
            <curve x1="2.47" x2="3.08" x3="7.21" y1="26.27" y2="20.23" y3="16.89"/>
            <curve x1="9" x2="11.17" x3="13.32" y1="15.43" y2="14.72" y3="14.72"/>
            <curve x1="15.48" x2="17.63" x3="19.38" y1="14.72" y2="15.43" y3="16.83"/>
            <curve x1="20.69" x2="21.54" x3="21.59" y1="17.8" y2="19.2" y3="19.28"/>
            <line x="22.6" y="19.32"/>
            <line x="27.03" y="19.26"/>
            <line x="28.59" y="21.98"/>
            <line x="32.34" y="21.63"/>
            <line x="33.78" y="23.1"/>
            <line x="35.09" y="23.39"/>
            <line x="38.51" y="21.34"/>
            <close/>
            <move x="9.08" y="26.67"/>
            <curve x1="10.49" x2="11.6" x3="11.59" y1="26.66" y2="25.5" y3="24.12"/>
            <curve x1="11.56" x2="10.43" x3="9.03" y1="22.74" y2="21.64" y3="21.64"/>
            <curve x1="9.03" x2="9.02" x3="9.01" y1="21.64" y2="21.64" y3="21.64"/>
            <curve x1="7.62" x2="6.5" x3="6.52" y1="21.66" y2="22.8" y3="24.2"/>
            <curve x1="6.53" x2="7.66" x3="9.04" y1="25.56" y2="26.67" y3="26.67"/>
            <curve x1="9.06" x2="9.07" x3="9.08" y1="26.67" y2="26.67" y3="26.67"/>
        </path>
        <fillstroke/>
        <fillcolor color="#769B3F"/>
        <path>
            <move x="14.48" y="12.54"/>
            <line x="10.99" y="9.28"/>
            <line x="12.57" y="6.34"/>
            <line x="30.71" y="6.09"/>
            <curve x1="30.75" x2="30.94" x3="31.27" y1="5.96" y2="5.53" y3="4.94"/>
            <curve x1="31.53" x2="31.86" x3="32.26" y1="4.42" y2="3.91" y3="3.46"/>
            <curve x1="32.62" x2="33.06" x3="33.56" y1="3" y2="2.56" y3="2.17"/>
            <curve x1="35.35" x2="37.51" x3="39.66" y1="0.71" y2="0" y3="0"/>
            <curve x1="42.11" x2="44.53" x3="46.38" y1="0" y2="0.92" y3="2.69"/>
            <curve x1="46.65" x2="46.9" x3="47.14" y1="2.94" y2="3.22" y3="3.51"/>
            <curve x1="50.53" x2="49.92" x3="45.79" y1="7.61" y2="13.65" y3="17"/>
            <curve x1="44" x2="41.83" x3="39.68" y1="18.46" y2="19.17" y3="19.17"/>
            <curve x1="37.52" x2="35.37" x3="33.62" y1="19.17" y2="18.45" y3="17.06"/>
            <curve x1="32.31" x2="31.46" x3="31.41" y1="16.09" y2="14.68" y3="14.61"/>
            <line x="30.4" y="14.56"/>
            <line x="25.97" y="14.62"/>
            <line x="24.41" y="11.91"/>
            <line x="20.66" y="12.26"/>
            <line x="19.22" y="10.78"/>
            <line x="17.91" y="10.49"/>
            <line x="14.48" y="12.54"/>
            <close/>
            <move x="43.91" y="7.22"/>
            <curve x1="42.51" x2="41.4" x3="41.41" y1="7.23" y2="8.38" y3="9.76"/>
            <curve x1="41.44" x2="42.57" x3="43.96" y1="11.15" y2="12.25" y3="12.25"/>
            <curve x1="43.97" x2="43.98" x3="43.99" y1="12.25" y2="12.25" y3="12.24"/>
            <curve x1="45.38" x2="46.5" x3="46.48" y1="12.22" y2="11.08" y3="9.69"/>
            <curve x1="46.47" x2="45.34" x3="43.96" y1="8.32" y2="7.21" y3="7.21"/>
            <curve x1="43.94" x2="43.93" x3="43.91" y1="7.22" y2="7.22" y3="7.22"/>
        </path>
        <fillstroke/>
        <strokecolor color="#444444"/>
        <fillcolor color="#FFFFFF"/>
        <strokewidth width="3"/>
        <miterlimit limit="10"/>
        <ellipse h="26.04" w="26.04" x="35.41" y="23.35"/>
        <fillstroke/>
        <path>
            <move x="49.5" y="28.2"/>
            <line x="49.43" y="39.57"/>
        </path>
        <fillstroke/>
        <path>
            <move x="43" y="38.44"/>
            <line x="50" y="38.44"/>
        </path>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="31" name="IAM STS" strokewidth="inherit" w="54">
    <connections>
        <constraint name="N1" perimeter="0" x="0.25" y="0"/>
        <constraint name="N" perimeter="0" x="0.5" y="0"/>
        <constraint name="N2" perimeter="0" x="0.75" y="0"/>
        <constraint name="S1" perimeter="0" x="0.25" y="1"/>
        <constraint name="S" perimeter="0" x="0.5" y="1"/>
        <constraint name="S2" perimeter="0" x="0.75" y="1"/>
        <constraint name="W1" perimeter="0" x="0" y="0.25"/>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="W2" perimeter="0" x="0" y="0.75"/>
        <constraint name="E1" perimeter="0" x="1" y="0.25"/>
        <constraint name="E" perimeter="0" x="1" y="0.5"/>
        <constraint name="E2" perimeter="0" x="1" y="0.75"/>
        <constraint name="NW" perimeter="0" x="0" y="0"/>
        <constraint name="NE" perimeter="0" x="1" y="0"/>
        <constraint name="SW" perimeter="0" x="0" y="1"/>
        <constraint name="SE" perimeter="0" x="1" y="1"/>
    </connections>
    <foreground>
        <fillcolor color="#3C492A"/>
        <path>
            <move x="11.5" y="12.01"/>
            <curve x1="11.49" x2="11.48" x3="11.48" y1="12.01" y2="13.01" y3="13.01"/>
            <curve x1="9.92" x2="8.65" x3="8.68" y1="13.04" y2="13.49" y3="15.05"/>
            <curve x1="8.69" x2="9.95" x3="11.51" y1="16.59" y2="18" y3="18"/>
            <curve x1="11.52" x2="11.54" x3="11.56" y1="18" y2="18" y3="18"/>
            <curve x1="13.13" x2="14.38" x3="14.37" y1="18" y2="16.52" y3="14.97"/>
            <curve x1="14.34" x2="13.06" x3="11.5" y1="13.42" y2="12.01" y3="12.01"/>
            <close/>
        </path>
        <fillstroke/>
        <path>
            <move x="0" y="3"/>
            <line x="0" y="31"/>
            <line x="54" y="31"/>
            <line x="54" y="3"/>
            <line x="0" y="3"/>
            <close/>
            <move x="46.75" y="20.64"/>
            <line x="26.38" y="20.93"/>
            <curve x1="26.34" x2="26.12" x3="25.76" y1="21.06" y2="21.56" y3="22.22"/>
            <curve x1="25.46" x2="25.09" x3="24.64" y1="22.8" y2="23.37" y3="23.88"/>
            <curve x1="24.24" x2="23.75" x3="23.18" y1="24.39" y2="24.89" y3="25.33"/>
            <curve x1="21.17" x2="18.75" x3="16.33" y1="26.96" y2="27.76" y3="27.76"/>
            <curve x1="13.59" x2="10.86" x3="8.79" y1="27.76" y2="26.73" y3="24.74"/>
            <curve x1="8.49" x2="8.2" x3="7.93" y1="24.46" y2="24.15" y3="23.82"/>
            <curve x1="4.13" x2="4.81" x3="9.45" y1="19.21" y2="12.44" y3="8.68"/>
            <curve x1="11.46" x2="13.89" x3="16.31" y1="7.04" y2="6.24" y3="6.24"/>
            <curve x1="18.74" x2="21.15" x3="23.11" y1="6.24" y2="7.04" y3="8.61"/>
            <curve x1="24.59" x2="25.55" x3="25.59" y1="9.7" y2="11.27" y3="11.36"/>
            <line x="26.73" y="11.41"/>
            <line x="31.7" y="11.34"/>
            <line x="33.46" y="14.39"/>
            <line x="37.67" y="14"/>
            <line x="39.28" y="15.66"/>
            <line x="40.76" y="15.98"/>
            <line x="44.6" y="13.68"/>
            <line x="48.53" y="17.34"/>
            <line x="46.75" y="20.64"/>
            <close/>
        </path>
        <fillstroke/>
        <rect h="1" w="5.69" x="8.68" y="14.5"/>
        <fillstroke/>
        <fillcolor color="#769B3F"/>
        <path>
            <move x="11.5" y="11.01"/>
            <curve x1="11.49" x2="11.48" x3="11.48" y1="11.01" y2="11.01" y3="11.01"/>
            <curve x1="9.92" x2="8.65" x3="8.68" y1="11.04" y2="12.49" y3="14.05"/>
            <curve x1="8.69" x2="9.95" x3="11.51" y1="15.59" y2="17" y3="17"/>
            <curve x1="11.52" x2="11.54" x3="11.56" y1="17" y2="17" y3="17"/>
            <curve x1="13.13" x2="14.38" x3="14.37" y1="17" y2="15.52" y3="13.97"/>
            <curve x1="14.34" x2="13.06" x3="11.5" y1="12.42" y2="11.01" y3="11.01"/>
            <close/>
        </path>
        <fillstroke/>
        <path>
            <move x="0" y="0"/>
            <line x="0" y="28"/>
            <line x="54" y="28"/>
            <line x="54" y="0"/>
            <line x="0" y="0"/>
            <close/>
            <move x="46.75" y="17.64"/>
            <line x="26.38" y="17.93"/>
            <curve x1="26.34" x2="26.12" x3="25.76" y1="18.06" y2="18.56" y3="19.22"/>
            <curve x1="25.46" x2="25.09" x3="24.64" y1="19.8" y2="20.37" y3="20.88"/>
            <curve x1="24.24" x2="23.75" x3="23.18" y1="21.39" y2="21.89" y3="22.33"/>
            <curve x1="21.17" x2="18.75" x3="16.33" y1="23.96" y2="24.76" y3="24.76"/>
            <curve x1="13.59" x2="10.86" x3="8.79" y1="24.76" y2="23.73" y3="21.74"/>
            <curve x1="8.49" x2="8.2" x3="7.93" y1="21.46" y2="21.15" y3="20.82"/>
            <curve x1="4.13" x2="4.81" x3="9.45" y1="16.21" y2="9.44" y3="5.68"/>
            <curve x1="11.46" x2="13.89" x3="16.31" y1="4.04" y2="3.24" y3="3.24"/>
            <curve x1="18.74" x2="21.15" x3="23.11" y1="3.24" y2="4.04" y3="5.61"/>
            <curve x1="24.59" x2="25.55" x3="25.59" y1="6.7" y2="8.27" y3="8.36"/>
            <line x="26.73" y="8.41"/>
            <line x="31.7" y="8.34"/>
            <line x="33.46" y="11.39"/>
            <line x="37.67" y="11"/>
            <line x="39.28" y="12.66"/>
            <line x="40.76" y="12.98"/>
            <line x="44.6" y="10.68"/>
            <line x="48.53" y="14.34"/>
            <line x="46.75" y="17.64"/>
            <close/>
        </path>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="72" name="OpsWorks" strokewidth="inherit" w="59.71">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0"/>
        <constraint name="S" perimeter="0" x="0.5" y="1"/>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="E" perimeter="0" x="1" y="0.5"/>
        <constraint name="NW" perimeter="0" x="0" y="0.21"/>
        <constraint name="NE" perimeter="0" x="1" y="0.21"/>
        <constraint name="SW" perimeter="0" x="0" y="0.79"/>
        <constraint name="SE" perimeter="0" x="1" y="0.79"/>
    </connections>
    <foreground>
        <fillcolor color="#4B612C"/>
        <path>
            <move x="59.71" y="53.62"/>
            <line x="59.71" y="36.04"/>
            <line x="41.63" y="36.04"/>
            <line x="41.63" y="58.67"/>
            <line x="51.35" y="61.29"/>
            <close/>
        </path>
        <fillstroke/>
        <path>
            <move x="29.86" y="50.68"/>
            <line x="51.21" y="55.02"/>
            <line x="51.21" y="10.7"/>
            <line x="29.86" y="16.49"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#759C3E"/>
        <path>
            <move x="29.86" y="36.04"/>
            <line x="29.86" y="0"/>
            <line x="45.6" y="7.89"/>
            <close/>
        </path>
        <fillstroke/>
        <path>
            <move x="59.71" y="36.04"/>
            <line x="59.71" y="57.15"/>
            <line x="51.33" y="61.34"/>
            <close/>
        </path>
        <fillstroke/>
        <path>
            <move x="59.71" y="23.5"/>
            <line x="59.71" y="14.88"/>
            <line x="51.21" y="10.7"/>
            <line x="29.86" y="50.68"/>
            <line x="29.86" y="72"/>
            <line x="45.65" y="64.1"/>
            <close/>
        </path>
        <fillstroke/>
        <path>
            <move x="0" y="53.62"/>
            <line x="0" y="36.04"/>
            <line x="18.08" y="36.04"/>
            <line x="18.08" y="58.67"/>
            <line x="8.37" y="61.29"/>
            <close/>
        </path>
        <fillstroke/>
        <path>
            <move x="29.86" y="50.68"/>
            <line x="8.5" y="55.02"/>
            <line x="8.5" y="10.7"/>
            <line x="29.86" y="16.49"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#4B612C"/>
        <path>
            <move x="29.86" y="36.04"/>
            <line x="29.86" y="0"/>
            <line x="14.11" y="7.89"/>
            <close/>
        </path>
        <fillstroke/>
        <path>
            <move x="0" y="36.04"/>
            <line x="0" y="57.15"/>
            <line x="8.38" y="61.34"/>
            <close/>
        </path>
        <fillstroke/>
        <path>
            <move x="0" y="23.5"/>
            <line x="0" y="14.88"/>
            <line x="8.5" y="10.7"/>
            <line x="29.86" y="50.68"/>
            <line x="29.86" y="72"/>
            <line x="14.06" y="64.1"/>
            <close/>
        </path>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="72" name="OpsWorks Apps" strokewidth="inherit" w="72">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0"/>
        <constraint name="S" perimeter="0" x="0.5" y="1"/>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="E" perimeter="0" x="1" y="0.5"/>
        <constraint name="NW" perimeter="0" x="0" y="0"/>
        <constraint name="NE" perimeter="0" x="1" y="0"/>
        <constraint name="SW" perimeter="0" x="0" y="1"/>
        <constraint name="SE" perimeter="0" x="1" y="1"/>
    </connections>
    <foreground>
        <fillcolor color="#4D622C"/>
        <rect h="22.5" w="72" x="0" y="0"/>
        <fillstroke/>
        <fillcolor color="#769B3F"/>
        <rect h="45.5" w="72" x="0" y="22.5"/>
        <fillstroke/>
        <fillcolor color="#4D622C"/>
        <rect h="4" w="72" x="0" y="68"/>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="68" name="OpsWorks Deployments" strokewidth="inherit" w="72">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0"/>
        <constraint name="S" perimeter="0" x="0.5" y="1"/>
        <constraint name="W" perimeter="0" x="0" y="0.65"/>
        <constraint name="E" perimeter="0" x="1" y="0.65"/>
        <constraint name="NW" perimeter="0" x="0.155" y="0.44"/>
        <constraint name="NE" perimeter="0" x="0.845" y="0.44"/>
        <constraint name="SW" perimeter="0" x="0.22" y="1"/>
        <constraint name="SE" perimeter="0" x="0.78" y="1"/>
    </connections>
    <foreground>
        <fillcolor color="#4D622C"/>
        <path>
            <move x="0" y="48"/>
            <line x="16" y="68"/>
            <line x="32" y="48"/>
            <close/>
        </path>
        <fillstroke/>
        <rect h="4" w="32" x="0" y="44"/>
        <fillstroke/>
        <path>
            <move x="40" y="48"/>
            <line x="56" y="68"/>
            <line x="72" y="48"/>
            <close/>
        </path>
        <fillstroke/>
        <path>
            <move x="60" y="34"/>
            <line x="40" y="14"/>
            <line x="42" y="4"/>
            <line x="30" y="4"/>
            <line x="32" y="14"/>
            <line x="12" y="34"/>
            <line x="12" y="48"/>
            <line x="20" y="48"/>
            <line x="20" y="38"/>
            <line x="36" y="22"/>
            <line x="36" y="22"/>
            <line x="52" y="38"/>
            <line x="52" y="48"/>
            <line x="60" y="48"/>
            <close/>
        </path>
        <fillstroke/>
        <rect h="4" w="32" x="40" y="44"/>
        <fillstroke/>
        <fillcolor color="#769B3F"/>
        <path>
            <move x="0" y="44"/>
            <line x="16" y="64"/>
            <line x="32" y="44"/>
            <close/>
        </path>
        <fillstroke/>
        <path>
            <move x="40" y="44"/>
            <line x="56" y="64"/>
            <line x="72" y="44"/>
            <close/>
        </path>
        <fillstroke/>
        <path>
            <move x="61" y="30"/>
            <line x="42" y="11"/>
            <line x="42" y="0"/>
            <line x="30" y="0"/>
            <line x="30" y="11"/>
            <line x="11" y="30"/>
            <line x="11" y="44"/>
            <line x="21" y="44"/>
            <line x="21" y="34"/>
            <line x="36" y="18"/>
            <line x="36" y="18"/>
            <line x="51" y="34"/>
            <line x="51" y="44"/>
            <line x="61" y="44"/>
            <close/>
        </path>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="72" name="OpsWorks Instances" strokewidth="inherit" w="72">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0"/>
        <constraint name="S" perimeter="0" x="0.5" y="1"/>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="E" perimeter="0" x="1" y="0.5"/>
        <constraint name="NW" perimeter="0" x="0" y="0"/>
        <constraint name="NE" perimeter="0" x="1" y="0"/>
        <constraint name="SW" perimeter="0" x="0" y="1"/>
        <constraint name="SE" perimeter="0" x="1" y="1"/>
    </connections>
    <foreground>
        <fillcolor color="#769B3F"/>
        <rect h="14" w="18" x="0" y="0"/>
        <fillstroke/>
        <rect h="14" w="18" x="27" y="0"/>
        <fillstroke/>
        <rect h="14" w="18" x="54" y="0"/>
        <fillstroke/>
        <rect h="14" w="18" x="0" y="27"/>
        <fillstroke/>
        <rect h="14" w="18" x="27" y="27"/>
        <fillstroke/>
        <rect h="14" w="18" x="54" y="27"/>
        <fillstroke/>
        <rect h="14" w="18" x="0" y="54"/>
        <fillstroke/>
        <rect h="14" w="18" x="27" y="54"/>
        <fillstroke/>
        <rect h="14" w="18" x="54" y="54"/>
        <fillstroke/>
        <fillcolor color="#4D622C"/>
        <rect h="4" w="18" x="0" y="14"/>
        <fillstroke/>
        <rect h="4" w="18" x="27" y="14"/>
        <fillstroke/>
        <rect h="4" w="18" x="54" y="14"/>
        <fillstroke/>
        <rect h="4" w="18" x="0" y="41"/>
        <fillstroke/>
        <rect h="4" w="18" x="27" y="41"/>
        <fillstroke/>
        <rect h="4" w="18" x="54" y="41"/>
        <fillstroke/>
        <rect h="4" w="18" x="0" y="68"/>
        <fillstroke/>
        <rect h="4" w="18" x="27" y="68"/>
        <fillstroke/>
        <rect h="4" w="18" x="54" y="68"/>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="72" name="OpsWorks Layers" strokewidth="inherit" w="72">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0"/>
        <constraint name="S" perimeter="0" x="0.5" y="1"/>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="E" perimeter="0" x="1" y="0.5"/>
        <constraint name="NW" perimeter="0" x="0" y="0"/>
        <constraint name="NE" perimeter="0" x="1" y="0"/>
        <constraint name="SW" perimeter="0" x="0" y="1"/>
        <constraint name="SE" perimeter="0" x="1" y="1"/>
    </connections>
    <foreground>
        <fillcolor color="#769B3F"/>
        <rect h="23" w="72" x="0" y="0"/>
        <fillstroke/>
        <rect h="23" w="72" x="0" y="45"/>
        <fillstroke/>
        <fillcolor color="#4D622C"/>
        <rect h="4" w="72" x="0" y="23"/>
        <fillstroke/>
        <rect h="4" w="72" x="0" y="68"/>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="60.25" name="OpsWorks Monitoring" strokewidth="inherit" w="72">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0.4"/>
        <constraint name="S" perimeter="0" x="0.5" y="1"/>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="E" perimeter="0" x="1" y="0.5"/>
        <constraint name="NW" perimeter="0" x="0.22" y="0.15"/>
        <constraint name="NE" perimeter="0" x="0.72" y="0"/>
        <constraint name="SW" perimeter="0" x="0" y="1"/>
        <constraint name="SE" perimeter="0" x="1" y="1"/>
    </connections>
    <foreground>
        <fillcolor color="#769B3F"/>
        <path>
            <move x="72" y="18"/>
            <line x="51.75" y="0"/>
            <line x="33.75" y="27"/>
            <line x="15.75" y="9"/>
            <line x="0" y="27"/>
            <line x="0" y="56.25"/>
            <line x="72" y="56.25"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#4D622C"/>
        <rect h="4" w="72" x="0" y="56.25"/>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="72" name="OpsWorks Permissions" strokewidth="inherit" w="59.5">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0"/>
        <constraint name="S" perimeter="0" x="0.5" y="1"/>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="E" perimeter="0" x="1" y="0.5"/>
        <constraint name="SW" perimeter="0" x="0" y="1"/>
        <constraint name="SE" perimeter="0" x="1" y="1"/>
    </connections>
    <foreground>
        <fillcolor color="#4D622C"/>
        <path>
            <move x="51" y="33.75"/>
            <line x="51" y="16.75"/>
            <curve x1="51" x2="45.29" x3="38.25" y1="9.71" y2="4" y3="4"/>
            <line x="21.25" y="4"/>
            <curve x1="14.21" x2="8.5" x3="8.5" y1="4" y2="9.71" y3="16.75"/>
            <line x="8.5" y="33.75"/>
            <line x="0" y="33.75"/>
            <line x="0" y="72"/>
            <line x="59.5" y="72"/>
            <line x="59.5" y="33.75"/>
            <line x="51" y="33.75"/>
            <close/>
            <move x="42.5" y="33.75"/>
            <line x="17" y="33.75"/>
            <line x="17" y="20.15"/>
            <curve x1="17" x2="20.43" x3="24.65" y1="15.93" y2="12.5" y3="12.5"/>
            <line x="34.85" y="12.5"/>
            <curve x1="39.07" x2="42.5" x3="42.5" y1="12.5" y2="15.93" y3="20.15"/>
            <line x="42.5" y="33.75"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#769B3F"/>
        <path>
            <move x="51" y="29.75"/>
            <line x="51" y="12.75"/>
            <curve x1="51" x2="45.29" x3="38.25" y1="5.71" y2="0" y3="0"/>
            <line x="21.25" y="0"/>
            <curve x1="14.21" x2="8.5" x3="8.5" y1="0" y2="5.71" y3="12.75"/>
            <line x="8.5" y="29.75"/>
            <line x="0" y="29.75"/>
            <line x="0" y="68"/>
            <line x="59.5" y="68"/>
            <line x="59.5" y="29.75"/>
            <line x="51" y="29.75"/>
            <close/>
            <move x="42.5" y="29.75"/>
            <line x="17" y="29.75"/>
            <line x="17" y="16.15"/>
            <curve x1="17" x2="20.43" x3="24.65" y1="11.93" y2="8.5" y3="8.5"/>
            <line x="34.85" y="8.5"/>
            <curve x1="39.07" x2="42.5" x3="42.5" y1="8.5" y2="11.93" y3="16.15"/>
            <line x="42.5" y="29.75"/>
            <close/>
        </path>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="70.86" name="OpsWorks Resources" strokewidth="inherit" w="60.44">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0"/>
        <constraint name="S" perimeter="0" x="0.5" y="1"/>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="E" perimeter="0" x="1" y="0.5"/>
        <constraint name="NW" perimeter="0" x="0" y="0.25"/>
        <constraint name="NE" perimeter="0" x="1" y="0.25"/>
        <constraint name="SW" perimeter="0" x="0" y="0.77"/>
        <constraint name="SE" perimeter="0" x="1" y="0.77"/>
    </connections>
    <foreground>
        <fillcolor color="#4D622C"/>
        <path>
            <move x="28.41" y="34.83"/>
            <line x="1.66" y="20.28"/>
            <line x="0" y="21.22"/>
            <line x="0" y="54.96"/>
            <line x="28.41" y="70.86"/>
            <close/>
        </path>
        <fillstroke/>
        <path>
            <move x="58.81" y="20.28"/>
            <line x="32.5" y="34.81"/>
            <line x="32.5" y="70.81"/>
            <line x="60.44" y="54.65"/>
            <line x="60.44" y="21.22"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#769B3F"/>
        <path>
            <move x="28.41" y="34.83"/>
            <line x="0" y="19.37"/>
            <line x="0" y="50.96"/>
            <line x="28.41" y="66.86"/>
            <close/>
        </path>
        <fillstroke/>
        <path>
            <move x="32.5" y="34.81"/>
            <line x="32.5" y="66.81"/>
            <line x="60.44" y="50.65"/>
            <line x="60.44" y="19.38"/>
            <close/>
        </path>
        <fillstroke/>
        <path>
            <move x="30.44" y="31.63"/>
            <line x="58.53" y="16.12"/>
            <line x="30.32" y="0"/>
            <line x="1.93" y="16.12"/>
            <close/>
        </path>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="72" name="OpsWorks Stack" strokewidth="inherit" w="70">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0"/>
        <constraint name="S" perimeter="0" x="0.5" y="1"/>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="E" perimeter="0" x="1" y="0.5"/>
        <constraint name="NW" perimeter="0" x="0" y="0"/>
        <constraint name="NE" perimeter="0" x="1" y="0"/>
        <constraint name="SW" perimeter="0" x="0" y="1"/>
        <constraint name="SE" perimeter="0" x="1" y="1"/>
    </connections>
    <foreground>
        <fillcolor color="#769B3F"/>
        <rect h="68" w="70" x="0" y="0"/>
        <fillstroke/>
        <fillcolor color="#4D622C"/>
        <rect h="4" w="70" x="0" y="68"/>
        <fillstroke/>
    </foreground>
</shape>
</shapes>