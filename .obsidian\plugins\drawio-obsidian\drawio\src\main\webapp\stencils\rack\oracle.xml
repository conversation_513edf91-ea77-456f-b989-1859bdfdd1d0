<shapes name="mxgraph.rack.Oracle">
<shape aspect="variable" h="121" name="Netra Blade X3-2B Server" strokewidth="inherit" w="19.5">
    <connections/>
    <background>
        <path>
            <move x="0" y="0"/>
            <line x="0" y="121"/>
            <line x="19.5" y="121"/>
            <line x="19.5" y="0"/>
            <close/>
        </path>
    </background>
    <foreground>
        <fill/>
        <fillcolor color="#000000"/>
        <strokewidth width="1"/>
        <linejoin join="miter"/>
        <linecap cap="butt"/>
        <miterlimit limit="4"/>
        <dashpattern pattern="none"/>
        <dashed dashed="1"/>
        <alpha alpha="1"/>
        <strokealpha alpha="1"/>
        <fillalpha alpha="0.644"/>
        <fontfamily family="sans-serif"/>
        <fontstyle style="0"/>
        <path>
            <move x="0" y="0"/>
            <line x="0" y="121"/>
            <line x="19.5" y="121"/>
            <line x="19.5" y="0"/>
            <close/>
            <move x="1" y="1"/>
            <line x="18.5" y="1"/>
            <line x="18.5" y="120"/>
            <line x="1" y="120"/>
            <close/>
            <move x="3" y="2"/>
            <line x="3" y="30"/>
            <line x="9" y="30"/>
            <line x="9" y="2"/>
            <close/>
            <move x="11" y="2"/>
            <line x="11" y="30"/>
            <line x="17" y="30"/>
            <line x="17" y="2"/>
            <close/>
            <move x="4" y="3"/>
            <line x="8" y="3"/>
            <line x="8" y="29"/>
            <line x="4" y="29"/>
            <close/>
            <move x="12" y="3"/>
            <line x="16" y="3"/>
            <line x="16" y="29"/>
            <line x="12" y="29"/>
            <close/>
            <move x="3" y="91"/>
            <line x="3" y="119"/>
            <line x="9" y="119"/>
            <line x="9" y="91"/>
            <close/>
            <move x="11" y="91"/>
            <line x="11" y="119"/>
            <line x="17" y="119"/>
            <line x="17" y="91"/>
            <close/>
            <move x="4" y="92"/>
            <line x="8" y="92"/>
            <line x="8" y="118"/>
            <line x="4" y="118"/>
            <close/>
            <move x="12" y="92"/>
            <line x="16" y="92"/>
            <line x="16" y="118"/>
            <line x="12" y="118"/>
            <close/>
        </path>
        <fill/>
        <strokewidth width="0.301"/>
        <fillalpha alpha="0.232"/>
        <path>
            <move x="5.5" y="61.5"/>
            <curve x1="3.84" x2="2.5" x3="2.5" y1="61.5" y2="62.84" y3="64.5"/>
            <line x="2.5" y="85.5"/>
            <curve x1="2.5" x2="3.84" x3="5.5" y1="87.16" y2="88.5" y3="88.5"/>
            <line x="14" y="88.5"/>
            <curve x1="15.66" x2="17" x3="17" y1="88.5" y2="87.16" y3="85.5"/>
            <line x="17" y="64.5"/>
            <curve x1="17" x2="15.66" x3="14" y1="62.84" y2="61.5" y3="61.5"/>
            <close/>
            <move x="5.5" y="32.5"/>
            <curve x1="3.84" x2="2.5" x3="2.5" y1="32.5" y2="33.84" y3="35.5"/>
            <line x="2.5" y="56.5"/>
            <curve x1="2.5" x2="3.84" x3="5.5" y1="58.16" y2="59.5" y3="59.5"/>
            <line x="14" y="59.5"/>
            <curve x1="15.66" x2="17" x3="17" y1="59.5" y2="58.16" y3="56.5"/>
            <line x="17" y="35.5"/>
            <curve x1="17" x2="15.66" x3="14" y1="33.84" y2="32.5" y3="32.5"/>
            <close/>
        </path>
        <fill/>
    </foreground>
</shape>
<shape aspect="variable" h="114" name="Netra CP3260 ATCA Blade Server" strokewidth="inherit" w="11.15">
    <connections/>
    <background>
        <path>
            <move x="0" y="0"/>
            <line x="0" y="114"/>
            <line x="11.15" y="114"/>
            <line x="11.15" y="0"/>
            <close/>
        </path>
    </background>
    <foreground>
        <fill/>
        <fillcolor color="#000000"/>
        <strokewidth width="1"/>
        <linejoin join="miter"/>
        <linecap cap="butt"/>
        <miterlimit limit="4"/>
        <dashpattern pattern="none"/>
        <dashed dashed="1"/>
        <alpha alpha="1"/>
        <strokealpha alpha="1"/>
        <fillalpha alpha="0.644"/>
        <fontfamily family="sans-serif"/>
        <fontstyle style="0"/>
        <path>
            <move x="0" y="0"/>
            <line x="0" y="114"/>
            <line x="11.15" y="114"/>
            <line x="11.15" y="0"/>
            <close/>
            <move x="1" y="1"/>
            <line x="10.15" y="1"/>
            <line x="10.15" y="113"/>
            <line x="1" y="113"/>
            <close/>
            <move x="2.5" y="65"/>
            <line x="2.5" y="71"/>
            <line x="8.5" y="71"/>
            <line x="8.5" y="65"/>
            <close/>
            <move x="3.5" y="66"/>
            <line x="7.5" y="66"/>
            <line x="7.5" y="70"/>
            <line x="3.5" y="70"/>
            <close/>
            <move x="2.5" y="75"/>
            <line x="2.5" y="81"/>
            <line x="8.5" y="81"/>
            <line x="8.5" y="75"/>
            <close/>
            <move x="3.5" y="76"/>
            <line x="7.5" y="76"/>
            <line x="7.5" y="80"/>
            <line x="3.5" y="80"/>
            <close/>
            <move x="2.5" y="85"/>
            <line x="2.5" y="91"/>
            <line x="8.5" y="91"/>
            <line x="8.5" y="85"/>
            <close/>
            <move x="3.5" y="86"/>
            <line x="7.5" y="86"/>
            <line x="7.5" y="90"/>
            <line x="3.5" y="90"/>
            <close/>
        </path>
        <fill/>
    </foreground>
</shape>
<shape aspect="variable" h="114" name="Netra CP3270 ATCA Blade Server" strokewidth="inherit" w="11.15">
    <connections/>
    <background>
        <path>
            <move x="0" y="0"/>
            <line x="0" y="114"/>
            <line x="11.15" y="114"/>
            <line x="11.15" y="0"/>
            <close/>
        </path>
    </background>
    <foreground>
        <fill/>
        <fillcolor color="#000000"/>
        <strokewidth width="1"/>
        <linejoin join="miter"/>
        <linecap cap="butt"/>
        <miterlimit limit="4"/>
        <dashpattern pattern="none"/>
        <dashed dashed="1"/>
        <alpha alpha="1"/>
        <strokealpha alpha="1"/>
        <fillalpha alpha="0.644"/>
        <fontfamily family="sans-serif"/>
        <fontstyle style="0"/>
        <path>
            <move x="0" y="0"/>
            <line x="0" y="114"/>
            <line x="11.15" y="114"/>
            <line x="11.15" y="0"/>
            <close/>
            <move x="1" y="1"/>
            <line x="10.15" y="1"/>
            <line x="10.15" y="113"/>
            <line x="1" y="113"/>
            <line x="1" y="30"/>
            <line x="8" y="30"/>
            <line x="8" y="4"/>
            <line x="1" y="4"/>
            <close/>
            <move x="1" y="5"/>
            <line x="7" y="5"/>
            <line x="7" y="29"/>
            <line x="1" y="29"/>
            <close/>
            <move x="2.5" y="35"/>
            <line x="2.5" y="41"/>
            <line x="8.5" y="41"/>
            <line x="8.5" y="35"/>
            <close/>
            <move x="3.5" y="36"/>
            <line x="7.5" y="36"/>
            <line x="7.5" y="40"/>
            <line x="3.5" y="40"/>
            <close/>
            <move x="2.5" y="45"/>
            <line x="2.5" y="51"/>
            <line x="8.5" y="51"/>
            <line x="8.5" y="45"/>
            <close/>
            <move x="3.5" y="46"/>
            <line x="7.5" y="46"/>
            <line x="7.5" y="50"/>
            <line x="3.5" y="50"/>
            <close/>
            <move x="2.5" y="55"/>
            <line x="2.5" y="61"/>
            <line x="8.5" y="61"/>
            <line x="8.5" y="55"/>
            <close/>
            <move x="3.5" y="56"/>
            <line x="7.5" y="56"/>
            <line x="7.5" y="60"/>
            <line x="3.5" y="60"/>
            <close/>
        </path>
        <fill/>
    </foreground>
</shape>
<shape aspect="variable" h="178.6" name="Netra CT900 ATCA Blade Server" strokewidth="inherit" w="161.9">
    <connections/>
    <background>
        <save/>
        <path>
            <move x="0" y="0"/>
            <line x="0" y="178.6"/>
            <line x="161.9" y="178.6"/>
            <line x="161.9" y="0"/>
            <close/>
        </path>
    </background>
    <foreground>
        <fill/>
        <fillcolor color="#000000"/>
        <strokewidth width="1"/>
        <linejoin join="miter"/>
        <linecap cap="butt"/>
        <miterlimit limit="4"/>
        <dashpattern pattern="none"/>
        <dashed dashed="1"/>
        <alpha alpha="1"/>
        <strokealpha alpha="1"/>
        <fillalpha alpha="0.644"/>
        <fontfamily family="sans-serif"/>
        <fontstyle style="0"/>
        <path>
            <move x="0" y="0"/>
            <line x="0" y="178.6"/>
            <line x="161.9" y="178.6"/>
            <line x="161.9" y="0"/>
            <close/>
            <move x="1" y="1"/>
            <line x="6" y="1"/>
            <line x="6" y="177.6"/>
            <line x="1" y="177.6"/>
            <close/>
            <move x="7" y="1"/>
            <line x="84" y="1"/>
            <line x="84" y="9"/>
            <line x="150" y="9"/>
            <line x="150" y="1"/>
            <line x="155" y="1"/>
            <line x="155" y="12"/>
            <line x="7" y="12"/>
            <close/>
            <move x="85" y="1"/>
            <line x="149" y="1"/>
            <line x="149" y="8"/>
            <line x="85" y="8"/>
            <close/>
            <move x="156" y="1"/>
            <line x="160.9" y="1"/>
            <line x="160.9" y="177.6"/>
            <line x="156" y="177.6"/>
            <close/>
            <move x="7" y="13"/>
            <line x="16.15" y="13"/>
            <line x="16.15" y="125"/>
            <line x="7" y="125"/>
            <close/>
            <move x="17.15" y="13"/>
            <line x="26.3" y="13"/>
            <line x="26.3" y="125"/>
            <line x="17.15" y="125"/>
            <close/>
            <move x="27.3" y="13"/>
            <line x="36.45" y="13"/>
            <line x="36.45" y="125"/>
            <line x="27.3" y="125"/>
            <close/>
            <move x="37.45" y="13"/>
            <line x="46.6" y="13"/>
            <line x="46.6" y="125"/>
            <line x="37.45" y="125"/>
            <close/>
            <move x="47.6" y="13"/>
            <line x="56.75" y="13"/>
            <line x="56.75" y="125"/>
            <line x="47.6" y="125"/>
            <close/>
            <move x="57.75" y="13"/>
            <line x="66.9" y="13"/>
            <line x="66.9" y="125"/>
            <line x="57.75" y="125"/>
            <close/>
            <move x="67.9" y="13"/>
            <line x="77.05" y="13"/>
            <line x="77.05" y="125"/>
            <line x="67.9" y="125"/>
            <close/>
            <move x="78.05" y="13"/>
            <line x="87.2" y="13"/>
            <line x="87.2" y="125"/>
            <line x="78.05" y="125"/>
            <close/>
            <move x="88.2" y="13"/>
            <line x="97.35" y="13"/>
            <line x="97.35" y="125"/>
            <line x="88.2" y="125"/>
            <close/>
            <move x="98.35" y="13"/>
            <line x="107.5" y="13"/>
            <line x="107.5" y="125"/>
            <line x="98.35" y="125"/>
            <close/>
            <move x="108.5" y="13"/>
            <line x="117.65" y="13"/>
            <line x="117.65" y="125"/>
            <line x="108.5" y="125"/>
            <close/>
            <move x="118.65" y="13"/>
            <line x="127.8" y="13"/>
            <line x="127.8" y="125"/>
            <line x="118.65" y="125"/>
            <close/>
            <move x="128.8" y="13"/>
            <line x="137.95" y="13"/>
            <line x="137.95" y="125"/>
            <line x="128.8" y="125"/>
            <close/>
            <move x="138.95" y="13"/>
            <line x="148" y="13"/>
            <line x="148" y="125"/>
            <line x="138.95" y="125"/>
            <close/>
            <move x="149" y="13"/>
            <line x="155" y="13"/>
            <line x="155" y="125"/>
            <line x="149" y="125"/>
            <close/>
            <move x="7" y="126"/>
            <line x="155" y="126"/>
            <line x="155" y="140"/>
            <line x="7" y="140"/>
            <close/>
            <move x="7" y="141"/>
            <line x="155" y="141"/>
            <line x="155" y="177.6"/>
            <line x="7" y="177.6"/>
            <close/>
        </path>
        <fill/>
        <restore/>
        <rect/>
        <stroke/>
        <fillcolor color="#000000"/>
        <fillalpha alpha="0.232"/>
        <path>
            <move x="109.5" y="141.75"/>
            <line x="151.5" y="141.75"/>
            <curve x1="153.16" x2="154.5" x3="154.5" y1="141.75" y2="143.09" y3="144.75"/>
            <line x="154.5" y="173.75"/>
            <curve x1="154.5" x2="153.16" x3="151.5" y1="175.41" y2="176.75" y3="176.75"/>
            <line x="109.5" y="176.75"/>
            <curve x1="107.84" x2="106.5" x3="106.5" y1="176.75" y2="175.41" y3="173.75"/>
            <line x="106.5" y="144.75"/>
            <curve x1="106.5" x2="107.84" x3="109.5" y1="143.09" y2="141.75" y3="141.75"/>
            <close/>
            <move x="60" y="141.75"/>
            <line x="102" y="141.75"/>
            <curve x1="103.66" x2="105" x3="105" y1="141.75" y2="143.09" y3="144.75"/>
            <line x="105" y="173.75"/>
            <curve x1="105" x2="103.66" x3="102" y1="175.41" y2="176.75" y3="176.75"/>
            <line x="60" y="176.75"/>
            <curve x1="58.34" x2="57" x3="57" y1="176.75" y2="175.41" y3="173.75"/>
            <line x="57" y="144.75"/>
            <curve x1="57" x2="58.34" x3="60" y1="143.09" y2="141.75" y3="141.75"/>
            <close/>
            <move x="10.5" y="141.75"/>
            <line x="52.5" y="141.75"/>
            <curve x1="54.16" x2="55.5" x3="55.5" y1="141.75" y2="143.09" y3="144.75"/>
            <line x="55.5" y="173.75"/>
            <curve x1="55.5" x2="54.16" x3="52.5" y1="175.41" y2="176.75" y3="176.75"/>
            <line x="10.5" y="176.75"/>
            <curve x1="8.84" x2="7.5" x3="7.5" y1="176.75" y2="175.41" y3="173.75"/>
            <line x="7.5" y="144.75"/>
            <curve x1="7.5" x2="8.84" x3="10.5" y1="143.09" y2="141.75" y3="141.75"/>
            <close/>
        </path>
        <fill/>
    </foreground>
</shape>
<shape aspect="variable" h="178.6" name="Netra CT9000 Server" strokewidth="inherit" w="161.9">
    <connections/>
    <background>
        <save/>
        <rect h="177.6" w="160.9" x="0.5" y="0.5"/>
    </background>
    <foreground>
        <fill/>
        <fillcolor color="#000000"/>
        <strokewidth width="0.301"/>
        <linejoin join="miter"/>
        <linecap cap="butt"/>
        <miterlimit limit="4"/>
        <dashpattern pattern="none"/>
        <dashed dashed="1"/>
        <alpha alpha="1"/>
        <strokealpha alpha="1"/>
        <fillalpha alpha="0.644"/>
        <fontfamily family="sans-serif"/>
        <fontstyle style="0"/>
        <path>
            <move x="7" y="141"/>
            <line x="155" y="141"/>
            <line x="155" y="177.6"/>
            <line x="7" y="177.6"/>
            <close/>
            <move x="7" y="126"/>
            <line x="155" y="126"/>
            <line x="155" y="140"/>
            <line x="7" y="140"/>
            <close/>
            <move x="149" y="13"/>
            <line x="155" y="13"/>
            <line x="155" y="125"/>
            <line x="149" y="125"/>
            <close/>
            <move x="138.95" y="13"/>
            <line x="148" y="13"/>
            <line x="148" y="125"/>
            <line x="138.95" y="125"/>
            <close/>
            <move x="128.8" y="13"/>
            <line x="137.95" y="13"/>
            <line x="137.95" y="125"/>
            <line x="128.8" y="125"/>
            <close/>
            <move x="118.65" y="13"/>
            <line x="127.8" y="13"/>
            <line x="127.8" y="125"/>
            <line x="118.65" y="125"/>
            <close/>
            <move x="108.5" y="13"/>
            <line x="117.65" y="13"/>
            <line x="117.65" y="125"/>
            <line x="108.5" y="125"/>
            <close/>
            <move x="98.35" y="13"/>
            <line x="107.5" y="13"/>
            <line x="107.5" y="125"/>
            <line x="98.35" y="125"/>
            <close/>
            <move x="88.2" y="13"/>
            <line x="97.35" y="13"/>
            <line x="97.35" y="125"/>
            <line x="88.2" y="125"/>
            <close/>
            <move x="78.05" y="13"/>
            <line x="87.2" y="13"/>
            <line x="87.2" y="125"/>
            <line x="78.05" y="125"/>
            <close/>
            <move x="67.9" y="13"/>
            <line x="77.05" y="13"/>
            <line x="77.05" y="125"/>
            <line x="67.9" y="125"/>
            <close/>
            <move x="57.75" y="13"/>
            <line x="66.9" y="13"/>
            <line x="66.9" y="125"/>
            <line x="57.75" y="125"/>
            <close/>
            <move x="47.6" y="13"/>
            <line x="56.75" y="13"/>
            <line x="56.75" y="125"/>
            <line x="47.6" y="125"/>
            <close/>
            <move x="37.45" y="13"/>
            <line x="46.6" y="13"/>
            <line x="46.6" y="125"/>
            <line x="37.45" y="125"/>
            <close/>
            <move x="27.3" y="13"/>
            <line x="36.45" y="13"/>
            <line x="36.45" y="125"/>
            <line x="27.3" y="125"/>
            <close/>
            <move x="17.15" y="13"/>
            <line x="26.3" y="13"/>
            <line x="26.3" y="125"/>
            <line x="17.15" y="125"/>
            <close/>
            <move x="7" y="13"/>
            <line x="16.15" y="13"/>
            <line x="16.15" y="125"/>
            <line x="7" y="125"/>
            <close/>
            <move x="156" y="1"/>
            <line x="160.9" y="1"/>
            <line x="160.9" y="177.6"/>
            <line x="156" y="177.6"/>
            <close/>
            <move x="85" y="1"/>
            <line x="149" y="1"/>
            <line x="149" y="8"/>
            <line x="85" y="8"/>
            <close/>
            <move x="7" y="1"/>
            <line x="84" y="1"/>
            <line x="84" y="9"/>
            <line x="150" y="9"/>
            <line x="150" y="1"/>
            <line x="155" y="1"/>
            <line x="155" y="12"/>
            <line x="7" y="12"/>
            <close/>
            <move x="1" y="1"/>
            <line x="6" y="1"/>
            <line x="6" y="177.6"/>
            <line x="1" y="177.6"/>
            <close/>
            <move x="0" y="0"/>
            <line x="0" y="178.6"/>
            <line x="161.9" y="178.6"/>
            <line x="161.9" y="0"/>
            <close/>
        </path>
        <fill/>
        <restore/>
        <rect/>
        <stroke/>
        <fillcolor color="#000000"/>
        <fillalpha alpha="0.232"/>
        <path>
            <move x="109.5" y="141.75"/>
            <line x="151.5" y="141.75"/>
            <curve x1="153.16" x2="154.5" x3="154.5" y1="141.75" y2="143.09" y3="144.75"/>
            <line x="154.5" y="173.75"/>
            <curve x1="154.5" x2="153.16" x3="151.5" y1="175.41" y2="176.75" y3="176.75"/>
            <line x="109.5" y="176.75"/>
            <curve x1="107.84" x2="106.5" x3="106.5" y1="176.75" y2="175.41" y3="173.75"/>
            <line x="106.5" y="144.75"/>
            <curve x1="106.5" x2="107.84" x3="109.5" y1="143.09" y2="141.75" y3="141.75"/>
            <close/>
            <move x="60" y="141.75"/>
            <line x="102" y="141.75"/>
            <curve x1="103.66" x2="105" x3="105" y1="141.75" y2="143.09" y3="144.75"/>
            <line x="105" y="173.75"/>
            <curve x1="105" x2="103.66" x3="102" y1="175.41" y2="176.75" y3="176.75"/>
            <line x="60" y="176.75"/>
            <curve x1="58.34" x2="57" x3="57" y1="176.75" y2="175.41" y3="173.75"/>
            <line x="57" y="144.75"/>
            <curve x1="57" x2="58.34" x3="60" y1="143.09" y2="141.75" y3="141.75"/>
            <close/>
            <move x="10.5" y="141.75"/>
            <line x="52.5" y="141.75"/>
            <curve x1="54.16" x2="55.5" x3="55.5" y1="141.75" y2="143.09" y3="144.75"/>
            <line x="55.5" y="173.75"/>
            <curve x1="55.5" x2="54.16" x3="52.5" y1="175.41" y2="176.75" y3="176.75"/>
            <line x="10.5" y="176.75"/>
            <curve x1="8.84" x2="7.5" x3="7.5" y1="176.75" y2="175.41" y3="173.75"/>
            <line x="7.5" y="144.75"/>
            <curve x1="7.5" x2="8.84" x3="10.5" y1="143.09" y2="141.75" y3="141.75"/>
            <close/>
        </path>
        <fill/>
    </foreground>
</shape>
<shape aspect="variable" h="30.6" name="Netra Server X3-2" strokewidth="inherit" w="161.9">
    <connections/>
    <background>
        <save/>
        <path>
            <move x="0" y="0"/>
            <line x="0" y="0.5"/>
            <line x="0" y="30.6"/>
            <line x="161.9" y="30.6"/>
            <line x="161.9" y="0"/>
            <close/>
        </path>
    </background>
    <foreground>
        <fill/>
        <fillcolor color="#000000"/>
        <strokewidth width="1"/>
        <linejoin join="miter"/>
        <linecap cap="butt"/>
        <miterlimit limit="4"/>
        <dashpattern pattern="none"/>
        <dashed dashed="1"/>
        <alpha alpha="1"/>
        <strokealpha alpha="1"/>
        <fillalpha alpha="0.644"/>
        <fontfamily family="sans-serif"/>
        <fontstyle style="0"/>
        <path>
            <move x="0" y="0"/>
            <line x="0" y="30.6"/>
            <line x="161.9" y="30.6"/>
            <line x="161.9" y="0"/>
            <close/>
            <move x="1" y="1"/>
            <line x="6" y="1"/>
            <line x="6" y="29.6"/>
            <line x="1" y="29.6"/>
            <close/>
            <move x="7" y="1"/>
            <line x="155" y="1"/>
            <line x="155" y="29.6"/>
            <line x="7" y="29.6"/>
            <close/>
            <move x="156" y="1"/>
            <line x="160.9" y="1"/>
            <line x="160.9" y="29.6"/>
            <line x="156" y="29.6"/>
            <close/>
            <move x="95" y="2"/>
            <line x="95" y="8"/>
            <line x="137" y="8"/>
            <line x="137" y="2"/>
            <close/>
            <move x="96" y="3"/>
            <line x="136" y="3"/>
            <line x="136" y="7"/>
            <line x="96" y="7"/>
            <close/>
        </path>
        <fill/>
        <restore/>
        <rect/>
        <stroke/>
        <fillcolor color="#000000"/>
        <strokewidth width="0.301"/>
        <miterlimit limit="4"/>
        <dashpattern pattern="none"/>
        <dashed dashed="1"/>
        <fillalpha alpha="0.232"/>
        <path>
            <move x="8.5" y="5.5"/>
            <curve x1="8.5" x2="9.84" x3="11.5" y1="3.84" y2="2.5" y3="2.5"/>
            <line x="89.5" y="2.5"/>
            <curve x1="91.16" x2="92.5" x3="92.5" y1="2.5" y2="3.84" y3="5.5"/>
            <line x="92.5" y="6.5"/>
            <curve x1="92.5" x2="93.84" x3="95.5" y1="8.16" y2="9.5" y3="9.5"/>
            <line x="150.5" y="9.5"/>
            <curve x1="152.16" x2="153.5" x3="153.5" y1="9.5" y2="10.84" y3="12.5"/>
            <line x="153.5" y="25.5"/>
            <curve x1="153.5" x2="152.16" x3="150.5" y1="27.16" y2="28.5" y3="28.5"/>
            <line x="11.5" y="28.5"/>
            <curve x1="9.84" x2="8.5" x3="8.5" y1="28.5" y2="27.16" y3="25.5"/>
            <close/>
            <move x="30.5" y="24.5"/>
            <line x="57.5" y="24.5"/>
            <line x="57.5" y="18.5"/>
            <line x="30.5" y="18.5"/>
            <close/>
            <move x="109.5" y="24.5"/>
            <line x="136.5" y="24.5"/>
            <line x="136.5" y="18.5"/>
            <line x="109.5" y="18.5"/>
            <close/>
        </path>
        <fill/>
    </foreground>
</shape>
<shape aspect="variable" h="30.6" name="Netra Sparc T4-1 Server" strokewidth="inherit" w="161.9">
    <connections/>
    <background>
        <save/>
        <path>
            <move x="0" y="0"/>
            <line x="0" y="0.5"/>
            <line x="0" y="30.6"/>
            <line x="161.9" y="30.6"/>
            <line x="161.9" y="0"/>
            <close/>
        </path>
    </background>
    <foreground>
        <fill/>
        <fillcolor color="#000000"/>
        <strokewidth width="1"/>
        <linejoin join="miter"/>
        <linecap cap="butt"/>
        <miterlimit limit="4"/>
        <dashpattern pattern="none"/>
        <dashed dashed="1"/>
        <alpha alpha="1"/>
        <strokealpha alpha="1"/>
        <fillalpha alpha="0.644"/>
        <fontfamily family="sans-serif"/>
        <fontstyle style="0"/>
        <path>
            <move x="0" y="0"/>
            <line x="0" y="30.6"/>
            <line x="161.9" y="30.6"/>
            <line x="161.9" y="0"/>
            <close/>
            <move x="1" y="1"/>
            <line x="6" y="1"/>
            <line x="6" y="29.6"/>
            <line x="1" y="29.6"/>
            <close/>
            <move x="7" y="1"/>
            <line x="155" y="1"/>
            <line x="155" y="29.6"/>
            <line x="7" y="29.6"/>
            <close/>
            <move x="156" y="1"/>
            <line x="160.9" y="1"/>
            <line x="160.9" y="29.6"/>
            <line x="156" y="29.6"/>
            <close/>
            <move x="127" y="1.5"/>
            <line x="127" y="29.5"/>
            <line x="133" y="29.5"/>
            <line x="133" y="1.5"/>
            <close/>
            <move x="134" y="1.5"/>
            <line x="134" y="29.5"/>
            <line x="140" y="29.5"/>
            <line x="140" y="1.5"/>
            <close/>
            <move x="141" y="1.5"/>
            <line x="141" y="29.5"/>
            <line x="147" y="29.5"/>
            <line x="147" y="1.5"/>
            <close/>
            <move x="148" y="1.5"/>
            <line x="148" y="29.5"/>
            <line x="154" y="29.5"/>
            <line x="154" y="1.5"/>
            <close/>
            <move x="128" y="2.5"/>
            <line x="132" y="2.5"/>
            <line x="132" y="28.5"/>
            <line x="128" y="28.5"/>
            <close/>
            <move x="135" y="2.5"/>
            <line x="139" y="2.5"/>
            <line x="139" y="28.5"/>
            <line x="135" y="28.5"/>
            <close/>
            <move x="142" y="2.5"/>
            <line x="146" y="2.5"/>
            <line x="146" y="28.5"/>
            <line x="142" y="28.5"/>
            <close/>
            <move x="149" y="2.5"/>
            <line x="153" y="2.5"/>
            <line x="153" y="28.5"/>
            <line x="149" y="28.5"/>
            <close/>
        </path>
        <fill/>
        <restore/>
        <rect/>
        <stroke/>
        <fillcolor color="#000000"/>
        <fillalpha alpha="0.232"/>
        <path>
            <move x="106.5" y="6.5"/>
            <line x="123.5" y="6.5"/>
            <curve x1="125.16" x2="126.5" x3="126.5" y1="6.5" y2="7.84" y3="9.5"/>
            <line x="126.5" y="26.5"/>
            <curve x1="126.5" x2="125.16" x3="123.5" y1="28.16" y2="29.5" y3="29.5"/>
            <line x="106.5" y="29.5"/>
            <curve x1="104.84" x2="103.5" x3="103.5" y1="29.5" y2="28.16" y3="26.5"/>
            <line x="103.5" y="9.5"/>
            <curve x1="103.5" x2="104.84" x3="106.5" y1="7.84" y2="6.5" y3="6.5"/>
            <close/>
            <move x="82.5" y="6.5"/>
            <line x="99.5" y="6.5"/>
            <curve x1="101.16" x2="102.5" x3="102.5" y1="6.5" y2="7.84" y3="9.5"/>
            <line x="102.5" y="26.5"/>
            <curve x1="102.5" x2="101.16" x3="99.5" y1="28.16" y2="29.5" y3="29.5"/>
            <line x="82.5" y="29.5"/>
            <curve x1="80.84" x2="79.5" x3="79.5" y1="29.5" y2="28.16" y3="26.5"/>
            <line x="79.5" y="9.5"/>
            <curve x1="79.5" x2="80.84" x3="82.5" y1="7.84" y2="6.5" y3="6.5"/>
            <close/>
            <move x="58.5" y="6.5"/>
            <line x="75.5" y="6.5"/>
            <curve x1="77.16" x2="78.5" x3="78.5" y1="6.5" y2="7.84" y3="9.5"/>
            <line x="78.5" y="26.5"/>
            <curve x1="78.5" x2="77.16" x3="75.5" y1="28.16" y2="29.5" y3="29.5"/>
            <line x="58.5" y="29.5"/>
            <curve x1="56.84" x2="55.5" x3="55.5" y1="29.5" y2="28.16" y3="26.5"/>
            <line x="55.5" y="9.5"/>
            <curve x1="55.5" x2="56.84" x3="58.5" y1="7.84" y2="6.5" y3="6.5"/>
            <close/>
            <move x="34.5" y="6.5"/>
            <line x="51.5" y="6.5"/>
            <curve x1="53.16" x2="54.5" x3="54.5" y1="6.5" y2="7.84" y3="9.5"/>
            <line x="54.5" y="26.5"/>
            <curve x1="54.5" x2="53.16" x3="51.5" y1="28.16" y2="29.5" y3="29.5"/>
            <line x="34.5" y="29.5"/>
            <curve x1="32.84" x2="31.5" x3="31.5" y1="29.5" y2="28.16" y3="26.5"/>
            <line x="31.5" y="9.5"/>
            <curve x1="31.5" x2="32.84" x3="34.5" y1="7.84" y2="6.5" y3="6.5"/>
            <close/>
            <move x="10.5" y="6.5"/>
            <line x="27.5" y="6.5"/>
            <curve x1="29.16" x2="30.5" x3="30.5" y1="6.5" y2="7.84" y3="9.5"/>
            <line x="30.5" y="26.5"/>
            <curve x1="30.5" x2="29.16" x3="27.5" y1="28.16" y2="29.5" y3="29.5"/>
            <line x="10.5" y="29.5"/>
            <curve x1="8.84" x2="7.5" x3="7.5" y1="29.5" y2="28.16" y3="26.5"/>
            <line x="7.5" y="9.5"/>
            <curve x1="7.5" x2="8.84" x3="10.5" y1="7.84" y2="6.5" y3="6.5"/>
            <close/>
        </path>
        <fill/>
    </foreground>
</shape>
<shape aspect="variable" h="121" name="Netra Sparc T4-1B Server Module" strokewidth="inherit" w="19.5">
    <connections/>
    <background>
        <save/>
        <path>
            <move x="0" y="0"/>
            <line x="0" y="0.5"/>
            <line x="0" y="121"/>
            <line x="19.5" y="121"/>
            <line x="19.5" y="0"/>
            <close/>
        </path>
    </background>
    <foreground>
        <fill/>
        <fillcolor color="rgb(0,          0, 0)"/>
        <strokewidth width="1"/>
        <linejoin join="miter"/>
        <linecap cap="butt"/>
        <miterlimit limit="4"/>
        <dashpattern pattern="none"/>
        <dashed dashed="1"/>
        <alpha alpha="1"/>
        <strokealpha alpha="1"/>
        <fillalpha alpha="0.644"/>
        <fontfamily family="sans-serif"/>
        <fontstyle style="0"/>
        <path>
            <move x="0" y="0"/>
            <line x="0" y="121"/>
            <line x="19.5" y="121"/>
            <line x="19.5" y="0"/>
            <close/>
            <move x="1" y="1"/>
            <line x="18.5" y="1"/>
            <line x="18.5" y="120"/>
            <line x="1" y="120"/>
            <close/>
            <move x="3" y="2"/>
            <line x="3" y="30"/>
            <line x="9" y="30"/>
            <line x="9" y="2"/>
            <close/>
            <move x="11" y="2"/>
            <line x="11" y="30"/>
            <line x="17" y="30"/>
            <line x="17" y="2"/>
            <close/>
            <move x="4" y="3"/>
            <line x="8" y="3"/>
            <line x="8" y="29"/>
            <line x="4" y="29"/>
            <close/>
            <move x="12" y="3"/>
            <line x="16" y="3"/>
            <line x="16" y="29"/>
            <line x="12" y="29"/>
            <close/>
        </path>
        <fill/>
        <restore/>
        <rect/>
        <stroke/>
        <fillcolor color="#000000"/>
        <fillalpha alpha="0.232"/>
        <roundrect arcsize="20.69" h="82" w="14.5" x="2.5" y="36.5"/>
        <fill/>
    </foreground>
</shape>
<shape aspect="variable" h="60.2" name="Netra Sparc T4-2 Server" strokewidth="inherit" w="161.9">
    <connections/>
    <background>
        <save/>
        <path>
            <move x="0" y="0"/>
            <line x="0" y="0.5"/>
            <line x="0" y="60.2"/>
            <line x="161.9" y="60.2"/>
            <line x="161.9" y="0"/>
            <close/>
        </path>
    </background>
    <foreground>
        <fill/>
        <fillcolor color="#000000"/>
        <strokewidth width="1"/>
        <linejoin join="miter"/>
        <linecap cap="butt"/>
        <miterlimit limit="4"/>
        <dashpattern pattern="none"/>
        <dashed dashed="1"/>
        <alpha alpha="1"/>
        <strokealpha alpha="1"/>
        <fillalpha alpha="0.644"/>
        <fontfamily family="sans-serif"/>
        <fontstyle style="0"/>
        <path>
            <move x="0" y="0"/>
            <line x="0" y="60.2"/>
            <line x="161.9" y="60.2"/>
            <line x="161.9" y="0"/>
            <close/>
            <move x="1" y="1"/>
            <line x="6" y="1"/>
            <line x="6" y="59.2"/>
            <line x="1" y="59.2"/>
            <close/>
            <move x="7" y="1"/>
            <line x="155" y="1"/>
            <line x="155" y="59.2"/>
            <line x="7" y="59.2"/>
            <close/>
            <move x="156" y="1"/>
            <line x="160.9" y="1"/>
            <line x="160.9" y="59.2"/>
            <line x="156" y="59.2"/>
            <close/>
            <move x="39" y="5"/>
            <line x="39" y="13"/>
            <line x="46" y="13"/>
            <line x="46" y="5"/>
            <close/>
            <move x="113" y="5"/>
            <line x="113" y="13"/>
            <line x="120" y="13"/>
            <line x="120" y="5"/>
            <close/>
            <move x="40" y="6"/>
            <line x="45" y="6"/>
            <line x="45" y="12"/>
            <line x="40" y="12"/>
            <close/>
            <move x="114" y="6"/>
            <line x="119" y="6"/>
            <line x="119" y="12"/>
            <line x="114" y="12"/>
            <close/>
            <move x="9" y="44"/>
            <line x="9" y="50"/>
            <line x="66" y="50"/>
            <line x="66" y="44"/>
            <close/>
            <move x="10" y="45"/>
            <line x="65" y="45"/>
            <line x="65" y="49"/>
            <line x="10" y="49"/>
            <close/>
        </path>
        <fill/>
        <restore/>
        <rect/>
        <stroke/>
        <fillcolor color="#000000"/>
        <strokewidth width="0.301"/>
        <miterlimit limit="4"/>
        <dashpattern pattern="none"/>
        <dashed dashed="1"/>
        <fillalpha alpha="0.232"/>
        <path>
            <move x="11.5" y="57.5"/>
            <arc large-arc-flag="0" rx="3" ry="3" sweep-flag="1" x="11.5" x-axis-rotation="0" y="51.5"/>
            <line x="65.5" y="51.5"/>
            <arc large-arc-flag="0" rx="3" ry="3" sweep-flag="0" x="68.5" x-axis-rotation="0" y="48.5"/>
            <line x="68.5" y="45.5"/>
            <arc large-arc-flag="0" rx="3" ry="3" sweep-flag="0" x="65.5" x-axis-rotation="0" y="42.5"/>
            <line x="11.5" y="42.5"/>
            <arc large-arc-flag="0" rx="3" ry="3" sweep-flag="1" x="8.5" x-axis-rotation="0" y="39.5"/>
            <line x="8.5" y="5.5"/>
            <arc large-arc-flag="0" rx="3" ry="3" sweep-flag="1" x="11.5" x-axis-rotation="0" y="2.5"/>
            <line x="33.5" y="2.5"/>
            <arc large-arc-flag="0" rx="3" ry="3" sweep-flag="1" x="36.5" x-axis-rotation="0" y="5.5"/>
            <line x="36.5" y="13.5"/>
            <arc large-arc-flag="0" rx="3" ry="3" sweep-flag="0" x="39.5" x-axis-rotation="0" y="16.5"/>
            <line x="45.5" y="16.5"/>
            <arc large-arc-flag="0" rx="3" ry="3" sweep-flag="0" x="48.5" x-axis-rotation="0" y="13.5"/>
            <line x="48.5" y="5.5"/>
            <arc large-arc-flag="0" rx="3" ry="3" sweep-flag="1" x="51.5" x-axis-rotation="0" y="2.5"/>
            <line x="107.5" y="2.5"/>
            <arc large-arc-flag="0" rx="3" ry="3" sweep-flag="1" x="110.5" x-axis-rotation="0" y="5.5"/>
            <line x="110.5" y="13.5"/>
            <arc large-arc-flag="0" rx="3" ry="3" sweep-flag="0" x="113.5" x-axis-rotation="0" y="16.5"/>
            <line x="119.5" y="16.5"/>
            <arc large-arc-flag="0" rx="3" ry="3" sweep-flag="0" x="122.5" x-axis-rotation="0" y="13.5"/>
            <line x="122.5" y="5.5"/>
            <arc large-arc-flag="0" rx="3" ry="3" sweep-flag="1" x="125.5" x-axis-rotation="0" y="2.5"/>
            <line x="150.5" y="2.5"/>
            <arc large-arc-flag="0" rx="3" ry="3" sweep-flag="1" x="153.5" x-axis-rotation="0" y="5.5"/>
            <line x="153.5" y="54.5"/>
            <arc large-arc-flag="0" rx="3" ry="3" sweep-flag="1" x="150.5" x-axis-rotation="0" y="57.5"/>
            <close/>
        </path>
        <fill/>
    </foreground>
</shape>
<shape aspect="variable" h="30.6" name="Netra X4270 Server" strokewidth="inherit" w="161.9">
    <connections/>
    <background>
        <save/>
        <path>
            <move x="0" y="0"/>
            <line x="0" y="0.5"/>
            <line x="0" y="30.6"/>
            <line x="161.9" y="30.6"/>
            <line x="161.9" y="0"/>
            <close/>
        </path>
    </background>
    <foreground>
        <fill/>
        <fillcolor color="#000000"/>
        <strokewidth width="1"/>
        <linejoin join="miter"/>
        <linecap cap="butt"/>
        <miterlimit limit="4"/>
        <dashpattern pattern="none"/>
        <dashed dashed="1"/>
        <alpha alpha="1"/>
        <strokealpha alpha="1"/>
        <fillalpha alpha="0.644"/>
        <fontfamily family="sans-serif"/>
        <fontstyle style="0"/>
        <path>
            <move x="0" y="0"/>
            <line x="0" y="30.6"/>
            <line x="161.9" y="30.6"/>
            <line x="161.9" y="0"/>
            <close/>
            <move x="1" y="1"/>
            <line x="160.9" y="1"/>
            <line x="160.9" y="29.6"/>
            <line x="1" y="29.6"/>
            <close/>
            <move x="111" y="2"/>
            <line x="111" y="8"/>
            <line x="153" y="8"/>
            <line x="153" y="2"/>
            <close/>
            <move x="112" y="3"/>
            <line x="152" y="3"/>
            <line x="152" y="7"/>
            <line x="112" y="7"/>
            <close/>
        </path>
        <fill/>
        <restore/>
        <rect/>
        <stroke/>
        <fillcolor color="#000000"/>
        <fillalpha alpha="0.232"/>
        <path>
            <move x="8.5" y="8.5"/>
            <arc large-arc-flag="0" rx="3" ry="3" sweep-flag="1" x="11.5" x-axis-rotation="0" y="5.5"/>
            <line x="102.5" y="5.5"/>
            <arc large-arc-flag="0" rx="3" ry="3" sweep-flag="1" x="105.5" x-axis-rotation="0" y="8.5"/>
            <line x="105.5" y="8.5"/>
            <arc large-arc-flag="0" rx="3" ry="3" sweep-flag="0" x="108.5" x-axis-rotation="0" y="11.5"/>
            <line x="150.5" y="11.5"/>
            <arc large-arc-flag="0" rx="3" ry="3" sweep-flag="1" x="153.5" x-axis-rotation="0" y="14.5"/>
            <line x="153.5" y="25.5"/>
            <arc large-arc-flag="0" rx="3" ry="3" sweep-flag="1" x="150.5" x-axis-rotation="0" y="28.5"/>
            <line x="11.5" y="28.5"/>
            <arc large-arc-flag="0" rx="3" ry="3" sweep-flag="1" x="8.5" x-axis-rotation="0" y="25.5"/>
            <close/>
        </path>
        <fill/>
    </foreground>
</shape>
<shape aspect="variable" h="121" name="Netra X6270 Server Module" strokewidth="inherit" w="19.5">
    <connections/>
    <background>
        <save/>
        <path>
            <move x="0" y="0"/>
            <line x="0" y="0.5"/>
            <line x="0" y="121"/>
            <line x="19.5" y="121"/>
            <line x="19.5" y="0"/>
            <close/>
        </path>
    </background>
    <foreground>
        <fill/>
        <fillcolor color="#000000"/>
        <strokewidth width="1"/>
        <linejoin join="miter"/>
        <linecap cap="butt"/>
        <miterlimit limit="4"/>
        <dashpattern pattern="none"/>
        <dashed dashed="1"/>
        <alpha alpha="1"/>
        <strokealpha alpha="1"/>
        <fillalpha alpha="0.644"/>
        <fontfamily family="sans-serif"/>
        <fontstyle style="0"/>
        <path>
            <move x="0" y="0"/>
            <line x="0" y="121"/>
            <line x="19.5" y="121"/>
            <line x="19.5" y="0"/>
            <close/>
            <move x="1" y="1"/>
            <line x="18.5" y="1"/>
            <line x="18.5" y="120"/>
            <line x="1" y="120"/>
            <close/>
            <move x="3" y="2"/>
            <line x="3" y="30"/>
            <line x="9" y="30"/>
            <line x="9" y="2"/>
            <close/>
            <move x="11" y="2"/>
            <line x="11" y="30"/>
            <line x="17" y="30"/>
            <line x="17" y="2"/>
            <close/>
            <move x="4" y="3"/>
            <line x="8" y="3"/>
            <line x="8" y="29"/>
            <line x="4" y="29"/>
            <close/>
            <move x="12" y="3"/>
            <line x="16" y="3"/>
            <line x="16" y="29"/>
            <line x="12" y="29"/>
            <close/>
            <move x="3" y="91"/>
            <line x="3" y="119"/>
            <line x="9" y="119"/>
            <line x="9" y="91"/>
            <close/>
            <move x="11" y="91"/>
            <line x="11" y="119"/>
            <line x="17" y="119"/>
            <line x="17" y="91"/>
            <close/>
            <move x="4" y="92"/>
            <line x="8" y="92"/>
            <line x="8" y="118"/>
            <line x="4" y="118"/>
            <close/>
            <move x="12" y="92"/>
            <line x="16" y="92"/>
            <line x="16" y="118"/>
            <line x="12" y="118"/>
            <close/>
        </path>
        <fill/>
        <restore/>
        <rect/>
        <stroke/>
        <fillcolor color="#000000"/>
        <fillalpha alpha="0.232"/>
        <roundrect arcsize="20.69" h="56" w="14.5" x="2.5" y="32.5"/>
        <fill/>
    </foreground>
</shape>
<shape aspect="variable" h="89.8" name="Sparc Enterprise M4000 Server" strokewidth="inherit" w="161.9">
    <connections/>
    <background>
        <save/>
        <path>
            <move x="0" y="0"/>
            <line x="0" y="0.5"/>
            <line x="0" y="89.8"/>
            <line x="161.9" y="89.8"/>
            <line x="161.9" y="0"/>
            <close/>
        </path>
    </background>
    <foreground>
        <fill/>
        <fillcolor color="#000000"/>
        <strokewidth width="1"/>
        <linejoin join="miter"/>
        <linecap cap="butt"/>
        <miterlimit limit="4"/>
        <dashpattern pattern="none"/>
        <dashed dashed="1"/>
        <alpha alpha="1"/>
        <strokealpha alpha="1"/>
        <fillalpha alpha="0.644"/>
        <fontfamily family="sans-serif"/>
        <fontstyle style="0"/>
        <path>
            <move x="0" y="0"/>
            <line x="0" y="89.8"/>
            <line x="161.9" y="89.8"/>
            <line x="161.9" y="0"/>
            <close/>
            <move x="1" y="1"/>
            <line x="160.9" y="1"/>
            <line x="160.9" y="88.8"/>
            <line x="1" y="88.8"/>
            <close/>
            <move x="10" y="3"/>
            <line x="10" y="9"/>
            <line x="67" y="9"/>
            <line x="67" y="3"/>
            <close/>
            <move x="11" y="4"/>
            <line x="66" y="4"/>
            <line x="66" y="8"/>
            <line x="11" y="8"/>
            <close/>
            <move x="10" y="12"/>
            <line x="10" y="18"/>
            <line x="38" y="18"/>
            <line x="38" y="12"/>
            <close/>
            <move x="44" y="12"/>
            <line x="44" y="26"/>
            <line x="80" y="26"/>
            <line x="80" y="12"/>
            <close/>
            <move x="11" y="13"/>
            <line x="37" y="13"/>
            <line x="37" y="17"/>
            <line x="11" y="17"/>
            <close/>
            <move x="45" y="13"/>
            <line x="79" y="13"/>
            <line x="79" y="25"/>
            <line x="45" y="25"/>
            <close/>
            <move x="10" y="19"/>
            <line x="10" y="25"/>
            <line x="38" y="25"/>
            <line x="38" y="19"/>
            <close/>
            <move x="11" y="20"/>
            <line x="37" y="20"/>
            <line x="37" y="24"/>
            <line x="11" y="24"/>
            <close/>
            <move x="3.5" y="22"/>
            <curve x1="2.4" x2="1.5" x3="1.5" y1="22" y2="22.9" y3="24"/>
            <line x="1.5" y="65.8"/>
            <curve x1="1.5" x2="2.4" x3="3.5" y1="66.9" y2="67.8" y3="67.8"/>
            <curve x1="4.6" x2="5.5" x3="5.5" y1="67.8" y2="66.9" y3="65.8"/>
            <line x="5.5" y="24"/>
            <curve x1="5.5" x2="4.6" x3="3.5" y1="22.9" y2="22" y3="22"/>
            <close/>
            <move x="158.4" y="22"/>
            <curve x1="157.3" x2="156.4" x3="156.4" y1="22" y2="22.9" y3="24"/>
            <line x="156.4" y="65.8"/>
            <curve x1="156.4" x2="157.3" x3="158.4" y1="66.9" y2="67.8" y3="67.8"/>
            <curve x1="159.5" x2="160.4" x3="160.4" y1="67.8" y2="66.9" y3="65.8"/>
            <line x="160.4" y="24"/>
            <curve x1="160.4" x2="159.5" x3="158.4" y1="22.9" y2="22" y3="22"/>
            <close/>
            <move x="3.5" y="23"/>
            <curve x1="4.06" x2="4.5" x3="4.5" y1="23" y2="23.44" y3="24"/>
            <line x="4.5" y="65.8"/>
            <curve x1="4.5" x2="4.06" x3="3.5" y1="66.36" y2="66.8" y3="66.8"/>
            <curve x1="2.94" x2="2.5" x3="2.5" y1="66.8" y2="66.36" y3="65.8"/>
            <line x="2.5" y="24"/>
            <curve x1="2.5" x2="2.94" x3="3.5" y1="23.44" y2="23" y3="23"/>
            <close/>
            <move x="158.4" y="23"/>
            <curve x1="158.96" x2="159.4" x3="159.4" y1="23" y2="23.44" y3="24"/>
            <line x="159.4" y="65.8"/>
            <curve x1="159.4" x2="158.96" x3="158.4" y1="66.36" y2="66.8" y3="66.8"/>
            <curve x1="157.84" x2="157.4" x3="157.4" y1="66.8" y2="66.36" y3="65.8"/>
            <line x="157.4" y="24"/>
            <curve x1="157.4" x2="157.84" x3="158.4" y1="23.44" y2="23" y3="23"/>
            <close/>
            <move x="15" y="28"/>
            <line x="15" y="86"/>
            <line x="46" y="86"/>
            <line x="46" y="28"/>
            <close/>
            <move x="49" y="28"/>
            <line x="49" y="86"/>
            <line x="80" y="86"/>
            <line x="80" y="28"/>
            <close/>
            <move x="16" y="29"/>
            <line x="45" y="29"/>
            <line x="45" y="85"/>
            <line x="16" y="85"/>
            <close/>
            <move x="50" y="29"/>
            <line x="79" y="29"/>
            <line x="79" y="85"/>
            <line x="50" y="85"/>
            <close/>
        </path>
        <fill/>
        <restore/>
        <rect/>
        <stroke/>
        <fillcolor color="#000000"/>
        <fillalpha alpha="0.232"/>
        <path>
            <move x="83.5" y="6.5"/>
            <arc large-arc-flag="0" rx="3" ry="3" sweep-flag="1" x="86.5" x-axis-rotation="0" y="3.5"/>
            <line x="137.5" y="3.5"/>
            <arc large-arc-flag="0" rx="3" ry="3" sweep-flag="1" x="140.5" x-axis-rotation="0" y="6.5"/>
            <line x="140.5" y="35.5"/>
            <arc large-arc-flag="0" rx="3" ry="3" sweep-flag="0" x="143.5" x-axis-rotation="0" y="38.5"/>
            <line x="150.5" y="38.5"/>
            <arc large-arc-flag="0" rx="3" ry="3" sweep-flag="1" x="153.5" x-axis-rotation="0" y="41.5"/>
            <line x="153.5" y="81.5"/>
            <arc large-arc-flag="0" rx="3" ry="3" sweep-flag="1" x="150.5" x-axis-rotation="0" y="84.5"/>
            <line x="86.5" y="84.5"/>
            <arc large-arc-flag="0" rx="3" ry="3" sweep-flag="1" x="83.5" x-axis-rotation="0" y="81.5"/>
            <close/>
        </path>
        <fill/>
    </foreground>
</shape>
<shape aspect="variable" h="149" name="Sparc Enterprise M5000 Server" strokewidth="inherit" w="161.9">
    <connections/>
    <background>
        <save/>
        <path>
            <move x="0" y="0"/>
            <line x="0" y="0.5"/>
            <line x="0" y="149"/>
            <line x="161.9" y="149"/>
            <line x="161.9" y="0"/>
            <close/>
        </path>
    </background>
    <foreground>
        <fill/>
        <fillcolor color="#000000"/>
        <strokewidth width="1"/>
        <linejoin join="miter"/>
        <linecap cap="butt"/>
        <miterlimit limit="4"/>
        <dashpattern pattern="none"/>
        <dashed dashed="1"/>
        <alpha alpha="1"/>
        <strokealpha alpha="1"/>
        <fillalpha alpha="0.644"/>
        <fontfamily family="sans-serif"/>
        <fontstyle style="0"/>
        <path>
            <move x="0" y="0"/>
            <line x="0" y="149"/>
            <line x="161.9" y="149"/>
            <line x="161.9" y="0"/>
            <close/>
            <move x="1" y="1"/>
            <line x="160.9" y="1"/>
            <line x="160.9" y="148"/>
            <line x="1" y="148"/>
            <close/>
            <move x="3.5" y="52"/>
            <curve x1="2.4" x2="1.5" x3="1.5" y1="52" y2="52.9" y3="54"/>
            <line x="1.5" y="95.8"/>
            <curve x1="1.5" x2="2.4" x3="3.5" y1="96.9" y2="97.8" y3="97.8"/>
            <curve x1="4.6" x2="5.5" x3="5.5" y1="97.8" y2="96.9" y3="95.8"/>
            <line x="5.5" y="54"/>
            <curve x1="5.5" x2="4.6" x3="3.5" y1="52.9" y2="52" y3="52"/>
            <close/>
            <move x="158.4" y="52"/>
            <curve x1="157.3" x2="156.4" x3="156.4" y1="52" y2="52.9" y3="54"/>
            <line x="156.4" y="95.8"/>
            <curve x1="156.4" x2="157.3" x3="158.4" y1="96.9" y2="97.8" y3="97.8"/>
            <curve x1="159.5" x2="160.4" x3="160.4" y1="97.8" y2="96.9" y3="95.8"/>
            <line x="160.4" y="54"/>
            <curve x1="160.4" x2="159.5" x3="158.4" y1="52.9" y2="52" y3="52"/>
            <close/>
            <move x="3.5" y="53"/>
            <curve x1="4.06" x2="4.5" x3="4.5" y1="53" y2="53.44" y3="54"/>
            <line x="4.5" y="95.8"/>
            <curve x1="4.5" x2="4.06" x3="3.5" y1="96.36" y2="96.8" y3="96.8"/>
            <curve x1="2.94" x2="2.5" x3="2.5" y1="96.8" y2="96.36" y3="95.8"/>
            <line x="2.5" y="54"/>
            <curve x1="2.5" x2="2.94" x3="3.5" y1="53.44" y2="53" y3="53"/>
            <close/>
            <move x="158.4" y="53"/>
            <curve x1="158.96" x2="159.4" x3="159.4" y1="53" y2="53.44" y3="54"/>
            <line x="159.4" y="95.8"/>
            <curve x1="159.4" x2="158.96" x3="158.4" y1="96.36" y2="96.8" y3="96.8"/>
            <curve x1="157.84" x2="157.4" x3="157.4" y1="96.8" y2="96.36" y3="95.8"/>
            <line x="157.4" y="54"/>
            <curve x1="157.4" x2="157.84" x3="158.4" y1="53.44" y2="53" y3="53"/>
            <close/>
            <move x="69" y="67.5"/>
            <line x="69" y="81.5"/>
            <line x="105" y="81.5"/>
            <line x="105" y="67.5"/>
            <close/>
            <move x="10" y="68"/>
            <line x="10" y="74"/>
            <line x="38" y="74"/>
            <line x="38" y="68"/>
            <close/>
            <move x="39" y="68"/>
            <line x="39" y="74"/>
            <line x="67" y="74"/>
            <line x="67" y="68"/>
            <close/>
            <move x="107" y="68"/>
            <line x="107" y="74"/>
            <line x="153" y="74"/>
            <line x="153" y="68"/>
            <close/>
            <move x="70" y="68.5"/>
            <line x="104" y="68.5"/>
            <line x="104" y="80.5"/>
            <line x="70" y="80.5"/>
            <close/>
            <move x="11" y="69"/>
            <line x="37" y="69"/>
            <line x="37" y="73"/>
            <line x="11" y="73"/>
            <close/>
            <move x="40" y="69"/>
            <line x="66" y="69"/>
            <line x="66" y="73"/>
            <line x="40" y="73"/>
            <close/>
            <move x="108" y="69"/>
            <line x="152" y="69"/>
            <line x="152" y="73"/>
            <line x="108" y="73"/>
            <close/>
            <move x="10" y="75"/>
            <line x="10" y="81"/>
            <line x="38" y="81"/>
            <line x="38" y="75"/>
            <close/>
            <move x="39" y="75"/>
            <line x="39" y="81"/>
            <line x="67" y="81"/>
            <line x="67" y="75"/>
            <close/>
            <move x="11" y="76"/>
            <line x="37" y="76"/>
            <line x="37" y="80"/>
            <line x="11" y="80"/>
            <close/>
            <move x="40" y="76"/>
            <line x="66" y="76"/>
            <line x="66" y="80"/>
            <line x="40" y="80"/>
            <close/>
            <move x="15" y="88"/>
            <line x="15" y="146"/>
            <line x="46" y="146"/>
            <line x="46" y="88"/>
            <close/>
            <move x="49" y="88"/>
            <line x="49" y="146"/>
            <line x="80" y="146"/>
            <line x="80" y="88"/>
            <close/>
            <move x="83" y="88"/>
            <line x="83" y="146"/>
            <line x="114" y="146"/>
            <line x="114" y="88"/>
            <close/>
            <move x="117" y="88"/>
            <line x="117" y="146"/>
            <line x="148" y="146"/>
            <line x="148" y="88"/>
            <close/>
            <move x="16" y="89"/>
            <line x="45" y="89"/>
            <line x="45" y="145"/>
            <line x="16" y="145"/>
            <close/>
            <move x="50" y="89"/>
            <line x="79" y="89"/>
            <line x="79" y="145"/>
            <line x="50" y="145"/>
            <close/>
            <move x="84" y="89"/>
            <line x="113" y="89"/>
            <line x="113" y="145"/>
            <line x="84" y="145"/>
            <close/>
            <move x="118" y="89"/>
            <line x="147" y="89"/>
            <line x="147" y="145"/>
            <line x="118" y="145"/>
            <close/>
        </path>
        <fill/>
        <restore/>
        <rect/>
        <stroke/>
        <fillcolor color="#000000"/>
        <fillalpha alpha="0.232"/>
        <path>
            <move x="8.5" y="6.5"/>
            <arc large-arc-flag="0" rx="3" ry="3" sweep-flag="1" x="11.5" x-axis-rotation="0" y="3.5"/>
            <line x="137.5" y="3.5"/>
            <arc large-arc-flag="0" rx="3" ry="3" sweep-flag="1" x="140.5" x-axis-rotation="0" y="6.5"/>
            <line x="140.5" y="35.5"/>
            <arc large-arc-flag="0" rx="3" ry="3" sweep-flag="0" x="143.5" x-axis-rotation="0" y="38.5"/>
            <line x="150.5" y="38.5"/>
            <arc large-arc-flag="0" rx="3" ry="3" sweep-flag="1" x="153.5" x-axis-rotation="0" y="41.5"/>
            <line x="153.5" y="60.5"/>
            <arc large-arc-flag="0" rx="3" ry="3" sweep-flag="1" x="150.5" x-axis-rotation="0" y="63.5"/>
            <line x="11.5" y="63.5"/>
            <arc large-arc-flag="0" rx="3" ry="3" sweep-flag="1" x="8.5" x-axis-rotation="0" y="60.5"/>
            <close/>
        </path>
        <fill/>
    </foreground>
</shape>
<shape aspect="variable" h="30.6" name="Sparc T4-1 Server" strokewidth="inherit" w="161.9">
    <connections/>
    <background>
        <save/>
        <path>
            <move x="0" y="0"/>
            <line x="0" y="0.5"/>
            <line x="0" y="30.6"/>
            <line x="161.9" y="30.6"/>
            <line x="161.9" y="0"/>
            <close/>
        </path>
    </background>
    <foreground>
        <fill/>
        <fillcolor color="#000000"/>
        <strokewidth width="1"/>
        <linejoin join="miter"/>
        <linecap cap="butt"/>
        <miterlimit limit="4"/>
        <dashpattern pattern="none"/>
        <dashed dashed="1"/>
        <alpha alpha="1"/>
        <strokealpha alpha="1"/>
        <fillalpha alpha="0.644"/>
        <fontfamily family="sans-serif"/>
        <fontstyle style="0"/>
        <path>
            <move x="0" y="0"/>
            <line x="0" y="30.6"/>
            <line x="161.9" y="30.6"/>
            <line x="161.9" y="0"/>
            <close/>
            <move x="1" y="1"/>
            <line x="6" y="1"/>
            <line x="6" y="29.6"/>
            <line x="1" y="29.6"/>
            <close/>
            <move x="7" y="1"/>
            <line x="155" y="1"/>
            <line x="155" y="29.6"/>
            <line x="7" y="29.6"/>
            <close/>
            <move x="156" y="1"/>
            <line x="160.9" y="1"/>
            <line x="160.9" y="29.6"/>
            <line x="156" y="29.6"/>
            <close/>
            <move x="8" y="17"/>
            <line x="8" y="23"/>
            <line x="36" y="23"/>
            <line x="36" y="17"/>
            <close/>
            <move x="37" y="17"/>
            <line x="37" y="23"/>
            <line x="65" y="23"/>
            <line x="65" y="17"/>
            <close/>
            <move x="66" y="17"/>
            <line x="66" y="23"/>
            <line x="94" y="23"/>
            <line x="94" y="17"/>
            <close/>
            <move x="95" y="17"/>
            <line x="95" y="23"/>
            <line x="152" y="23"/>
            <line x="152" y="17"/>
            <close/>
            <move x="9" y="18"/>
            <line x="35" y="18"/>
            <line x="35" y="22"/>
            <line x="9" y="22"/>
            <close/>
            <move x="38" y="18"/>
            <line x="64" y="18"/>
            <line x="64" y="22"/>
            <line x="38" y="22"/>
            <close/>
            <move x="67" y="18"/>
            <line x="93" y="18"/>
            <line x="93" y="22"/>
            <line x="67" y="22"/>
            <close/>
            <move x="96" y="18"/>
            <line x="151" y="18"/>
            <line x="151" y="22"/>
            <line x="96" y="22"/>
            <close/>
            <move x="8" y="23.5"/>
            <line x="8" y="29.5"/>
            <line x="36" y="29.5"/>
            <line x="36" y="23.5"/>
            <close/>
            <move x="37" y="23.5"/>
            <line x="37" y="29.5"/>
            <line x="65" y="29.5"/>
            <line x="65" y="23.5"/>
            <close/>
            <move x="66" y="23.5"/>
            <line x="66" y="29.5"/>
            <line x="94" y="29.5"/>
            <line x="94" y="23.5"/>
            <close/>
            <move x="95" y="23.5"/>
            <line x="95" y="29.5"/>
            <line x="123" y="29.5"/>
            <line x="123" y="23.5"/>
            <close/>
            <move x="124" y="23.5"/>
            <line x="124" y="29.5"/>
            <line x="152" y="29.5"/>
            <line x="152" y="23.5"/>
            <close/>
            <move x="9" y="24.5"/>
            <line x="35" y="24.5"/>
            <line x="35" y="28.5"/>
            <line x="9" y="28.5"/>
            <close/>
            <move x="38" y="24.5"/>
            <line x="64" y="24.5"/>
            <line x="64" y="28.5"/>
            <line x="38" y="28.5"/>
            <close/>
            <move x="67" y="24.5"/>
            <line x="93" y="24.5"/>
            <line x="93" y="28.5"/>
            <line x="67" y="28.5"/>
            <close/>
            <move x="96" y="24.5"/>
            <line x="122" y="24.5"/>
            <line x="122" y="28.5"/>
            <line x="96" y="28.5"/>
            <close/>
            <move x="125" y="24.5"/>
            <line x="151" y="24.5"/>
            <line x="151" y="28.5"/>
            <line x="125" y="28.5"/>
            <close/>
        </path>
        <fill/>
        <restore/>
        <rect/>
        <stroke/>
        <fillcolor color="#000000"/>
        <fillalpha alpha="0.232"/>
        <roundrect arcsize="25" h="12" w="143" x="9.5" y="2.5"/>
        <fill/>
    </foreground>
</shape>
<shape aspect="variable" h="121" name="Sparc T4-1B Server Module" strokewidth="inherit" w="19.5">
    <connections/>
    <background>
        <save/>
        <path>
            <move x="0" y="0"/>
            <line x="0" y="0.5"/>
            <line x="0" y="121"/>
            <line x="19.5" y="121"/>
            <line x="19.5" y="0"/>
            <close/>
        </path>
    </background>
    <foreground>
        <fill/>
        <fillcolor color="#000000"/>
        <strokewidth width="1"/>
        <linejoin join="miter"/>
        <linecap cap="butt"/>
        <miterlimit limit="4"/>
        <dashpattern pattern="none"/>
        <dashed dashed="1"/>
        <alpha alpha="1"/>
        <strokealpha alpha="1"/>
        <fillalpha alpha="0.644"/>
        <fontfamily family="sans-serif"/>
        <fontstyle style="0"/>
        <path>
            <move x="0" y="0"/>
            <curve x1="0" x2="0" x3="0" y1="40.33" y2="80.67" y3="121"/>
            <line x="19.5" y="121"/>
            <line x="19.5" y="0"/>
            <close/>
            <move x="1" y="1"/>
            <line x="18.5" y="1"/>
            <line x="18.5" y="120"/>
            <line x="1" y="120"/>
            <close/>
            <move x="3" y="2"/>
            <curve x1="3" x2="3" x3="3" y1="11.33" y2="20.67" y3="30"/>
            <line x="9" y="30"/>
            <line x="9" y="2"/>
            <close/>
            <move x="11" y="2"/>
            <curve x1="11" x2="11" x3="11" y1="11.33" y2="20.67" y3="30"/>
            <line x="17" y="30"/>
            <line x="17" y="2"/>
            <close/>
            <move x="4" y="3"/>
            <line x="8" y="3"/>
            <line x="8" y="29"/>
            <line x="4" y="29"/>
            <close/>
            <move x="12" y="3"/>
            <line x="16" y="3"/>
            <line x="16" y="29"/>
            <line x="12" y="29"/>
            <close/>
        </path>
        <fill/>
        <restore/>
        <rect/>
        <stroke/>
        <fillcolor color="#000000"/>
        <fillalpha alpha="0.232"/>
        <roundrect arcsize="20.69" h="82" w="14.5" x="2.5" y="36.5"/>
        <fill/>
    </foreground>
</shape>
<shape aspect="variable" h="45.4" name="Sparc T4-2 Server" strokewidth="inherit" w="161.9">
    <connections/>
    <background>
        <save/>
        <path>
            <move x="0" y="0"/>
            <line x="0" y="11"/>
            <line x="8" y="11"/>
            <line x="8" y="34.4"/>
            <line x="0" y="34.4"/>
            <line x="0" y="45.4"/>
            <line x="161.9" y="45.4"/>
            <line x="161.9" y="34.4"/>
            <line x="153.9" y="34.4"/>
            <line x="153.9" y="10.81"/>
            <line x="161.9" y="11.01"/>
            <line x="161.9" y="0"/>
            <close/>
        </path>
    </background>
    <foreground>
        <fill/>
        <fillcolor color="#000000"/>
        <strokewidth width="1"/>
        <linejoin join="miter"/>
        <linecap cap="butt"/>
        <miterlimit limit="4"/>
        <dashpattern pattern="none"/>
        <dashed dashed="1"/>
        <alpha alpha="1"/>
        <strokealpha alpha="1"/>
        <fillalpha alpha="0.644"/>
        <fontfamily family="sans-serif"/>
        <fontstyle style="0"/>
        <path>
            <move x="0" y="0"/>
            <line x="0" y="11"/>
            <line x="8" y="11"/>
            <line x="8" y="34.4"/>
            <line x="0" y="34.4"/>
            <line x="0" y="45.4"/>
            <line x="161.9" y="45.4"/>
            <line x="161.9" y="44.9"/>
            <line x="161.9" y="34.4"/>
            <line x="153.9" y="34.4"/>
            <line x="153.9" y="10.81"/>
            <line x="161.9" y="11.01"/>
            <line x="161.9" y="0"/>
            <close/>
            <move x="1" y="1"/>
            <line x="160.9" y="1"/>
            <line x="160.9" y="9.99"/>
            <line x="152.9" y="9.79"/>
            <line x="152.9" y="35.4"/>
            <line x="160.9" y="35.4"/>
            <line x="160.9" y="44.4"/>
            <line x="157" y="44.4"/>
            <line x="157" y="39.5"/>
            <line x="100" y="39.5"/>
            <line x="100" y="44.4"/>
            <line x="1" y="44.4"/>
            <line x="1" y="35.4"/>
            <line x="9" y="35.4"/>
            <line x="9" y="10"/>
            <line x="1" y="10"/>
            <close/>
            <move x="124" y="3"/>
            <line x="124" y="39"/>
            <line x="152" y="39"/>
            <line x="152" y="3"/>
            <close/>
            <move x="125" y="4"/>
            <line x="151" y="4"/>
            <line x="151" y="8"/>
            <line x="125" y="8"/>
            <close/>
            <move x="125" y="10"/>
            <line x="151" y="10"/>
            <line x="151" y="14"/>
            <line x="125" y="14"/>
            <close/>
            <move x="125" y="16"/>
            <line x="151" y="16"/>
            <line x="151" y="20"/>
            <line x="125" y="20"/>
            <close/>
            <move x="125" y="22"/>
            <line x="151" y="22"/>
            <line x="151" y="26"/>
            <line x="125" y="26"/>
            <close/>
            <move x="125" y="28"/>
            <line x="151" y="28"/>
            <line x="151" y="32"/>
            <line x="125" y="32"/>
            <close/>
            <move x="125" y="34"/>
            <line x="151" y="34"/>
            <line x="151" y="38"/>
            <line x="125" y="38"/>
            <close/>
            <move x="101" y="40.5"/>
            <line x="156" y="40.5"/>
            <line x="156" y="44.4"/>
            <line x="101" y="44.4"/>
            <close/>
        </path>
        <fill/>
        <restore/>
        <rect/>
        <stroke/>
        <fillcolor color="#000000"/>
        <fillalpha alpha="0.232"/>
        <roundrect arcsize="8.82" h="34" w="103" x="10.5" y="4.5"/>
        <fill/>
    </foreground>
</shape>
<shape aspect="variable" h="75" name="Sparc T4-4 Server" strokewidth="inherit" w="161.9">
    <connections/>
    <background>
        <save/>
        <path>
            <move x="0" y="0"/>
            <line x="0" y="75"/>
            <line x="161.9" y="75"/>
            <line x="161.9" y="0"/>
            <close/>
        </path>
    </background>
    <foreground>
        <fill/>
        <fillcolor color="#000000"/>
        <strokewidth width="1"/>
        <linejoin join="miter"/>
        <linecap cap="butt"/>
        <miterlimit limit="4"/>
        <dashpattern pattern="none"/>
        <dashed dashed="1"/>
        <alpha alpha="1"/>
        <strokealpha alpha="1"/>
        <fillalpha alpha="0.644"/>
        <fontfamily family="sans-serif"/>
        <fontstyle style="0"/>
        <path>
            <move x="0" y="0"/>
            <line x="0" y="75"/>
            <line x="161.9" y="75"/>
            <line x="161.9" y="0"/>
            <close/>
            <move x="1" y="1"/>
            <line x="160.9" y="1"/>
            <line x="160.9" y="33"/>
            <line x="1" y="33"/>
            <close/>
            <move x="1" y="34"/>
            <line x="12.5" y="34"/>
            <line x="12.5" y="74"/>
            <line x="1" y="74"/>
            <close/>
            <move x="13.5" y="34"/>
            <line x="25" y="34"/>
            <line x="25" y="74"/>
            <line x="13.5" y="74"/>
            <close/>
            <move x="26" y="34"/>
            <line x="33.5" y="34"/>
            <line x="33.5" y="74"/>
            <line x="26" y="74"/>
            <close/>
            <move x="34.5" y="34"/>
            <line x="42" y="34"/>
            <line x="42" y="74"/>
            <line x="34.5" y="74"/>
            <close/>
            <move x="43" y="34"/>
            <line x="50.5" y="34"/>
            <line x="50.5" y="74"/>
            <line x="43" y="74"/>
            <close/>
            <move x="51.5" y="34"/>
            <line x="59" y="34"/>
            <line x="59" y="74"/>
            <line x="51.5" y="74"/>
            <close/>
            <move x="60" y="34"/>
            <line x="67.5" y="34"/>
            <line x="67.5" y="74"/>
            <line x="60" y="74"/>
            <close/>
            <move x="68.5" y="34"/>
            <line x="76" y="34"/>
            <line x="76" y="74"/>
            <line x="68.5" y="74"/>
            <close/>
            <move x="77" y="34"/>
            <line x="84.5" y="34"/>
            <line x="84.5" y="74"/>
            <line x="77" y="74"/>
            <close/>
            <move x="85.5" y="34"/>
            <line x="93" y="34"/>
            <line x="93" y="74"/>
            <line x="85.5" y="74"/>
            <close/>
            <move x="94" y="34"/>
            <line x="101.5" y="34"/>
            <line x="101.5" y="74"/>
            <line x="94" y="74"/>
            <close/>
            <move x="102.5" y="34"/>
            <line x="110" y="34"/>
            <line x="110" y="74"/>
            <line x="102.5" y="74"/>
            <close/>
            <move x="111" y="34"/>
            <line x="118.5" y="34"/>
            <line x="118.5" y="74"/>
            <line x="111" y="74"/>
            <close/>
            <move x="119.5" y="34"/>
            <line x="127" y="34"/>
            <line x="127" y="74"/>
            <line x="119.5" y="74"/>
            <close/>
            <move x="128" y="34"/>
            <line x="135.5" y="34"/>
            <line x="135.5" y="74"/>
            <line x="128" y="74"/>
            <close/>
            <move x="136.5" y="34"/>
            <line x="144" y="34"/>
            <line x="144" y="74"/>
            <line x="136.5" y="74"/>
            <close/>
            <move x="145" y="34"/>
            <line x="152.5" y="34"/>
            <line x="152.5" y="74"/>
            <line x="145" y="74"/>
            <close/>
            <move x="153.5" y="34"/>
            <line x="160.9" y="34"/>
            <line x="160.9" y="74"/>
            <line x="153.5" y="74"/>
            <close/>
        </path>
        <fill/>
        <restore/>
        <rect/>
        <stroke/>
        <fillcolor color="#000000"/>
        <fillalpha alpha="0.232"/>
        <path>
            <move x="132.5" y="1.5"/>
            <line x="157.5" y="1.5"/>
            <curve x1="159.16" x2="160.5" x3="160.5" y1="1.5" y2="2.84" y3="4.5"/>
            <line x="160.5" y="26.5"/>
            <curve x1="160.5" x2="159.16" x3="157.5" y1="28.16" y2="29.5" y3="29.5"/>
            <line x="132.5" y="29.5"/>
            <curve x1="130.84" x2="129.5" x3="129.5" y1="29.5" y2="28.16" y3="26.5"/>
            <line x="129.5" y="4.5"/>
            <curve x1="129.5" x2="130.84" x3="132.5" y1="2.84" y2="1.5" y3="1.5"/>
            <close/>
            <move x="100.5" y="1.5"/>
            <line x="125.5" y="1.5"/>
            <curve x1="127.16" x2="128.5" x3="128.5" y1="1.5" y2="2.84" y3="4.5"/>
            <line x="128.5" y="26.5"/>
            <curve x1="128.5" x2="127.16" x3="125.5" y1="28.16" y2="29.5" y3="29.5"/>
            <line x="100.5" y="29.5"/>
            <curve x1="98.84" x2="97.5" x3="97.5" y1="29.5" y2="28.16" y3="26.5"/>
            <line x="97.5" y="4.5"/>
            <curve x1="97.5" x2="98.84" x3="100.5" y1="2.84" y2="1.5" y3="1.5"/>
            <close/>
            <move x="68.5" y="1.5"/>
            <line x="93.5" y="1.5"/>
            <curve x1="95.16" x2="96.5" x3="96.5" y1="1.5" y2="2.84" y3="4.5"/>
            <line x="96.5" y="26.5"/>
            <curve x1="96.5" x2="95.16" x3="93.5" y1="28.16" y2="29.5" y3="29.5"/>
            <line x="68.5" y="29.5"/>
            <curve x1="66.84" x2="65.5" x3="65.5" y1="29.5" y2="28.16" y3="26.5"/>
            <line x="65.5" y="4.5"/>
            <curve x1="65.5" x2="66.84" x3="68.5" y1="2.84" y2="1.5" y3="1.5"/>
            <close/>
            <move x="36.5" y="1.5"/>
            <line x="61.5" y="1.5"/>
            <curve x1="63.16" x2="64.5" x3="64.5" y1="1.5" y2="2.84" y3="4.5"/>
            <line x="64.5" y="26.5"/>
            <curve x1="64.5" x2="63.16" x3="61.5" y1="28.16" y2="29.5" y3="29.5"/>
            <line x="36.5" y="29.5"/>
            <curve x1="34.84" x2="33.5" x3="33.5" y1="29.5" y2="28.16" y3="26.5"/>
            <line x="33.5" y="4.5"/>
            <curve x1="33.5" x2="34.84" x3="36.5" y1="2.84" y2="1.5" y3="1.5"/>
            <close/>
            <move x="4.5" y="1.5"/>
            <line x="29.5" y="1.5"/>
            <curve x1="31.16" x2="32.5" x3="32.5" y1="1.5" y2="2.84" y3="4.5"/>
            <line x="32.5" y="26.5"/>
            <curve x1="32.5" x2="31.16" x3="29.5" y1="28.16" y2="29.5" y3="29.5"/>
            <line x="4.5" y="29.5"/>
            <curve x1="2.84" x2="1.5" x3="1.5" y1="29.5" y2="28.16" y3="26.5"/>
            <line x="1.5" y="4.5"/>
            <curve x1="1.5" x2="2.84" x3="4.5" y1="2.84" y2="1.5" y3="1.5"/>
            <close/>
        </path>
        <fill/>
    </foreground>
</shape>
<shape aspect="variable" h="149" name="Sun Blade 6000 Enclosure" strokewidth="inherit" w="161.9">
    <connections/>
    <background>
        <save/>
        <path>
            <move x="0" y="0"/>
            <line x="0" y="0.5"/>
            <line x="0" y="149"/>
            <line x="161.9" y="149"/>
            <line x="161.9" y="0"/>
            <close/>
        </path>
    </background>
    <foreground>
        <fill/>
        <fillcolor color="#000000"/>
        <strokewidth width="1"/>
        <linejoin join="miter"/>
        <linecap cap="butt"/>
        <miterlimit limit="4"/>
        <dashpattern pattern="none"/>
        <dashed dashed="1"/>
        <alpha alpha="1"/>
        <strokealpha alpha="1"/>
        <fillalpha alpha="0.644"/>
        <fontfamily family="sans-serif"/>
        <fontstyle style="0"/>
        <path>
            <move x="0" y="0"/>
            <line x="0" y="149"/>
            <line x="161.9" y="149"/>
            <line x="161.9" y="0"/>
            <close/>
            <move x="1" y="1"/>
            <line x="6" y="1"/>
            <line x="6" y="148"/>
            <line x="1" y="148"/>
            <close/>
            <move x="7" y="1"/>
            <line x="10.5" y="1"/>
            <line x="10.5" y="28"/>
            <line x="7" y="28"/>
            <close/>
            <move x="11.5" y="1"/>
            <line x="80.5" y="1"/>
            <line x="80.5" y="28"/>
            <line x="11.5" y="28"/>
            <close/>
            <move x="81.5" y="1"/>
            <line x="150.5" y="1"/>
            <line x="150.5" y="28"/>
            <line x="81.5" y="28"/>
            <close/>
            <move x="151.5" y="1"/>
            <line x="155" y="1"/>
            <line x="155" y="28"/>
            <line x="151.5" y="28"/>
            <close/>
            <move x="156" y="1"/>
            <line x="160.9" y="1"/>
            <line x="160.9" y="148"/>
            <line x="156" y="148"/>
            <close/>
            <move x="7" y="29"/>
            <line x="24.62" y="29"/>
            <line x="24.62" y="148"/>
            <line x="7" y="148"/>
            <close/>
            <move x="25.62" y="29"/>
            <line x="43.25" y="29"/>
            <line x="43.25" y="148"/>
            <line x="25.62" y="148"/>
            <close/>
            <move x="44.25" y="29"/>
            <line x="61.88" y="29"/>
            <line x="61.88" y="148"/>
            <line x="44.25" y="148"/>
            <close/>
            <move x="62.88" y="29"/>
            <line x="80.5" y="29"/>
            <line x="80.5" y="148"/>
            <line x="62.88" y="148"/>
            <close/>
            <move x="81.5" y="29"/>
            <line x="99.12" y="29"/>
            <line x="99.12" y="148"/>
            <line x="81.5" y="148"/>
            <close/>
            <move x="100.12" y="29"/>
            <line x="117.75" y="29"/>
            <line x="117.75" y="148"/>
            <line x="100.12" y="148"/>
            <close/>
            <move x="118.75" y="29"/>
            <line x="136.38" y="29"/>
            <line x="136.38" y="148"/>
            <line x="118.75" y="148"/>
            <close/>
            <move x="137.38" y="29"/>
            <line x="155" y="29"/>
            <line x="155" y="148"/>
            <line x="137.38" y="148"/>
            <close/>
        </path>
        <fill/>
        <restore/>
        <rect/>
        <stroke/>
        <fillcolor color="#000000"/>
        <fillalpha alpha="0.232"/>
        <path>
            <move x="88.5" y="4.5"/>
            <line x="143.5" y="4.5"/>
            <curve x1="145.16" x2="146.5" x3="146.5" y1="4.5" y2="5.84" y3="7.5"/>
            <line x="146.5" y="21.5"/>
            <curve x1="146.5" x2="145.16" x3="143.5" y1="23.16" y2="24.5" y3="24.5"/>
            <line x="88.5" y="24.5"/>
            <curve x1="86.84" x2="85.5" x3="85.5" y1="24.5" y2="23.16" y3="21.5"/>
            <line x="85.5" y="7.5"/>
            <curve x1="85.5" x2="86.84" x3="88.5" y1="5.84" y2="4.5" y3="4.5"/>
            <close/>
            <move x="18.5" y="4.5"/>
            <line x="73.5" y="4.5"/>
            <curve x1="75.16" x2="76.5" x3="76.5" y1="4.5" y2="5.84" y3="7.5"/>
            <line x="76.5" y="21.5"/>
            <curve x1="76.5" x2="75.16" x3="73.5" y1="23.16" y2="24.5" y3="24.5"/>
            <line x="18.5" y="24.5"/>
            <curve x1="16.84" x2="15.5" x3="15.5" y1="24.5" y2="23.16" y3="21.5"/>
            <line x="15.5" y="7.5"/>
            <curve x1="15.5" x2="16.84" x3="18.5" y1="5.84" y2="4.5" y3="4.5"/>
            <close/>
        </path>
        <fill/>
    </foreground>
</shape>
<shape aspect="variable" h="121" name="Sun Blade 6000 Ethernet Network Express Module 24p 10 GbE" strokewidth="inherit" w="19.62">
    <connections/>
    <background>
        <save/>
        <path>
            <move x="0" y="0"/>
            <line x="0" y="121"/>
            <line x="19.62" y="121"/>
            <line x="19.62" y="0"/>
            <close/>
        </path>
    </background>
    <foreground>
        <fill/>
        <fillcolor color="#000000"/>
        <strokewidth width="1"/>
        <linejoin join="miter"/>
        <linecap cap="butt"/>
        <miterlimit limit="4"/>
        <dashpattern pattern="none"/>
        <dashed dashed="1"/>
        <alpha alpha="1"/>
        <strokealpha alpha="1"/>
        <fillalpha alpha="0.644"/>
        <fontfamily family="sans-serif"/>
        <fontstyle style="0"/>
        <path>
            <move x="0" y="0"/>
            <line x="0" y="121"/>
            <line x="19.62" y="121"/>
            <line x="19.62" y="0"/>
            <close/>
            <move x="1" y="1"/>
            <line x="18.62" y="1"/>
            <line x="18.62" y="120"/>
            <line x="1" y="120"/>
            <close/>
            <move x="14" y="44"/>
            <line x="14" y="65"/>
            <line x="17" y="65"/>
            <line x="17" y="44"/>
            <close/>
            <move x="15" y="45"/>
            <line x="16" y="45"/>
            <line x="16" y="49"/>
            <line x="15" y="49"/>
            <close/>
            <move x="15" y="50"/>
            <line x="16" y="50"/>
            <line x="16" y="54"/>
            <line x="15" y="54"/>
            <close/>
            <move x="15" y="55"/>
            <line x="16" y="55"/>
            <line x="16" y="59"/>
            <line x="15" y="59"/>
            <close/>
            <move x="15" y="60"/>
            <line x="16" y="60"/>
            <line x="16" y="64"/>
            <line x="15" y="64"/>
            <close/>
            <move x="13" y="76"/>
            <line x="13" y="83"/>
            <line x="17" y="83"/>
            <line x="17" y="76"/>
            <close/>
            <move x="14" y="77"/>
            <line x="16" y="77"/>
            <line x="16" y="82"/>
            <line x="14" y="82"/>
            <close/>
            <move x="13" y="84"/>
            <line x="13" y="91"/>
            <line x="17" y="91"/>
            <line x="17" y="84"/>
            <close/>
            <move x="14" y="85"/>
            <line x="16" y="85"/>
            <line x="16" y="90"/>
            <line x="14" y="90"/>
            <close/>
            <move x="13" y="92"/>
            <line x="13" y="99"/>
            <line x="17" y="99"/>
            <line x="17" y="92"/>
            <close/>
            <move x="14" y="93"/>
            <line x="16" y="93"/>
            <line x="16" y="98"/>
            <line x="14" y="98"/>
            <close/>
            <move x="13" y="108"/>
            <line x="13" y="117"/>
            <line x="17" y="117"/>
            <line x="17" y="112"/>
            <line x="17" y="108"/>
            <close/>
            <move x="14" y="109"/>
            <line x="16" y="109"/>
            <line x="16" y="112"/>
            <line x="14" y="112"/>
            <close/>
            <move x="14" y="113"/>
            <line x="16" y="113"/>
            <line x="16" y="116"/>
            <line x="14" y="116"/>
            <close/>
        </path>
        <fill/>
        <restore/>
        <rect/>
        <stroke/>
        <fillcolor color="#000000"/>
        <fillalpha alpha="0.232"/>
        <roundrect arcsize="18.75" h="42" w="16" x="1.5" y="1.5"/>
        <fill/>
    </foreground>
</shape>
<shape aspect="variable" h="121" name="Sun Blade 6000 Virtualized 40 GbE Network Express Module" strokewidth="inherit" w="19.62">
    <connections/>
    <background>
        <save/>
        <path>
            <move x="0" y="0"/>
            <line x="0" y="121"/>
            <line x="19.62" y="121"/>
            <line x="19.62" y="0"/>
            <close/>
        </path>
    </background>
    <foreground>
        <fill/>
        <fillcolor color="#000000"/>
        <strokewidth width="1"/>
        <linejoin join="miter"/>
        <linecap cap="butt"/>
        <miterlimit limit="4"/>
        <dashpattern pattern="none"/>
        <dashed dashed="1"/>
        <alpha alpha="1"/>
        <strokealpha alpha="1"/>
        <fillalpha alpha="0.644"/>
        <fontfamily family="sans-serif"/>
        <fontstyle style="0"/>
        <path>
            <move x="0" y="0"/>
            <line x="0" y="121"/>
            <line x="19.62" y="121"/>
            <line x="19.62" y="0"/>
            <close/>
            <move x="1" y="1"/>
            <line x="18.62" y="1"/>
            <line x="18.62" y="120"/>
            <line x="1" y="120"/>
            <close/>
            <move x="13" y="6"/>
            <line x="13" y="23"/>
            <line x="17" y="23"/>
            <line x="17" y="6"/>
            <close/>
            <move x="14" y="7"/>
            <line x="16" y="7"/>
            <line x="16" y="10"/>
            <line x="14" y="10"/>
            <close/>
            <move x="14" y="11"/>
            <line x="16" y="11"/>
            <line x="16" y="14"/>
            <line x="14" y="14"/>
            <close/>
            <move x="14" y="15"/>
            <line x="16" y="15"/>
            <line x="16" y="18"/>
            <line x="14" y="18"/>
            <close/>
            <move x="14" y="19"/>
            <line x="16" y="19"/>
            <line x="16" y="22"/>
            <line x="14" y="22"/>
            <close/>
            <move x="13" y="80"/>
            <line x="13" y="97"/>
            <line x="17" y="97"/>
            <line x="17" y="80"/>
            <close/>
            <move x="14" y="81"/>
            <line x="16" y="81"/>
            <line x="16" y="84"/>
            <line x="14" y="84"/>
            <close/>
            <move x="14" y="85"/>
            <line x="16" y="85"/>
            <line x="16" y="88"/>
            <line x="14" y="88"/>
            <close/>
            <move x="14" y="89"/>
            <line x="16" y="89"/>
            <line x="16" y="92"/>
            <line x="14" y="92"/>
            <close/>
            <move x="14" y="93"/>
            <line x="16" y="93"/>
            <line x="16" y="96"/>
            <line x="14" y="96"/>
            <close/>
            <move x="13" y="100"/>
            <line x="13" y="117"/>
            <line x="17" y="117"/>
            <line x="17" y="100"/>
            <close/>
            <move x="14" y="101"/>
            <line x="16" y="101"/>
            <line x="16" y="104"/>
            <line x="14" y="104"/>
            <close/>
            <move x="14" y="105"/>
            <line x="16" y="105"/>
            <line x="16" y="108"/>
            <line x="14" y="108"/>
            <close/>
            <move x="14" y="109"/>
            <line x="16" y="109"/>
            <line x="16" y="112"/>
            <line x="14" y="112"/>
            <close/>
            <move x="14" y="113"/>
            <line x="16" y="113"/>
            <line x="16" y="116"/>
            <line x="14" y="116"/>
            <close/>
        </path>
        <fill/>
        <restore/>
        <rect/>
        <stroke/>
        <fillcolor color="#000000"/>
        <fillalpha alpha="0.232"/>
        <roundrect arcsize="18.75" h="42" w="16" x="1.5" y="36.5"/>
        <fill/>
    </foreground>
</shape>
<shape aspect="variable" h="121" name="Sun Blade X3-2B Server" strokewidth="inherit" w="19.5">
    <connections/>
    <background>
        <save/>
        <path>
            <move x="0" y="0"/>
            <line x="0" y="121"/>
            <line x="19.5" y="121"/>
            <line x="19.5" y="0"/>
            <close/>
        </path>
    </background>
    <foreground>
        <fill/>
        <fillcolor color="#000000"/>
        <strokewidth width="1"/>
        <linejoin join="miter"/>
        <linecap cap="butt"/>
        <miterlimit limit="4"/>
        <dashpattern pattern="none"/>
        <dashed dashed="1"/>
        <alpha alpha="1"/>
        <strokealpha alpha="1"/>
        <fillalpha alpha="0.644"/>
        <fontfamily family="sans-serif"/>
        <fontstyle style="0"/>
        <path>
            <move x="11" y="91"/>
            <line x="11" y="119"/>
            <line x="17" y="119"/>
            <line x="17" y="91"/>
            <close/>
            <move x="12" y="92"/>
            <line x="16" y="92"/>
            <line x="16" y="118"/>
            <line x="12" y="118"/>
            <close/>
            <move x="3" y="91"/>
            <line x="3" y="119"/>
            <line x="9" y="119"/>
            <line x="9" y="91"/>
            <close/>
            <move x="4" y="92"/>
            <line x="8" y="92"/>
            <line x="8" y="118"/>
            <line x="4" y="118"/>
            <close/>
            <move x="11" y="2"/>
            <line x="11" y="30"/>
            <line x="17" y="30"/>
            <line x="17" y="2"/>
            <close/>
            <move x="12" y="3"/>
            <line x="16" y="3"/>
            <line x="16" y="29"/>
            <line x="12" y="29"/>
            <close/>
            <move x="3" y="2"/>
            <line x="3" y="30"/>
            <line x="9" y="30"/>
            <line x="9" y="2"/>
            <close/>
            <move x="4" y="3"/>
            <line x="8" y="3"/>
            <line x="8" y="29"/>
            <line x="4" y="29"/>
            <close/>
            <move x="0" y="0"/>
            <line x="0" y="121"/>
            <line x="19.5" y="121"/>
            <line x="19.5" y="0"/>
            <close/>
            <move x="1" y="1"/>
            <line x="18.5" y="1"/>
            <line x="18.5" y="120"/>
            <line x="1" y="120"/>
            <close/>
        </path>
        <fill/>
        <restore/>
        <rect/>
        <stroke/>
        <fillcolor color="#000000"/>
        <fillalpha alpha="0.232"/>
        <path>
            <move x="5.5" y="61.5"/>
            <line x="14" y="61.5"/>
            <curve x1="15.66" x2="17" x3="17" y1="61.5" y2="62.84" y3="64.5"/>
            <line x="17" y="85.5"/>
            <curve x1="17" x2="15.66" x3="14" y1="87.16" y2="88.5" y3="88.5"/>
            <line x="5.5" y="88.5"/>
            <curve x1="3.84" x2="2.5" x3="2.5" y1="88.5" y2="87.16" y3="85.5"/>
            <line x="2.5" y="64.5"/>
            <curve x1="2.5" x2="3.84" x3="5.5" y1="62.84" y2="61.5" y3="61.5"/>
            <close/>
            <move x="5.5" y="32.5"/>
            <line x="14" y="32.5"/>
            <curve x1="15.66" x2="17" x3="17" y1="32.5" y2="33.84" y3="35.5"/>
            <line x="17" y="56.5"/>
            <curve x1="17" x2="15.66" x3="14" y1="58.16" y2="59.5" y3="59.5"/>
            <line x="5.5" y="59.5"/>
            <curve x1="3.84" x2="2.5" x3="2.5" y1="59.5" y2="58.16" y3="56.5"/>
            <line x="2.5" y="35.5"/>
            <curve x1="2.5" x2="3.84" x3="5.5" y1="33.84" y2="32.5" y3="32.5"/>
            <close/>
        </path>
        <fill/>
    </foreground>
</shape>
<shape aspect="variable" h="121" name="Sun Blade X6270 M2 Server Module" strokewidth="inherit" w="19.5">
    <connections/>
    <background>
        <save/>
        <path>
            <move x="0" y="0"/>
            <line x="0" y="121"/>
            <line x="19.5" y="121"/>
            <line x="19.5" y="0"/>
            <close/>
        </path>
    </background>
    <foreground>
        <fill/>
        <fillcolor color="#000000"/>
        <strokewidth width="1"/>
        <linejoin join="miter"/>
        <linecap cap="butt"/>
        <miterlimit limit="4"/>
        <dashpattern pattern="none"/>
        <dashed dashed="1"/>
        <alpha alpha="1"/>
        <strokealpha alpha="1"/>
        <fillalpha alpha="0.644"/>
        <fontfamily family="sans-serif"/>
        <fontstyle style="0"/>
        <path>
            <move x="11" y="91"/>
            <line x="11" y="119"/>
            <line x="17" y="119"/>
            <line x="17" y="91"/>
            <close/>
            <move x="12" y="92"/>
            <line x="16" y="92"/>
            <line x="16" y="118"/>
            <line x="12" y="118"/>
            <close/>
            <move x="3" y="91"/>
            <line x="3" y="119"/>
            <line x="9" y="119"/>
            <line x="9" y="91"/>
            <close/>
            <move x="4" y="92"/>
            <line x="8" y="92"/>
            <line x="8" y="118"/>
            <line x="4" y="118"/>
            <close/>
            <move x="11" y="2"/>
            <line x="11" y="30"/>
            <line x="17" y="30"/>
            <line x="17" y="2"/>
            <close/>
            <move x="12" y="3"/>
            <line x="16" y="3"/>
            <line x="16" y="29"/>
            <line x="12" y="29"/>
            <close/>
            <move x="3" y="2"/>
            <line x="3" y="30"/>
            <line x="9" y="30"/>
            <line x="9" y="2"/>
            <close/>
            <move x="4" y="3"/>
            <line x="8" y="3"/>
            <line x="8" y="29"/>
            <line x="4" y="29"/>
            <close/>
            <move x="0" y="0"/>
            <line x="0" y="121"/>
            <line x="19.5" y="121"/>
            <line x="19.5" y="0"/>
            <close/>
            <move x="1" y="1"/>
            <line x="18.5" y="1"/>
            <line x="18.5" y="120"/>
            <line x="1" y="120"/>
            <close/>
        </path>
        <fill/>
        <restore/>
        <rect/>
        <stroke/>
        <fillcolor color="#000000"/>
        <fillalpha alpha="0.232"/>
        <roundrect arcsize="20.69" h="56" w="14.5" x="2.5" y="32.5"/>
        <fill/>
    </foreground>
</shape>
<shape aspect="variable" h="15.8" name="Sun DataCenter Infiniband Switch 36" strokewidth="inherit" w="161.9">
    <connections/>
    <background>
        <path>
            <move x="0" y="0"/>
            <line x="0" y="15.8"/>
            <line x="161.9" y="15.8"/>
            <line x="161.9" y="0"/>
            <close/>
        </path>
    </background>
    <foreground>
        <fill/>
        <fillcolor color="#000000"/>
        <strokewidth width="1"/>
        <linejoin join="miter"/>
        <linecap cap="butt"/>
        <miterlimit limit="4"/>
        <dashpattern pattern="none"/>
        <dashed dashed="1"/>
        <alpha alpha="1"/>
        <strokealpha alpha="1"/>
        <fillalpha alpha="0.644"/>
        <fontfamily family="sans-serif"/>
        <fontstyle style="0"/>
        <path>
            <move x="0" y="0"/>
            <line x="0" y="15.8"/>
            <line x="161.9" y="15.8"/>
            <line x="161.9" y="0"/>
            <close/>
            <move x="1" y="1"/>
            <line x="6" y="1"/>
            <line x="6" y="14.8"/>
            <line x="1" y="14.8"/>
            <close/>
            <move x="7" y="1"/>
            <line x="155" y="1"/>
            <line x="155" y="14.8"/>
            <line x="7" y="14.8"/>
            <close/>
            <move x="156" y="1"/>
            <line x="160.9" y="1"/>
            <line x="160.9" y="14.8"/>
            <line x="156" y="14.8"/>
            <close/>
            <move x="10" y="3"/>
            <line x="10" y="8"/>
            <line x="32" y="8"/>
            <line x="32" y="3"/>
            <close/>
            <move x="33" y="3"/>
            <line x="33" y="8"/>
            <line x="55" y="8"/>
            <line x="55" y="3"/>
            <close/>
            <move x="56" y="3"/>
            <line x="56" y="8"/>
            <line x="79" y="8"/>
            <line x="79" y="3"/>
            <close/>
            <move x="80" y="3"/>
            <line x="80" y="8"/>
            <line x="102" y="8"/>
            <line x="102" y="3"/>
            <close/>
            <move x="103" y="3"/>
            <line x="103" y="8"/>
            <line x="125" y="8"/>
            <line x="125" y="3"/>
            <close/>
            <move x="126" y="3"/>
            <line x="126" y="8"/>
            <line x="148" y="8"/>
            <line x="148" y="3"/>
            <close/>
            <move x="11" y="4"/>
            <line x="31" y="4"/>
            <line x="31" y="7"/>
            <line x="11" y="7"/>
            <close/>
            <move x="34" y="4"/>
            <line x="54" y="4"/>
            <line x="54" y="7"/>
            <line x="34" y="7"/>
            <close/>
            <move x="57" y="4"/>
            <line x="78" y="4"/>
            <line x="78" y="7"/>
            <line x="57" y="7"/>
            <close/>
            <move x="81" y="4"/>
            <line x="101" y="4"/>
            <line x="101" y="7"/>
            <line x="81" y="7"/>
            <close/>
            <move x="104" y="4"/>
            <line x="124" y="4"/>
            <line x="124" y="7"/>
            <line x="104" y="7"/>
            <close/>
            <move x="127" y="4"/>
            <line x="147" y="4"/>
            <line x="147" y="7"/>
            <line x="127" y="7"/>
            <close/>
            <move x="10" y="9"/>
            <line x="10" y="14"/>
            <line x="32" y="14"/>
            <line x="32" y="9"/>
            <close/>
            <move x="33" y="9"/>
            <line x="33" y="14"/>
            <line x="55" y="14"/>
            <line x="55" y="9"/>
            <close/>
            <move x="56" y="9"/>
            <line x="56" y="14"/>
            <line x="79" y="14"/>
            <line x="79" y="9"/>
            <close/>
            <move x="80" y="9"/>
            <line x="80" y="14"/>
            <line x="102" y="14"/>
            <line x="102" y="9"/>
            <close/>
            <move x="103" y="9"/>
            <line x="103" y="14"/>
            <line x="125" y="14"/>
            <line x="125" y="9"/>
            <close/>
            <move x="126" y="9"/>
            <line x="126" y="14"/>
            <line x="148" y="14"/>
            <line x="148" y="9"/>
            <close/>
            <move x="11" y="10"/>
            <line x="31" y="10"/>
            <line x="31" y="13"/>
            <line x="11" y="13"/>
            <close/>
            <move x="34" y="10"/>
            <line x="54" y="10"/>
            <line x="54" y="13"/>
            <line x="34" y="13"/>
            <close/>
            <move x="57" y="10"/>
            <line x="78" y="10"/>
            <line x="78" y="13"/>
            <line x="57" y="13"/>
            <close/>
            <move x="81" y="10"/>
            <line x="101" y="10"/>
            <line x="101" y="13"/>
            <line x="81" y="13"/>
            <close/>
            <move x="104" y="10"/>
            <line x="124" y="10"/>
            <line x="124" y="13"/>
            <line x="104" y="13"/>
            <close/>
            <move x="127" y="10"/>
            <line x="147" y="10"/>
            <line x="147" y="13"/>
            <line x="127" y="13"/>
            <close/>
        </path>
        <fill/>
    </foreground>
</shape>
<shape aspect="variable" h="15.8" name="Sun Network 10GbE Switch 72p" strokewidth="inherit" w="161.9">
    <connections/>
    <background>
        <path>
            <move x="0" y="0"/>
            <curve x1="0" x2="0" x3="0" y1="5.27" y2="10.53" y3="15.8"/>
            <line x="161.9" y="15.8"/>
            <line x="161.9" y="0"/>
            <close/>
        </path>
    </background>
    <foreground>
        <fill/>
        <fillcolor color="#000000"/>
        <strokewidth width="1"/>
        <linejoin join="miter"/>
        <linecap cap="butt"/>
        <miterlimit limit="4"/>
        <dashpattern pattern="none"/>
        <dashed dashed="1"/>
        <alpha alpha="1"/>
        <strokealpha alpha="1"/>
        <fillalpha alpha="0.644"/>
        <fontfamily family="sans-serif"/>
        <fontstyle style="0"/>
        <path>
            <move x="0" y="0"/>
            <line x="0" y="15.8"/>
            <line x="161.9" y="15.8"/>
            <line x="161.9" y="0"/>
            <close/>
            <move x="1" y="1"/>
            <line x="160.9" y="1"/>
            <line x="160.9" y="14.8"/>
            <line x="1" y="14.8"/>
            <close/>
            <move x="3" y="2"/>
            <line x="3" y="6"/>
            <line x="8" y="6"/>
            <line x="8" y="2"/>
            <close/>
            <move x="10" y="2"/>
            <line x="10" y="6"/>
            <line x="15" y="6"/>
            <line x="15" y="2"/>
            <close/>
            <move x="4" y="3"/>
            <line x="7" y="3"/>
            <line x="7" y="5"/>
            <line x="4" y="5"/>
            <close/>
            <move x="11" y="3"/>
            <line x="14" y="3"/>
            <line x="14" y="5"/>
            <line x="11" y="5"/>
            <close/>
            <move x="17" y="4"/>
            <line x="17" y="8"/>
            <line x="24" y="8"/>
            <line x="24" y="4"/>
            <close/>
            <move x="31" y="4"/>
            <line x="31" y="8"/>
            <line x="38" y="8"/>
            <line x="38" y="4"/>
            <close/>
            <move x="45" y="4"/>
            <line x="45" y="8"/>
            <line x="52" y="8"/>
            <line x="52" y="4"/>
            <close/>
            <move x="59" y="4"/>
            <line x="59" y="8"/>
            <line x="66" y="8"/>
            <line x="66" y="4"/>
            <close/>
            <move x="73" y="4"/>
            <line x="73" y="8"/>
            <line x="80" y="8"/>
            <line x="80" y="4"/>
            <close/>
            <move x="87" y="4"/>
            <line x="87" y="8"/>
            <line x="94" y="8"/>
            <line x="94" y="4"/>
            <close/>
            <move x="101" y="4"/>
            <line x="101" y="8"/>
            <line x="108" y="8"/>
            <line x="108" y="4"/>
            <close/>
            <move x="115" y="4"/>
            <line x="115" y="8"/>
            <line x="122" y="8"/>
            <line x="122" y="4"/>
            <close/>
            <move x="125" y="4"/>
            <line x="125" y="8"/>
            <line x="130" y="8"/>
            <line x="130" y="4"/>
            <close/>
            <move x="132" y="4"/>
            <line x="132" y="8"/>
            <line x="137" y="8"/>
            <line x="137" y="4"/>
            <close/>
            <move x="139" y="4"/>
            <line x="139" y="8"/>
            <line x="144" y="8"/>
            <line x="144" y="4"/>
            <close/>
            <move x="146" y="4"/>
            <line x="146" y="8"/>
            <line x="151" y="8"/>
            <line x="151" y="4"/>
            <close/>
            <move x="153" y="4"/>
            <line x="153" y="8"/>
            <line x="158" y="8"/>
            <line x="158" y="4"/>
            <close/>
            <move x="18" y="5"/>
            <line x="23" y="5"/>
            <line x="23" y="7"/>
            <line x="18" y="7"/>
            <close/>
            <move x="32" y="5"/>
            <line x="37" y="5"/>
            <line x="37" y="7"/>
            <line x="32" y="7"/>
            <close/>
            <move x="46" y="5"/>
            <line x="51" y="5"/>
            <line x="51" y="7"/>
            <line x="46" y="7"/>
            <close/>
            <move x="60" y="5"/>
            <line x="65" y="5"/>
            <line x="65" y="7"/>
            <line x="60" y="7"/>
            <close/>
            <move x="74" y="5"/>
            <line x="79" y="5"/>
            <line x="79" y="7"/>
            <line x="74" y="7"/>
            <close/>
            <move x="88" y="5"/>
            <line x="93" y="5"/>
            <line x="93" y="7"/>
            <line x="88" y="7"/>
            <close/>
            <move x="102" y="5"/>
            <line x="107" y="5"/>
            <line x="107" y="7"/>
            <line x="102" y="7"/>
            <close/>
            <move x="116" y="5"/>
            <line x="121" y="5"/>
            <line x="121" y="7"/>
            <line x="116" y="7"/>
            <close/>
            <move x="126" y="5"/>
            <line x="129" y="5"/>
            <line x="129" y="7"/>
            <line x="126" y="7"/>
            <close/>
            <move x="133" y="5"/>
            <line x="136" y="5"/>
            <line x="136" y="7"/>
            <line x="133" y="7"/>
            <close/>
            <move x="140" y="5"/>
            <line x="143" y="5"/>
            <line x="143" y="7"/>
            <line x="140" y="7"/>
            <close/>
            <move x="147" y="5"/>
            <line x="150" y="5"/>
            <line x="150" y="7"/>
            <line x="147" y="7"/>
            <close/>
            <move x="154" y="5"/>
            <line x="157" y="5"/>
            <line x="157" y="7"/>
            <line x="154" y="7"/>
            <close/>
            <move x="17" y="10"/>
            <line x="17" y="14"/>
            <line x="24" y="14"/>
            <line x="24" y="10"/>
            <close/>
            <move x="31" y="10"/>
            <line x="31" y="14"/>
            <line x="38" y="14"/>
            <line x="38" y="10"/>
            <close/>
            <move x="45" y="10"/>
            <line x="45" y="14"/>
            <line x="52" y="14"/>
            <line x="52" y="10"/>
            <close/>
            <move x="59" y="10"/>
            <line x="59" y="14"/>
            <line x="66" y="14"/>
            <line x="66" y="10"/>
            <close/>
            <move x="73" y="10"/>
            <line x="73" y="14"/>
            <line x="80" y="14"/>
            <line x="80" y="10"/>
            <close/>
            <move x="87" y="10"/>
            <line x="87" y="14"/>
            <line x="94" y="14"/>
            <line x="94" y="10"/>
            <close/>
            <move x="101" y="10"/>
            <line x="101" y="14"/>
            <line x="108" y="14"/>
            <line x="108" y="10"/>
            <close/>
            <move x="115" y="10"/>
            <line x="115" y="14"/>
            <line x="122" y="14"/>
            <line x="122" y="10"/>
            <close/>
            <move x="132" y="10"/>
            <line x="132" y="14"/>
            <line x="137" y="14"/>
            <line x="137" y="10"/>
            <close/>
            <move x="139" y="10"/>
            <line x="139" y="14"/>
            <line x="144" y="14"/>
            <line x="144" y="10"/>
            <close/>
            <move x="146" y="10"/>
            <line x="146" y="14"/>
            <line x="151" y="14"/>
            <line x="151" y="10"/>
            <close/>
            <move x="153" y="10"/>
            <line x="153" y="14"/>
            <line x="158" y="14"/>
            <line x="158" y="10"/>
            <close/>
            <move x="18" y="11"/>
            <line x="23" y="11"/>
            <line x="23" y="13"/>
            <line x="18" y="13"/>
            <close/>
            <move x="32" y="11"/>
            <line x="37" y="11"/>
            <line x="37" y="13"/>
            <line x="32" y="13"/>
            <close/>
            <move x="46" y="11"/>
            <line x="51" y="11"/>
            <line x="51" y="13"/>
            <line x="46" y="13"/>
            <close/>
            <move x="60" y="11"/>
            <line x="65" y="11"/>
            <line x="65" y="13"/>
            <line x="60" y="13"/>
            <close/>
            <move x="74" y="11"/>
            <line x="79" y="11"/>
            <line x="79" y="13"/>
            <line x="74" y="13"/>
            <close/>
            <move x="88" y="11"/>
            <line x="93" y="11"/>
            <line x="93" y="13"/>
            <line x="88" y="13"/>
            <close/>
            <move x="102" y="11"/>
            <line x="107" y="11"/>
            <line x="107" y="13"/>
            <line x="102" y="13"/>
            <close/>
            <move x="116" y="11"/>
            <line x="121" y="11"/>
            <line x="121" y="13"/>
            <line x="116" y="13"/>
            <close/>
            <move x="133" y="11"/>
            <line x="136" y="11"/>
            <line x="136" y="13"/>
            <line x="133" y="13"/>
            <close/>
            <move x="140" y="11"/>
            <line x="143" y="11"/>
            <line x="143" y="13"/>
            <line x="140" y="13"/>
            <close/>
            <move x="147" y="11"/>
            <line x="150" y="11"/>
            <line x="150" y="13"/>
            <line x="147" y="13"/>
            <close/>
            <move x="154" y="11"/>
            <line x="157" y="11"/>
            <line x="157" y="13"/>
            <line x="154" y="13"/>
            <close/>
        </path>
        <fill/>
    </foreground>
</shape>
<shape aspect="variable" h="15.8" name="Sun Network QDR InfiniBand Gateway Switch" strokewidth="inherit" w="161.9">
    <connections/>
    <background>
        <path>
            <move x="0" y="0"/>
            <line x="0" y="15.8"/>
            <line x="161.9" y="15.8"/>
            <line x="161.9" y="0"/>
            <close/>
        </path>
    </background>
    <foreground>
        <fill/>
        <fillcolor color="rgb(0, 0, 0)"/>
        <strokewidth width="1"/>
        <linejoin join="miter"/>
        <linecap cap="butt"/>
        <miterlimit limit="4"/>
        <dashpattern pattern="none"/>
        <dashed dashed="1"/>
        <alpha alpha="1"/>
        <strokealpha alpha="1"/>
        <fillalpha alpha="0.644"/>
        <fontfamily family="sans-serif"/>
        <fontstyle style="0"/>
        <path>
            <move x="0" y="0"/>
            <line x="0" y="15.8"/>
            <line x="161.9" y="15.8"/>
            <line x="161.9" y="0"/>
            <close/>
            <move x="1" y="1"/>
            <line x="160.9" y="1"/>
            <line x="160.9" y="14.8"/>
            <line x="1" y="14.8"/>
            <close/>
            <move x="10" y="2"/>
            <line x="10" y="6"/>
            <line x="15" y="6"/>
            <line x="15" y="2"/>
            <close/>
            <move x="17" y="2"/>
            <line x="17" y="6"/>
            <line x="22" y="6"/>
            <line x="22" y="2"/>
            <close/>
            <move x="11" y="3"/>
            <line x="14" y="3"/>
            <line x="14" y="5"/>
            <line x="11" y="5"/>
            <close/>
            <move x="18" y="3"/>
            <line x="21" y="3"/>
            <line x="21" y="5"/>
            <line x="18" y="5"/>
            <close/>
            <move x="24" y="4"/>
            <line x="24" y="8"/>
            <line x="43" y="8"/>
            <line x="43" y="4"/>
            <close/>
            <move x="46" y="4"/>
            <line x="46" y="8"/>
            <line x="65" y="8"/>
            <line x="65" y="4"/>
            <close/>
            <move x="68" y="4"/>
            <line x="68" y="8"/>
            <line x="87" y="8"/>
            <line x="87" y="4"/>
            <close/>
            <move x="90" y="4"/>
            <line x="90" y="8"/>
            <line x="109" y="8"/>
            <line x="109" y="4"/>
            <close/>
            <move x="112" y="4"/>
            <line x="112" y="8"/>
            <line x="131" y="8"/>
            <line x="131" y="4"/>
            <close/>
            <move x="134" y="4"/>
            <line x="134" y="8"/>
            <line x="153" y="8"/>
            <line x="153" y="4"/>
            <close/>
            <move x="25" y="5"/>
            <line x="30" y="5"/>
            <line x="30" y="7"/>
            <line x="25" y="7"/>
            <close/>
            <move x="31" y="5"/>
            <line x="36" y="5"/>
            <line x="36" y="7"/>
            <line x="31" y="7"/>
            <close/>
            <move x="37" y="5"/>
            <line x="42" y="5"/>
            <line x="42" y="7"/>
            <line x="37" y="7"/>
            <close/>
            <move x="47" y="5"/>
            <line x="52" y="5"/>
            <line x="52" y="7"/>
            <line x="47" y="7"/>
            <close/>
            <move x="53" y="5"/>
            <line x="58" y="5"/>
            <line x="58" y="7"/>
            <line x="53" y="7"/>
            <close/>
            <move x="59" y="5"/>
            <line x="64" y="5"/>
            <line x="64" y="7"/>
            <line x="59" y="7"/>
            <close/>
            <move x="69" y="5"/>
            <line x="74" y="5"/>
            <line x="74" y="7"/>
            <line x="69" y="7"/>
            <close/>
            <move x="75" y="5"/>
            <line x="80" y="5"/>
            <line x="80" y="7"/>
            <line x="75" y="7"/>
            <close/>
            <move x="81" y="5"/>
            <line x="86" y="5"/>
            <line x="86" y="7"/>
            <line x="81" y="7"/>
            <close/>
            <move x="91" y="5"/>
            <line x="96" y="5"/>
            <line x="96" y="7"/>
            <line x="91" y="7"/>
            <close/>
            <move x="97" y="5"/>
            <line x="102" y="5"/>
            <line x="102" y="7"/>
            <line x="97" y="7"/>
            <close/>
            <move x="103" y="5"/>
            <line x="108" y="5"/>
            <line x="108" y="7"/>
            <line x="103" y="7"/>
            <close/>
            <move x="113" y="5"/>
            <line x="118" y="5"/>
            <line x="118" y="7"/>
            <line x="113" y="7"/>
            <close/>
            <move x="119" y="5"/>
            <line x="124" y="5"/>
            <line x="124" y="7"/>
            <line x="119" y="7"/>
            <close/>
            <move x="125" y="5"/>
            <line x="130" y="5"/>
            <line x="130" y="7"/>
            <line x="125" y="7"/>
            <close/>
            <move x="135" y="5"/>
            <line x="140" y="5"/>
            <line x="140" y="7"/>
            <line x="135" y="7"/>
            <close/>
            <move x="141" y="5"/>
            <line x="146" y="5"/>
            <line x="146" y="7"/>
            <line x="141" y="7"/>
            <close/>
            <move x="147" y="5"/>
            <line x="152" y="5"/>
            <line x="152" y="7"/>
            <line x="147" y="7"/>
            <close/>
            <move x="24" y="10"/>
            <line x="24" y="14"/>
            <line x="43" y="14"/>
            <line x="43" y="10"/>
            <close/>
            <move x="46" y="10"/>
            <line x="46" y="14"/>
            <line x="65" y="14"/>
            <line x="65" y="10"/>
            <close/>
            <move x="68" y="10"/>
            <line x="68" y="14"/>
            <line x="87" y="14"/>
            <line x="87" y="10"/>
            <close/>
            <move x="90" y="10"/>
            <line x="90" y="14"/>
            <line x="109" y="14"/>
            <line x="109" y="10"/>
            <close/>
            <move x="112" y="10"/>
            <line x="112" y="14"/>
            <line x="131" y="14"/>
            <line x="131" y="10"/>
            <close/>
            <move x="134" y="10"/>
            <line x="134" y="14"/>
            <line x="153" y="14"/>
            <line x="153" y="10"/>
            <close/>
            <move x="25" y="11"/>
            <line x="30" y="11"/>
            <line x="30" y="13"/>
            <line x="25" y="13"/>
            <close/>
            <move x="31" y="11"/>
            <line x="36" y="11"/>
            <line x="36" y="13"/>
            <line x="31" y="13"/>
            <close/>
            <move x="37" y="11"/>
            <line x="42" y="11"/>
            <line x="42" y="13"/>
            <line x="37" y="13"/>
            <close/>
            <move x="47" y="11"/>
            <line x="52" y="11"/>
            <line x="52" y="13"/>
            <line x="47" y="13"/>
            <close/>
            <move x="53" y="11"/>
            <line x="58" y="11"/>
            <line x="58" y="13"/>
            <line x="53" y="13"/>
            <close/>
            <move x="59" y="11"/>
            <line x="64" y="11"/>
            <line x="64" y="13"/>
            <line x="59" y="13"/>
            <close/>
            <move x="69" y="11"/>
            <line x="74" y="11"/>
            <line x="74" y="13"/>
            <line x="69" y="13"/>
            <close/>
            <move x="75" y="11"/>
            <line x="80" y="11"/>
            <line x="80" y="13"/>
            <line x="75" y="13"/>
            <close/>
            <move x="81" y="11"/>
            <line x="86" y="11"/>
            <line x="86" y="13"/>
            <line x="81" y="13"/>
            <close/>
            <move x="91" y="11"/>
            <line x="96" y="11"/>
            <line x="96" y="13"/>
            <line x="91" y="13"/>
            <close/>
            <move x="97" y="11"/>
            <line x="102" y="11"/>
            <line x="102" y="13"/>
            <line x="97" y="13"/>
            <close/>
            <move x="103" y="11"/>
            <line x="108" y="11"/>
            <line x="108" y="13"/>
            <line x="103" y="13"/>
            <close/>
            <move x="113" y="11"/>
            <line x="118" y="11"/>
            <line x="118" y="13"/>
            <line x="113" y="13"/>
            <close/>
            <move x="119" y="11"/>
            <line x="124" y="11"/>
            <line x="124" y="13"/>
            <line x="119" y="13"/>
            <close/>
            <move x="125" y="11"/>
            <line x="130" y="11"/>
            <line x="130" y="13"/>
            <line x="125" y="13"/>
            <close/>
            <move x="135" y="11"/>
            <line x="140" y="11"/>
            <line x="140" y="13"/>
            <line x="135" y="13"/>
            <close/>
            <move x="141" y="11"/>
            <line x="146" y="11"/>
            <line x="146" y="13"/>
            <line x="141" y="13"/>
            <close/>
            <move x="147" y="11"/>
            <line x="152" y="11"/>
            <line x="152" y="13"/>
            <line x="147" y="13"/>
            <close/>
        </path>
        <fill/>
    </foreground>
</shape>
<shape aspect="variable" h="45.4" name="Sun Server X2-4" strokewidth="inherit" w="161.9">
    <connections/>
    <background>
        <save/>
        <path>
            <move x="0" y="0"/>
            <line x="0" y="11"/>
            <line x="8" y="11"/>
            <line x="8" y="34.4"/>
            <line x="0" y="34.4"/>
            <line x="0" y="45.4"/>
            <line x="161.9" y="45.4"/>
            <line x="161.9" y="34.4"/>
            <line x="153.9" y="34.4"/>
            <line x="153.9" y="10.91"/>
            <line x="161.9" y="10.91"/>
            <line x="161.9" y="0"/>
            <close/>
        </path>
    </background>
    <foreground>
        <fill/>
        <fillcolor color="#000000"/>
        <strokewidth width="1"/>
        <linejoin join="miter"/>
        <linecap cap="butt"/>
        <miterlimit limit="4"/>
        <dashpattern pattern="none"/>
        <dashed dashed="1"/>
        <alpha alpha="1"/>
        <strokealpha alpha="1"/>
        <fillalpha alpha="0.644"/>
        <fontfamily family="sans-serif"/>
        <fontstyle style="0"/>
        <path>
            <move x="0" y="0"/>
            <line x="0" y="11"/>
            <line x="8" y="11"/>
            <line x="8" y="34.4"/>
            <line x="0" y="34.4"/>
            <line x="0" y="45.4"/>
            <line x="161.9" y="45.4"/>
            <line x="161.9" y="34.4"/>
            <line x="153.9" y="34.4"/>
            <line x="153.9" y="10.91"/>
            <line x="161.9" y="10.91"/>
            <line x="161.9" y="0"/>
            <close/>
            <move x="1" y="1"/>
            <line x="160.9" y="1"/>
            <line x="160.9" y="9.89"/>
            <line x="152.9" y="9.89"/>
            <line x="152.9" y="35.4"/>
            <line x="160.9" y="35.4"/>
            <line x="160.9" y="44.4"/>
            <line x="157" y="44.4"/>
            <line x="157" y="39.5"/>
            <line x="100" y="39.5"/>
            <line x="100" y="44.4"/>
            <line x="1" y="44.4"/>
            <line x="1" y="35.4"/>
            <line x="9" y="35.4"/>
            <line x="9" y="10"/>
            <line x="1" y="10"/>
            <close/>
            <move x="124" y="3"/>
            <line x="124" y="39"/>
            <line x="152" y="39"/>
            <line x="152" y="3"/>
            <close/>
            <move x="125" y="4"/>
            <line x="151" y="4"/>
            <line x="151" y="8"/>
            <line x="125" y="8"/>
            <close/>
            <move x="125" y="10"/>
            <line x="151" y="10"/>
            <line x="151" y="14"/>
            <line x="125" y="14"/>
            <close/>
            <move x="125" y="16"/>
            <line x="151" y="16"/>
            <line x="151" y="20"/>
            <line x="125" y="20"/>
            <close/>
            <move x="125" y="22"/>
            <line x="151" y="22"/>
            <line x="151" y="26"/>
            <line x="125" y="26"/>
            <close/>
            <move x="125" y="28"/>
            <line x="151" y="28"/>
            <line x="151" y="32"/>
            <line x="125" y="32"/>
            <close/>
            <move x="125" y="34"/>
            <line x="151" y="34"/>
            <line x="151" y="38"/>
            <line x="125" y="38"/>
            <close/>
            <move x="101" y="40.5"/>
            <line x="156" y="40.5"/>
            <line x="156" y="44.4"/>
            <line x="101" y="44.4"/>
            <close/>
        </path>
        <fill/>
        <restore/>
        <rect/>
        <stroke/>
        <fillcolor color="#000000"/>
        <fillalpha alpha="0.232"/>
        <roundrect arcsize="8.82" h="34" w="103" x="10.5" y="4.5"/>
        <fill/>
    </foreground>
</shape>
<shape aspect="variable" h="75" name="Sun Server X2-8" strokewidth="inherit" w="161.9">
    <connections/>
    <background>
        <save/>
        <path>
            <move x="0" y="0"/>
            <line x="0" y="75"/>
            <line x="161.9" y="75"/>
            <line x="161.9" y="0"/>
            <close/>
        </path>
    </background>
    <foreground>
        <fill/>
        <fillcolor color="#000000"/>
        <strokewidth width="1"/>
        <linejoin join="miter"/>
        <linecap cap="butt"/>
        <miterlimit limit="4"/>
        <dashpattern pattern="none"/>
        <dashed dashed="1"/>
        <alpha alpha="1"/>
        <strokealpha alpha="1"/>
        <fillalpha alpha="0.644"/>
        <fontfamily family="sans-serif"/>
        <fontstyle style="0"/>
        <path>
            <move x="0" y="0"/>
            <line x="0" y="75"/>
            <line x="161.9" y="75"/>
            <line x="161.9" y="0"/>
            <close/>
            <move x="1" y="1"/>
            <line x="6" y="1"/>
            <line x="6" y="74"/>
            <line x="1" y="74"/>
            <close/>
            <move x="7" y="1"/>
            <line x="37" y="1"/>
            <line x="37" y="4"/>
            <line x="7" y="4"/>
            <close/>
            <move x="38" y="1"/>
            <line x="155" y="1"/>
            <line x="155" y="14.8"/>
            <line x="38" y="14.8"/>
            <close/>
            <move x="156" y="1"/>
            <line x="160.9" y="1"/>
            <line x="160.9" y="74"/>
            <line x="156" y="74"/>
            <close/>
            <move x="7" y="5"/>
            <line x="37" y="5"/>
            <line x="37" y="20"/>
            <line x="7" y="20"/>
            <close/>
            <move x="38" y="15.8"/>
            <line x="155" y="15.8"/>
            <line x="155" y="29.6"/>
            <line x="38" y="29.6"/>
            <close/>
            <move x="7" y="21"/>
            <line x="37" y="21"/>
            <line x="37" y="36"/>
            <line x="7" y="36"/>
            <close/>
            <move x="38" y="30.6"/>
            <line x="155" y="30.6"/>
            <line x="155" y="44.4"/>
            <line x="38" y="44.4"/>
            <close/>
            <move x="7" y="37"/>
            <line x="37" y="37"/>
            <line x="37" y="52"/>
            <line x="7" y="52"/>
            <close/>
            <move x="38" y="45.4"/>
            <line x="155" y="45.4"/>
            <line x="155" y="59.2"/>
            <line x="38" y="59.2"/>
            <close/>
            <move x="7" y="53"/>
            <line x="37" y="53"/>
            <line x="37" y="68"/>
            <line x="7" y="68"/>
            <close/>
            <move x="38" y="60.2"/>
            <line x="155" y="60.2"/>
            <line x="155" y="74"/>
            <line x="38" y="74"/>
            <close/>
            <move x="39" y="61.4"/>
            <line x="39" y="67.4"/>
            <line x="67" y="67.4"/>
            <line x="67" y="61.4"/>
            <close/>
            <move x="68" y="61.4"/>
            <line x="68" y="67.4"/>
            <line x="96" y="67.4"/>
            <line x="96" y="61.4"/>
            <close/>
            <move x="97" y="61.4"/>
            <line x="97" y="67.4"/>
            <line x="125" y="67.4"/>
            <line x="125" y="61.4"/>
            <close/>
            <move x="126" y="61.4"/>
            <line x="126" y="67.4"/>
            <line x="154" y="67.4"/>
            <line x="154" y="61.4"/>
            <close/>
            <move x="40" y="62.4"/>
            <line x="66" y="62.4"/>
            <line x="66" y="66.4"/>
            <line x="40" y="66.4"/>
            <close/>
            <move x="69" y="62.4"/>
            <line x="95" y="62.4"/>
            <line x="95" y="66.4"/>
            <line x="69" y="66.4"/>
            <close/>
            <move x="98" y="62.4"/>
            <line x="124" y="62.4"/>
            <line x="124" y="66.4"/>
            <line x="98" y="66.4"/>
            <close/>
            <move x="127" y="62.4"/>
            <line x="153" y="62.4"/>
            <line x="153" y="66.4"/>
            <line x="127" y="66.4"/>
            <close/>
            <move x="39" y="67.9"/>
            <line x="39" y="73.9"/>
            <line x="67" y="73.9"/>
            <line x="67" y="67.9"/>
            <close/>
            <move x="68" y="67.9"/>
            <line x="68" y="73.9"/>
            <line x="96" y="73.9"/>
            <line x="96" y="67.9"/>
            <close/>
            <move x="97" y="67.9"/>
            <line x="97" y="73.9"/>
            <line x="125" y="73.9"/>
            <line x="125" y="67.9"/>
            <close/>
            <move x="126" y="67.9"/>
            <line x="126" y="73.9"/>
            <line x="154" y="73.9"/>
            <line x="154" y="67.9"/>
            <close/>
            <move x="40" y="68.9"/>
            <line x="66" y="68.9"/>
            <line x="66" y="72.9"/>
            <line x="40" y="72.9"/>
            <close/>
            <move x="69" y="68.9"/>
            <line x="95" y="68.9"/>
            <line x="95" y="72.9"/>
            <line x="69" y="72.9"/>
            <close/>
            <move x="98" y="68.9"/>
            <line x="124" y="68.9"/>
            <line x="124" y="72.9"/>
            <line x="98" y="72.9"/>
            <close/>
            <move x="127" y="68.9"/>
            <line x="153" y="68.9"/>
            <line x="153" y="72.9"/>
            <line x="127" y="72.9"/>
            <close/>
            <move x="7" y="69"/>
            <line x="37" y="69"/>
            <line x="37" y="74"/>
            <line x="7" y="74"/>
            <close/>
        </path>
        <fill/>
        <restore/>
        <rect/>
        <stroke/>
        <fillcolor color="#000000"/>
        <fillalpha alpha="0.232"/>
        <path>
            <move x="42.5" y="46.4"/>
            <line x="150.5" y="46.4"/>
            <curve x1="152.16" x2="153.5" x3="153.5" y1="46.4" y2="47.74" y3="49.4"/>
            <line x="153.5" y="55.4"/>
            <curve x1="153.5" x2="152.16" x3="150.5" y1="57.06" y2="58.4" y3="58.4"/>
            <line x="42.5" y="58.4"/>
            <curve x1="40.84" x2="39.5" x3="39.5" y1="58.4" y2="57.06" y3="55.4"/>
            <line x="39.5" y="49.4"/>
            <curve x1="39.5" x2="40.84" x3="42.5" y1="47.74" y2="46.4" y3="46.4"/>
            <close/>
            <move x="42.5" y="31.6"/>
            <line x="150.5" y="31.6"/>
            <curve x1="152.16" x2="153.5" x3="153.5" y1="31.6" y2="32.94" y3="34.6"/>
            <line x="153.5" y="40.6"/>
            <curve x1="153.5" x2="152.16" x3="150.5" y1="42.26" y2="43.6" y3="43.6"/>
            <line x="42.5" y="43.6"/>
            <curve x1="40.84" x2="39.5" x3="39.5" y1="43.6" y2="42.26" y3="40.6"/>
            <line x="39.5" y="34.6"/>
            <curve x1="39.5" x2="40.84" x3="42.5" y1="32.94" y2="31.6" y3="31.6"/>
            <close/>
            <move x="42.5" y="16.8"/>
            <line x="150.5" y="16.8"/>
            <curve x1="152.16" x2="153.5" x3="153.5" y1="16.8" y2="18.14" y3="19.8"/>
            <line x="153.5" y="25.8"/>
            <curve x1="153.5" x2="152.16" x3="150.5" y1="27.46" y2="28.8" y3="28.8"/>
            <line x="42.5" y="28.8"/>
            <curve x1="40.84" x2="39.5" x3="39.5" y1="28.8" y2="27.46" y3="25.8"/>
            <line x="39.5" y="19.8"/>
            <curve x1="39.5" x2="40.84" x3="42.5" y1="18.14" y2="16.8" y3="16.8"/>
            <close/>
            <move x="42.5" y="2"/>
            <line x="150.5" y="2"/>
            <curve x1="152.16" x2="153.5" x3="153.5" y1="2" y2="3.34" y3="5"/>
            <line x="153.5" y="11"/>
            <curve x1="153.5" x2="152.16" x3="150.5" y1="12.66" y2="14" y3="14"/>
            <line x="42.5" y="14"/>
            <curve x1="40.84" x2="39.5" x3="39.5" y1="14" y2="12.66" y3="11"/>
            <line x="39.5" y="5"/>
            <curve x1="39.5" x2="40.84" x3="42.5" y1="3.34" y2="2" y3="2"/>
            <close/>
            <move x="13.5" y="54.5"/>
            <line x="33.5" y="54.5"/>
            <curve x1="35.16" x2="36.5" x3="36.5" y1="54.5" y2="55.84" y3="57.5"/>
            <line x="36.5" y="63.5"/>
            <curve x1="36.5" x2="35.16" x3="33.5" y1="65.16" y2="66.5" y3="66.5"/>
            <line x="13.5" y="66.5"/>
            <curve x1="11.84" x2="10.5" x3="10.5" y1="66.5" y2="65.16" y3="63.5"/>
            <line x="10.5" y="57.5"/>
            <curve x1="10.5" x2="11.84" x3="13.5" y1="55.84" y2="54.5" y3="54.5"/>
            <close/>
            <move x="13.5" y="38.5"/>
            <line x="33.5" y="38.5"/>
            <curve x1="35.16" x2="36.5" x3="36.5" y1="38.5" y2="39.84" y3="41.5"/>
            <line x="36.5" y="47.5"/>
            <curve x1="36.5" x2="35.16" x3="33.5" y1="49.16" y2="50.5" y3="50.5"/>
            <line x="13.5" y="50.5"/>
            <curve x1="11.84" x2="10.5" x3="10.5" y1="50.5" y2="49.16" y3="47.5"/>
            <line x="10.5" y="41.5"/>
            <curve x1="10.5" x2="11.84" x3="13.5" y1="39.84" y2="38.5" y3="38.5"/>
            <close/>
            <move x="13.5" y="22.5"/>
            <line x="33.5" y="22.5"/>
            <curve x1="35.16" x2="36.5" x3="36.5" y1="22.5" y2="23.84" y3="25.5"/>
            <line x="36.5" y="31.5"/>
            <curve x1="36.5" x2="35.16" x3="33.5" y1="33.16" y2="34.5" y3="34.5"/>
            <line x="13.5" y="34.5"/>
            <curve x1="11.84" x2="10.5" x3="10.5" y1="34.5" y2="33.16" y3="31.5"/>
            <line x="10.5" y="25.5"/>
            <curve x1="10.5" x2="11.84" x3="13.5" y1="23.84" y2="22.5" y3="22.5"/>
            <close/>
            <move x="13.5" y="6.5"/>
            <line x="33.5" y="6.5"/>
            <curve x1="35.16" x2="36.5" x3="36.5" y1="6.5" y2="7.84" y3="9.5"/>
            <line x="36.5" y="15.5"/>
            <curve x1="36.5" x2="35.16" x3="33.5" y1="17.16" y2="18.5" y3="18.5"/>
            <line x="13.5" y="18.5"/>
            <curve x1="11.84" x2="10.5" x3="10.5" y1="18.5" y2="17.16" y3="15.5"/>
            <line x="10.5" y="9.5"/>
            <curve x1="10.5" x2="11.84" x3="13.5" y1="7.84" y2="6.5" y3="6.5"/>
            <close/>
        </path>
        <fill/>
    </foreground>
</shape>
<shape aspect="variable" h="15.8" name="Sun Server X3-2" strokewidth="inherit" w="161.9">
    <connections/>
    <background>
        <save/>
        <path>
            <move x="0" y="0"/>
            <line x="0" y="15.8"/>
            <line x="161.9" y="15.8"/>
            <line x="161.9" y="0"/>
            <close/>
        </path>
    </background>
    <foreground>
        <fill/>
        <fillcolor color="#000000"/>
        <strokewidth width="1"/>
        <linejoin join="miter"/>
        <linecap cap="butt"/>
        <miterlimit limit="4"/>
        <dashpattern pattern="none"/>
        <dashed dashed="1"/>
        <alpha alpha="1"/>
        <strokealpha alpha="1"/>
        <fillalpha alpha="0.644"/>
        <fontfamily family="sans-serif"/>
        <fontstyle style="0"/>
        <path>
            <move x="0" y="0"/>
            <line x="0" y="15.8"/>
            <line x="161.9" y="15.8"/>
            <line x="161.9" y="0"/>
            <close/>
            <move x="1" y="1"/>
            <line x="6" y="1"/>
            <line x="6" y="14.8"/>
            <line x="1" y="14.8"/>
            <close/>
            <move x="7" y="1"/>
            <line x="155" y="1"/>
            <line x="155" y="14.8"/>
            <line x="7" y="14.8"/>
            <close/>
            <move x="156" y="1"/>
            <line x="160.9" y="1"/>
            <line x="160.9" y="14.8"/>
            <line x="156" y="14.8"/>
            <close/>
            <move x="108" y="2"/>
            <line x="108" y="8"/>
            <line x="154" y="8"/>
            <line x="154" y="2"/>
            <close/>
            <move x="109" y="3"/>
            <line x="153" y="3"/>
            <line x="153" y="7"/>
            <line x="109" y="7"/>
            <close/>
            <move x="10" y="8.7"/>
            <line x="10" y="14.7"/>
            <line x="38" y="14.7"/>
            <line x="38" y="8.7"/>
            <close/>
            <move x="39" y="8.7"/>
            <line x="39" y="14.7"/>
            <line x="67" y="14.7"/>
            <line x="67" y="8.7"/>
            <close/>
            <move x="68" y="8.7"/>
            <line x="68" y="14.7"/>
            <line x="96" y="14.7"/>
            <line x="96" y="8.7"/>
            <close/>
            <move x="97" y="8.7"/>
            <line x="97" y="14.7"/>
            <line x="125" y="14.7"/>
            <line x="125" y="8.7"/>
            <close/>
            <move x="126" y="8.7"/>
            <line x="126" y="14.7"/>
            <line x="154" y="14.7"/>
            <line x="154" y="8.7"/>
            <close/>
            <move x="11" y="9.7"/>
            <line x="37" y="9.7"/>
            <line x="37" y="13.7"/>
            <line x="11" y="13.7"/>
            <close/>
            <move x="40" y="9.7"/>
            <line x="66" y="9.7"/>
            <line x="66" y="13.7"/>
            <line x="40" y="13.7"/>
            <close/>
            <move x="69" y="9.7"/>
            <line x="95" y="9.7"/>
            <line x="95" y="13.7"/>
            <line x="69" y="13.7"/>
            <close/>
            <move x="98" y="9.7"/>
            <line x="124" y="9.7"/>
            <line x="124" y="13.7"/>
            <line x="98" y="13.7"/>
            <close/>
            <move x="127" y="9.7"/>
            <line x="153" y="9.7"/>
            <line x="153" y="13.7"/>
            <line x="127" y="13.7"/>
            <close/>
        </path>
        <fill/>
        <restore/>
        <rect/>
        <stroke/>
        <fillcolor color="#000000"/>
        <fillalpha alpha="0.232"/>
        <roundrect arcsize="50" h="5" w="95" x="11.5" y="2.5"/>
        <fill/>
    </foreground>
</shape>
<shape aspect="variable" h="30.6" name="Sun Server X3-2L" strokewidth="inherit" w="161.9">
    <connections/>
    <background>
        <save/>
        <path>
            <move x="0" y="0"/>
            <line x="0" y="0.5"/>
            <line x="0" y="30.6"/>
            <line x="161.9" y="30.6"/>
            <line x="161.9" y="0"/>
            <close/>
        </path>
    </background>
    <foreground>
        <fill/>
        <fillcolor color="#000000"/>
        <strokewidth width="1"/>
        <linejoin join="miter"/>
        <linecap cap="butt"/>
        <miterlimit limit="4"/>
        <dashpattern pattern="none"/>
        <dashed dashed="1"/>
        <alpha alpha="1"/>
        <strokealpha alpha="1"/>
        <fillalpha alpha="0.644"/>
        <fontfamily family="sans-serif"/>
        <fontstyle style="0"/>
        <path>
            <move x="0" y="0"/>
            <line x="0" y="30.6"/>
            <line x="161.9" y="30.6"/>
            <line x="161.9" y="0"/>
            <close/>
            <move x="1" y="1"/>
            <line x="6" y="1"/>
            <line x="6" y="29.6"/>
            <line x="1" y="29.6"/>
            <close/>
            <move x="7" y="1"/>
            <line x="155" y="1"/>
            <line x="155" y="29.6"/>
            <line x="7" y="29.6"/>
            <close/>
            <move x="156" y="1"/>
            <line x="160.9" y="1"/>
            <line x="160.9" y="29.6"/>
            <line x="156" y="29.6"/>
            <close/>
            <move x="95" y="9.6"/>
            <line x="95" y="15.6"/>
            <line x="152" y="15.6"/>
            <line x="152" y="9.6"/>
            <close/>
            <move x="96" y="10.6"/>
            <line x="151" y="10.6"/>
            <line x="151" y="14.6"/>
            <line x="96" y="14.6"/>
            <close/>
            <move x="8" y="17"/>
            <line x="8" y="23"/>
            <line x="36" y="23"/>
            <line x="36" y="17"/>
            <close/>
            <move x="37" y="17"/>
            <line x="37" y="23"/>
            <line x="65" y="23"/>
            <line x="65" y="17"/>
            <close/>
            <move x="66" y="17"/>
            <line x="66" y="23"/>
            <line x="94" y="23"/>
            <line x="94" y="17"/>
            <close/>
            <move x="95" y="17"/>
            <line x="95" y="23"/>
            <line x="123" y="23"/>
            <line x="123" y="17"/>
            <close/>
            <move x="124" y="17"/>
            <line x="124" y="23"/>
            <line x="152" y="23"/>
            <line x="152" y="17"/>
            <close/>
            <move x="9" y="18"/>
            <line x="35" y="18"/>
            <line x="35" y="22"/>
            <line x="9" y="22"/>
            <close/>
            <move x="38" y="18"/>
            <line x="64" y="18"/>
            <line x="64" y="22"/>
            <line x="38" y="22"/>
            <close/>
            <move x="67" y="18"/>
            <line x="93" y="18"/>
            <line x="93" y="22"/>
            <line x="67" y="22"/>
            <close/>
            <move x="96" y="18"/>
            <line x="122" y="18"/>
            <line x="122" y="22"/>
            <line x="96" y="22"/>
            <close/>
            <move x="125" y="18"/>
            <line x="151" y="18"/>
            <line x="151" y="22"/>
            <line x="125" y="22"/>
            <close/>
            <move x="8" y="23.5"/>
            <line x="8" y="29.5"/>
            <line x="36" y="29.5"/>
            <line x="36" y="23.5"/>
            <close/>
            <move x="37" y="23.5"/>
            <line x="37" y="29.5"/>
            <line x="65" y="29.5"/>
            <line x="65" y="23.5"/>
            <close/>
            <move x="66" y="23.5"/>
            <line x="66" y="29.5"/>
            <line x="94" y="29.5"/>
            <line x="94" y="23.5"/>
            <close/>
            <move x="95" y="23.5"/>
            <line x="95" y="29.5"/>
            <line x="123" y="29.5"/>
            <line x="123" y="23.5"/>
            <close/>
            <move x="124" y="23.5"/>
            <line x="124" y="29.5"/>
            <line x="152" y="29.5"/>
            <line x="152" y="23.5"/>
            <close/>
            <move x="9" y="24.5"/>
            <line x="35" y="24.5"/>
            <line x="35" y="28.5"/>
            <line x="9" y="28.5"/>
            <close/>
            <move x="38" y="24.5"/>
            <line x="64" y="24.5"/>
            <line x="64" y="28.5"/>
            <line x="38" y="28.5"/>
            <close/>
            <move x="67" y="24.5"/>
            <line x="93" y="24.5"/>
            <line x="93" y="28.5"/>
            <line x="67" y="28.5"/>
            <close/>
            <move x="96" y="24.5"/>
            <line x="122" y="24.5"/>
            <line x="122" y="28.5"/>
            <line x="96" y="28.5"/>
            <close/>
            <move x="125" y="24.5"/>
            <line x="151" y="24.5"/>
            <line x="151" y="28.5"/>
            <line x="125" y="28.5"/>
            <close/>
        </path>
        <fill/>
        <restore/>
        <rect/>
        <stroke/>
        <fillcolor color="#000000"/>
        <fillalpha alpha="0.232"/>
        <path>
            <move x="9.5" y="5.5"/>
            <arc large-arc-flag="0" rx="3" ry="3" sweep-flag="1" x="12.5" x-axis-rotation="0" y="2.5"/>
            <line x="149.5" y="2.5"/>
            <arc large-arc-flag="0" rx="3" ry="3" sweep-flag="1" x="152.5" x-axis-rotation="0" y="5.5"/>
            <line x="152.5" y="5.5"/>
            <arc large-arc-flag="0" rx="3" ry="3" sweep-flag="1" x="149.5" x-axis-rotation="0" y="8.5"/>
            <line x="96.5" y="8.5"/>
            <arc large-arc-flag="0" rx="3" ry="3" sweep-flag="0" x="93.5" x-axis-rotation="0" y="11.5"/>
            <line x="93.5" y="12.5"/>
            <arc large-arc-flag="0" rx="3" ry="3" sweep-flag="1" x="90.5" x-axis-rotation="0" y="15.5"/>
            <line x="12.5" y="15.5"/>
            <arc large-arc-flag="0" rx="3" ry="3" sweep-flag="1" x="9.5" x-axis-rotation="0" y="12.5"/>
            <close/>
        </path>
        <fill/>
    </foreground>
</shape>
<shape aspect="variable" h="30.6" name="Sun Storage 2500-M2 Array" strokewidth="inherit" w="161.9">
    <connections/>
    <background>
        <path>
            <move x="0" y="0"/>
            <line x="0" y="30.6"/>
            <line x="161.9" y="30.6"/>
            <line x="161.9" y="0"/>
            <close/>
        </path>
    </background>
    <foreground>
        <fill/>
        <fillcolor color="#000000"/>
        <strokewidth width="1"/>
        <linejoin join="miter"/>
        <linecap cap="butt"/>
        <miterlimit limit="4"/>
        <dashpattern pattern="none"/>
        <dashed dashed="1"/>
        <alpha alpha="1"/>
        <strokealpha alpha="1"/>
        <fillalpha alpha="0.644"/>
        <fontfamily family="sans-serif"/>
        <fontstyle style="0"/>
        <path>
            <move x="0" y="0"/>
            <line x="0" y="30.6"/>
            <line x="161.9" y="30.6"/>
            <line x="161.9" y="0"/>
            <close/>
            <move x="1" y="1"/>
            <line x="6" y="1"/>
            <line x="6" y="29.6"/>
            <line x="1" y="29.6"/>
            <close/>
            <move x="7" y="1"/>
            <line x="155" y="1"/>
            <line x="155" y="29.6"/>
            <line x="7" y="29.6"/>
            <close/>
            <move x="156" y="1"/>
            <line x="160.9" y="1"/>
            <line x="160.9" y="29.6"/>
            <line x="156" y="29.6"/>
            <close/>
            <move x="8" y="2"/>
            <line x="8" y="10"/>
            <line x="44" y="10"/>
            <line x="44" y="2"/>
            <close/>
            <move x="45" y="2"/>
            <line x="45" y="10"/>
            <line x="81" y="10"/>
            <line x="81" y="2"/>
            <close/>
            <move x="82" y="2"/>
            <line x="82" y="10"/>
            <line x="118" y="10"/>
            <line x="118" y="2"/>
            <close/>
            <move x="119" y="2"/>
            <line x="119" y="10"/>
            <line x="154" y="10"/>
            <line x="154" y="2"/>
            <close/>
            <move x="9" y="3"/>
            <line x="43" y="3"/>
            <line x="43" y="9"/>
            <line x="9" y="9"/>
            <close/>
            <move x="46" y="3"/>
            <line x="80" y="3"/>
            <line x="80" y="9"/>
            <line x="46" y="9"/>
            <close/>
            <move x="83" y="3"/>
            <line x="117" y="3"/>
            <line x="117" y="9"/>
            <line x="83" y="9"/>
            <close/>
            <move x="120" y="3"/>
            <line x="153" y="3"/>
            <line x="153" y="9"/>
            <line x="120" y="9"/>
            <close/>
            <move x="8" y="11"/>
            <line x="8" y="19"/>
            <line x="44" y="19"/>
            <line x="44" y="11"/>
            <close/>
            <move x="45" y="11"/>
            <line x="45" y="19"/>
            <line x="81" y="19"/>
            <line x="81" y="11"/>
            <close/>
            <move x="82" y="11"/>
            <line x="82" y="19"/>
            <line x="118" y="19"/>
            <line x="118" y="11"/>
            <close/>
            <move x="119" y="11"/>
            <line x="119" y="19"/>
            <line x="154" y="19"/>
            <line x="154" y="11"/>
            <close/>
            <move x="9" y="12"/>
            <line x="43" y="12"/>
            <line x="43" y="18"/>
            <line x="9" y="18"/>
            <close/>
            <move x="46" y="12"/>
            <line x="80" y="12"/>
            <line x="80" y="18"/>
            <line x="46" y="18"/>
            <close/>
            <move x="83" y="12"/>
            <line x="117" y="12"/>
            <line x="117" y="18"/>
            <line x="83" y="18"/>
            <close/>
            <move x="120" y="12"/>
            <line x="153" y="12"/>
            <line x="153" y="18"/>
            <line x="120" y="18"/>
            <close/>
            <move x="8" y="20"/>
            <line x="8" y="28"/>
            <line x="44" y="28"/>
            <line x="44" y="20"/>
            <close/>
            <move x="45" y="20"/>
            <line x="45" y="28"/>
            <line x="81" y="28"/>
            <line x="81" y="20"/>
            <close/>
            <move x="82" y="20"/>
            <line x="82" y="28"/>
            <line x="118" y="28"/>
            <line x="118" y="20"/>
            <close/>
            <move x="119" y="20"/>
            <line x="119" y="28"/>
            <line x="154" y="28"/>
            <line x="154" y="20"/>
            <close/>
            <move x="9" y="21"/>
            <line x="43" y="21"/>
            <line x="43" y="27"/>
            <line x="9" y="27"/>
            <close/>
            <move x="46" y="21"/>
            <line x="80" y="21"/>
            <line x="80" y="27"/>
            <line x="46" y="27"/>
            <close/>
            <move x="83" y="21"/>
            <line x="117" y="21"/>
            <line x="117" y="27"/>
            <line x="83" y="27"/>
            <close/>
            <move x="120" y="21"/>
            <line x="153" y="21"/>
            <line x="153" y="27"/>
            <line x="120" y="27"/>
            <close/>
        </path>
        <fill/>
    </foreground>
</shape>
<shape aspect="variable" h="30.6" name="Sun ZFS Storage 7120" strokewidth="inherit" w="161.9">
    <connections/>
    <background>
        <path>
            <move x="0" y="0"/>
            <line x="0" y="30.6"/>
            <line x="161.9" y="30.6"/>
            <line x="161.9" y="0"/>
            <close/>
        </path>
    </background>
    <foreground>
        <fill/>
        <fillcolor color="#000000"/>
        <strokewidth width="1"/>
        <linejoin join="miter"/>
        <linecap cap="butt"/>
        <miterlimit limit="4"/>
        <dashpattern pattern="none"/>
        <dashed dashed="1"/>
        <alpha alpha="1"/>
        <strokealpha alpha="1"/>
        <fillalpha alpha="0.644"/>
        <fontfamily family="sans-serif"/>
        <fontstyle style="0"/>
        <path>
            <move x="0" y="0"/>
            <line x="0" y="30.6"/>
            <line x="161.9" y="30.6"/>
            <line x="161.9" y="0"/>
            <close/>
            <move x="1" y="1"/>
            <line x="6" y="1"/>
            <line x="6" y="29.6"/>
            <line x="1" y="29.6"/>
            <close/>
            <move x="7" y="1"/>
            <line x="155" y="1"/>
            <line x="155" y="29.6"/>
            <line x="7" y="29.6"/>
            <close/>
            <move x="156" y="1"/>
            <line x="160.9" y="1"/>
            <line x="160.9" y="29.6"/>
            <line x="156" y="29.6"/>
            <close/>
            <move x="8" y="2"/>
            <line x="8" y="10"/>
            <line x="44" y="10"/>
            <line x="44" y="2"/>
            <close/>
            <move x="45" y="2"/>
            <line x="45" y="10"/>
            <line x="81" y="10"/>
            <line x="81" y="2"/>
            <close/>
            <move x="82" y="2"/>
            <line x="82" y="10"/>
            <line x="118" y="10"/>
            <line x="118" y="2"/>
            <close/>
            <move x="119" y="2"/>
            <line x="119" y="10"/>
            <line x="154" y="10"/>
            <line x="154" y="2"/>
            <close/>
            <move x="9" y="3"/>
            <line x="43" y="3"/>
            <line x="43" y="9"/>
            <line x="9" y="9"/>
            <close/>
            <move x="46" y="3"/>
            <line x="80" y="3"/>
            <line x="80" y="9"/>
            <line x="46" y="9"/>
            <close/>
            <move x="83" y="3"/>
            <line x="117" y="3"/>
            <line x="117" y="9"/>
            <line x="83" y="9"/>
            <close/>
            <move x="120" y="3"/>
            <line x="153" y="3"/>
            <line x="153" y="9"/>
            <line x="120" y="9"/>
            <close/>
            <move x="8" y="11"/>
            <line x="8" y="19"/>
            <line x="44" y="19"/>
            <line x="44" y="11"/>
            <close/>
            <move x="45" y="11"/>
            <line x="45" y="19"/>
            <line x="81" y="19"/>
            <line x="81" y="11"/>
            <close/>
            <move x="82" y="11"/>
            <line x="82" y="19"/>
            <line x="118" y="19"/>
            <line x="118" y="11"/>
            <close/>
            <move x="119" y="11"/>
            <line x="119" y="19"/>
            <line x="154" y="19"/>
            <line x="154" y="11"/>
            <close/>
            <move x="9" y="12"/>
            <line x="43" y="12"/>
            <line x="43" y="18"/>
            <line x="9" y="18"/>
            <close/>
            <move x="46" y="12"/>
            <line x="80" y="12"/>
            <line x="80" y="18"/>
            <line x="46" y="18"/>
            <close/>
            <move x="83" y="12"/>
            <line x="117" y="12"/>
            <line x="117" y="18"/>
            <line x="83" y="18"/>
            <close/>
            <move x="120" y="12"/>
            <line x="153" y="12"/>
            <line x="153" y="18"/>
            <line x="120" y="18"/>
            <close/>
            <move x="8" y="20"/>
            <line x="8" y="28"/>
            <line x="44" y="28"/>
            <line x="44" y="20"/>
            <close/>
            <move x="45" y="20"/>
            <line x="45" y="28"/>
            <line x="81" y="28"/>
            <line x="81" y="20"/>
            <close/>
            <move x="82" y="20"/>
            <line x="82" y="28"/>
            <line x="118" y="28"/>
            <line x="118" y="20"/>
            <close/>
            <move x="119" y="20"/>
            <line x="119" y="28"/>
            <line x="154" y="28"/>
            <line x="154" y="20"/>
            <close/>
            <move x="9" y="21"/>
            <line x="43" y="21"/>
            <line x="43" y="27"/>
            <line x="9" y="27"/>
            <close/>
            <move x="46" y="21"/>
            <line x="80" y="21"/>
            <line x="80" y="27"/>
            <line x="46" y="27"/>
            <close/>
            <move x="83" y="21"/>
            <line x="117" y="21"/>
            <line x="117" y="27"/>
            <line x="83" y="27"/>
            <close/>
            <move x="120" y="21"/>
            <line x="153" y="21"/>
            <line x="153" y="27"/>
            <line x="120" y="27"/>
            <close/>
        </path>
        <fill/>
    </foreground>
</shape>
<shape aspect="variable" h="60.2" name="Sun ZFS Storage 7320" strokewidth="inherit" w="161.9">
    <connections/>
    <background>
        <path>
            <move x="0" y="0"/>
            <curve x1="0" x2="0" x3="0" y1="20.07" y2="40.13" y3="60.2"/>
            <line x="161.9" y="60.2"/>
            <line x="161.9" y="0"/>
            <close/>
        </path>
    </background>
    <foreground>
        <fill/>
        <fillcolor color="#000000"/>
        <strokewidth width="1"/>
        <linejoin join="miter"/>
        <linecap cap="butt"/>
        <miterlimit limit="4"/>
        <dashpattern pattern="none"/>
        <dashed dashed="1"/>
        <alpha alpha="1"/>
        <strokealpha alpha="1"/>
        <fillalpha alpha="0.644"/>
        <fontfamily family="sans-serif"/>
        <fontstyle style="0"/>
        <path>
            <move x="0" y="0"/>
            <line x="0" y="60.2"/>
            <line x="161.9" y="60.2"/>
            <line x="161.9" y="0"/>
            <close/>
            <move x="1" y="1"/>
            <line x="6" y="1"/>
            <line x="6" y="59.2"/>
            <line x="1" y="59.2"/>
            <close/>
            <move x="7" y="1"/>
            <line x="155" y="1"/>
            <line x="155" y="59.2"/>
            <line x="7" y="59.2"/>
            <close/>
            <move x="156" y="1"/>
            <line x="160.9" y="1"/>
            <line x="160.9" y="59.2"/>
            <line x="156" y="59.2"/>
            <close/>
            <move x="8" y="3"/>
            <line x="8" y="11"/>
            <line x="44" y="11"/>
            <line x="44" y="3"/>
            <close/>
            <move x="45" y="3"/>
            <line x="45" y="11"/>
            <line x="81" y="11"/>
            <line x="81" y="3"/>
            <close/>
            <move x="82" y="3"/>
            <line x="82" y="11"/>
            <line x="118" y="11"/>
            <line x="118" y="3"/>
            <close/>
            <move x="119" y="3"/>
            <line x="119" y="11"/>
            <line x="154" y="11"/>
            <line x="154" y="3"/>
            <close/>
            <move x="9" y="4"/>
            <line x="43" y="4"/>
            <line x="43" y="10"/>
            <line x="9" y="10"/>
            <close/>
            <move x="46" y="4"/>
            <line x="80" y="4"/>
            <line x="80" y="10"/>
            <line x="46" y="10"/>
            <close/>
            <move x="83" y="4"/>
            <line x="117" y="4"/>
            <line x="117" y="10"/>
            <line x="83" y="10"/>
            <close/>
            <move x="120" y="4"/>
            <line x="153" y="4"/>
            <line x="153" y="10"/>
            <line x="120" y="10"/>
            <close/>
            <move x="8" y="12"/>
            <line x="8" y="20"/>
            <line x="44" y="20"/>
            <line x="44" y="12"/>
            <close/>
            <move x="45" y="12"/>
            <line x="45" y="20"/>
            <line x="81" y="20"/>
            <line x="81" y="12"/>
            <close/>
            <move x="82" y="12"/>
            <line x="82" y="20"/>
            <line x="118" y="20"/>
            <line x="118" y="12"/>
            <close/>
            <move x="119" y="12"/>
            <line x="119" y="20"/>
            <line x="154" y="20"/>
            <line x="154" y="12"/>
            <close/>
            <move x="9" y="13"/>
            <line x="43" y="13"/>
            <line x="43" y="19"/>
            <line x="9" y="19"/>
            <close/>
            <move x="46" y="13"/>
            <line x="80" y="13"/>
            <line x="80" y="19"/>
            <line x="46" y="19"/>
            <close/>
            <move x="83" y="13"/>
            <line x="117" y="13"/>
            <line x="117" y="19"/>
            <line x="83" y="19"/>
            <close/>
            <move x="120" y="13"/>
            <line x="153" y="13"/>
            <line x="153" y="19"/>
            <line x="120" y="19"/>
            <close/>
            <move x="8" y="21"/>
            <line x="8" y="29"/>
            <line x="44" y="29"/>
            <line x="44" y="21"/>
            <close/>
            <move x="45" y="21"/>
            <line x="45" y="29"/>
            <line x="81" y="29"/>
            <line x="81" y="21"/>
            <close/>
            <move x="82" y="21"/>
            <line x="82" y="29"/>
            <line x="118" y="29"/>
            <line x="118" y="21"/>
            <close/>
            <move x="119" y="21"/>
            <line x="119" y="29"/>
            <line x="154" y="29"/>
            <line x="154" y="21"/>
            <close/>
            <move x="9" y="22"/>
            <line x="43" y="22"/>
            <line x="43" y="28"/>
            <line x="9" y="28"/>
            <close/>
            <move x="46" y="22"/>
            <line x="80" y="22"/>
            <line x="80" y="28"/>
            <line x="46" y="28"/>
            <close/>
            <move x="83" y="22"/>
            <line x="117" y="22"/>
            <line x="117" y="28"/>
            <line x="83" y="28"/>
            <close/>
            <move x="120" y="22"/>
            <line x="153" y="22"/>
            <line x="153" y="28"/>
            <line x="120" y="28"/>
            <close/>
            <move x="8" y="31"/>
            <line x="8" y="39"/>
            <line x="44" y="39"/>
            <line x="44" y="31"/>
            <close/>
            <move x="45" y="31"/>
            <line x="45" y="39"/>
            <line x="81" y="39"/>
            <line x="81" y="31"/>
            <close/>
            <move x="82" y="31"/>
            <line x="82" y="39"/>
            <line x="118" y="39"/>
            <line x="118" y="31"/>
            <close/>
            <move x="119" y="31"/>
            <line x="119" y="39"/>
            <line x="154" y="39"/>
            <line x="154" y="31"/>
            <close/>
            <move x="9" y="32"/>
            <line x="43" y="32"/>
            <line x="43" y="38"/>
            <line x="9" y="38"/>
            <close/>
            <move x="46" y="32"/>
            <line x="80" y="32"/>
            <line x="80" y="38"/>
            <line x="46" y="38"/>
            <close/>
            <move x="83" y="32"/>
            <line x="117" y="32"/>
            <line x="117" y="38"/>
            <line x="83" y="38"/>
            <close/>
            <move x="120" y="32"/>
            <line x="153" y="32"/>
            <line x="153" y="38"/>
            <line x="120" y="38"/>
            <close/>
            <move x="8" y="40"/>
            <line x="8" y="48"/>
            <line x="44" y="48"/>
            <line x="44" y="40"/>
            <close/>
            <move x="45" y="40"/>
            <line x="45" y="48"/>
            <line x="81" y="48"/>
            <line x="81" y="40"/>
            <close/>
            <move x="82" y="40"/>
            <line x="82" y="48"/>
            <line x="118" y="48"/>
            <line x="118" y="40"/>
            <close/>
            <move x="119" y="40"/>
            <line x="119" y="48"/>
            <line x="154" y="48"/>
            <line x="154" y="40"/>
            <close/>
            <move x="9" y="41"/>
            <line x="43" y="41"/>
            <line x="43" y="47"/>
            <line x="9" y="47"/>
            <close/>
            <move x="46" y="41"/>
            <line x="80" y="41"/>
            <line x="80" y="47"/>
            <line x="46" y="47"/>
            <close/>
            <move x="83" y="41"/>
            <line x="117" y="41"/>
            <line x="117" y="47"/>
            <line x="83" y="47"/>
            <close/>
            <move x="120" y="41"/>
            <line x="153" y="41"/>
            <line x="153" y="47"/>
            <line x="120" y="47"/>
            <close/>
            <move x="8" y="49"/>
            <line x="8" y="57"/>
            <line x="44" y="57"/>
            <line x="44" y="49"/>
            <close/>
            <move x="45" y="49"/>
            <line x="45" y="57"/>
            <line x="81" y="57"/>
            <line x="81" y="49"/>
            <close/>
            <move x="82" y="49"/>
            <line x="82" y="57"/>
            <line x="118" y="57"/>
            <line x="118" y="49"/>
            <close/>
            <move x="119" y="49"/>
            <line x="119" y="57"/>
            <line x="154" y="57"/>
            <line x="154" y="49"/>
            <close/>
            <move x="9" y="50"/>
            <line x="43" y="50"/>
            <line x="43" y="56"/>
            <line x="9" y="56"/>
            <close/>
            <move x="46" y="50"/>
            <line x="80" y="50"/>
            <line x="80" y="56"/>
            <line x="46" y="56"/>
            <close/>
            <move x="83" y="50"/>
            <line x="117" y="50"/>
            <line x="117" y="56"/>
            <line x="83" y="56"/>
            <close/>
            <move x="120" y="50"/>
            <line x="153" y="50"/>
            <line x="153" y="56"/>
            <line x="120" y="56"/>
            <close/>
        </path>
        <fill/>
    </foreground>
</shape>
<shape aspect="variable" h="15.85" name="SunFire T1000" strokewidth="inherit" w="161.8">
    <connections/>
    <background>
        <path>
            <move x="0" y="0"/>
            <line x="0" y="0.5"/>
            <line x="0" y="15.85"/>
            <line x="161.8" y="15.85"/>
            <line x="161.8" y="0"/>
            <close/>
        </path>
    </background>
    <foreground>
        <fill/>
        <fillcolor color="#000000"/>
        <fillalpha alpha="0.644"/>
        <path>
            <move x="158.77" y="11.52"/>
            <arc large-arc-flag="0" rx="1.67" ry="1.67" sweep-flag="1" x="157.1" x-axis-rotation="0" y="13.2"/>
            <arc large-arc-flag="0" rx="1.67" ry="1.67" sweep-flag="1" x="155.44" x-axis-rotation="0" y="11.52"/>
            <arc large-arc-flag="0" rx="1.67" ry="1.67" sweep-flag="1" x="157.1" x-axis-rotation="0" y="9.85"/>
            <arc large-arc-flag="0" rx="1.67" ry="1.67" sweep-flag="1" x="158.77" x-axis-rotation="0" y="11.52"/>
            <close/>
            <move x="158.77" y="4.17"/>
            <arc large-arc-flag="0" rx="1.67" ry="1.67" sweep-flag="1" x="157.1" x-axis-rotation="0" y="5.85"/>
            <arc large-arc-flag="0" rx="1.67" ry="1.67" sweep-flag="1" x="155.44" x-axis-rotation="0" y="4.17"/>
            <arc large-arc-flag="0" rx="1.67" ry="1.67" sweep-flag="1" x="157.1" x-axis-rotation="0" y="2.5"/>
            <arc large-arc-flag="0" rx="1.67" ry="1.67" sweep-flag="1" x="158.77" x-axis-rotation="0" y="4.17"/>
            <close/>
            <move x="5.83" y="11.52"/>
            <arc large-arc-flag="0" rx="1.67" ry="1.67" sweep-flag="1" x="4.17" x-axis-rotation="0" y="13.2"/>
            <arc large-arc-flag="0" rx="1.67" ry="1.67" sweep-flag="1" x="2.5" x-axis-rotation="0" y="11.52"/>
            <arc large-arc-flag="0" rx="1.67" ry="1.67" sweep-flag="1" x="4.17" x-axis-rotation="0" y="9.85"/>
            <arc large-arc-flag="0" rx="1.67" ry="1.67" sweep-flag="1" x="5.83" x-axis-rotation="0" y="11.52"/>
            <close/>
            <move x="5.83" y="4.17"/>
            <arc large-arc-flag="0" rx="1.67" ry="1.67" sweep-flag="1" x="4.17" x-axis-rotation="0" y="5.85"/>
            <arc large-arc-flag="0" rx="1.67" ry="1.67" sweep-flag="1" x="2.5" x-axis-rotation="0" y="4.17"/>
            <arc large-arc-flag="0" rx="1.67" ry="1.67" sweep-flag="1" x="4.17" x-axis-rotation="0" y="2.5"/>
            <arc large-arc-flag="0" rx="1.67" ry="1.67" sweep-flag="1" x="5.83" x-axis-rotation="0" y="4.17"/>
            <close/>
            <move x="0" y="0"/>
            <line x="0" y="0.5"/>
            <line x="0" y="15.85"/>
            <line x="161.8" y="15.85"/>
            <line x="161.8" y="0"/>
            <close/>
            <move x="1" y="1"/>
            <line x="160.8" y="1"/>
            <line x="160.8" y="14.85"/>
            <line x="1" y="14.85"/>
            <close/>
        </path>
        <fill/>
        <fillalpha alpha="0.232"/>
        <roundrect arcsize="16.67" h="10.02" w="143.27" x="9.16" y="2.84"/>
        <fill/>
    </foreground>
</shape>
<shape aspect="variable" h="15.85" name="SunFire X2100" strokewidth="inherit" w="162">
    <connections/>
    <background>
        <save/>
        <path>
            <move x="0" y="0"/>
            <line x="0" y="15.85"/>
            <line x="162" y="15.85"/>
            <line x="162" y="0"/>
            <close/>
        </path>
    </background>
    <foreground>
        <fill/>
        <fillcolor color="#000000"/>
        <strokewidth width="1"/>
        <linejoin join="miter"/>
        <linecap cap="butt"/>
        <miterlimit limit="4"/>
        <dashpattern pattern="none"/>
        <dashed dashed="1"/>
        <alpha alpha="1"/>
        <strokealpha alpha="1"/>
        <fillalpha alpha="0.644"/>
        <fontfamily family="sans-serif"/>
        <fontstyle style="0"/>
        <path>
            <move x="0" y="0"/>
            <line x="0" y="15.85"/>
            <line x="162" y="15.85"/>
            <line x="162" y="0"/>
            <close/>
            <move x="1" y="1"/>
            <line x="161" y="1"/>
            <line x="161" y="14.85"/>
            <line x="1" y="14.85"/>
            <close/>
            <move x="80.07" y="6.35"/>
            <line x="80.07" y="13.7"/>
            <line x="118.76" y="13.7"/>
            <line x="118.76" y="6.35"/>
            <close/>
            <move x="121.1" y="6.35"/>
            <line x="121.1" y="13.7"/>
            <line x="159.8" y="13.7"/>
            <line x="159.8" y="6.35"/>
            <close/>
            <move x="81.07" y="7.35"/>
            <line x="117.76" y="7.35"/>
            <line x="117.76" y="12.7"/>
            <line x="81.07" y="12.7"/>
            <close/>
            <move x="122.1" y="7.35"/>
            <line x="158.8" y="7.35"/>
            <line x="158.8" y="12.7"/>
            <line x="122.1" y="12.7"/>
            <close/>
            <move x="3.34" y="8.69"/>
            <line x="3.34" y="13.36"/>
            <line x="76.73" y="13.36"/>
            <line x="76.73" y="8.69"/>
            <close/>
            <move x="4.34" y="9.69"/>
            <line x="75.73" y="9.69"/>
            <line x="75.73" y="12.36"/>
            <line x="4.34" y="12.36"/>
            <close/>
        </path>
        <fill/>
        <restore/>
        <rect/>
        <stroke/>
        <fillcolor color="#000000"/>
        <fillalpha alpha="0.232"/>
        <roundrect arcsize="19.96" h="5.01" w="72.39" x="3.84" y="3.17"/>
        <fill/>
    </foreground>
</shape>
<shape aspect="variable" h="15.95" name="SunFire X4100" strokewidth="inherit" w="161.9">
    <connections/>
    <background>
        <save/>
        <path>
            <move x="0" y="0"/>
            <line x="0" y="15.95"/>
            <line x="161.9" y="15.95"/>
            <line x="161.9" y="0"/>
            <close/>
        </path>
    </background>
    <foreground>
        <fill/>
        <fillcolor color="#000000"/>
        <strokewidth width="1"/>
        <linejoin join="miter"/>
        <linecap cap="butt"/>
        <miterlimit limit="4"/>
        <dashpattern pattern="none"/>
        <dashed dashed="1"/>
        <alpha alpha="1"/>
        <strokealpha alpha="1"/>
        <fillalpha alpha="0.644"/>
        <fontfamily family="sans-serif"/>
        <fontstyle style="0"/>
        <path>
            <move x="0" y="0"/>
            <line x="0" y="15.95"/>
            <line x="161.9" y="15.95"/>
            <line x="161.9" y="0"/>
            <close/>
            <move x="1" y="1"/>
            <line x="160.9" y="1"/>
            <line x="160.9" y="14.95"/>
            <line x="1" y="14.95"/>
            <close/>
            <move x="101.52" y="2.35"/>
            <curve x1="99.96" x2="98.69" x3="98.69" y1="2.35" y2="3.62" y3="5.18"/>
            <line x="98.69" y="10.95"/>
            <curve x1="98.69" x2="99.96" x3="101.52" y1="12.51" y2="13.78" y3="13.78"/>
            <line x="149.87" y="13.78"/>
            <curve x1="151.43" x2="152.7" x3="152.7" y1="13.78" y2="12.51" y3="10.95"/>
            <line x="152.7" y="5.18"/>
            <curve x1="152.7" x2="151.43" x3="149.87" y1="3.62" y2="2.35" y3="2.35"/>
            <close/>
            <move x="101.52" y="3.35"/>
            <line x="149.87" y="3.35"/>
            <curve x1="150.89" x2="151.7" x3="151.7" y1="3.35" y2="4.16" y3="5.18"/>
            <line x="151.7" y="10.95"/>
            <curve x1="151.7" x2="150.89" x3="149.87" y1="11.97" y2="12.78" y3="12.78"/>
            <line x="101.52" y="12.78"/>
            <curve x1="100.5" x2="99.69" x3="99.69" y1="12.78" y2="11.97" y3="10.95"/>
            <line x="99.69" y="5.18"/>
            <curve x1="99.69" x2="100.5" x3="101.52" y1="4.16" y2="3.35" y3="3.35"/>
            <close/>
        </path>
        <fill/>
        <restore/>
        <rect/>
        <stroke/>
        <fillcolor color="#000000"/>
        <fillalpha alpha="0.232"/>
        <roundrect arcsize="17.13" h="9.75" w="85.35" x="9.17" y="3.19"/>
        <fill/>
    </foreground>
</shape>
<shape aspect="variable" h="60.2" name="SunFire X4500" strokewidth="inherit" w="161.8">
    <connections/>
    <background>
        <save/>
        <path>
            <move x="0" y="0"/>
            <line x="0" y="60.2"/>
            <line x="161.8" y="60.2"/>
            <line x="161.8" y="0"/>
            <close/>
        </path>
    </background>
    <foreground>
        <fill/>
        <fillcolor color="#000000"/>
        <strokewidth width="1"/>
        <linejoin join="miter"/>
        <linecap cap="butt"/>
        <miterlimit limit="4"/>
        <dashpattern pattern="none"/>
        <dashed dashed="1"/>
        <alpha alpha="1"/>
        <strokealpha alpha="1"/>
        <fillalpha alpha="0.644"/>
        <fontfamily family="sans-serif"/>
        <fontstyle style="0"/>
        <path>
            <move x="0" y="0"/>
            <line x="0" y="0.5"/>
            <line x="0" y="60.2"/>
            <line x="161.8" y="60.2"/>
            <line x="161.8" y="0"/>
            <close/>
            <move x="1" y="1"/>
            <line x="160.8" y="1"/>
            <line x="160.8" y="59.2"/>
            <line x="1" y="59.2"/>
            <close/>
        </path>
        <fill/>
        <restore/>
        <rect/>
        <stroke/>
        <fillcolor color="#000000"/>
        <fillalpha alpha="0.232"/>
        <path>
            <move x="12.49" y="31.8"/>
            <line x="149.77" y="31.8"/>
            <curve x1="151.06" x2="152.1" x3="152.1" y1="31.8" y2="32.84" y3="34.13"/>
            <line x="152.1" y="52.44"/>
            <curve x1="152.1" x2="151.06" x3="149.77" y1="53.73" y2="54.77" y3="54.77"/>
            <line x="12.49" y="54.77"/>
            <curve x1="11.2" x2="10.16" x3="10.16" y1="54.77" y2="53.73" y3="52.44"/>
            <line x="10.16" y="34.13"/>
            <curve x1="10.16" x2="11.2" x3="12.49" y1="32.84" y2="31.8" y3="31.8"/>
            <close/>
            <move x="12.49" y="5.83"/>
            <line x="149.77" y="5.83"/>
            <curve x1="151.06" x2="152.1" x3="152.1" y1="5.83" y2="6.87" y3="8.16"/>
            <line x="152.1" y="26.47"/>
            <curve x1="152.1" x2="151.06" x3="149.77" y1="27.76" y2="28.8" y3="28.8"/>
            <line x="12.49" y="28.8"/>
            <curve x1="11.2" x2="10.16" x3="10.16" y1="28.8" y2="27.76" y3="26.47"/>
            <line x="10.16" y="8.16"/>
            <curve x1="10.16" x2="11.2" x3="12.49" y1="6.87" y2="5.83" y3="5.83"/>
            <close/>
        </path>
        <fill/>
    </foreground>
</shape>
<shape aspect="variable" h="60.2" name="SunFire X4600" strokewidth="inherit" w="161.8">
    <connections/>
    <background>
        <save/>
        <path>
            <move x="0" y="0"/>
            <line x="0" y="60.2"/>
            <line x="161.8" y="60.2"/>
            <line x="161.8" y="0"/>
            <close/>
        </path>
    </background>
    <foreground>
        <fill/>
        <fillcolor color="rgb(0, 0, 0)"/>
        <strokewidth width="1"/>
        <linejoin join="miter"/>
        <linecap cap="butt"/>
        <miterlimit limit="4"/>
        <dashpattern pattern="none"/>
        <dashed dashed="1"/>
        <alpha alpha="1"/>
        <strokealpha alpha="1"/>
        <fillalpha alpha="0.644"/>
        <fontfamily family="sans-serif"/>
        <fontstyle style="0"/>
        <path>
            <move x="0" y="0"/>
            <line x="0" y="60.2"/>
            <line x="161.8" y="60.2"/>
            <line x="161.8" y="0"/>
            <close/>
            <move x="1" y="1"/>
            <line x="160.8" y="1"/>
            <line x="160.8" y="59.2"/>
            <line x="1" y="59.2"/>
            <close/>
            <move x="118.79" y="5.66"/>
            <curve x1="117.6" x2="116.62" x3="116.62" y1="5.66" y2="6.64" y3="7.83"/>
            <line x="116.62" y="52.77"/>
            <curve x1="116.62" x2="117.6" x3="118.79" y1="53.96" y2="54.94" y3="54.94"/>
            <line x="124.45" y="54.94"/>
            <curve x1="125.64" x2="126.62" x3="126.62" y1="54.94" y2="53.96" y3="52.77"/>
            <line x="126.62" y="7.83"/>
            <curve x1="126.62" x2="125.64" x3="124.45" y1="6.64" y2="5.66" y3="5.66"/>
            <close/>
            <move x="132.78" y="5.66"/>
            <curve x1="131.59" x2="130.61" x3="130.61" y1="5.66" y2="6.64" y3="7.83"/>
            <line x="130.61" y="52.77"/>
            <curve x1="130.61" x2="131.59" x3="132.78" y1="53.96" y2="54.94" y3="54.94"/>
            <line x="138.44" y="54.94"/>
            <curve x1="139.63" x2="140.61" x3="140.61" y1="54.94" y2="53.96" y3="52.77"/>
            <line x="140.61" y="7.83"/>
            <curve x1="140.61" x2="139.63" x3="138.44" y1="6.64" y2="5.66" y3="5.66"/>
            <close/>
            <move x="146.78" y="5.66"/>
            <curve x1="145.58" x2="144.61" x3="144.61" y1="5.66" y2="6.64" y3="7.83"/>
            <line x="144.61" y="52.77"/>
            <curve x1="144.61" x2="145.58" x3="146.78" y1="53.96" y2="54.94" y3="54.94"/>
            <line x="152.43" y="54.94"/>
            <curve x1="153.63" x2="154.6" x3="154.6" y1="54.94" y2="53.96" y3="52.77"/>
            <line x="154.6" y="7.83"/>
            <curve x1="154.6" x2="153.63" x3="152.43" y1="6.64" y2="5.66" y3="5.66"/>
            <close/>
            <move x="118.79" y="6.66"/>
            <line x="124.45" y="6.66"/>
            <curve x1="125.1" x2="125.62" x3="125.62" y1="6.66" y2="7.17" y3="7.83"/>
            <line x="125.62" y="52.77"/>
            <curve x1="125.62" x2="125.1" x3="124.45" y1="53.43" y2="53.94" y3="53.94"/>
            <line x="118.79" y="53.94"/>
            <curve x1="118.13" x2="117.62" x3="117.62" y1="53.94" y2="53.43" y3="52.77"/>
            <line x="117.62" y="7.83"/>
            <curve x1="117.62" x2="118.13" x3="118.79" y1="7.17" y2="6.66" y3="6.66"/>
            <close/>
            <move x="132.78" y="6.66"/>
            <line x="138.44" y="6.66"/>
            <curve x1="139.1" x2="139.61" x3="139.61" y1="6.66" y2="7.17" y3="7.83"/>
            <line x="139.61" y="52.77"/>
            <curve x1="139.61" x2="139.1" x3="138.44" y1="53.43" y2="53.94" y3="53.94"/>
            <line x="132.78" y="53.94"/>
            <curve x1="132.13" x2="131.61" x3="131.61" y1="53.94" y2="53.43" y3="52.77"/>
            <line x="131.61" y="7.83"/>
            <curve x1="131.61" x2="132.13" x3="132.78" y1="7.17" y2="6.66" y3="6.66"/>
            <close/>
            <move x="146.78" y="6.66"/>
            <line x="152.43" y="6.66"/>
            <curve x1="153.09" x2="153.6" x3="153.6" y1="6.66" y2="7.17" y3="7.83"/>
            <line x="153.6" y="52.77"/>
            <curve x1="153.6" x2="153.09" x3="152.43" y1="53.43" y2="53.94" y3="53.94"/>
            <line x="146.78" y="53.94"/>
            <curve x1="146.12" x2="145.61" x3="145.61" y1="53.94" y2="53.43" y3="52.77"/>
            <line x="145.61" y="7.83"/>
            <curve x1="145.61" x2="146.12" x3="146.78" y1="7.17" y2="6.66" y3="6.66"/>
            <close/>
        </path>
        <fill/>
        <restore/>
        <rect/>
        <stroke/>
        <fillcolor color="#000000"/>
        <fillalpha alpha="0.232"/>
        <roundrect arcsize="10.15" h="49.28" w="96.29" x="9.83" y="5.49"/>
        <fill/>
    </foreground>
</shape>
</shapes>