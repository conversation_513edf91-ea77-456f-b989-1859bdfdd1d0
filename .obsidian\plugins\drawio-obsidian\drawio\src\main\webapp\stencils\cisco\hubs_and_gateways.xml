<shapes name="mxgraph.cisco.hubs_and_gateways">
<shape name="100BaseT Hub" h="28.33" w="56" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
<constraint x="0.185" y="0" perimeter="0" name="NW"/>
<constraint x="0" y="1" perimeter="0" name="SW"/>
<constraint x="1" y="0" perimeter="0" name="NE"/>
<constraint x="0.88" y="1" perimeter="0" name="SE"/>
</connections>
<background>
<path>
<move x="49.33" y="13.66"/>
<line x="56" y="0"/>
<line x="10.33" y="0"/>
<line x="0" y="13.66"/>
<close/>
<move x="56" y="12.33"/>
<line x="56" y="0"/>
<line x="49.33" y="13.66"/>
<line x="49.33" y="28.33"/>
<close/>
<move x="49.33" y="28.33"/>
<line x="49.33" y="13.66"/>
<line x="0" y="13.66"/>
<line x="0" y="28.33"/>
<close/>
</path>
</background>
<foreground>
<linejoin join="round"/>
<fillstroke/>
<fillcolor color="#ffffff"/>
<path>
<move x="47.66" y="6.33"/>
<line x="29" y="4"/>
<line x="28.33" y="5.33"/>
<line x="12" y="5.33"/>
<line x="10" y="7.66"/>
<line x="27" y="7.66"/>
<line x="26" y="9"/>
<close/>
</path>
<fill/>
</foreground>
</shape>
<shape name="Cisco Hub" h="36.67" w="41" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
<constraint x="0.05" y="0.05" perimeter="0" name="NW"/>
<constraint x="0" y="1" perimeter="0" name="SW"/>
<constraint x="1" y="0" perimeter="0" name="NE"/>
<constraint x="0.95" y="0.94" perimeter="0" name="SE"/>
</connections>
<background>
<save/>
<path>
<move x="0" y="36.67"/>
<line x="0" y="4.34"/>
<line x="36.66" y="4.34"/>
<line x="36.66" y="36.67"/>
<close/>
<move x="4" y="0"/>
<line x="0" y="4.34"/>
<line x="36.66" y="4.34"/>
<line x="41" y="0"/>
<close/>
<move x="41" y="32.34"/>
<line x="41" y="0"/>
<line x="36.66" y="4.34"/>
<line x="36.66" y="36.67"/>
<close/>
</path>
</background>
<foreground>
<linejoin join="round"/>
<fillstroke/>
<restore/>
<rect/>
<stroke/>
<path>
<move x="33.66" y="7.34"/>
<line x="3.33" y="7.34"/>
<line x="3.33" y="34"/>
<line x="33.66" y="34"/>
<close/>
</path>
<stroke/>
<linejoin join="round"/>
<path>
<move x="32" y="17"/>
<curve x1="32" y1="20" x2="26" y2="22.34" x3="18.66" y3="22.34"/>
<curve x1="11.33" y1="22.34" x2="5.33" y2="20" x3="5.33" y3="17"/>
<curve x1="5.33" y1="24.67" x2="5.33" y2="24.67" x3="5.33" y3="24.67"/>
<curve x1="5.33" y1="27.67" x2="11.33" y2="30" x3="18.66" y3="30"/>
<curve x1="26" y1="30" x2="32" y2="27.67" x3="32" y3="24.67"/>
<close/>
</path>
<fillstroke/>
<path>
<move x="18.66" y="22.34"/>
<curve x1="26" y1="22.34" x2="32" y2="20" x3="32" y3="17"/>
<curve x1="32" y1="14.34" x2="26" y2="12" x3="18.66" y3="12"/>
<curve x1="11.33" y1="12" x2="5.33" y2="14.34" x3="5.33" y3="17"/>
<curve x1="5.33" y1="20" x2="11.33" y2="22.34" x3="18.66" y3="22.34"/>
<close/>
</path>
<fillstroke/>
<fillcolor color="#ffffff"/>
<path>
<move x="15.66" y="15"/>
<line x="16.66" y="16.67"/>
<line x="12.66" y="17.67"/>
<line x="13.33" y="17"/>
<line x="7" y="16"/>
<line x="8.66" y="14.67"/>
<line x="14.66" y="15.67"/>
<close/>
<move x="21.33" y="19"/>
<line x="20.66" y="17.34"/>
<line x="24.33" y="16.67"/>
<line x="23.66" y="17.34"/>
<line x="30" y="18.34"/>
<line x="28.66" y="19.67"/>
<line x="22.33" y="18.34"/>
<close/>
<move x="19.33" y="14.34"/>
<line x="23.66" y="13"/>
<line x="23.66" y="15"/>
<line x="22.66" y="14.67"/>
<line x="20.66" y="16.34"/>
<line x="18.66" y="16"/>
<line x="20.66" y="14.34"/>
<close/>
<move x="17.66" y="20.67"/>
<line x="13.66" y="21.34"/>
<line x="13.33" y="19.67"/>
<line x="14.66" y="19.67"/>
<line x="16.66" y="18"/>
<line x="18.66" y="18.34"/>
<line x="16.33" y="20.34"/>
<close/>
</path>
<fill/>
</foreground>
</shape>
<shape name="Generic Gateway" h="46.66" w="41.66" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
<constraint x="0.12" y="0.06" perimeter="0" name="NW"/>
<constraint x="0.12" y="0.94" perimeter="0" name="SW"/>
<constraint x="0.88" y="0.06" perimeter="0" name="NE"/>
<constraint x="0.88" y="0.94" perimeter="0" name="SE"/>
</connections>
<background>
<save/>
<path>
<move x="41.66" y="8"/>
<curve x1="41.66" y1="12.66" x2="32.33" y2="16.33" x3="21" y3="16.33"/>
<curve x1="9.33" y1="16.33" x2="0" y2="12.66" x3="0" y3="8"/>
<curve x1="0" y1="38.66" x2="0" y2="38.66" x3="0" y3="38.66"/>
<curve x1="0" y1="43" x2="9.33" y2="46.66" x3="21" y3="46.66"/>
<curve x1="32.33" y1="46.66" x2="41.66" y2="43" x3="41.66" y3="38.66"/>
<close/>
<move x="21" y="16.33"/>
<curve x1="32.33" y1="16.33" x2="41.66" y2="12.66" x3="41.66" y3="8"/>
<curve x1="41.66" y1="3.66" x2="32.33" y2="0" x3="21" y3="0"/>
<curve x1="9.33" y1="0" x2="0" y2="3.66" x3="0" y3="8"/>
<curve x1="0" y1="12.66" x2="9.33" y2="16.33" x3="21" y3="16.33"/>
<close/>
</path>
</background>
<foreground>
<linejoin join="round"/>
<fillstroke/>
<restore/>
<rect/>
<stroke/>
<fillcolor color="#ffffff"/>
<path>
<move x="16" y="5"/>
<line x="18" y="7.66"/>
<line x="11.33" y="9"/>
<line x="12.66" y="8"/>
<line x="2.66" y="6"/>
<line x="5.33" y="4.33"/>
<line x="15" y="6"/>
<close/>
<move x="25.33" y="11.33"/>
<line x="24" y="8.66"/>
<line x="30" y="7.33"/>
<line x="29" y="8.33"/>
<line x="38.66" y="10"/>
<line x="36.33" y="11.66"/>
<line x="26.66" y="10"/>
<close/>
<move x="22" y="3.66"/>
<line x="28.66" y="1.66"/>
<line x="28.66" y="4.66"/>
<line x="27" y="4.33"/>
<line x="24" y="7"/>
<line x="20.66" y="6.33"/>
<line x="24" y="4"/>
<close/>
<move x="19.33" y="13.66"/>
<line x="13" y="14.66"/>
<line x="12.66" y="12"/>
<line x="14.66" y="12.33"/>
<line x="18" y="9.33"/>
<line x="21" y="10"/>
<line x="17.33" y="13"/>
<close/>
<move x="21.33" y="27"/>
<line x="21.33" y="20.33"/>
<line x="22.33" y="20.33"/>
<line x="20.66" y="18"/>
<line x="19" y="20.33"/>
<line x="20" y="20.33"/>
<line x="20" y="27"/>
<close/>
<move x="18" y="28"/>
<line x="13.33" y="23.33"/>
<line x="14" y="22.33"/>
<line x="11" y="21.66"/>
<line x="11.66" y="24.66"/>
<line x="12.33" y="24"/>
<line x="17" y="28.66"/>
<close/>
<move x="16" y="31"/>
<line x="9.66" y="31"/>
<line x="9.66" y="29.66"/>
<line x="7" y="31.33"/>
<line x="9.66" y="33"/>
<line x="9.66" y="32"/>
<line x="16" y="32"/>
<close/>
<move x="17" y="34.33"/>
<line x="12.33" y="39"/>
<line x="11.66" y="38"/>
<line x="11" y="41"/>
<line x="14" y="40.33"/>
<line x="13.33" y="39.66"/>
<line x="18" y="35"/>
<close/>
<move x="20" y="36"/>
<line x="20" y="42.66"/>
<line x="19" y="42.66"/>
<line x="20.66" y="45"/>
<line x="22.33" y="42.66"/>
<line x="21.33" y="42.66"/>
<line x="21.33" y="36"/>
<close/>
<move x="23.33" y="35"/>
<line x="28" y="39.66"/>
<line x="27.33" y="40.33"/>
<line x="30.33" y="41"/>
<line x="29.66" y="38"/>
<line x="29" y="39"/>
<line x="24.33" y="34.33"/>
<close/>
<move x="25.33" y="32"/>
<line x="31.66" y="32"/>
<line x="31.66" y="33"/>
<line x="34.33" y="31.33"/>
<line x="31.66" y="29.66"/>
<line x="31.66" y="31"/>
<line x="25.33" y="31"/>
<close/>
<move x="24.33" y="28.66"/>
<line x="29" y="24"/>
<line x="29.66" y="24.66"/>
<line x="30.33" y="21.66"/>
<line x="27.33" y="22.33"/>
<line x="28" y="23.33"/>
<line x="23.33" y="28"/>
<close/>
</path>
<fill/>
<path>
<move x="24.66" y="37"/>
<curve x1="27.66" y1="34.66" x2="28.66" y2="30.66" x3="26.33" y3="27.33"/>
<curve x1="24.33" y1="24.33" x2="20" y2="23.66" x3="17" y3="25.66"/>
<curve x1="13.66" y1="28" x2="13" y2="32.33" x3="15.33" y3="35.33"/>
<curve x1="17.33" y1="38.33" x2="21.66" y2="39" x3="24.66" y3="37"/>
<close/>
</path>
<fill/>
</foreground>
</shape>
<shape name="Hub" h="36.66" w="41.33" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
<constraint x="0.06" y="0.05" perimeter="0" name="NW"/>
<constraint x="0" y="1" perimeter="0" name="SW"/>
<constraint x="1" y="0" perimeter="0" name="NE"/>
<constraint x="0.95" y="0.94" perimeter="0" name="SE"/>
</connections>
<background>
<save/>
<path>
<move x="0" y="36.66"/>
<line x="0" y="4.33"/>
<line x="37" y="4.33"/>
<line x="37" y="36.66"/>
<close/>
<move x="4.33" y="0"/>
<line x="0" y="4.33"/>
<line x="37" y="4.33"/>
<line x="41.33" y="0"/>
<close/>
<move x="41.33" y="32.33"/>
<line x="41.33" y="0"/>
<line x="37" y="4.33"/>
<line x="37" y="36.66"/>
<close/>
</path>
</background>
<foreground>
<linejoin join="round"/>
<fillstroke/>
<restore/>
<rect/>
<stroke/>
<path>
<move x="23" y="7.33"/>
<line x="2.66" y="7.33"/>
<line x="2.66" y="34"/>
<line x="23" y="34"/>
<close/>
<move x="6.33" y="31.66"/>
<line x="6.33" y="10"/>
<move x="9.33" y="31.66"/>
<line x="9.33" y="10"/>
<move x="12.66" y="31.66"/>
<line x="12.66" y="10"/>
<move x="16" y="31.66"/>
<line x="16" y="10"/>
<move x="19" y="31.66"/>
<line x="19" y="10"/>
</path>
<stroke/>
</foreground>
</shape>
<shape name="MAS Gateway" h="37" w="36.33" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
<constraint x="0.1" y="0.07" perimeter="0" name="NW"/>
<constraint x="0" y="1" perimeter="0" name="SW"/>
<constraint x="1" y="0" perimeter="0" name="NE"/>
<constraint x="0.93" y="0.88" perimeter="0" name="SE"/>
</connections>
<background>
<save/>
<save/>
<path>
<move x="30.33" y="36.67"/>
<line x="30.33" y="6"/>
<line x="0" y="6"/>
<line x="0" y="37"/>
<close/>
<move x="36.33" y="0"/>
<line x="30.33" y="6"/>
<line x="0" y="6"/>
<line x="6.33" y="0"/>
<close/>
<move x="36.33" y="29.34"/>
<line x="36.33" y="0"/>
<line x="30.33" y="6"/>
<line x="30.33" y="36.67"/>
<close/>
</path>
</background>
<foreground>
<linejoin join="round"/>
<fillstroke/>
<restore/>
<rect/>
<stroke/>
<fillcolor color="#ffffff"/>
<path>
<move x="18.33" y="29.34"/>
<line x="20.67" y="26.67"/>
<line x="17" y="25.67"/>
<close/>
</path>
<fill/>
<path>
<move x="4.33" y="16"/>
<line x="7" y="13.67"/>
<line x="3" y="12.34"/>
<line x="4.33" y="16"/>
<close/>
</path>
<fill/>
<path>
<move x="5.67" y="23.67"/>
<line x="5.67" y="20"/>
<line x="2" y="22"/>
<line x="5.67" y="23.67"/>
<close/>
</path>
<fill/>
<path>
<move x="18.33" y="14.67"/>
<line x="20.67" y="17.34"/>
<line x="17" y="18.34"/>
<line x="18.33" y="14.67"/>
<close/>
</path>
<fill/>
<path>
<move x="4.33" y="28"/>
<line x="7" y="30.34"/>
<line x="3" y="31.67"/>
<line x="4.33" y="28"/>
<close/>
</path>
<fill/>
<strokecolor color="#ffffff"/>
<strokewidth width="1"/>
<path>
<move x="18.67" y="27"/>
<line x="23" y="31.67"/>
</path>
<stroke/>
<path>
<move x="4.67" y="13.67"/>
<line x="9.33" y="18.34"/>
</path>
<stroke/>
<path>
<move x="4" y="21.67"/>
<line x="10.67" y="21.67"/>
</path>
<stroke/>
<path>
<move x="18.67" y="17"/>
<line x="23" y="12.34"/>
</path>
<stroke/>
<path>
<move x="4.67" y="30.34"/>
<line x="9.33" y="25.67"/>
</path>
<stroke/>
<restore/>
<rect/>
<stroke/>
<strokecolor color="#ffffff"/>
<path>
<move x="9.33" y="18"/>
<line x="16.33" y="18"/>
<line x="16.33" y="25.67"/>
<line x="9.33" y="25.67"/>
<close/>
<move x="22" y="27"/>
<line x="28" y="27"/>
<line x="28" y="33"/>
<line x="22" y="33"/>
<close/>
<move x="22" y="11"/>
<line x="28" y="11"/>
<line x="28" y="17"/>
<line x="22" y="17"/>
<close/>
</path>
<fillstroke/>
</foreground>
</shape>
<shape name="Small Hub" h="28" w="56.34" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="0.99" y="0.5" perimeter="0" name="E"/>
<constraint x="0" y="1" perimeter="0" name="SW"/>
<constraint x="1" y="0" perimeter="0" name="NE"/>
</connections>
<background>
<path>
<move x="49.67" y="13.33"/>
<line x="56.34" y="0"/>
<line x="10.34" y="0"/>
<line x="0" y="13.33"/>
<close/>
<move x="56.34" y="12.33"/>
<line x="56.34" y="0"/>
<line x="49.67" y="13.33"/>
<line x="49.67" y="28"/>
<close/>
<move x="49.67" y="28"/>
<line x="49.67" y="13.33"/>
<line x="0" y="13.33"/>
<line x="0" y="28"/>
<close/>
</path>
</background>
<foreground>
<save/>
<linejoin join="round"/>
<fillstroke/>
<restore/>
<rect/>
<stroke/>
<path>
<move x="8.34" y="26"/>
<line x="8.34" y="23"/>
<line x="5.34" y="23"/>
<line x="5.34" y="26"/>
<close/>
<move x="12.67" y="26"/>
<line x="12.67" y="23"/>
<line x="9.67" y="23"/>
<line x="9.67" y="26"/>
<close/>
<move x="17" y="26"/>
<line x="17" y="23"/>
<line x="14" y="23"/>
<line x="14" y="26"/>
<close/>
<move x="21.34" y="26"/>
<line x="21.34" y="23"/>
<line x="18.34" y="23"/>
<line x="18.34" y="26"/>
<close/>
<move x="25.67" y="26"/>
<line x="25.67" y="23"/>
<line x="22.67" y="23"/>
<line x="22.67" y="26"/>
<close/>
<move x="30" y="26"/>
<line x="30" y="23"/>
<line x="27" y="23"/>
<line x="27" y="26"/>
<close/>
<move x="34.34" y="26"/>
<line x="34.34" y="23"/>
<line x="31.34" y="23"/>
<line x="31.34" y="26"/>
<close/>
<move x="38.67" y="26"/>
<line x="38.67" y="23"/>
<line x="35.67" y="23"/>
<line x="35.67" y="26"/>
<close/>
<move x="43" y="26"/>
<line x="43" y="23"/>
<line x="40" y="23"/>
<line x="40" y="26"/>
<close/>
</path>
<stroke/>
<fillcolor color="#ffffff"/>
<path>
<move x="41.67" y="6"/>
<line x="43.34" y="4.33"/>
<line x="46.67" y="6.33"/>
<line x="40" y="8"/>
<line x="41.34" y="6.66"/>
<line x="28.34" y="6.66"/>
<line x="28.67" y="6"/>
<close/>
<move x="29.34" y="6"/>
<line x="29" y="6.66"/>
<line x="16" y="6.66"/>
<line x="14.67" y="8"/>
<line x="11" y="6.33"/>
<line x="18.34" y="4"/>
<line x="16.34" y="6"/>
<close/>
</path>
<fill/>
</foreground>
</shape>
<shape name="Universal Gateway" h="35" w="34.67" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
<constraint x="0.12" y="0.07" perimeter="0" name="NW"/>
<constraint x="0" y="1" perimeter="0" name="SW"/>
<constraint x="1" y="0" perimeter="0" name="NE"/>
<constraint x="0.91" y="0.92" perimeter="0" name="SE"/>
</connections>
<background>
<save/>
<path>
<move x="28.67" y="35"/>
<line x="28.67" y="5.67"/>
<line x="0" y="5.67"/>
<line x="0" y="35"/>
<close/>
</path>
</background>
<foreground>
<linejoin join="round"/>
<fillstroke/>
<strokecolor color="#ffffff"/>
<strokewidth width="1.33"/>
<path>
<move x="14.34" y="26.67"/>
<curve x1="18" y1="26.67" x2="20.67" y2="23.67" x3="20.67" y3="20.33"/>
<curve x1="20.67" y1="16.67" x2="18" y2="14" x3="14.34" y3="14"/>
<curve x1="11" y1="14" x2="8" y2="16.67" x3="8" y3="20.33"/>
<curve x1="8" y1="23.67" x2="11" y2="26.67" x3="14.34" y3="26.67"/>
<close/>
</path>
<stroke/>
<strokewidth width="1.33"/>
<path>
<move x="14.34" y="14"/>
<line x="24" y="14"/>
</path>
<stroke/>
<restore/>
<save/>
<fillcolor color="#ffffff"/>
<strokecolor color="#ffffff"/>
<strokewidth width="1.33"/>
<path>
<move x="24" y="10.33"/>
<line x="24" y="17.33"/>
<line x="28.34" y="14"/>
<close/>
</path>
<fill/>
<path>
<move x="8" y="20.33"/>
<line x="8" y="10.67"/>
</path>
<stroke/>
<path>
<move x="4.34" y="10.67"/>
<line x="11.34" y="10.67"/>
<line x="8" y="6.67"/>
<close/>
</path>
<fill/>
<path>
<move x="20.67" y="20.33"/>
<line x="20.67" y="30"/>
</path>
<stroke/>
<path>
<move x="24.34" y="30"/>
<line x="17.34" y="30"/>
<line x="20.67" y="34"/>
<close/>
</path>
<fill/>
<path>
<move x="14.34" y="26.67"/>
<line x="5" y="26.67"/>
</path>
<stroke/>
<path>
<move x="4.67" y="30.33"/>
<line x="4.67" y="23.33"/>
<line x="0.67" y="26.67"/>
<close/>
</path>
<fill/>
<restore/>
<linejoin join="round"/>
<path>
<move x="28.67" y="5.67"/>
<line x="0" y="5.67"/>
<line x="7.34" y="0"/>
<line x="34.67" y="0"/>
<line x="34.67" y="0"/>
<line x="34.67" y="29"/>
<line x="28.67" y="35"/>
<close/>
<move x="34.67" y="0"/>
<line x="28.67" y="5.67"/>
</path>
<fillstroke/>
<fillcolor color="#ffffff"/>
<path>
<move x="17.67" y="17.33"/>
<curve x1="17.67" y1="21.33" x2="17.67" y2="21.33" x3="17.67" y3="21.33"/>
<curve x1="17.67" y1="23.33" x2="16.67" y2="24.67" x3="14.34" y3="24.67"/>
<curve x1="12" y1="24.67" x2="11" y2="23.33" x3="11" y3="21.67"/>
<curve x1="11" y1="17.33" x2="11" y2="17.33" x3="11" y3="17.33"/>
<curve x1="12" y1="17.33" x2="12" y2="17.33" x3="12" y3="17.33"/>
<curve x1="12" y1="21.33" x2="12" y2="21.33" x3="12" y3="21.33"/>
<curve x1="12" y1="23" x2="13.34" y2="23.67" x3="14.34" y3="23.67"/>
<curve x1="15.34" y1="23.67" x2="16.67" y2="23" x3="16.67" y3="21.33"/>
<curve x1="16.67" y1="17.33" x2="16.67" y2="17.33" x3="16.67" y3="17.33"/>
<close/>
</path>
<fill/>
</foreground>
</shape>
<shape name="VPN Gateway" h="30.34" w="57.67" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
<constraint x="0.1" y="0.15" perimeter="0" name="NW"/>
<constraint x="0" y="1" perimeter="0" name="SW"/>
<constraint x="1" y="0" perimeter="0" name="NE"/>
<constraint x="0.93" y="0.82" perimeter="0" name="SE"/>
</connections>
<background>
<save/>
<path>
<move x="0" y="30.34"/>
<line x="0" y="9"/>
<line x="47.67" y="9"/>
<line x="47.67" y="30.34"/>
<close/>
<move x="47.67" y="9"/>
<line x="0" y="9"/>
<line x="12" y="0"/>
<line x="57.67" y="0"/>
<close/>
<move x="57.67" y="0"/>
<line x="57.67" y="20.67"/>
<line x="47.67" y="30.34"/>
<line x="47.67" y="9"/>
<close/>
</path>
</background>
<foreground>
<linejoin join="round"/>
<fillstroke/>
<restore/>
<rect/>
<stroke/>
<strokecolor color="#036897"/>
<strokewidth width="1"/>
<fillcolor color="#ffffff"/>
<path>
<move x="25.33" y="17.34"/>
<curve x1="27" y1="17.34" x2="27" y2="17.34" x3="27" y3="17.34"/>
<curve x1="27.33" y1="17.67" x2="27.33" y2="17.67" x3="27.33" y3="17.67"/>
<curve x1="28.67" y1="17.67" x2="28.67" y2="17.67" x3="28.67" y3="17.67"/>
<curve x1="30" y1="16.67" x2="30" y2="16.67" x3="30" y3="16.67"/>
<curve x1="31.33" y1="16.67" x2="31.33" y2="16.67" x3="31.33" y3="16.67"/>
<curve x1="32.67" y1="18" x2="32.67" y2="18" x3="32.67" y3="18"/>
<curve x1="34" y1="18" x2="34" y2="18" x3="34" y3="18"/>
<curve x1="35.67" y1="16.67" x2="35.67" y2="16.67" x3="35.67" y3="16.67"/>
<curve x1="37" y1="16.67" x2="37" y2="16.67" x3="37" y3="16.67"/>
<curve x1="38" y1="17.67" x2="38" y2="17.67" x3="38" y3="17.67"/>
<curve x1="39.33" y1="17.67" x2="39.33" y2="17.67" x3="39.33" y3="17.67"/>
<curve x1="40" y1="16.67" x2="40" y2="16.67" x3="40" y3="16.67"/>
<curve x1="43.67" y1="19" x2="43.67" y2="19" x3="43.67" y3="19"/>
<curve x1="41" y1="21.34" x2="41" y2="21.34" x3="41" y3="21.34"/>
<curve x1="25.33" y1="21.34" x2="25.33" y2="21.34" x3="25.33" y3="21.34"/>
<curve x1="25.33" y1="22" x2="25.33" y2="22" x3="25.33" y3="22"/>
<curve x1="23" y1="22" x2="23" y2="22" x3="23" y3="22"/>
<curve x1="22.33" y1="22" x2="21.67" y2="22.34" x3="21.33" y3="22.67"/>
<curve x1="20.33" y1="24.67" x2="18.33" y2="26" x3="16" y3="26"/>
<curve x1="12.33" y1="26" x2="9.33" y2="23" x3="9.33" y3="19.34"/>
<curve x1="9.33" y1="15.67" x2="12.33" y2="12.67" x3="16" y3="12.67"/>
<curve x1="18.33" y1="12.67" x2="20.33" y2="13.67" x3="21.33" y3="15.67"/>
<curve x1="21.67" y1="16.34" x2="22.33" y2="16.67" x3="23" y3="16.67"/>
<curve x1="25.33" y1="16.67" x2="25.33" y2="16.67" x3="25.33" y3="16.67"/>
<curve x1="25.33" y1="17.34" x2="25.33" y2="17.34" x3="25.33" y3="17.34"/>
</path>
<fillstroke/>
<path>
<move x="42.33" y="20.34"/>
<curve x1="25.33" y1="20.34" x2="25.33" y2="20.34" x3="25.33" y3="20.34"/>
<curve x1="24.67" y1="20.34" x2="23.67" y2="20.34" x3="23" y3="21"/>
<curve x1="23.67" y1="21.34" x2="24.67" y2="21.34" x3="25.33" y3="21.34"/>
</path>
<stroke/>
<path>
<move x="43" y="18.67"/>
<curve x1="26" y1="18.67" x2="26" y2="18.67" x3="26" y3="18.67"/>
<curve x1="25.33" y1="18.67" x2="23.67" y2="18.67" x3="23.33" y3="18.34"/>
</path>
<stroke/>
<path>
<move x="16" y="25"/>
<curve x1="19.33" y1="25" x2="21.67" y2="22.34" x3="21.67" y3="19.34"/>
<curve x1="21.67" y1="16" x2="19.33" y2="13.34" x3="16" y3="13.34"/>
<curve x1="12.67" y1="13.34" x2="10" y2="16" x3="10" y3="19.34"/>
<curve x1="10" y1="22.67" x2="12.67" y2="25" x3="16" y3="25"/>
<close/>
</path>
<stroke/>
<path>
<move x="12.67" y="20.67"/>
<curve x1="13.33" y1="20.67" x2="14" y2="20" x3="14" y3="19.34"/>
<curve x1="14" y1="18.34" x2="13.33" y2="17.67" x3="12.67" y3="17.67"/>
<curve x1="11.67" y1="17.67" x2="11" y2="18.34" x3="11" y3="19.34"/>
<curve x1="11" y1="20" x2="11.67" y2="20.67" x3="12.67" y3="20.67"/>
<close/>
</path>
<stroke/>
</foreground>
</shape>
</shapes>