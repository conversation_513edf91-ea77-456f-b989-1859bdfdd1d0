html body .geToolbarContainer .geButton, html body .geToolbarContainer .geLabel {
	padding:3px 5px 5px 5px;
	position: relative;
	text-align: center;
	vertical-align: middle;
	border-radius:3px;
}
html body .geMenubarContainer .geBigButton {
	margin-top: 4px;
}
html body .geBigStandardButton:active, .geSidebarContainer .geTitle:active {
	background-color: #DEEBFF;
	color: #0052CC;
}
body .geToolbarContainer .geButton:active, body .geToolbarContainer .geLabel:active {
	background-color: #DEEBFF;
	color: #0052CC;
}
body > .geToolbarContainer .geLabel, body > .geToolbarContainer .geButton {
	-webkit-box-shadow: none !important;
	-moz-box-shadow: none !important;
	box-shadow: none !important;
	border: 1px solid transparent !important;
}
body > .geToolbarContainer {
	background: #f5f5f5 !important;
	border-bottom: 1px solid #ccc !important;
	-webkit-box-shadow: none !important;
	-moz-box-shadow: none !important;
	box-shadow: none !important;
}
body > .geToolbarContainer > .geToolbar {
	padding-top:4px !important;
}
body > .geToolbarContainer > .geToolbar .geSeparator {
	width:1px !important;
	background:#cccccc !important;
	margin-top:3px;
	height:24px;
}
.geSidebarContainer .geToolbarContainer .geButton {
	padding:0px 2px 4px 2px;
}
.geToolbarContainer .geLabel {
	height:18px;
	_height:31px;
}
html body .geStatus .geStatusAlert {
	color:#ffffff !important;
	font-size:12px;
	border:none;
	border-radius:6px;
	text-shadow: rgb(41, 89, 137) 0px 1px 0px;
	background-color:#428bca;
	background-image:linear-gradient(rgb(70, 135, 206) 0px, rgb(48, 104, 162) 100%);
	-webkit-box-shadow: rgba(255, 255, 255, 0.0980392) 0px 1px 0px 0px inset, rgba(0, 0, 0, 0.2) 0px 1px 1px 0px;
	-moz-box-shadow: rgba(255, 255, 255, 0.0980392) 0px 1px 0px 0px inset, rgba(0, 0, 0, 0.2) 0px 1px 1px 0px;
	box-shadow: rgba(255, 255, 255, 0.0980392) 0px 1px 0px 0px inset, rgba(0, 0, 0, 0.2) 0px 1px 1px 0px;
}
html body .geStatus .geStatusAlert:hover {
    background-color: #2d6ca2;
    background-image: linear-gradient(rgb(90, 148, 211) 0px, rgb(54, 115, 181) 100%);
}
html body .geStatus .geStatusMessage {
	color:#ffffff !important;
	font-size:12px;
	border:none;
	border-radius:6px;
	text-shadow: rgb(41, 89, 137) 0px 1px 0px;
	background-color:#428bca;
	background-image:linear-gradient(rgb(70, 135, 206) 0px, rgb(48, 104, 162) 100%);
	-webkit-box-shadow: rgba(255, 255, 255, 0.0980392) 0px 1px 0px 0px inset, rgba(0, 0, 0, 0.2) 0px 1px 1px 0px;
	-moz-box-shadow: rgba(255, 255, 255, 0.0980392) 0px 1px 0px 0px inset, rgba(0, 0, 0, 0.2) 0px 1px 1px 0px;
	box-shadow: rgba(255, 255, 255, 0.0980392) 0px 1px 0px 0px inset, rgba(0, 0, 0, 0.2) 0px 1px 1px 0px;
}
html body .geStatus .geStatusMessage:hover {
    background-color: #2d6ca2;
    background-image: linear-gradient(rgb(90, 148, 211) 0px, rgb(54, 115, 181) 100%);
}
html body div.mxWindow .geToolbarContainer {
	font-size:11px !important;	
	color: #000000 !important;
	-webkit-box-shadow: none !important;
	-moz-box-shadow: none !important;
	box-shadow: none !important;
	border-width: 0px 0px 1px !important;
	border-color: rgb(195, 195, 195) !important;
	border-style: solid !important;
	border-bottom:1px solid #e0e0e0;
}
html body div.mxWindow .geButton, .mxWindow .geLabel {
	-webkit-box-shadow: none !important;
	-moz-box-shadow: none !important;
	box-shadow: none !important;
	background-image: none !important;
	border:1px solid transparent !important;
}
div.mxWindow .geButton {
	margin:1px !important;
}
div.mxWindow .geLabel {
	padding:3px 5px 3px 5px !important;
	margin:2px;
}
div.mxWindow .geButton:hover, .mxWindow .geLabel:hover {
	-webkit-box-shadow: none !important;
	-moz-box-shadow: none !important;
	box-shadow: none !important;
	background: none !important;
	border:1px solid gray;
}
div.mxWindow .geButton:active, .mxWindow .geLabel:active {
	-webkit-box-shadow: none !important;
	-moz-box-shadow: none !important;
	box-shadow: none !important;
	background-image: none !important;
	border:1px solid black;
}
body > .geToolbarContainer .geButton {
	margin:0px -1px 0px 0px !important;
	height:20px;
}
html body .geSidebarTooltip, .geSidebarTooltipImage {
	z-index:2;
}
html body .geSidebarContainer .geTitle {
	font-size:13px;
	padding:8px 0px 8px 16px;
}
html body .geMenubarContainer * {
	color: #DEEBFF;
}
html body .geMenubarContainer .geStatus {
	color: rgb(179, 179, 179);
}
.geMenubarContainer .geItem:hover:not(.geStatus) {
	background-color: rgba(9, 30, 66, 0.48) !important;
}
html body .geToolbarContainer .geLabel {
	margin:0px;
	padding:6px 20px 4px 10px !important;
}
.geToolbar .geSeparator {
	width:0px !important;
}
.geMenubarContainer .geItem, .geToolbar .geButton, .geToolbar .geLabel, .geSidebar, .geSidebarContainer .geTitle, .geSidebar .geItem, .mxPopupMenuItem {
	-webkit-transition: none;
	-moz-transition: none;
	-o-transition: none;
	-ms-transition: none;
	transition: none;
}
html body .geMenubarContainer {
	background-color: #0049B0;
	color: #ffffff;
	font-size: 14px;
}
html body .geMenubar > .geItem {
	padding-left:14px;
	padding-right:15px;
}
html body .geSidebarContainer .geToolbarContainer {
	-webkit-box-shadow: none;
	-moz-box-shadow: none;
	box-shadow: none;
	border:none;
}
html body .geSidebarContainer .geToolbarContainer .geButton {
	margin:2px !important;
	height:20px !important;
}
html body .geSidebarContainer .geToolbarContainer .geLabel {
	margin:2px !important;
	padding:4px !important;
}
html body .geToolbar {
	margin:0px;
	padding:8px 10px 0px 10px;
	-webkit-box-shadow:none;
	-moz-box-shadow:none;
	box-shadow:none;
	border:none;
}
html body .geMenubarContainer .mxDisabled {
	opacity: 1;
	color: rgb(179, 179, 179);
}
html .geButtonContainer {
	padding-right:10px;
}
.geDialogTitle {
	box-sizing:border-box;
	white-space:nowrap;
	background:rgb(32, 80, 129);
	border-bottom:1px solid rgb(192, 192, 192);
	font-size:15px;
	font-weight:bold;
	text-align:center;
	color:white;
}
.geDialogFooter {
	background:whiteSmoke;
	white-space:nowrap;
	text-align:right;
	box-sizing:border-box;
	border-top:1px solid #e5e5e5;
	color:darkGray;
}
html .geNotification-bell {
  opacity: 1;
}
html .geNotification-bell * {
  background-color: #DEEBFF;
  box-shadow: 0px 0px 10px #DEEBFF;
}