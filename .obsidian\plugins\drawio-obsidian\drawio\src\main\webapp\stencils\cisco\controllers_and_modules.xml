<shapes name="mxgraph.cisco.controllers_and_modules">
<shape name="10GE FCoE" h="55" w="40" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="0.98" y="0.5" perimeter="0" name="E"/>
<constraint x="0.2" y="0" perimeter="0" name="NW"/>
<constraint x="0" y="1" perimeter="0" name="SW"/>
<constraint x="0.98" y="0" perimeter="0" name="NE"/>
<constraint x="0.82" y="1" perimeter="0" name="SE"/>
</connections>
<background>
<save/>
<fillcolor color="#6daed7"/>
<path>
<move x="33" y="55"/>
<line x="33" y="29.66"/>
<line x="0" y="29.66"/>
<line x="0" y="55"/>
<close/>
</path>
</background>
<foreground>
<linejoin join="round"/>
<fillstroke/>
<restore/>
<linejoin join="round"/>
<path>
<move x="40" y="48.66"/>
<line x="40" y="23.66"/>
<line x="33" y="29.66"/>
<line x="33" y="55"/>
<close/>
</path>
<fillstroke/>
<path>
<move x="40" y="23.33"/>
<line x="33" y="29.66"/>
<line x="0" y="29.66"/>
<line x="8.33" y="23.33"/>
<close/>
</path>
<fillstroke/>
<path>
<move x="40" y="0"/>
<line x="33" y="6.66"/>
<line x="0" y="6.66"/>
<line x="8.33" y="0"/>
<close/>
<move x="33" y="30"/>
<line x="33" y="6.66"/>
<line x="0" y="6.66"/>
<line x="0" y="30"/>
<close/>
<move x="40" y="24"/>
<line x="40" y="0.33"/>
<line x="33" y="6.66"/>
<line x="33" y="30"/>
<close/>
</path>
<fillstroke/>
<fillcolor color="#ffffff"/>
<path>
<move x="13.33" y="17.66"/>
<line x="8.33" y="17.66"/>
<line x="8.33" y="16.33"/>
<line x="6.66" y="18.33"/>
<line x="8.33" y="20.33"/>
<line x="8.33" y="19"/>
<line x="13.33" y="19"/>
<close/>
<move x="16" y="21.66"/>
<line x="16" y="26.66"/>
<line x="15" y="26.66"/>
<line x="16.66" y="28.66"/>
<line x="18.66" y="26.66"/>
<line x="17.33" y="26.66"/>
<line x="17.33" y="21.66"/>
<close/>
<move x="16" y="15"/>
<line x="16" y="10"/>
<line x="15" y="10"/>
<line x="16.66" y="8.33"/>
<line x="18.66" y="10"/>
<line x="17.33" y="10"/>
<line x="17.33" y="15"/>
<close/>
<move x="20.33" y="19"/>
<line x="25" y="19"/>
<line x="25" y="20.33"/>
<line x="27" y="18.33"/>
<line x="25" y="16.33"/>
<line x="25" y="17.66"/>
<line x="20.33" y="17.66"/>
<close/>
</path>
<fill/>
<strokecolor color="#ffffff"/>
<strokewidth width="0.67"/>
<path>
<move x="24.66" y="26.33"/>
<curve x1="24" y1="27.33" x2="19.66" y2="24.66" x3="15.33" y3="20.33"/>
<curve x1="11" y1="16" x2="8" y2="11.66" x3="9" y3="10.66"/>
<curve x1="10" y1="9.66" x2="14.33" y2="12.33" x3="18.66" y3="16.66"/>
<curve x1="23" y1="21" x2="25.66" y2="25.33" x3="24.66" y3="26.33"/>
<close/>
</path>
<stroke/>
<path>
<move x="24.66" y="10.66"/>
<curve x1="25.66" y1="11.66" x2="22.66" y2="16" x3="18.33" y3="20.33"/>
<curve x1="14" y1="24.66" x2="10" y2="27.33" x3="9" y3="26.33"/>
<curve x1="8" y1="25.33" x2="10.66" y2="21.33" x3="15" y3="16.66"/>
<curve x1="19.33" y1="12.33" x2="23.66" y2="9.66" x3="24.66" y3="10.66"/>
<close/>
</path>
<stroke/>
<fillcolor color="#b5b5b5"/>
<path>
<move x="18.66" y="21.33"/>
<curve x1="20.33" y1="20" x2="21" y2="18" x3="19.66" y3="16.33"/>
<curve x1="18.66" y1="14.66" x2="16.33" y2="14.33" x3="14.66" y3="15.33"/>
<curve x1="13" y1="16.33" x2="12.66" y2="18.66" x3="13.66" y3="20.33"/>
<curve x1="15" y1="22" x2="17.33" y2="22.33" x3="18.66" y3="21.33"/>
<close/>
</path>
<fill/>
<strokecolor color="#ffffff"/>
<strokewidth width="2"/>
<path>
<move x="15.33" y="38.33"/>
<line x="7" y="38.33"/>
<move x="18.33" y="35.66"/>
<line x="27.66" y="35.66"/>
<move x="15.33" y="49.33"/>
<line x="7" y="49.33"/>
<move x="18.33" y="46.66"/>
<line x="27.66" y="46.66"/>
</path>
<stroke/>
<strokewidth width="1"/>
<fillcolor color="#ffffff"/>
<path>
<move x="25.66" y="32.33"/>
<line x="25.66" y="38.66"/>
<line x="29.33" y="35.66"/>
<close/>
<move x="8" y="41.33"/>
<line x="8" y="35.33"/>
<line x="4" y="38.33"/>
<close/>
<move x="25.66" y="43.33"/>
<line x="25.66" y="49.66"/>
<line x="29.33" y="46.66"/>
<close/>
<move x="8" y="52.33"/>
<line x="8" y="46.33"/>
<line x="4" y="49.33"/>
<close/>
</path>
<fill/>
</foreground>
</shape>
<shape name="3174 (Desktop) Cluster Controller" h="20.33" w="48" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
<constraint x="0.11" y="0.17" perimeter="0" name="NW"/>
<constraint x="0" y="1" perimeter="0" name="SW"/>
<constraint x="1" y="0" perimeter="0" name="NE"/>
<constraint x="0.89" y="0.83" perimeter="0" name="SE"/>
</connections>
<background>
<path>
<move x="0" y="20.33"/>
<line x="0" y="7.67"/>
<line x="39.67" y="7.67"/>
<line x="39.67" y="20.33"/>
<close/>
<move x="48" y="0"/>
<line x="48" y="11"/>
<line x="39.67" y="20.33"/>
<line x="39.67" y="7.67"/>
<line x="0" y="7.67"/>
<line x="10" y="0"/>
<close/>
</path>
</background>
<foreground>
<linejoin join="round"/>
<fillstroke/>
<strokewidth width="0.67"/>
<path>
<move x="39.67" y="7.67"/>
<line x="47.67" y="0"/>
</path>
<stroke/>
<path>
<move x="13" y="16.67"/>
<line x="13" y="14.67"/>
<line x="4" y="14.67"/>
<line x="4" y="16.67"/>
<close/>
</path>
<fillstroke/>
<path>
<move x="25.67" y="11.67"/>
<line x="35.67" y="11.67"/>
</path>
<stroke/>
<path>
<move x="25.67" y="16"/>
<line x="35.67" y="16"/>
</path>
<stroke/>
</foreground>
</shape>
<shape name="3X74 (Floor) Cluster Controller" h="41.67" w="48.66" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0.01" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
<constraint x="0.07" y="0.07" perimeter="0" name="NW"/>
<constraint x="0.095" y="1" perimeter="0" name="SW"/>
<constraint x="1" y="0" perimeter="0" name="NE"/>
<constraint x="0.815" y="1" perimeter="0" name="SE"/>
</connections>
<background>
<save/>
<path>
<move x="42.33" y="6.67"/>
<line x="9" y="6.67"/>
<line x="9" y="39.34"/>
<line x="42.33" y="39.34"/>
<close/>
</path>
</background>
<foreground>
<linejoin join="round"/>
<fillstroke/>
<path>
<move x="42.33" y="39.34"/>
<line x="48.66" y="32.67"/>
<line x="48.66" y="0"/>
<line x="6.66" y="0"/>
<line x="0" y="6.67"/>
<line x="42.33" y="6.67"/>
<close/>
</path>
<fillstroke/>
<restore/>
<rect/>
<stroke/>
<path>
<move x="42.33" y="6.67"/>
<line x="48.66" y="0"/>
<move x="28.33" y="11.67"/>
<line x="34" y="11.67"/>
<move x="37" y="14.34"/>
<line x="37" y="8"/>
<line x="10" y="8"/>
<line x="10" y="14.34"/>
<close/>
<move x="37" y="22.67"/>
<line x="37" y="17.34"/>
<line x="33" y="17.34"/>
<line x="33" y="22.67"/>
<close/>
</path>
<stroke/>
<save/>
<strokecolor color="#000000"/>
<fillcolor color="#ffffff"/>
<path>
<move x="0.66" y="38.67"/>
<line x="0.66" y="7"/>
<line x="9" y="7"/>
<line x="9" y="38.67"/>
<close/>
</path>
<fillstroke/>
<path>
<move x="2.33" y="8.67"/>
<line x="7.66" y="8.67"/>
<move x="2.33" y="10.34"/>
<line x="7.66" y="10.34"/>
<move x="2.33" y="12"/>
<line x="7.66" y="12"/>
<move x="2.33" y="13.67"/>
<line x="7.66" y="13.67"/>
<move x="2.33" y="15.34"/>
<line x="7.66" y="15.34"/>
<move x="2.33" y="17"/>
<line x="7.66" y="17"/>
<move x="2.33" y="18.67"/>
<line x="7.66" y="18.67"/>
<move x="2.33" y="20.34"/>
<line x="7.66" y="20.34"/>
<move x="2.33" y="22"/>
<line x="7.66" y="22"/>
<move x="2.33" y="23.67"/>
<line x="7.66" y="23.67"/>
<move x="2.33" y="25.34"/>
<line x="7.66" y="25.34"/>
<move x="2.33" y="27"/>
<line x="7.66" y="27"/>
<move x="2.33" y="28.67"/>
<line x="7.66" y="28.67"/>
<move x="2.33" y="30.34"/>
<line x="7.66" y="30.34"/>
<move x="2.33" y="32"/>
<line x="7.66" y="32"/>
<move x="2.33" y="33.67"/>
<line x="7.66" y="33.67"/>
<move x="2.33" y="35.34"/>
<line x="7.66" y="35.34"/>
<move x="2.33" y="37"/>
<line x="7.66" y="37"/>
</path>
<stroke/>
<restore/>
<linejoin join="round"/>
<path>
<move x="6.33" y="39.34"/>
<line x="4.66" y="41.67"/>
<line x="39.66" y="41.67"/>
<line x="38.66" y="39.34"/>
<close/>
<move x="39.66" y="41.67"/>
<line x="41.66" y="39.34"/>
<line x="38.66" y="39.34"/>
<close/>
</path>
<fillstroke/>
</foreground>
</shape>
<shape name="Content Switch Module" h="40.67" w="54.34" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
<constraint x="0.05" y="0.05" perimeter="0" name="NW"/>
<constraint x="0" y="1" perimeter="0" name="SW"/>
<constraint x="1" y="0" perimeter="0" name="NE"/>
<constraint x="0.96" y="0.91" perimeter="0" name="SE"/>
</connections>
<background>
<path>
<move x="50" y="40.67"/>
<line x="14.34" y="40.67"/>
<line x="14.34" y="4.67"/>
<line x="50" y="4.67"/>
<close/>
<move x="19" y="0"/>
<line x="54.34" y="0"/>
<line x="50" y="4.67"/>
<line x="14.34" y="4.67"/>
<close/>
<move x="54.34" y="33.67"/>
<line x="50" y="40.33"/>
<line x="50" y="4.33"/>
<line x="54.34" y="0"/>
<close/>
<move x="14.34" y="40.67"/>
<line x="0" y="40.67"/>
<line x="0" y="4.67"/>
<line x="14.34" y="4.67"/>
<close/>
<move x="4.67" y="0"/>
<line x="19" y="0"/>
<line x="14.34" y="4.67"/>
<line x="0" y="4.67"/>
<close/>
</path>
</background>
<foreground>
<linejoin join="round"/>
<fillstroke/>
<path>
<move x="14.67" y="17.67"/>
<line x="0.34" y="17.67"/>
</path>
<stroke/>
<fillcolor color="#ffffff"/>
<path>
<move x="27" y="22"/>
<line x="19.34" y="22"/>
<line x="19.34" y="20.67"/>
<line x="16.34" y="22.67"/>
<line x="19.34" y="24.33"/>
<line x="19.34" y="23"/>
<line x="27" y="23"/>
<close/>
<move x="28.34" y="26"/>
<line x="22.67" y="31.33"/>
<line x="21.67" y="30.33"/>
<line x="21" y="33.67"/>
<line x="24.67" y="33"/>
<line x="23.67" y="32.33"/>
<line x="29" y="26.67"/>
<close/>
<move x="31.67" y="28"/>
<line x="31.67" y="35.67"/>
<line x="30.34" y="35.67"/>
<line x="32.34" y="38.33"/>
<line x="34.34" y="35.67"/>
<line x="33" y="35.67"/>
<line x="33" y="28"/>
<close/>
<move x="35.67" y="26.67"/>
<line x="41.34" y="32.33"/>
<line x="40.34" y="33"/>
<line x="43.67" y="33.67"/>
<line x="43" y="30.33"/>
<line x="42" y="31.33"/>
<line x="36.67" y="26"/>
<close/>
<move x="37.67" y="23"/>
<line x="45.34" y="23"/>
<line x="45.34" y="24.33"/>
<line x="48.34" y="22.67"/>
<line x="45.34" y="20.67"/>
<line x="45.34" y="22"/>
<line x="37.67" y="22"/>
<close/>
<move x="36.67" y="19.33"/>
<line x="42" y="13.67"/>
<line x="43" y="14.67"/>
<line x="43.67" y="11.33"/>
<line x="40.34" y="12"/>
<line x="41.34" y="13"/>
<line x="35.67" y="18.33"/>
<close/>
<move x="33" y="17.33"/>
<line x="33" y="9.33"/>
<line x="34.34" y="9.33"/>
<line x="32.34" y="6.67"/>
<line x="30.34" y="9.33"/>
<line x="31.67" y="9.33"/>
<line x="31.67" y="17.33"/>
<close/>
<move x="29" y="18.33"/>
<line x="23.67" y="13"/>
<line x="24.67" y="12"/>
<line x="21" y="11.33"/>
<line x="21.67" y="14.67"/>
<line x="22.67" y="13.67"/>
<line x="28.34" y="19.33"/>
<close/>
</path>
<fill/>
<path>
<move x="39" y="17.67"/>
<curve x1="36.34" y1="14.33" x2="31.34" y2="13.33" x3="27.67" y3="15.67"/>
<curve x1="24" y1="18.33" x2="23.34" y2="23.33" x3="25.67" y3="27"/>
<curve x1="28.34" y1="30.67" x2="33.34" y2="31.33" x3="37" y3="29"/>
<curve x1="40.67" y1="26.33" x2="41.34" y2="21.33" x3="39" y3="17.67"/>
<close/>
</path>
<fill/>
<path>
<move x="5.67" y="11.33"/>
<line x="3.67" y="11.33"/>
<line x="3.67" y="10.67"/>
<line x="2.67" y="11.33"/>
<line x="3.67" y="12"/>
<line x="3.67" y="11.67"/>
<line x="5.67" y="11.67"/>
<close/>
<move x="6" y="12.33"/>
<line x="4.67" y="14"/>
<line x="4.34" y="13.67"/>
<line x="4" y="14.67"/>
<line x="5" y="14.67"/>
<line x="4.67" y="14.33"/>
<line x="6.34" y="12.67"/>
<close/>
<move x="7.34" y="13"/>
<line x="7.34" y="15.33"/>
<line x="7" y="15.33"/>
<line x="7.34" y="16.33"/>
<line x="8" y="15.33"/>
<line x="7.67" y="15.33"/>
<line x="7.67" y="13"/>
<close/>
<move x="8.34" y="12.67"/>
<line x="10" y="14.33"/>
<line x="9.67" y="14.67"/>
<line x="10.67" y="14.67"/>
<line x="10.67" y="13.67"/>
<line x="10.34" y="14"/>
<line x="8.67" y="12.33"/>
<close/>
<move x="9" y="11.67"/>
<line x="11.34" y="11.67"/>
<line x="11.34" y="12"/>
<line x="12.34" y="11.33"/>
<line x="11.34" y="10.67"/>
<line x="11.34" y="11.33"/>
<line x="9" y="11.33"/>
<close/>
<move x="8.67" y="10.33"/>
<line x="10.34" y="8.67"/>
<line x="10.67" y="9"/>
<line x="10.67" y="8"/>
<line x="9.67" y="8.33"/>
<line x="10" y="8.33"/>
<line x="8.34" y="10"/>
<close/>
<move x="7.67" y="9.67"/>
<line x="7.67" y="7.33"/>
<line x="8" y="7.33"/>
<line x="7.34" y="6.67"/>
<line x="7" y="7.33"/>
<line x="7.34" y="7.33"/>
<line x="7.34" y="9.67"/>
<close/>
<move x="6.34" y="10"/>
<line x="4.67" y="8.33"/>
<line x="5" y="8.33"/>
<line x="4" y="8"/>
<line x="4.34" y="9"/>
<line x="4.67" y="8.67"/>
<line x="6" y="10.33"/>
<close/>
</path>
<fill/>
<path>
<move x="9.34" y="10"/>
<curve x1="8.67" y1="9" x2="7" y2="8.67" x3="6" y3="9.33"/>
<curve x1="5" y1="10" x2="4.67" y2="11.67" x3="5.34" y3="12.67"/>
<curve x1="6.34" y1="13.67" x2="7.67" y2="14" x3="8.67" y3="13.33"/>
<curve x1="10" y1="12.67" x2="10" y2="11" x3="9.34" y3="10"/>
<close/>
</path>
<fill/>
<path>
<move x="6.67" y="29.33"/>
<line x="6.67" y="35.67"/>
<line x="5.67" y="35.67"/>
<line x="7" y="38.33"/>
<line x="8.67" y="35.67"/>
<line x="7.67" y="35.67"/>
<line x="7.67" y="29.33"/>
<close/>
<move x="8.34" y="29.33"/>
<line x="10.67" y="35.33"/>
<line x="9.67" y="35.67"/>
<line x="12" y="37.33"/>
<line x="12.67" y="34.67"/>
<line x="11.67" y="35"/>
<line x="9.34" y="29"/>
<close/>
<move x="6" y="29.33"/>
<line x="3.67" y="35.33"/>
<line x="4.67" y="35.67"/>
<line x="2.34" y="37.33"/>
<line x="1.67" y="34.67"/>
<line x="2.67" y="35"/>
<line x="5" y="29"/>
<close/>
<move x="6.67" y="19.33"/>
<line x="6.67" y="25.67"/>
<line x="5.67" y="25.67"/>
<line x="7" y="28"/>
<line x="8.67" y="25.67"/>
<line x="7.67" y="25.67"/>
<line x="7.67" y="19.33"/>
<close/>
</path>
<fill/>
</foreground>
</shape>
<shape name="Firewall Service Module (FwSM)" h="53" w="33" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0.005" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="0.99" y="0.5" perimeter="0" name="E"/>
<constraint x="0.08" y="0.04" perimeter="0" name="NW"/>
<constraint x="0" y="1" perimeter="0" name="SW"/>
<constraint x="0.99" y="0.01" perimeter="0" name="NE"/>
<constraint x="0.95" y="0.95" perimeter="0" name="SE"/>
</connections>
<background>
<path>
<move x="32.67" y="0"/>
<line x="29" y="4.33"/>
<line x="0" y="4.33"/>
<line x="5" y="0"/>
<close/>
<move x="0" y="16"/>
<line x="0" y="4.33"/>
<line x="29" y="4.33"/>
<line x="29" y="16"/>
<close/>
<move x="32.67" y="12"/>
<line x="32.67" y="0.33"/>
<line x="29" y="4.33"/>
<line x="29" y="16"/>
<close/>
<move x="29" y="16"/>
<line x="29" y="39"/>
<line x="32.67" y="35"/>
<line x="32.67" y="12"/>
<close/>
<move x="29" y="16"/>
<line x="29" y="38.66"/>
<line x="0" y="38.66"/>
<line x="0" y="16"/>
<close/>
<move x="0" y="53"/>
<line x="0" y="38.66"/>
<line x="29" y="38.66"/>
<line x="29" y="53"/>
<close/>
<move x="32.67" y="49"/>
<line x="32.67" y="35"/>
<line x="29" y="38.66"/>
<line x="29" y="53"/>
<close/>
</path>
</background>
<foreground>
<linejoin join="round"/>
<fillstroke/>
<path>
<move x="29" y="8"/>
<line x="0.34" y="8"/>
<move x="31" y="2"/>
<line x="3" y="2"/>
<move x="29" y="12"/>
<line x="0.34" y="12"/>
<move x="32.67" y="3.66"/>
<line x="29.34" y="7.66"/>
<move x="32.67" y="7.66"/>
<line x="29.34" y="11.66"/>
<move x="8.67" y="4.33"/>
<line x="8.67" y="8"/>
<move x="20" y="4.33"/>
<line x="20" y="8"/>
<move x="8.67" y="12"/>
<line x="8.67" y="15.66"/>
<move x="20" y="12"/>
<line x="20" y="15.66"/>
<move x="14.67" y="8"/>
<line x="14.67" y="11.66"/>
<move x="3.34" y="8"/>
<line x="3.34" y="11.66"/>
<move x="25.67" y="8"/>
<line x="25.67" y="11.66"/>
<move x="13" y="0"/>
<line x="8.67" y="4"/>
<move x="24" y="0"/>
<line x="20" y="4"/>
</path>
<stroke/>
<path>
<move x="31" y="2"/>
<line x="31" y="13.66"/>
</path>
<stroke/>
<fillcolor color="#ffffff"/>
<path>
<move x="15" y="24.66"/>
<line x="15" y="19.66"/>
<line x="16" y="19.66"/>
<line x="14.67" y="17.66"/>
<line x="13.34" y="19.66"/>
<line x="14.34" y="19.66"/>
<line x="14.34" y="24.66"/>
<close/>
<move x="12.67" y="25.33"/>
<line x="9" y="21.66"/>
<line x="9.67" y="21.33"/>
<line x="7.34" y="20.66"/>
<line x="8" y="23"/>
<line x="8.34" y="22.33"/>
<line x="12" y="26"/>
<close/>
<move x="11.34" y="27.66"/>
<line x="6.34" y="27.66"/>
<line x="6.34" y="26.66"/>
<line x="4.34" y="28"/>
<line x="6.34" y="29.33"/>
<line x="6.34" y="28.33"/>
<line x="11.34" y="28.33"/>
<close/>
<move x="12" y="30.33"/>
<line x="8.34" y="33.66"/>
<line x="8" y="33"/>
<line x="7.34" y="35.33"/>
<line x="9.67" y="35"/>
<line x="9" y="34.33"/>
<line x="12.67" y="30.66"/>
<close/>
<move x="14.34" y="29.66"/>
<line x="14.34" y="39.33"/>
<line x="13.34" y="39.33"/>
<line x="14.67" y="41.33"/>
<line x="16" y="39.33"/>
<line x="15" y="39.33"/>
<line x="15" y="29.66"/>
<close/>
<move x="17" y="30.66"/>
<line x="20.34" y="34.33"/>
<line x="19.67" y="35"/>
<line x="22" y="35.33"/>
<line x="21.67" y="33"/>
<line x="21" y="33.66"/>
<line x="17.34" y="30.33"/>
<close/>
<move x="18" y="28.33"/>
<line x="23.34" y="28.33"/>
<line x="23.34" y="29.33"/>
<line x="25" y="28"/>
<line x="23.34" y="26.66"/>
<line x="23.34" y="27.66"/>
<line x="18" y="27.66"/>
<close/>
<move x="17.34" y="26"/>
<line x="21" y="22.33"/>
<line x="21.67" y="23"/>
<line x="22" y="20.66"/>
<line x="19.67" y="21.33"/>
<line x="20.34" y="21.66"/>
<line x="17" y="25.33"/>
<close/>
</path>
<fill/>
<path>
<move x="17.67" y="32.33"/>
<curve x1="20" y1="30.66" x2="20.67" y2="27.33" x3="19" y3="25"/>
<curve x1="17.34" y1="22.66" x2="14.34" y2="22" x3="11.67" y3="23.66"/>
<curve x1="9.34" y1="25.33" x2="9" y2="28.66" x3="10.67" y3="31"/>
<curve x1="12.34" y1="33.33" x2="15.34" y2="34" x3="17.67" y3="32.33"/>
<close/>
</path>
<fill/>
<fillcolor color="#ffffff"/>
<path>
<move x="14.34" y="44.33"/>
<line x="22" y="44.33"/>
<line x="22" y="43.66"/>
<line x="24.67" y="45"/>
<line x="22" y="46.66"/>
<line x="22" y="46"/>
<line x="14.34" y="46"/>
<close/>
<move x="14.34" y="49"/>
<line x="22" y="49"/>
<line x="22" y="48.33"/>
<line x="24.67" y="49.66"/>
<line x="22" y="51.33"/>
<line x="22" y="50.66"/>
<line x="14.34" y="50.66"/>
<close/>
<move x="14.67" y="42"/>
<line x="7" y="42"/>
<line x="7" y="41.33"/>
<line x="4.34" y="42.66"/>
<line x="7" y="44.33"/>
<line x="7" y="43.66"/>
<line x="14.67" y="43.66"/>
<close/>
<move x="14.67" y="46.66"/>
<line x="7" y="46.66"/>
<line x="7" y="46"/>
<line x="4.34" y="47.66"/>
<line x="7" y="49"/>
<line x="7" y="48.33"/>
<line x="14.67" y="48.33"/>
<close/>
</path>
<fill/>
</foreground>
</shape>
<shape name="Service Module" h="41.33" w="32.66" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
<constraint x="0.07" y="0.05" perimeter="0" name="NW"/>
<constraint x="0" y="1" perimeter="0" name="SW"/>
<constraint x="1" y="0" perimeter="0" name="NE"/>
<constraint x="0.94" y="0.96" perimeter="0" name="SE"/>
</connections>
<background>
<path>
<move x="32.66" y="0"/>
<line x="29" y="4.33"/>
<line x="0" y="4.33"/>
<line x="4.66" y="0"/>
<close/>
<move x="29" y="4.33"/>
<line x="29" y="18.66"/>
<line x="32.66" y="14.66"/>
<line x="32.66" y="0"/>
<close/>
</path>
</background>
<foreground>
<linejoin join="round"/>
<fillstroke/>
<path>
<move x="29" y="41.33"/>
<line x="29" y="18.33"/>
<line x="0" y="18.33"/>
<line x="0" y="41.33"/>
<close/>
</path>
<fillstroke/>
<path>
<move x="29" y="41.33"/>
<line x="29" y="18.33"/>
<line x="0" y="18.33"/>
<line x="0" y="41.33"/>
</path>
<stroke/>
<path>
<move x="0" y="4.33"/>
<line x="0" y="18.33"/>
<line x="29" y="18.33"/>
<line x="29" y="4.33"/>
<close/>
<move x="32.66" y="37.66"/>
<line x="32.66" y="14.66"/>
<line x="29" y="18.66"/>
<line x="29" y="41.33"/>
<close/>
</path>
<fillstroke/>
<fillcolor color="#ffffff"/>
<path>
<move x="15" y="32.66"/>
<line x="15" y="37.66"/>
<line x="15.66" y="37.66"/>
<line x="14.33" y="39.33"/>
<line x="13.33" y="37.66"/>
<line x="14" y="37.66"/>
<line x="14" y="32.66"/>
<close/>
<move x="12.33" y="32"/>
<line x="8.66" y="35.33"/>
<line x="9.33" y="36"/>
<line x="7" y="36.33"/>
<line x="7.66" y="34.33"/>
<line x="8" y="34.66"/>
<line x="11.66" y="31.33"/>
<close/>
<move x="11" y="29.66"/>
<line x="6" y="29.66"/>
<line x="6" y="30.33"/>
<line x="4" y="29"/>
<line x="6" y="28"/>
<line x="6" y="28.66"/>
<line x="11" y="28.66"/>
<close/>
<move x="11.66" y="27"/>
<line x="8" y="23.33"/>
<line x="7.66" y="24"/>
<line x="7" y="21.66"/>
<line x="9.33" y="22.33"/>
<line x="8.66" y="23"/>
<line x="12.33" y="26.33"/>
<close/>
<move x="14" y="27.33"/>
<line x="14" y="17.66"/>
<line x="13.33" y="17.66"/>
<line x="14.33" y="16"/>
<line x="15.66" y="17.66"/>
<line x="15" y="17.66"/>
<line x="15" y="27.33"/>
<close/>
<move x="16.66" y="26.33"/>
<line x="20" y="23"/>
<line x="19.66" y="22.33"/>
<line x="21.66" y="21.66"/>
<line x="21.33" y="24"/>
<line x="20.66" y="23.33"/>
<line x="17" y="27"/>
<close/>
<move x="18" y="28.66"/>
<line x="23" y="28.66"/>
<line x="23" y="28"/>
<line x="24.66" y="29"/>
<line x="23" y="30.33"/>
<line x="23" y="29.66"/>
<line x="18" y="29.66"/>
<close/>
<move x="17" y="31.33"/>
<line x="20.66" y="34.66"/>
<line x="21.33" y="34.33"/>
<line x="21.66" y="36.33"/>
<line x="19.66" y="36"/>
<line x="20" y="35.33"/>
<line x="16.66" y="32"/>
<close/>
</path>
<fill/>
<path>
<move x="17.33" y="25"/>
<curve x1="20" y1="26.66" x2="20.33" y2="29.66" x3="18.66" y3="32"/>
<curve x1="17" y1="34.66" x2="14" y2="35" x3="11.66" y3="33.33"/>
<curve x1="9.33" y1="31.66" x2="8.66" y2="28.66" x3="10.33" y3="26.33"/>
<curve x1="12" y1="24" x2="15" y2="23.33" x3="17.33" y3="25"/>
<close/>
</path>
<fill/>
<path>
<move x="14" y="9"/>
<line x="21.66" y="9"/>
<line x="21.66" y="8"/>
<line x="24.33" y="9.66"/>
<line x="21.66" y="11"/>
<line x="21.66" y="10.33"/>
<line x="14" y="10.33"/>
<close/>
<move x="14" y="13.66"/>
<line x="21.66" y="13.66"/>
<line x="21.66" y="12.66"/>
<line x="24.33" y="14.33"/>
<line x="21.66" y="16"/>
<line x="21.66" y="15"/>
<line x="14" y="15"/>
<close/>
<move x="14.33" y="6.66"/>
<line x="6.66" y="6.66"/>
<line x="6.66" y="5.66"/>
<line x="4.33" y="7.33"/>
<line x="6.66" y="9"/>
<line x="6.66" y="8"/>
<line x="14.33" y="8"/>
<close/>
<move x="14.33" y="11.33"/>
<line x="6.66" y="11.33"/>
<line x="6.66" y="10.66"/>
<line x="4.33" y="12"/>
<line x="6.66" y="13.66"/>
<line x="6.66" y="13"/>
<line x="14.33" y="13"/>
<close/>
</path>
<fill/>
</foreground>
</shape>
<shape name="System Controller" h="36.33" w="38.66" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
<constraint x="0.13" y="0.09" perimeter="0" name="NW"/>
<constraint x="0" y="1" perimeter="0" name="SW"/>
<constraint x="1" y="0" perimeter="0" name="NE"/>
<constraint x="0.89" y="0.89" perimeter="0" name="SE"/>
</connections>
<background>
<save/>
<path>
<move x="28.66" y="36.33"/>
<line x="28.66" y="7"/>
<line x="0" y="7"/>
<line x="0" y="36.33"/>
<close/>
<move x="28.66" y="7"/>
<line x="38.66" y="0"/>
<line x="10" y="0"/>
<line x="0" y="7"/>
<close/>
<move x="38.66" y="29.33"/>
<line x="38.66" y="0"/>
<line x="29" y="7"/>
<line x="29" y="36.33"/>
<close/>
</path>
</background>
<foreground>
<linejoin join="round"/>
<fillstroke/>
<strokecolor color="#ffffff"/>
<strokewidth width="2"/>
<path>
<move x="14.33" y="21"/>
<curve x1="17" y1="21" x2="19.33" y2="18.66" x3="19.33" y3="16"/>
<curve x1="19.33" y1="13" x2="17" y2="11" x3="14.33" y3="11"/>
<curve x1="11.33" y1="11" x2="9" y2="13" x3="9" y3="16"/>
<curve x1="9" y1="18.66" x2="11.33" y2="21" x3="14.33" y3="21"/>
<close/>
</path>
<stroke/>
<restore/>
<fillcolor color="#ffffff"/>
<strokecolor color="#ffffff"/>
<strokewidth width="2"/>
<path>
</path>
<fill/>
<path>
<move x="17.66" y="30.33"/>
<line x="11" y="30.33"/>
<line x="14.33" y="34.33"/>
<close/>
<move x="9.33" y="30"/>
<line x="3.66" y="26.66"/>
<line x="4.33" y="31.33"/>
<close/>
<move x="19.33" y="30"/>
<line x="24.66" y="26.66"/>
<line x="24" y="31.33"/>
<close/>
</path>
<fill/>
<path>
<move x="14.33" y="22.66"/>
<line x="14.33" y="30.33"/>
<move x="10.66" y="21.66"/>
<line x="6.33" y="28.33"/>
<move x="17.66" y="21.66"/>
<line x="22" y="28.33"/>
</path>
<stroke/>
</foreground>
</shape>
<shape name="Virtual Switch Controller (VSC3000)" h="58.67" w="29.66" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
<constraint x="0.1" y="0.04" perimeter="0" name="NW"/>
<constraint x="0" y="1" perimeter="0" name="SW"/>
<constraint x="1" y="0" perimeter="0" name="NE"/>
<constraint x="0.91" y="0.95" perimeter="0" name="SE"/>
</connections>
<background>
<path>
<move x="0" y="58.67"/>
<line x="24" y="58.67"/>
<line x="24" y="5.34"/>
<line x="0" y="5.34"/>
<close/>
<move x="0" y="5.34"/>
<line x="5.66" y="0"/>
<line x="29.66" y="0"/>
<line x="24" y="5.34"/>
<line x="0" y="5.34"/>
<close/>
<move x="24" y="58.67"/>
<line x="29.66" y="53.34"/>
<line x="29.66" y="0"/>
<line x="24" y="5.34"/>
<line x="24" y="58.67"/>
<close/>
</path>
</background>
<foreground>
<linejoin join="round"/>
<fillstroke/>
<fillcolor color="#ffffff"/>
<path>
<move x="9" y="43.34"/>
<curve x1="9" y1="43" x2="9" y2="42" x3="10" y3="40.34"/>
<curve x1="10.33" y1="40" x2="10.66" y2="39.67" x3="10.66" y3="39.67"/>
<curve x1="8" y1="42" x2="8" y2="42" x3="8" y3="42"/>
<curve x1="7.66" y1="42.34" x2="7.33" y2="42.67" x3="7" y3="43"/>
<curve x1="6" y1="44.67" x2="6" y2="45.67" x3="6" y3="45.67"/>
<close/>
</path>
<fill/>
<path>
<move x="18" y="41.67"/>
<curve x1="16.66" y1="40.34" x2="15" y2="38.34" x3="11.33" y3="39.34"/>
<curve x1="11" y1="39.34" x2="11" y2="39.34" x3="10.66" y3="39.67"/>
<curve x1="8" y1="42" x2="8" y2="42" x3="8" y3="42"/>
<curve x1="9.33" y1="41" x2="12.66" y2="40" x3="15.66" y3="44"/>
<curve x1="13.66" y1="45.34" x2="13.66" y2="45.34" x3="13.66" y3="45.34"/>
<curve x1="18" y1="45.67" x2="18" y2="45.67" x3="18" y3="45.67"/>
<curve x1="19.33" y1="40.34" x2="19.33" y2="40.34" x3="19.33" y3="40.34"/>
<close/>
</path>
<fill/>
<path>
<move x="19.66" y="45"/>
<curve x1="16.66" y1="47.67" x2="16.66" y2="47.67" x3="16.66" y3="47.67"/>
<curve x1="16.66" y1="48" x2="17" y2="49.67" x3="15.66" y3="52.34"/>
<curve x1="13.66" y1="53" x2="11" y2="52" x3="9" y3="47.67"/>
<curve x1="10.66" y1="46.34" x2="10.66" y2="46.34" x3="10.66" y3="46.34"/>
<curve x1="7.33" y1="45.34" x2="7.33" y2="45.34" x3="7.33" y3="45.34"/>
<curve x1="5.33" y1="51" x2="5.33" y2="51" x3="5.33" y3="51"/>
<curve x1="7" y1="49.34" x2="7" y2="49.34" x3="7" y3="49.34"/>
<curve x1="7.66" y1="50.67" x2="8.33" y2="51.67" x3="9.33" y3="52.34"/>
<curve x1="6.33" y1="52.34" x2="6.33" y2="52.34" x3="6.33" y3="52.34"/>
<curve x1="7.66" y1="51.34" x2="7.66" y2="51.34" x3="7.66" y3="51.34"/>
<curve x1="1" y1="53.34" x2="1" y2="53.34" x3="1" y3="53.34"/>
<curve x1="3.66" y1="55" x2="3.66" y2="55" x3="3.66" y3="55"/>
<curve x1="4.33" y1="54.34" x2="4.33" y2="54.34" x3="4.33" y3="54.34"/>
<curve x1="13" y1="54.34" x2="13" y2="54.34" x3="13" y3="54.34"/>
<curve x1="13" y1="54.34" x2="13" y2="54.34" x3="13" y3="54.34"/>
<curve x1="13.33" y1="54.34" x2="14" y2="54.34" x3="14.33" y3="54"/>
<curve x1="14.33" y1="54" x2="14.33" y2="54" x3="14.66" y3="53.67"/>
<curve x1="14.66" y1="53.67" x2="14.66" y2="54" x3="14.66" y3="54"/>
<curve x1="14.66" y1="54" x2="17.33" y2="52" x3="17.66" y3="51.67"/>
<curve x1="18.66" y1="50.34" x2="19.66" y2="48.67" x3="19.66" y3="45"/>
<close/>
</path>
<fill/>
<path>
<move x="11" y="35.67"/>
<curve x1="13.33" y1="33.34" x2="13.33" y2="33.34" x3="13.33" y3="33.34"/>
<curve x1="11" y1="33.34" x2="10" y2="33.34" x3="8.33" y3="33.67"/>
<curve x1="7.66" y1="36.34" x2="7" y2="38" x3="6.33" y3="39.67"/>
<curve x1="8.66" y1="37.67" x2="8.66" y2="37.67" x3="8.66" y3="37.67"/>
<curve x1="10.66" y1="39" x2="12.66" y2="39.67" x3="14.33" y3="38.67"/>
<curve x1="14.33" y1="38.67" x2="14.66" y2="38.67" x3="14.66" y3="38.67"/>
<curve x1="17.33" y1="36" x2="17.33" y2="36" x3="17.33" y3="36"/>
<curve x1="15.66" y1="37.67" x2="13" y2="37.34" x3="11" y3="35.67"/>
<close/>
</path>
<fill/>
<path>
<move x="9" y="31.67"/>
<curve x1="8.33" y1="29.34" x2="8.66" y2="27" x3="10" y3="25"/>
<curve x1="10.33" y1="24.67" x2="10.66" y2="24.34" x3="10.66" y3="24.34"/>
<curve x1="8" y1="27" x2="8" y2="27" x3="8" y3="27"/>
<curve x1="7.66" y1="27" x2="7.33" y2="27.34" x3="7" y3="27.67"/>
<curve x1="6" y1="29.67" x2="5.66" y2="32" x3="6" y3="34.34"/>
<close/>
</path>
<fill/>
<path>
<move x="14.66" y="24.34"/>
<curve x1="13.66" y1="23.67" x2="12.33" y2="23.67" x3="11.33" y3="24"/>
<curve x1="11" y1="24" x2="11" y2="24" x3="10.66" y3="24.34"/>
<curve x1="8" y1="27" x2="8" y2="27" x3="8" y3="27"/>
<curve x1="9.33" y1="25.67" x2="11" y2="25.34" x3="12.66" y3="26"/>
<curve x1="11" y1="27.34" x2="11" y2="27.34" x3="11" y3="27.34"/>
<curve x1="16" y1="27.34" x2="16" y2="27.34" x3="16" y3="27.34"/>
<curve x1="16.66" y1="22.34" x2="16.66" y2="22.34" x3="16.66" y3="22.34"/>
<close/>
</path>
<fill/>
<path>
<move x="18.33" y="26"/>
<curve x1="20.33" y1="29.67" x2="19.66" y2="33.34" x3="18.33" y3="35"/>
<curve x1="18" y1="35.34" x2="17.66" y2="35.67" x3="17.33" y3="36"/>
<curve x1="14.66" y1="38.67" x2="14.66" y2="38.67" x3="14.66" y3="38.67"/>
<curve x1="15" y1="38.34" x2="15" y2="38" x3="15.33" y3="37.67"/>
<curve x1="17.66" y1="34.34" x2="16.33" y2="30.67" x3="15.66" y3="28.67"/>
<close/>
</path>
<fill/>
<path>
<move x="14.33" y="23.34"/>
<curve x1="14.33" y1="23.34" x2="14.66" y2="23.34" x3="14.66" y3="23"/>
<curve x1="17.33" y1="20.67" x2="17.33" y2="20.67" x3="17.33" y3="20.67"/>
<curve x1="15.66" y1="22.34" x2="13" y2="22" x3="11" y3="20"/>
<curve x1="8.66" y1="22" x2="8.66" y2="22" x3="8.66" y3="22"/>
<curve x1="10.33" y1="23.67" x2="12.66" y2="24" x3="14.33" y3="23.34"/>
<close/>
</path>
<fill/>
<path>
<move x="11" y="14.67"/>
<curve x1="9" y1="16.34" x2="9" y2="16.34" x3="9" y3="16.34"/>
<curve x1="8.33" y1="14" x2="8.66" y2="11.67" x3="10" y3="9.67"/>
<curve x1="10.33" y1="9.34" x2="10.66" y2="9" x3="10.66" y3="8.67"/>
<curve x1="8" y1="11.34" x2="8" y2="11.34" x3="8" y3="11.34"/>
<curve x1="7.66" y1="11.67" x2="7.33" y2="12" x3="7" y3="12.34"/>
<curve x1="6" y1="14" x2="5.66" y2="16.67" x3="6" y3="19"/>
<curve x1="4" y1="21" x2="4" y2="21" x3="4" y3="21"/>
<curve x1="5.66" y1="21" x2="7" y2="21" x3="9" y3="20.67"/>
<curve x1="9.66" y1="18.67" x2="10" y2="17" x3="11" y3="14.67"/>
</path>
<fill/>
<path>
<move x="21.33" y="11"/>
<curve x1="19.33" y1="13" x2="19.33" y2="13" x3="19.33" y3="13"/>
<curve x1="20" y1="15.34" x2="19.66" y2="17.67" x3="18.33" y3="19.67"/>
<curve x1="18" y1="20" x2="17.66" y2="20.34" x3="17.33" y3="20.67"/>
<curve x1="14.66" y1="23" x2="14.66" y2="23" x3="14.66" y3="23"/>
<curve x1="15" y1="23" x2="15" y2="22.67" x3="15.33" y3="22.34"/>
<curve x1="16.66" y1="20.34" x2="17" y2="18" x3="16.33" y3="15.67"/>
<curve x1="14.33" y1="17.34" x2="14.33" y2="17.34" x3="14.33" y3="17.34"/>
<curve x1="15" y1="15.67" x2="15.66" y2="14" x3="16.33" y3="11.34"/>
<curve x1="18" y1="11" x2="19" y2="11" x3="21.33" y3="11"/>
</path>
<fill/>
<path>
<move x="23" y="8.34"/>
<curve x1="13.33" y1="8.34" x2="13.33" y2="8.34" x3="13.33" y3="8.34"/>
<curve x1="12.66" y1="8.34" x2="12" y2="8.34" x3="11.33" y3="8.34"/>
<curve x1="11" y1="8.67" x2="11" y2="8.67" x3="10.66" y3="8.67"/>
<curve x1="8" y1="11.34" x2="8" y2="11.34" x3="8" y3="11.34"/>
<curve x1="9.66" y1="9.67" x2="12.66" y2="10" x3="14.66" y3="12"/>
<curve x1="16.66" y1="10.34" x2="16.66" y2="10.34" x3="16.66" y3="10.34"/>
<curve x1="21" y1="10.34" x2="21" y2="10.34" x3="21" y3="10.34"/>
<close/>
</path>
<fill/>
</foreground>
</shape>
</shapes>