<?xml version="1.0" encoding="UTF-8"?>
<!-- Do not edit this file with editors other than draw.io -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="1427px" height="1069px" viewBox="-0.5 -0.5 1427 1069" content="&lt;mxfile modified=&quot;2023-09-19T13:50:01.906Z&quot; host=&quot;test.draw.io&quot; agent=&quot;Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36&quot; etag=&quot;IFsqPCGm04itRM0G_ace&quot; version=&quot;@DRAWIO-VERSION@&quot; type=&quot;device&quot;&gt;&#10;  &lt;diagram name=&quot;Page-1&quot; id=&quot;150dc974-5404-6732-309c-fd6db42db779&quot;&gt;&#10;    &lt;mxGraphModel dx=&quot;1213&quot; dy=&quot;1078&quot; grid=&quot;0&quot; gridSize=&quot;10&quot; guides=&quot;1&quot; tooltips=&quot;1&quot; connect=&quot;1&quot; arrows=&quot;1&quot; fold=&quot;1&quot; page=&quot;0&quot; pageScale=&quot;1&quot; pageWidth=&quot;1654&quot; pageHeight=&quot;1169&quot; background=&quot;#EEEEEE&quot; math=&quot;0&quot; shadow=&quot;0&quot;&gt;&#10;      &lt;root&gt;&#10;        &lt;mxCell id=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;1&quot; parent=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;6Wb5NVzeuDtcSk8M-yD3-340&quot; value=&quot;&quot; style=&quot;shape=image;verticalLabelPosition=bottom;labelBackgroundColor=#ffffff;verticalAlign=top;aspect=fixed;imageAspect=0;image=data:image/svg+xml,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB3aWR0aD0iMTZweCIgaGVpZ2h0PSIxNnB4IiB2aWV3Qm94PSIwIDAgMjQgMjQiIHZlcnNpb249IjEuMSI+PHBhdGggc3Ryb2tlPSIjMjliNmYyIiBmaWxsPSIjMjliNmYyIiBkPSJNMTUuNTUgNS41NUwxMSAxdjMuMDdDNy4wNiA0LjU2IDQgNy45MiA0IDEyczMuMDUgNy40NCA3IDcuOTN2LTIuMDJjLTIuODQtLjQ4LTUtMi45NC01LTUuOTFzMi4xNi01LjQzIDUtNS45MVYxMGw0LjU1LTQuNDV6TTE5LjkzIDExYy0uMTctMS4zOS0uNzItMi43My0xLjYyLTMuODlsLTEuNDIgMS40MmMuNTQuNzUuODggMS42IDEuMDIgMi40N2gyLjAyek0xMyAxNy45djIuMDJjMS4zOS0uMTcgMi43NC0uNzEgMy45LTEuNjFsLTEuNDQtMS40NGMtLjc1LjU0LTEuNTkuODktMi40NiAxLjAzem0zLjg5LTIuNDJsMS40MiAxLjQxYy45LTEuMTYgMS40NS0yLjUgMS42Mi0zLjg5aC0yLjAyYy0uMTQuODctLjQ4IDEuNzItMS4wMiAyLjQ4eiIvPjwvc3ZnPg==;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;480.5799999999999&quot; y=&quot;1023&quot; width=&quot;24.83&quot; height=&quot;24.83&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;100&quot; y=&quot;443&quot; width=&quot;340&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;5&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;100&quot; y=&quot;506.9999999999999&quot; width=&quot;340&quot; height=&quot;31&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;6&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;100&quot; y=&quot;475&quot; width=&quot;340&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;100&quot; y=&quot;540&quot; width=&quot;340&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;8&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;100&quot; y=&quot;572&quot; width=&quot;340&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;9&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;100&quot; y=&quot;636&quot; width=&quot;340&quot; height=&quot;31&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;10&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;100&quot; y=&quot;604&quot; width=&quot;340&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;11&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;100&quot; y=&quot;669&quot; width=&quot;340&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;12&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;100&quot; y=&quot;701&quot; width=&quot;340&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;13&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;100&quot; y=&quot;733&quot; width=&quot;340&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;14&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;100&quot; y=&quot;765&quot; width=&quot;340&quot; height=&quot;31&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;15&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;100&quot; y=&quot;798&quot; width=&quot;340&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;16&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;100&quot; y=&quot;830&quot; width=&quot;340&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;17&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;100&quot; y=&quot;862&quot; width=&quot;340&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;18&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;100&quot; y=&quot;893.9999999999999&quot; width=&quot;340&quot; height=&quot;31&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;20&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;100&quot; y=&quot;1056&quot; width=&quot;340&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;21&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;100&quot; y=&quot;1024&quot; width=&quot;340&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;22&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;100&quot; y=&quot;991&quot; width=&quot;340&quot; height=&quot;31&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;23&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;100&quot; y=&quot;959&quot; width=&quot;340&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;24&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;100&quot; y=&quot;927&quot; width=&quot;340&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;25&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;460&quot; y=&quot;443&quot; width=&quot;340&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;26&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;460&quot; y=&quot;506.9999999999999&quot; width=&quot;340&quot; height=&quot;31&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;27&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;460&quot; y=&quot;540&quot; width=&quot;340&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;28&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;460&quot; y=&quot;572&quot; width=&quot;340&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;29&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;460&quot; y=&quot;636&quot; width=&quot;340&quot; height=&quot;31&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;30&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;460&quot; y=&quot;604&quot; width=&quot;340&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;32&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;460&quot; y=&quot;475&quot; width=&quot;340&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;33&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;460&quot; y=&quot;669&quot; width=&quot;340&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;34&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;460&quot; y=&quot;701&quot; width=&quot;340&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;35&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;460&quot; y=&quot;733&quot; width=&quot;340&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;36&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;460&quot; y=&quot;765&quot; width=&quot;340&quot; height=&quot;31&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;37&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;460&quot; y=&quot;798&quot; width=&quot;340&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;38&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;460&quot; y=&quot;830&quot; width=&quot;340&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;39&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;460&quot; y=&quot;862&quot; width=&quot;340&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;40&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;460&quot; y=&quot;894&quot; width=&quot;340&quot; height=&quot;31&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;41&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;460&quot; y=&quot;927&quot; width=&quot;340&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;42&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;460&quot; y=&quot;123&quot; width=&quot;340&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;43&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;460&quot; y=&quot;155&quot; width=&quot;340&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;44&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;460&quot; y=&quot;187&quot; width=&quot;340&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;45&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;460&quot; y=&quot;219&quot; width=&quot;340&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;46&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;460&quot; y=&quot;250.99999999999994&quot; width=&quot;340&quot; height=&quot;31&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;47&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;460&quot; y=&quot;316&quot; width=&quot;340&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;48&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;460&quot; y=&quot;284&quot; width=&quot;340&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;49&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;460&quot; y=&quot;348&quot; width=&quot;340&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;50&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;100&quot; y=&quot;123&quot; width=&quot;340&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;51&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;100&quot; y=&quot;155&quot; width=&quot;340&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;52&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;100&quot; y=&quot;187&quot; width=&quot;340&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;53&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;100&quot; y=&quot;219&quot; width=&quot;340&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;54&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;100&quot; y=&quot;250.99999999999994&quot; width=&quot;340&quot; height=&quot;31&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;55&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;100&quot; y=&quot;284&quot; width=&quot;340&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;56&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;100&quot; y=&quot;316&quot; width=&quot;340&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;57&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;100&quot; y=&quot;348&quot; width=&quot;340&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;58&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;820&quot; y=&quot;123&quot; width=&quot;340&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;59&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;820&quot; y=&quot;155&quot; width=&quot;340&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;60&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;820&quot; y=&quot;187&quot; width=&quot;340&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;61&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;820&quot; y=&quot;219&quot; width=&quot;340&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;62&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;820&quot; y=&quot;250.9999999999999&quot; width=&quot;340&quot; height=&quot;31&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;63&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;820&quot; y=&quot;284&quot; width=&quot;340&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;64&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;820&quot; y=&quot;316&quot; width=&quot;340&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;65&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;820&quot; y=&quot;348&quot; width=&quot;340&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;66&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1180&quot; y=&quot;348&quot; width=&quot;340&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;67&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1180&quot; y=&quot;316&quot; width=&quot;340&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;68&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1180&quot; y=&quot;284&quot; width=&quot;340&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;69&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1180&quot; y=&quot;251.0000000000001&quot; width=&quot;340&quot; height=&quot;31&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;70&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1180&quot; y=&quot;219&quot; width=&quot;340&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;71&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1180&quot; y=&quot;187&quot; width=&quot;340&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;72&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1180&quot; y=&quot;155&quot; width=&quot;340&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;73&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1180&quot; y=&quot;123&quot; width=&quot;340&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;74&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;820&quot; y=&quot;443&quot; width=&quot;700&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;75&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;820&quot; y=&quot;475&quot; width=&quot;700&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;76&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;820&quot; y=&quot;507&quot; width=&quot;700&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;77&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;820&quot; y=&quot;539.0000000000002&quot; width=&quot;700&quot; height=&quot;31&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;78&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;820&quot; y=&quot;572&quot; width=&quot;700&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;79&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;820&quot; y=&quot;668.0000000000001&quot; width=&quot;340&quot; height=&quot;31&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;80&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;820&quot; y=&quot;701&quot; width=&quot;340&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;81&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;820&quot; y=&quot;733&quot; width=&quot;340&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;83&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;820&quot; y=&quot;765.0000000000003&quot; width=&quot;340&quot; height=&quot;31&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;84&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;820&quot; y=&quot;798&quot; width=&quot;340&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;85&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;820&quot; y=&quot;830&quot; width=&quot;340&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;86&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;820&quot; y=&quot;862&quot; width=&quot;340&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;87&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;820&quot; y=&quot;893.9999999999999&quot; width=&quot;340&quot; height=&quot;31&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;88&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;820&quot; y=&quot;927&quot; width=&quot;340&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;89&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;820&quot; y=&quot;991&quot; width=&quot;340&quot; height=&quot;31&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;90&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;820&quot; y=&quot;959&quot; width=&quot;340&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;91&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;820&quot; y=&quot;1024&quot; width=&quot;340&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;92&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;820&quot; y=&quot;1056&quot; width=&quot;340&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;94&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1180&quot; y=&quot;668.0000000000001&quot; width=&quot;340&quot; height=&quot;31&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;95&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1180&quot; y=&quot;701&quot; width=&quot;340&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;96&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1180&quot; y=&quot;733&quot; width=&quot;340&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;97&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1180&quot; y=&quot;765&quot; width=&quot;340&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;98&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1180&quot; y=&quot;797.0000000000001&quot; width=&quot;340&quot; height=&quot;31&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;99&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1180&quot; y=&quot;862&quot; width=&quot;340&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;100&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1180&quot; y=&quot;894&quot; width=&quot;340&quot; height=&quot;31&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;101&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1180&quot; y=&quot;927&quot; width=&quot;340&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;102&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1180&quot; y=&quot;959&quot; width=&quot;340&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;103&quot; value=&quot;(Shift+)Enter&quot; style=&quot;text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;114&quot; y=&quot;156.99999999999994&quot; width=&quot;120.00000000000003&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;104&quot; value=&quot;Enter / Tab&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;114&quot; y=&quot;189.00000000000017&quot; width=&quot;120.00000000000003&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;105&quot; value=&quot;Enter / F2&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;114&quot; y=&quot;220.99999999999994&quot; width=&quot;120.00000000000003&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;106&quot; value=&quot;Ctrl+Enter / Shift+Tab / F2 / Esc&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;114&quot; y=&quot;253.5&quot; width=&quot;235&quot; height=&quot;34.5&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;107&quot; value=&quot;Ctrl+B / I&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;114&quot; y=&quot;285.99999999999983&quot; width=&quot;120.00000000000003&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;108&quot; value=&quot;Ctrl+U&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;114&quot; y=&quot;317.99999999999994&quot; width=&quot;120.00000000000003&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;109&quot; value=&quot;Ctrl+. / ,&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;114&quot; y=&quot;350&quot; width=&quot;120.00000000000003&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;110&quot; value=&quot;1&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontSize=10;spacingTop=-4;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;295&quot; y=&quot;253.5&quot; width=&quot;10&quot; height=&quot;16&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;111&quot; value=&quot;New line in formatted labels&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;221&quot; y=&quot;157&quot; width=&quot;200&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;112&quot; value=&quot;Line break / Tab / Indent in labels&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;221&quot; y=&quot;189&quot; width=&quot;203&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;113&quot; value=&quot;Start editing label of selected cell&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;221&quot; y=&quot;221&quot; width=&quot;200&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;114&quot; value=&quot;Stop editing&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;315&quot; y=&quot;253.5&quot; width=&quot;117&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;115&quot; value=&quot;Toggle bold / italic on selected text&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;221&quot; y=&quot;285.9999999999998&quot; width=&quot;200&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;116&quot; value=&quot;Toggle underline on selected text&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;221&quot; y=&quot;318&quot; width=&quot;200&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;117&quot; value=&quot;Superscript / Subscript on selected text&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;221&quot; y=&quot;350&quot; width=&quot;219&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;118&quot; value=&quot;(Shift+)Tab&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;474&quot; y=&quot;125.00000000000006&quot; width=&quot;90.00000000000001&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;119&quot; value=&quot;Alt+Shift+P / Alt+Tab&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;474&quot; y=&quot;157&quot; width=&quot;132&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;120&quot; value=&quot;Alt+Shift+X / C / S&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;474&quot; y=&quot;189&quot; width=&quot;114&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;121&quot; value=&quot;Alt+Drag&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;474&quot; y=&quot;221&quot; width=&quot;100&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;122&quot; value=&quot;Ctrl+(Shift+)A&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;474&quot; y=&quot;253.5&quot; width=&quot;90.00000000000001&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;123&quot; value=&quot;Ctrl+Shift+I / E&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;474&quot; y=&quot;286&quot; width=&quot;136&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;124&quot; value=&quot;Ctrl / Shift+Select / Drag&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;474&quot; y=&quot;318&quot; width=&quot;161&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;125&quot; value=&quot;Alt+Click&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;474&quot; y=&quot;350&quot; width=&quot;110&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;126&quot; value=&quot;Select next / previous&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;595&quot; y=&quot;125&quot; width=&quot;155&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;127&quot; value=&quot;Select parent&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;605&quot; y=&quot;157.00000000000006&quot; width=&quot;155&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;128&quot; value=&quot;Select (all) children / siblings&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;595&quot; y=&quot;189&quot; width=&quot;189&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;129&quot; value=&quot;Start selection / Select intersection&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;595&quot; y=&quot;221&quot; width=&quot;212&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;130&quot; value=&quot;Select all / none&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;595&quot; y=&quot;253.49999999999994&quot; width=&quot;110&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;131&quot; value=&quot;Select vertices / edges&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;595&quot; y=&quot;286&quot; width=&quot;186&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;132&quot; value=&quot;Toggle selection state&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;625&quot; y=&quot;317.99999999999994&quot; width=&quot;145&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;133&quot; value=&quot;Select cell below&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;595&quot; y=&quot;350&quot; width=&quot;136&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;134&quot; value=&quot;Alt+Wheel&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;467&quot; y=&quot;444.9999999999999&quot; width=&quot;166&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;136&quot; value=&quot;Wheel&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;467&quot; y=&quot;509.4999999999998&quot; width=&quot;130&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;137&quot; value=&quot;Shift+Wheel&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;467&quot; y=&quot;542.0000000000002&quot; width=&quot;130&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;138&quot; value=&quot;Space / Right-click+Drag&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;467&quot; y=&quot;573.9999999999998&quot; width=&quot;160&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;139&quot; value=&quot;Alt+Ctrl+Shift+Drag&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;467&quot; y=&quot;605.9999999999999&quot; width=&quot;130&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;140&quot; value=&quot;Alt+Resize / Move&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;467&quot; y=&quot;638.5&quot; width=&quot;133&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;141&quot; value=&quot;Shift+Home&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;467&quot; y=&quot;671&quot; width=&quot;130&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;142&quot; value=&quot;End&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;467&quot; y=&quot;703&quot; width=&quot;130&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;143&quot; value=&quot;Enter / Home&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;467&quot; y=&quot;735.0000000000001&quot; width=&quot;130&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;144&quot; value=&quot;Ctrl+Shift+H&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;467&quot; y=&quot;767.5000000000001&quot; width=&quot;130&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;145&quot; value=&quot;Ctrl+J&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;467&quot; y=&quot;800&quot; width=&quot;130&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;146&quot; value=&quot;Ctrl+Shift+J&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;467&quot; y=&quot;832.0000000000002&quot; width=&quot;130&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;147&quot; value=&quot;Ctrl + (Numpad)&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;467&quot; y=&quot;896.4999999999998&quot; width=&quot;130&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;148&quot; value=&quot;Ctrl - (Numpad)&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;467&quot; y=&quot;929&quot; width=&quot;130&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;149&quot; value=&quot;Ctrl+0&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;467&quot; y=&quot;863.9999999999999&quot; width=&quot;130&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;150&quot; value=&quot;Canvas zoom in/out&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;637&quot; y=&quot;445&quot; width=&quot;125&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;152&quot; value=&quot;Canvas vertical scroll&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;637&quot; y=&quot;509.5&quot; width=&quot;150&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;153&quot; value=&quot;Canvas horizontal scroll&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;637&quot; y=&quot;542&quot; width=&quot;150&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;154&quot; value=&quot;Pan canvas&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;637&quot; y=&quot;574&quot; width=&quot;130&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;155&quot; value=&quot;Create / Remove space&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;637&quot; y=&quot;606&quot; width=&quot;150&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;156&quot; value=&quot;Ignore grid&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;637&quot; y=&quot;638.5&quot; width=&quot;150&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;157&quot; value=&quot;Home&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;637&quot; y=&quot;671&quot; width=&quot;150&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;158&quot; value=&quot;Refresh&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;637&quot; y=&quot;703&quot; width=&quot;150&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;159&quot; value=&quot;Reset view&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;637&quot; y=&quot;735.0000000000001&quot; width=&quot;150&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;160&quot; value=&quot;Fit window&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;637&quot; y=&quot;767.5&quot; width=&quot;150&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;161&quot; value=&quot;Fit page&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;637&quot; y=&quot;800&quot; width=&quot;150&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;162&quot; value=&quot;Fit two pages&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;637&quot; y=&quot;832&quot; width=&quot;150&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;163&quot; value=&quot;Custom zoom&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;637&quot; y=&quot;863.9999999999999&quot; width=&quot;150&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;164&quot; value=&quot;Zoom in&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;637&quot; y=&quot;896.4999999999998&quot; width=&quot;150&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;165&quot; value=&quot;Zoom out&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;637&quot; y=&quot;929&quot; width=&quot;150&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;166&quot; value=&quot;Click to connect and clone (Ctrl+Click to clone,&amp;#xa;Shift+Click to connect), Drag to connect&amp;#xa;(Ctrl+Drag to clone)&quot; style=&quot;rounded=1;align=left;strokeColor=none;fontSize=12;fontColor=#333333;fillColor=none;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;518&quot; y=&quot;969&quot; width=&quot;248&quot; height=&quot;48&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;168&quot; value=&quot;&quot; style=&quot;shape=image;aspect=fixed;image=data:image/png,iVBORw0KGgoAAAANSUhEUgAAAA8AAAAZAgMAAACTN5xfAAAACVBMVEX///////8AAACO9MPsAAAAAXRSTlMAQObYZgAAAFRJREFUeF5VyjEKgDAQRNGPRSA38AB6JEWsUkggt9hLGDbdlh5Tp7R5DPzBAEK46OI2UcQhdrGZKB/nA13B9Q5hqwEij+akVi/yFDMJFjL8VlUdw1+gHxbW+YsglAAAAABJRU5ErkJggg==;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;485.49499999999983&quot; y=&quot;1053&quot; width=&quot;15&quot; height=&quot;25&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;169&quot; value=&quot;Click to rotate 90° clockwise, Drag to rotate&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;overflow=hidden;fontSize=12;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;518&quot; y=&quot;1024&quot; width=&quot;239&quot; height=&quot;20&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;170&quot; value=&quot;Ctrl: Ask   Alt: Origin   Shift: As Image&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;overflow=hidden;fontSize=12;fontColor=#333333;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;518&quot; y=&quot;1055&quot; width=&quot;258&quot; height=&quot;20&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;171&quot; value=&quot;Ctrl+Shift+L&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;834&quot; y=&quot;124.99999999999994&quot; width=&quot;100&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;172&quot; value=&quot;Ctrl+Shift+O&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;834&quot; y=&quot;157.0000000000001&quot; width=&quot;100&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;173&quot; value=&quot;Ctrl+M&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;834&quot; y=&quot;318&quot; width=&quot;80.00000000000001&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;174&quot; value=&quot;Ctrl+Shift+P&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;834&quot; y=&quot;189&quot; width=&quot;100&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;175&quot; value=&quot;Ctrl+Shift+M&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;834&quot; y=&quot;286&quot; width=&quot;100&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;176&quot; value=&quot;Ctrl+K&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;834&quot; y=&quot;253.49999999999994&quot; width=&quot;86&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;177&quot; value=&quot;Ctrl+F&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;834&quot; y=&quot;350.0000000000002&quot; width=&quot;80&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;178&quot; value=&quot;Toggle Layers&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;943&quot; y=&quot;125&quot; width=&quot;170&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;179&quot; value=&quot;Toggle Outline&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;943&quot; y=&quot;157.00000000000023&quot; width=&quot;170&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;180&quot; value=&quot;Edit metadata&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;943&quot; y=&quot;318&quot; width=&quot;170&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;181&quot; value=&quot;Toggle Format&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;943&quot; y=&quot;188.99999999999994&quot; width=&quot;170&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;182&quot; value=&quot;Edit vertex geometry&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;943&quot; y=&quot;285.9999999999998&quot; width=&quot;170&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;183&quot; value=&quot;Toggle Tags&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;943&quot; y=&quot;253.50000000000006&quot; width=&quot;160&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;184&quot; value=&quot;Find/Replace&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;943&quot; y=&quot;350&quot; width=&quot;93&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;185&quot; value=&quot;Alt+(Shift+)Drag shape from library&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;834&quot; y=&quot;445.0000000000002&quot; width=&quot;230&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;186&quot; value=&quot;Alt+(Shift / Ctrl)+Click on a library shape&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;834&quot; y=&quot;476.9999999999999&quot; width=&quot;240&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;187&quot; value=&quot;Shift+Click on a library shape&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;834&quot; y=&quot;509.00000000000045&quot; width=&quot;230&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;188&quot; value=&quot;Click on a library shape&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;834&quot; y=&quot;541.5000000000003&quot; width=&quot;230&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;189&quot; value=&quot;Alt / Shift+Connect&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;834&quot; y=&quot;574.0000000000003&quot; width=&quot;230&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;190&quot; value=&quot;Disable replace and connect on drop targets (Shift ignores current style)&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1094&quot; y=&quot;445&quot; width=&quot;416&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;191&quot; value=&quot;Insert and connect the selected item (Shift ignores current style)&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1094&quot; y=&quot;477&quot; width=&quot;416&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;192&quot; value=&quot;Replace the selected item with the clicked item&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1094&quot; y=&quot;509.0000000000002&quot; width=&quot;270&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;193&quot; value=&quot;Connect to unconnected side of selected edge&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1094&quot; y=&quot;541.4999999999995&quot; width=&quot;270&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;194&quot; value=&quot;Ignore shape / Connect to a fixed point&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1094&quot; y=&quot;574&quot; width=&quot;306&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;195&quot; value=&quot;Ctrl+(Shift+)S&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;834&quot; y=&quot;670.4999999999999&quot; width=&quot;90&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;197&quot; value=&quot;Ctrl+Z&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;834&quot; y=&quot;702.9999999999997&quot; width=&quot;90&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;198&quot; value=&quot;Alt+Shift+A&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;834&quot; y=&quot;734.9999999999997&quot; width=&quot;90&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;199&quot; value=&quot;Hold Alt&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;834&quot; y=&quot;799.5&quot; width=&quot;90&quot; height=&quot;27&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;200&quot; value=&quot;Ctrl+Shift+G&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;834&quot; y=&quot;832.0000000000001&quot; width=&quot;90&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;201&quot; value=&quot;Ctrl+P&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;834&quot; y=&quot;864.0000000000003&quot; width=&quot;90&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;202&quot; value=&quot;Ctrl+Y&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;834&quot; y=&quot;896.5000000000001&quot; width=&quot;90&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;203&quot; value=&quot;Ctrl+Shift+Z&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;834&quot; y=&quot;929&quot; width=&quot;90&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;204&quot; value=&quot;A / S / D / F / C&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;834&quot; y=&quot;961&quot; width=&quot;110&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;205&quot; value=&quot;X&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;834&quot; y=&quot;993.4999999999998&quot; width=&quot;90&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;206&quot; value=&quot;Ctrl / Right click&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;834&quot; y=&quot;1026&quot; width=&quot;106&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;207&quot; value=&quot;(Ctrl / Shift)+Esc&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;834&quot; y=&quot;1058&quot; width=&quot;109&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;210&quot; value=&quot;Save (as)&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;944&quot; y=&quot;670.5000000000001&quot; width=&quot;140&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;211&quot; value=&quot;Undo&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;944&quot; y=&quot;703.0000000000001&quot; width=&quot;140&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;212&quot; value=&quot;Connection arrows&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;944&quot; y=&quot;735.0000000000001&quot; width=&quot;140&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;213&quot; value=&quot;Ignore handles under the mouse&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;944&quot; y=&quot;798.9999999999999&quot; width=&quot;203&quot; height=&quot;28&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;214&quot; value=&quot;Toggle grid&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;944&quot; y=&quot;832.0000000000005&quot; width=&quot;140&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;215&quot; value=&quot;Print&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;944&quot; y=&quot;864.0000000000002&quot; width=&quot;140&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;216&quot; value=&quot;Redo (Windows)&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;944&quot; y=&quot;896.5&quot; width=&quot;140&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;217&quot; value=&quot;Redo (Linux/macOS)&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;944&quot; y=&quot;929&quot; width=&quot;140&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;218&quot; value=&quot;Add Text/Note/Rectangle/Ellipse/Line&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;944&quot; y=&quot;961&quot; width=&quot;210&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;219&quot; value=&quot;Toggle Freehand Mode&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;944&quot; y=&quot;993.5000000000002&quot; width=&quot;140&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;220&quot; value=&quot;Context Menu&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;944&quot; y=&quot;1026&quot; width=&quot;214&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;221&quot; value=&quot;Cancel editing / action&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;944&quot; y=&quot;1058&quot; width=&quot;140&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;223&quot; value=&quot;Drag&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1194&quot; y=&quot;864&quot; width=&quot;110.00000000000001&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;224&quot; value=&quot;Tap and hold&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1194&quot; y=&quot;896.5&quot; width=&quot;110.00000000000001&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;225&quot; value=&quot;Pinch&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1194&quot; y=&quot;929&quot; width=&quot;110.00000000000001&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;226&quot; value=&quot;Tap selected cell&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1194&quot; y=&quot;961&quot; width=&quot;110.00000000000001&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;227&quot; value=&quot;Move cell / Pan canvas&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1304&quot; y=&quot;864&quot; width=&quot;140&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;228&quot; value=&quot;Toggle selection / Rubberband&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1304&quot; y=&quot;897&quot; width=&quot;196&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;229&quot; value=&quot;Zoom&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1304&quot; y=&quot;929&quot; width=&quot;120&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;230&quot; value=&quot;Context menu&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1304&quot; y=&quot;961&quot; width=&quot;120&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;231&quot; value=&quot;Cursor&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1194&quot; y=&quot;125&quot; width=&quot;120.00000000000001&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;232&quot; value=&quot;Shift+Cursor&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1194&quot; y=&quot;156.99999999999994&quot; width=&quot;120.00000000000001&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;233&quot; value=&quot;Ctrl+Cursor&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1194&quot; y=&quot;188.9999999999999&quot; width=&quot;120.00000000000001&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;234&quot; value=&quot;Ctrl+Shift+Cursor&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1194&quot; y=&quot;221.0000000000001&quot; width=&quot;120.00000000000001&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;235&quot; value=&quot;Alt+Shift+Cursor&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1194&quot; y=&quot;253.5000000000001&quot; width=&quot;120.00000000000001&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;236&quot; value=&quot;Alt+Cursor&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1194&quot; y=&quot;286&quot; width=&quot;120.00000000000001&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;237&quot; value=&quot;Ctrl+Shift+Pg Up&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1194&quot; y=&quot;318.00000000000006&quot; width=&quot;120&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;238&quot; value=&quot;Ctrl+Shift+Pg Down&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1194&quot; y=&quot;349.99999999999994&quot; width=&quot;140&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;239&quot; value=&quot;Scroll / Move cell (pt)&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1334&quot; y=&quot;125&quot; width=&quot;149&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;240&quot; value=&quot;Move cell (grid) or page&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1334&quot; y=&quot;157&quot; width=&quot;178&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;241&quot; value=&quot;Resize cell (pt) or select page&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1334&quot; y=&quot;189&quot; width=&quot;185&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;242&quot; value=&quot;Resize cell (grid size)&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1334&quot; y=&quot;221&quot; width=&quot;176&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;243&quot; value=&quot;Clone and connect&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1334&quot; y=&quot;253.5&quot; width=&quot;149&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;244&quot; value=&quot;Scroll page&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1334&quot; y=&quot;286&quot; width=&quot;149&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;245&quot; value=&quot;Previous page&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1334&quot; y=&quot;318&quot; width=&quot;130&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;246&quot; value=&quot;Next page&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1334&quot; y=&quot;350&quot; width=&quot;140&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;247&quot; value=&quot;Ctrl+Shift+R&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1194&quot; y=&quot;670&quot; width=&quot;100&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;248&quot; value=&quot;Ctrl+E&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1194&quot; y=&quot;703&quot; width=&quot;100&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;249&quot; value=&quot;Ctrl+Shift+D&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1194&quot; y=&quot;734.9999999999998&quot; width=&quot;100&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;252&quot; value=&quot;Clear default style&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1304&quot; y=&quot;670&quot; width=&quot;120&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;253&quot; value=&quot;Edit style&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1304&quot; y=&quot;703&quot; width=&quot;120&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;254&quot; value=&quot;Set as default style&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1304&quot; y=&quot;735&quot; width=&quot;120&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;257&quot; value=&quot;CURSOR / PAGE KEYS&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1;fontSize=14;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1173&quot; y=&quot;89&quot; width=&quot;173&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;258&quot; value=&quot;TOOLS&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1;fontSize=14;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;814&quot; y=&quot;89&quot; width=&quot;173&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;259&quot; value=&quot;SELECTION&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1;fontSize=14;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;458&quot; y=&quot;89&quot; width=&quot;173&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;260&quot; value=&quot;LABELS&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1;fontSize=14;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;94&quot; y=&quot;89&quot; width=&quot;173&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;261&quot; value=&quot;CANVAS&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1;fontSize=14;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;93&quot; y=&quot;410&quot; width=&quot;173&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;262&quot; value=&quot;VIEW&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1;fontSize=14;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;454&quot; y=&quot;410&quot; width=&quot;173&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;263&quot; value=&quot;LIBRARY / CONNECT&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1;fontSize=14;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;814&quot; y=&quot;410&quot; width=&quot;173&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;264&quot; value=&quot;STYLES&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1;fontSize=14;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1174&quot; y=&quot;634&quot; width=&quot;173&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;265&quot; value=&quot;DOCUMENT&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1;fontSize=14;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;814&quot; y=&quot;634&quot; width=&quot;173&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;267&quot; value=&quot;Keyboard Shortcuts&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0;fontSize=27;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;260&quot; y=&quot;18.5&quot; width=&quot;281&quot; height=&quot;51.5&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;274&quot; value=&quot;Ctrl+Shift+K&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;834&quot; y=&quot;220.9999999999999&quot; width=&quot;88&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;275&quot; value=&quot;Toggle Shapes&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;943&quot; y=&quot;221&quot; width=&quot;188&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;UserObject label=&quot;Open in draw.io...&quot; link=&quot;https://app.diagrams.net/#Uhttps%3A%2F%2Fapp.diagrams.net%2Fshortcuts.svg&quot; linkTarget=&quot;_blank&quot; id=&quot;276&quot;&gt;&#10;          &lt;mxCell style=&quot;rounded=0;align=left;strokeColor=none;fontSize=12;fontColor=#F08705;fontStyle=4;fillColor=none;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;            &lt;mxGeometry x=&quot;1204.67&quot; y=&quot;1042&quot; width=&quot;132.33&quot; height=&quot;18&quot; as=&quot;geometry&quot; /&gt;&#10;          &lt;/mxCell&gt;&#10;        &lt;/UserObject&gt;&#10;        &lt;mxCell id=&quot;277&quot; value=&quot;Ctrl+Shift+Wheel&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;467&quot; y=&quot;477&quot; width=&quot;150&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;278&quot; value=&quot;Canvas zoom in/out&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;637&quot; y=&quot;477&quot; width=&quot;123&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;279&quot; value=&quot;Connection points&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;944&quot; y=&quot;767.5000000000006&quot; width=&quot;140&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;280&quot; value=&quot;Alt+Shift+O&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;834&quot; y=&quot;767.4999999999998&quot; width=&quot;90&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;281&quot; value=&quot;Insert text or add an edge label&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;221&quot; y=&quot;125&quot; width=&quot;200&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;282&quot; value=&quot;Double-click&quot; style=&quot;text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;114&quot; y=&quot;124.99999999999989&quot; width=&quot;120.00000000000003&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;286&quot; value=&quot;1&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontSize=10;spacingTop=-4;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;193&quot; y=&quot;157&quot; width=&quot;10&quot; height=&quot;16&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;287&quot; value=&quot;Ctrl / Shift+Enter: New Line / Apply in Safari&quot; style=&quot;rounded=1;align=left;strokeColor=none;fontSize=12;fontColor=#333333;fillColor=none;verticalAlign=middle;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1205&quot; y=&quot;1019&quot; width=&quot;255&quot; height=&quot;19&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;288&quot; value=&quot;1&quot; style=&quot;text;resizable=0;points=[];autosize=1;align=left;verticalAlign=top;spacingTop=-4;fontColor=#333333;fontSize=10;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1198.333333333333&quot; y=&quot;1013&quot; width=&quot;20&quot; height=&quot;10&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;289&quot; value=&quot;Ctrl+X / C / Ctrl+Alt+X&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1;opacity=70;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;114&quot; y=&quot;445.0000000000001&quot; width=&quot;140&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;291&quot; value=&quot;Ctrl+V / Ctrl+Shift+V&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1;opacity=70;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;114&quot; y=&quot;476.9999999999998&quot; width=&quot;140&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;292&quot; value=&quot;Ctrl+G&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1;opacity=70;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;114&quot; y=&quot;509.5&quot; width=&quot;140&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;293&quot; value=&quot;Ctrl+Shift+U&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1;opacity=70;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;114&quot; y=&quot;542&quot; width=&quot;140&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;294&quot; value=&quot;Ctrl+L / Alt+Shift+L&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1;opacity=70;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;114&quot; y=&quot;574&quot; width=&quot;140&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;295&quot; value=&quot;Ctrl+Enter / D&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1;opacity=70;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;114&quot; y=&quot;605.9999999999999&quot; width=&quot;140.00000000000003&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;296&quot; value=&quot;Backspace or Delete&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1;opacity=70;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;114&quot; y=&quot;638.4999999999999&quot; width=&quot;140.00000000000003&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;297&quot; value=&quot;Ctrl / Shift+Delete&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1;opacity=70;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;114&quot; y=&quot;671&quot; width=&quot;140.00000000000003&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;298&quot; value=&quot;Ctrl+R&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1;opacity=70;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;114&quot; y=&quot;702.9999999999999&quot; width=&quot;140.00000000000003&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;299&quot; value=&quot;Shift+Resize&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1;opacity=70;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;114&quot; y=&quot;735.0000000000001&quot; width=&quot;140&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;300&quot; value=&quot;Ctrl+Resize&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1;opacity=70;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;114&quot; y=&quot;767.5&quot; width=&quot;140&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;301&quot; value=&quot;Ctrl+Home&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1;opacity=70;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;114&quot; y=&quot;799.9999999999999&quot; width=&quot;140&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;302&quot; value=&quot;Ctrl+End&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1;opacity=70;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;114&quot; y=&quot;831.9999999999999&quot; width=&quot;140&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;303&quot; value=&quot;Ctrl+Shift+Home&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1;opacity=70;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;114&quot; y=&quot;864&quot; width=&quot;140&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;304&quot; value=&quot;Ctrl+Shift+End&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1;opacity=70;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;114&quot; y=&quot;896.4999999999999&quot; width=&quot;140&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;305&quot; value=&quot;Ctrl+Shift+F / B&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1;opacity=70;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;114&quot; y=&quot;928.9999999999998&quot; width=&quot;140&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;306&quot; value=&quot;Alt+Shift+F/V / B/E&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1;opacity=70;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;114&quot; y=&quot;961&quot; width=&quot;140&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;307&quot; value=&quot;Alt+Shift+R / T&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1;opacity=70;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;114&quot; y=&quot;993.5&quot; width=&quot;140&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;308&quot; value=&quot;Ctrl+Shift+Y&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1;opacity=70;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;114&quot; y=&quot;1026&quot; width=&quot;140&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;309&quot; value=&quot;Ctrl / Shift+Drag&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1;opacity=70;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;114&quot; y=&quot;1058.0000000000002&quot; width=&quot;140&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;310&quot; value=&quot;Cut / Copy / Copy as Image&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;254&quot; y=&quot;445&quot; width=&quot;172&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;312&quot; value=&quot;Paste / Paste unformatted text&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;254&quot; y=&quot;477&quot; width=&quot;174&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;313&quot; value=&quot;Group&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;254&quot; y=&quot;509.5&quot; width=&quot;150&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;314&quot; value=&quot;Ungroup&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;254&quot; y=&quot;542&quot; width=&quot;150&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;315&quot; value=&quot;Lock/Unlock / Edit link&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;254&quot; y=&quot;574.0000000000002&quot; width=&quot;150&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;316&quot; value=&quot;Duplicate&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;254&quot; y=&quot;606&quot; width=&quot;150&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;317&quot; value=&quot;Delete selected cells&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;254&quot; y=&quot;638.5&quot; width=&quot;150&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;318&quot; value=&quot;...with connections / labels&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;254&quot; y=&quot;671&quot; width=&quot;187&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;319&quot; value=&quot;Turn / Rotate 90° clockwise&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;254&quot; y=&quot;703&quot; width=&quot;159&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;320&quot; value=&quot;Maintain proportions&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;254&quot; y=&quot;735&quot; width=&quot;150&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;321&quot; value=&quot;Bring to front / Send to back&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;254&quot; y=&quot;929&quot; width=&quot;181&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;322&quot; value=&quot;Collapse container&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;254&quot; y=&quot;800&quot; width=&quot;150&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;323&quot; value=&quot;Expand container&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;254&quot; y=&quot;832&quot; width=&quot;150&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;324&quot; value=&quot;Exit group&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;254&quot; y=&quot;864&quot; width=&quot;150&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;325&quot; value=&quot;Enter group&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;254&quot; y=&quot;896.5&quot; width=&quot;150&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;326&quot; value=&quot;Copy/paste size / data&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;254&quot; y=&quot;961&quot; width=&quot;150&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;327&quot; value=&quot;Centered resize&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;254&quot; y=&quot;767.5&quot; width=&quot;150&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;328&quot; value=&quot;Clear waypoints / Edit tooltip&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;254&quot; y=&quot;994&quot; width=&quot;176&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;329&quot; value=&quot;Autosize&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;254&quot; y=&quot;1026&quot; width=&quot;150&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;330&quot; value=&quot;Clone / Swap / Constrain&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;254&quot; y=&quot;1058&quot; width=&quot;156&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;331&quot; value=&quot;Cmd instead of Ctrl, Option instead of Alt for macOS &quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#777777;fontStyle=0;fontSize=18;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;574&quot; y=&quot;31.25&quot; width=&quot;456&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;334&quot; value=&quot;Alt+C&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1194&quot; y=&quot;766.9999999999998&quot; width=&quot;117&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;335&quot; value=&quot;Alt+V&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=1&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1194&quot; y=&quot;799.5000000000005&quot; width=&quot;116&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;336&quot; value=&quot;Copy style&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1304&quot; y=&quot;767&quot; width=&quot;154&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;337&quot; value=&quot;Paste style&quot; style=&quot;text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontColor=#333333;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1304&quot; y=&quot;799.5&quot; width=&quot;146&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;mfAzyJ5tXE3OUO8pGESb-337&quot; value=&quot;&quot; style=&quot;shape=image;verticalLabelPosition=bottom;labelBackgroundColor=#ffffff;verticalAlign=top;aspect=fixed;imageAspect=0;image=data:image/svg+xml,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB3aWR0aD0iMjZweCIgaGVpZ2h0PSIxOHB4IiB2ZXJzaW9uPSIxLjEiIHZpZXdCb3g9IjAgMCAyNiAxOCI+PHBhdGggZD0ibSAxIDYgTCAxNCA2IEwgMTQgMSBMIDI2IDkgTCAxNCAxOCBMIDE0IDEyIEwgMSAxMiB6IiBzdHJva2U9IiNmZmYiIGZpbGw9IiMyOWI2ZjIiLz48L3N2Zz4=;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;479.99499999999983&quot; y=&quot;975&quot; width=&quot;26&quot; height=&quot;18&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;6Wb5NVzeuDtcSk8M-yD3-341&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 20px&amp;quot;&amp;gt;draw.&amp;lt;font color=&amp;quot;#f08707&amp;quot; style=&amp;quot;font-size: 20px&amp;quot;&amp;gt;io&amp;lt;/font&amp;gt;&amp;lt;/font&amp;gt;&quot; style=&quot;dashed=0;outlineConnect=0;html=1;align=left;labelPosition=right;verticalLabelPosition=middle;verticalAlign=middle;shape=mxgraph.weblogos.drawio3;fillColor=#1A5BA3;fontSize=15;fontColor=#1A5BA3;fontStyle=1;spacingLeft=4;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;100&quot; y=&quot;29.75&quot; width=&quot;35&quot; height=&quot;35&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;      &lt;/root&gt;&#10;    &lt;/mxGraphModel&gt;&#10;  &lt;/diagram&gt;&#10;&lt;/mxfile&gt;&#10;" style="background-color: rgb(238, 238, 238);"><defs><clipPath id="mx-clip-25-143-112-26-0"><rect x="25" y="143" width="112" height="26"/></clipPath><clipPath id="mx-clip-25-175-112-26-0"><rect x="25" y="175" width="112" height="26"/></clipPath><clipPath id="mx-clip-25-207-112-26-0"><rect x="25" y="207" width="112" height="26"/></clipPath><clipPath id="mx-clip-25-240-227-35-0"><rect x="25" y="240" width="227" height="35"/></clipPath><clipPath id="mx-clip-25-272-112-26-0"><rect x="25" y="272" width="112" height="26"/></clipPath><clipPath id="mx-clip-25-304-112-26-0"><rect x="25" y="304" width="112" height="26"/></clipPath><clipPath id="mx-clip-25-336-112-26-0"><rect x="25" y="336" width="112" height="26"/></clipPath><clipPath id="mx-clip-202-236-10-20-0"><rect x="202" y="236" width="10" height="20"/></clipPath><clipPath id="mx-clip-132-143-192-26-0"><rect x="132" y="143" width="192" height="26"/></clipPath><clipPath id="mx-clip-132-175-195-26-0"><rect x="132" y="175" width="195" height="26"/></clipPath><clipPath id="mx-clip-132-207-192-26-0"><rect x="132" y="207" width="192" height="26"/></clipPath><clipPath id="mx-clip-226-240-109-26-0"><rect x="226" y="240" width="109" height="26"/></clipPath><clipPath id="mx-clip-132-272-192-26-0"><rect x="132" y="272" width="192" height="26"/></clipPath><clipPath id="mx-clip-132-304-192-26-0"><rect x="132" y="304" width="192" height="26"/></clipPath><clipPath id="mx-clip-132-336-211-26-0"><rect x="132" y="336" width="211" height="26"/></clipPath><clipPath id="mx-clip-385-111-82-26-0"><rect x="385" y="111" width="82" height="26"/></clipPath><clipPath id="mx-clip-385-143-124-26-0"><rect x="385" y="143" width="124" height="26"/></clipPath><clipPath id="mx-clip-385-175-106-26-0"><rect x="385" y="175" width="106" height="26"/></clipPath><clipPath id="mx-clip-385-207-92-26-0"><rect x="385" y="207" width="92" height="26"/></clipPath><clipPath id="mx-clip-385-240-82-26-0"><rect x="385" y="240" width="82" height="26"/></clipPath><clipPath id="mx-clip-385-272-128-26-0"><rect x="385" y="272" width="128" height="26"/></clipPath><clipPath id="mx-clip-385-304-153-26-0"><rect x="385" y="304" width="153" height="26"/></clipPath><clipPath id="mx-clip-385-336-102-26-0"><rect x="385" y="336" width="102" height="26"/></clipPath><clipPath id="mx-clip-506-111-147-26-0"><rect x="506" y="111" width="147" height="26"/></clipPath><clipPath id="mx-clip-516-143-147-26-0"><rect x="516" y="143" width="147" height="26"/></clipPath><clipPath id="mx-clip-506-175-181-26-0"><rect x="506" y="175" width="181" height="26"/></clipPath><clipPath id="mx-clip-506-207-204-26-0"><rect x="506" y="207" width="204" height="26"/></clipPath><clipPath id="mx-clip-506-240-102-26-0"><rect x="506" y="240" width="102" height="26"/></clipPath><clipPath id="mx-clip-506-272-178-26-0"><rect x="506" y="272" width="178" height="26"/></clipPath><clipPath id="mx-clip-536-304-137-26-0"><rect x="536" y="304" width="137" height="26"/></clipPath><clipPath id="mx-clip-506-336-128-26-0"><rect x="506" y="336" width="128" height="26"/></clipPath><clipPath id="mx-clip-378-431-158-26-0"><rect x="378" y="431" width="158" height="26"/></clipPath><clipPath id="mx-clip-378-495-122-26-0"><rect x="378" y="495" width="122" height="26"/></clipPath><clipPath id="mx-clip-378-528-122-26-0"><rect x="378" y="528" width="122" height="26"/></clipPath><clipPath id="mx-clip-378-560-152-26-0"><rect x="378" y="560" width="152" height="26"/></clipPath><clipPath id="mx-clip-378-592-122-26-0"><rect x="378" y="592" width="122" height="26"/></clipPath><clipPath id="mx-clip-378-625-125-26-0"><rect x="378" y="625" width="125" height="26"/></clipPath><clipPath id="mx-clip-378-657-122-26-0"><rect x="378" y="657" width="122" height="26"/></clipPath><clipPath id="mx-clip-378-689-122-26-0"><rect x="378" y="689" width="122" height="26"/></clipPath><clipPath id="mx-clip-378-721-122-26-0"><rect x="378" y="721" width="122" height="26"/></clipPath><clipPath id="mx-clip-378-754-122-26-0"><rect x="378" y="754" width="122" height="26"/></clipPath><clipPath id="mx-clip-378-786-122-26-0"><rect x="378" y="786" width="122" height="26"/></clipPath><clipPath id="mx-clip-378-818-122-26-0"><rect x="378" y="818" width="122" height="26"/></clipPath><clipPath id="mx-clip-378-882-122-26-0"><rect x="378" y="882" width="122" height="26"/></clipPath><clipPath id="mx-clip-378-915-122-26-0"><rect x="378" y="915" width="122" height="26"/></clipPath><clipPath id="mx-clip-378-850-122-26-0"><rect x="378" y="850" width="122" height="26"/></clipPath><clipPath id="mx-clip-548-431-117-26-0"><rect x="548" y="431" width="117" height="26"/></clipPath><clipPath id="mx-clip-548-496-142-26-0"><rect x="548" y="496" width="142" height="26"/></clipPath><clipPath id="mx-clip-548-528-142-26-0"><rect x="548" y="528" width="142" height="26"/></clipPath><clipPath id="mx-clip-548-560-122-26-0"><rect x="548" y="560" width="122" height="26"/></clipPath><clipPath id="mx-clip-548-592-142-26-0"><rect x="548" y="592" width="142" height="26"/></clipPath><clipPath id="mx-clip-548-625-142-26-0"><rect x="548" y="625" width="142" height="26"/></clipPath><clipPath id="mx-clip-548-657-142-26-0"><rect x="548" y="657" width="142" height="26"/></clipPath><clipPath id="mx-clip-548-689-142-26-0"><rect x="548" y="689" width="142" height="26"/></clipPath><clipPath id="mx-clip-548-721-142-26-0"><rect x="548" y="721" width="142" height="26"/></clipPath><clipPath id="mx-clip-548-754-142-26-0"><rect x="548" y="754" width="142" height="26"/></clipPath><clipPath id="mx-clip-548-786-142-26-0"><rect x="548" y="786" width="142" height="26"/></clipPath><clipPath id="mx-clip-548-818-142-26-0"><rect x="548" y="818" width="142" height="26"/></clipPath><clipPath id="mx-clip-548-850-142-26-0"><rect x="548" y="850" width="142" height="26"/></clipPath><clipPath id="mx-clip-548-882-142-26-0"><rect x="548" y="882" width="142" height="26"/></clipPath><clipPath id="mx-clip-548-915-142-26-0"><rect x="548" y="915" width="142" height="26"/></clipPath><clipPath id="mx-clip-425-1005-239-20-0"><rect x="425" y="1005" width="239" height="20"/></clipPath><clipPath id="mx-clip-425-1036-258-20-0"><rect x="425" y="1036" width="258" height="20"/></clipPath><clipPath id="mx-clip-745-111-92-26-0"><rect x="745" y="111" width="92" height="26"/></clipPath><clipPath id="mx-clip-745-143-92-26-0"><rect x="745" y="143" width="92" height="26"/></clipPath><clipPath id="mx-clip-745-304-72-26-0"><rect x="745" y="304" width="72" height="26"/></clipPath><clipPath id="mx-clip-745-175-92-26-0"><rect x="745" y="175" width="92" height="26"/></clipPath><clipPath id="mx-clip-745-272-92-26-0"><rect x="745" y="272" width="92" height="26"/></clipPath><clipPath id="mx-clip-745-240-78-26-0"><rect x="745" y="240" width="78" height="26"/></clipPath><clipPath id="mx-clip-745-336-72-26-0"><rect x="745" y="336" width="72" height="26"/></clipPath><clipPath id="mx-clip-854-111-162-26-0"><rect x="854" y="111" width="162" height="26"/></clipPath><clipPath id="mx-clip-854-143-162-26-0"><rect x="854" y="143" width="162" height="26"/></clipPath><clipPath id="mx-clip-854-304-162-26-0"><rect x="854" y="304" width="162" height="26"/></clipPath><clipPath id="mx-clip-854-175-162-26-0"><rect x="854" y="175" width="162" height="26"/></clipPath><clipPath id="mx-clip-854-272-162-26-0"><rect x="854" y="272" width="162" height="26"/></clipPath><clipPath id="mx-clip-854-240-152-26-0"><rect x="854" y="240" width="152" height="26"/></clipPath><clipPath id="mx-clip-854-336-85-26-0"><rect x="854" y="336" width="85" height="26"/></clipPath><clipPath id="mx-clip-745-431-222-26-0"><rect x="745" y="431" width="222" height="26"/></clipPath><clipPath id="mx-clip-745-463-232-26-0"><rect x="745" y="463" width="232" height="26"/></clipPath><clipPath id="mx-clip-745-495-222-26-0"><rect x="745" y="495" width="222" height="26"/></clipPath><clipPath id="mx-clip-745-528-222-26-0"><rect x="745" y="528" width="222" height="26"/></clipPath><clipPath id="mx-clip-745-560-222-26-0"><rect x="745" y="560" width="222" height="26"/></clipPath><clipPath id="mx-clip-1005-431-408-26-0"><rect x="1005" y="431" width="408" height="26"/></clipPath><clipPath id="mx-clip-1005-463-408-26-0"><rect x="1005" y="463" width="408" height="26"/></clipPath><clipPath id="mx-clip-1005-495-262-26-0"><rect x="1005" y="495" width="262" height="26"/></clipPath><clipPath id="mx-clip-1005-527-262-26-0"><rect x="1005" y="527" width="262" height="26"/></clipPath><clipPath id="mx-clip-1005-560-298-26-0"><rect x="1005" y="560" width="298" height="26"/></clipPath><clipPath id="mx-clip-745-657-82-26-0"><rect x="745" y="657" width="82" height="26"/></clipPath><clipPath id="mx-clip-745-689-82-26-0"><rect x="745" y="689" width="82" height="26"/></clipPath><clipPath id="mx-clip-745-721-82-26-0"><rect x="745" y="721" width="82" height="26"/></clipPath><clipPath id="mx-clip-745-786-82-27-0"><rect x="745" y="786" width="82" height="27"/></clipPath><clipPath id="mx-clip-745-818-82-26-0"><rect x="745" y="818" width="82" height="26"/></clipPath><clipPath id="mx-clip-745-850-82-26-0"><rect x="745" y="850" width="82" height="26"/></clipPath><clipPath id="mx-clip-745-883-82-26-0"><rect x="745" y="883" width="82" height="26"/></clipPath><clipPath id="mx-clip-745-915-82-26-0"><rect x="745" y="915" width="82" height="26"/></clipPath><clipPath id="mx-clip-745-947-102-26-0"><rect x="745" y="947" width="102" height="26"/></clipPath><clipPath id="mx-clip-745-980-82-26-0"><rect x="745" y="980" width="82" height="26"/></clipPath><clipPath id="mx-clip-745-1012-98-26-0"><rect x="745" y="1012" width="98" height="26"/></clipPath><clipPath id="mx-clip-745-1044-101-26-0"><rect x="745" y="1044" width="101" height="26"/></clipPath><clipPath id="mx-clip-855-657-132-26-0"><rect x="855" y="657" width="132" height="26"/></clipPath><clipPath id="mx-clip-855-689-132-26-0"><rect x="855" y="689" width="132" height="26"/></clipPath><clipPath id="mx-clip-855-721-132-26-0"><rect x="855" y="721" width="132" height="26"/></clipPath><clipPath id="mx-clip-855-785-195-28-0"><rect x="855" y="785" width="195" height="28"/></clipPath><clipPath id="mx-clip-855-818-132-26-0"><rect x="855" y="818" width="132" height="26"/></clipPath><clipPath id="mx-clip-855-850-132-26-0"><rect x="855" y="850" width="132" height="26"/></clipPath><clipPath id="mx-clip-855-883-132-26-0"><rect x="855" y="883" width="132" height="26"/></clipPath><clipPath id="mx-clip-855-915-132-26-0"><rect x="855" y="915" width="132" height="26"/></clipPath><clipPath id="mx-clip-855-947-202-26-0"><rect x="855" y="947" width="202" height="26"/></clipPath><clipPath id="mx-clip-855-980-132-26-0"><rect x="855" y="980" width="132" height="26"/></clipPath><clipPath id="mx-clip-855-1012-206-26-0"><rect x="855" y="1012" width="206" height="26"/></clipPath><clipPath id="mx-clip-855-1044-132-26-0"><rect x="855" y="1044" width="132" height="26"/></clipPath><clipPath id="mx-clip-1105-850-102-26-0"><rect x="1105" y="850" width="102" height="26"/></clipPath><clipPath id="mx-clip-1105-883-102-26-0"><rect x="1105" y="883" width="102" height="26"/></clipPath><clipPath id="mx-clip-1105-915-102-26-0"><rect x="1105" y="915" width="102" height="26"/></clipPath><clipPath id="mx-clip-1105-947-102-26-0"><rect x="1105" y="947" width="102" height="26"/></clipPath><clipPath id="mx-clip-1215-850-132-26-0"><rect x="1215" y="850" width="132" height="26"/></clipPath><clipPath id="mx-clip-1215-883-188-26-0"><rect x="1215" y="883" width="188" height="26"/></clipPath><clipPath id="mx-clip-1215-915-112-26-0"><rect x="1215" y="915" width="112" height="26"/></clipPath><clipPath id="mx-clip-1215-947-112-26-0"><rect x="1215" y="947" width="112" height="26"/></clipPath><clipPath id="mx-clip-1105-111-112-26-0"><rect x="1105" y="111" width="112" height="26"/></clipPath><clipPath id="mx-clip-1105-143-112-26-0"><rect x="1105" y="143" width="112" height="26"/></clipPath><clipPath id="mx-clip-1105-175-112-26-0"><rect x="1105" y="175" width="112" height="26"/></clipPath><clipPath id="mx-clip-1105-207-112-26-0"><rect x="1105" y="207" width="112" height="26"/></clipPath><clipPath id="mx-clip-1105-240-112-26-0"><rect x="1105" y="240" width="112" height="26"/></clipPath><clipPath id="mx-clip-1105-272-112-26-0"><rect x="1105" y="272" width="112" height="26"/></clipPath><clipPath id="mx-clip-1105-304-112-26-0"><rect x="1105" y="304" width="112" height="26"/></clipPath><clipPath id="mx-clip-1105-336-132-26-0"><rect x="1105" y="336" width="132" height="26"/></clipPath><clipPath id="mx-clip-1245-111-141-26-0"><rect x="1245" y="111" width="141" height="26"/></clipPath><clipPath id="mx-clip-1245-143-170-26-0"><rect x="1245" y="143" width="170" height="26"/></clipPath><clipPath id="mx-clip-1245-175-177-26-0"><rect x="1245" y="175" width="177" height="26"/></clipPath><clipPath id="mx-clip-1245-207-168-26-0"><rect x="1245" y="207" width="168" height="26"/></clipPath><clipPath id="mx-clip-1245-240-141-26-0"><rect x="1245" y="240" width="141" height="26"/></clipPath><clipPath id="mx-clip-1245-272-141-26-0"><rect x="1245" y="272" width="141" height="26"/></clipPath><clipPath id="mx-clip-1245-304-122-26-0"><rect x="1245" y="304" width="122" height="26"/></clipPath><clipPath id="mx-clip-1245-336-132-26-0"><rect x="1245" y="336" width="132" height="26"/></clipPath><clipPath id="mx-clip-1105-656-92-26-0"><rect x="1105" y="656" width="92" height="26"/></clipPath><clipPath id="mx-clip-1105-689-92-26-0"><rect x="1105" y="689" width="92" height="26"/></clipPath><clipPath id="mx-clip-1105-721-92-26-0"><rect x="1105" y="721" width="92" height="26"/></clipPath><clipPath id="mx-clip-1215-656-112-26-0"><rect x="1215" y="656" width="112" height="26"/></clipPath><clipPath id="mx-clip-1215-689-112-26-0"><rect x="1215" y="689" width="112" height="26"/></clipPath><clipPath id="mx-clip-1215-721-112-26-0"><rect x="1215" y="721" width="112" height="26"/></clipPath><clipPath id="mx-clip-1084-75-165-26-0"><rect x="1084" y="75" width="165" height="26"/></clipPath><clipPath id="mx-clip-725-75-165-26-0"><rect x="725" y="75" width="165" height="26"/></clipPath><clipPath id="mx-clip-369-75-165-26-0"><rect x="369" y="75" width="165" height="26"/></clipPath><clipPath id="mx-clip-5-75-165-26-0"><rect x="5" y="75" width="165" height="26"/></clipPath><clipPath id="mx-clip-4-396-165-26-0"><rect x="4" y="396" width="165" height="26"/></clipPath><clipPath id="mx-clip-365-396-165-26-0"><rect x="365" y="396" width="165" height="26"/></clipPath><clipPath id="mx-clip-725-396-165-26-0"><rect x="725" y="396" width="165" height="26"/></clipPath><clipPath id="mx-clip-1085-620-165-26-0"><rect x="1085" y="620" width="165" height="26"/></clipPath><clipPath id="mx-clip-725-620-165-26-0"><rect x="725" y="620" width="165" height="26"/></clipPath><clipPath id="mx-clip-171-0-273-52-0"><rect x="171" y="0" width="273" height="52"/></clipPath><clipPath id="mx-clip-745-207-80-26-0"><rect x="745" y="207" width="80" height="26"/></clipPath><clipPath id="mx-clip-854-207-180-26-0"><rect x="854" y="207" width="180" height="26"/></clipPath><clipPath id="mx-clip-378-463-142-26-0"><rect x="378" y="463" width="142" height="26"/></clipPath><clipPath id="mx-clip-548-463-115-26-0"><rect x="548" y="463" width="115" height="26"/></clipPath><clipPath id="mx-clip-855-754-132-26-0"><rect x="855" y="754" width="132" height="26"/></clipPath><clipPath id="mx-clip-745-753-82-26-0"><rect x="745" y="753" width="82" height="26"/></clipPath><clipPath id="mx-clip-132-111-192-26-0"><rect x="132" y="111" width="192" height="26"/></clipPath><clipPath id="mx-clip-25-111-112-26-0"><rect x="25" y="111" width="112" height="26"/></clipPath><clipPath id="mx-clip-100-139-10-20-0"><rect x="100" y="139" width="10" height="20"/></clipPath><clipPath id="mx-clip-25-431-132-26-0"><rect x="25" y="431" width="132" height="26"/></clipPath><clipPath id="mx-clip-25-463-132-26-0"><rect x="25" y="463" width="132" height="26"/></clipPath><clipPath id="mx-clip-25-496-132-26-0"><rect x="25" y="496" width="132" height="26"/></clipPath><clipPath id="mx-clip-25-528-132-26-0"><rect x="25" y="528" width="132" height="26"/></clipPath><clipPath id="mx-clip-25-560-132-26-0"><rect x="25" y="560" width="132" height="26"/></clipPath><clipPath id="mx-clip-25-592-132-26-0"><rect x="25" y="592" width="132" height="26"/></clipPath><clipPath id="mx-clip-25-625-132-26-0"><rect x="25" y="625" width="132" height="26"/></clipPath><clipPath id="mx-clip-25-657-132-26-0"><rect x="25" y="657" width="132" height="26"/></clipPath><clipPath id="mx-clip-25-689-132-26-0"><rect x="25" y="689" width="132" height="26"/></clipPath><clipPath id="mx-clip-25-721-132-26-0"><rect x="25" y="721" width="132" height="26"/></clipPath><clipPath id="mx-clip-25-754-132-26-0"><rect x="25" y="754" width="132" height="26"/></clipPath><clipPath id="mx-clip-25-786-132-26-0"><rect x="25" y="786" width="132" height="26"/></clipPath><clipPath id="mx-clip-25-818-132-26-0"><rect x="25" y="818" width="132" height="26"/></clipPath><clipPath id="mx-clip-25-850-132-26-0"><rect x="25" y="850" width="132" height="26"/></clipPath><clipPath id="mx-clip-25-883-132-26-0"><rect x="25" y="883" width="132" height="26"/></clipPath><clipPath id="mx-clip-25-915-132-26-0"><rect x="25" y="915" width="132" height="26"/></clipPath><clipPath id="mx-clip-25-947-132-26-0"><rect x="25" y="947" width="132" height="26"/></clipPath><clipPath id="mx-clip-25-980-132-26-0"><rect x="25" y="980" width="132" height="26"/></clipPath><clipPath id="mx-clip-25-1012-132-26-0"><rect x="25" y="1012" width="132" height="26"/></clipPath><clipPath id="mx-clip-25-1044-132-26-0"><rect x="25" y="1044" width="132" height="26"/></clipPath><clipPath id="mx-clip-165-431-164-26-0"><rect x="165" y="431" width="164" height="26"/></clipPath><clipPath id="mx-clip-165-463-166-26-0"><rect x="165" y="463" width="166" height="26"/></clipPath><clipPath id="mx-clip-165-496-142-26-0"><rect x="165" y="496" width="142" height="26"/></clipPath><clipPath id="mx-clip-165-528-142-26-0"><rect x="165" y="528" width="142" height="26"/></clipPath><clipPath id="mx-clip-165-560-142-26-0"><rect x="165" y="560" width="142" height="26"/></clipPath><clipPath id="mx-clip-165-592-142-26-0"><rect x="165" y="592" width="142" height="26"/></clipPath><clipPath id="mx-clip-165-625-142-26-0"><rect x="165" y="625" width="142" height="26"/></clipPath><clipPath id="mx-clip-165-657-179-26-0"><rect x="165" y="657" width="179" height="26"/></clipPath><clipPath id="mx-clip-165-689-151-26-0"><rect x="165" y="689" width="151" height="26"/></clipPath><clipPath id="mx-clip-165-721-142-26-0"><rect x="165" y="721" width="142" height="26"/></clipPath><clipPath id="mx-clip-165-915-173-26-0"><rect x="165" y="915" width="173" height="26"/></clipPath><clipPath id="mx-clip-165-786-142-26-0"><rect x="165" y="786" width="142" height="26"/></clipPath><clipPath id="mx-clip-165-818-142-26-0"><rect x="165" y="818" width="142" height="26"/></clipPath><clipPath id="mx-clip-165-850-142-26-0"><rect x="165" y="850" width="142" height="26"/></clipPath><clipPath id="mx-clip-165-883-142-26-0"><rect x="165" y="883" width="142" height="26"/></clipPath><clipPath id="mx-clip-165-947-142-26-0"><rect x="165" y="947" width="142" height="26"/></clipPath><clipPath id="mx-clip-165-754-142-26-0"><rect x="165" y="754" width="142" height="26"/></clipPath><clipPath id="mx-clip-165-980-168-26-0"><rect x="165" y="980" width="168" height="26"/></clipPath><clipPath id="mx-clip-165-1012-142-26-0"><rect x="165" y="1012" width="142" height="26"/></clipPath><clipPath id="mx-clip-165-1044-148-26-0"><rect x="165" y="1044" width="148" height="26"/></clipPath><clipPath id="mx-clip-485-17-448-26-0"><rect x="485" y="17" width="448" height="26"/></clipPath><clipPath id="mx-clip-1105-753-109-26-0"><rect x="1105" y="753" width="109" height="26"/></clipPath><clipPath id="mx-clip-1105-786-108-26-0"><rect x="1105" y="786" width="108" height="26"/></clipPath><clipPath id="mx-clip-1215-753-146-26-0"><rect x="1215" y="753" width="146" height="26"/></clipPath><clipPath id="mx-clip-1215-786-138-26-0"><rect x="1215" y="786" width="138" height="26"/></clipPath></defs><g><image x="387.08" y="1003.5" width="24.83" height="24.83" xlink:href="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB3aWR0aD0iMTZweCIgaGVpZ2h0PSIxNnB4IiB2aWV3Qm94PSIwIDAgMjQgMjQiIHZlcnNpb249IjEuMSI+PHBhdGggc3Ryb2tlPSIjMjliNmYyIiBmaWxsPSIjMjliNmYyIiBkPSJNMTUuNTUgNS41NUwxMSAxdjMuMDdDNy4wNiA0LjU2IDQgNy45MiA0IDEyczMuMDUgNy40NCA3IDcuOTN2LTIuMDJjLTIuODQtLjQ4LTUtMi45NC01LTUuOTFzMi4xNi01LjQzIDUtNS45MVYxMGw0LjU1LTQuNDV6TTE5LjkzIDExYy0uMTctMS4zOS0uNzItMi43My0xLjYyLTMuODlsLTEuNDIgMS40MmMuNTQuNzUuODggMS42IDEuMDIgMi40N2gyLjAyek0xMyAxNy45djIuMDJjMS4zOS0uMTcgMi43NC0uNzEgMy45LTEuNjFsLTEuNDQtMS40NGMtLjc1LjU0LTEuNTkuODktMi40NiAxLjAzem0zLjg5LTIuNDJsMS40MiAxLjQxYy45LTEuMTYgMS40NS0yLjUgMS42Mi0zLjg5aC0yLjAyYy0uMTQuODctLjQ4IDEuNzItMS4wMiAyLjQ4eiIvPjwvc3ZnPg==" preserveAspectRatio="none"/><rect x="7" y="424" width="340" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="7" y="488" width="340" height="31" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="7" y="456" width="340" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="7" y="521" width="340" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="7" y="553" width="340" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="7" y="617" width="340" height="31" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="7" y="585" width="340" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="7" y="650" width="340" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="7" y="682" width="340" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="7" y="714" width="340" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="7" y="746" width="340" height="31" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="7" y="779" width="340" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="7" y="811" width="340" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="7" y="843" width="340" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="7" y="875" width="340" height="31" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="7" y="1037" width="340" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="7" y="1005" width="340" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="7" y="972" width="340" height="31" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="7" y="940" width="340" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="7" y="908" width="340" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="367" y="424" width="340" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="367" y="488" width="340" height="31" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="367" y="521" width="340" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="367" y="553" width="340" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="367" y="617" width="340" height="31" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="367" y="585" width="340" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="367" y="456" width="340" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="367" y="650" width="340" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="367" y="682" width="340" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="367" y="714" width="340" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="367" y="746" width="340" height="31" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="367" y="779" width="340" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="367" y="811" width="340" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="367" y="843" width="340" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="367" y="875" width="340" height="31" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="367" y="908" width="340" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="367" y="104" width="340" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="367" y="136" width="340" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="367" y="168" width="340" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="367" y="200" width="340" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="367" y="232" width="340" height="31" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="367" y="297" width="340" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="367" y="265" width="340" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="367" y="329" width="340" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="7" y="104" width="340" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="7" y="136" width="340" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="7" y="168" width="340" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="7" y="200" width="340" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="7" y="232" width="340" height="31" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="7" y="265" width="340" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="7" y="297" width="340" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="7" y="329" width="340" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="727" y="104" width="340" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="727" y="136" width="340" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="727" y="168" width="340" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="727" y="200" width="340" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="727" y="232" width="340" height="31" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="727" y="265" width="340" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="727" y="297" width="340" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="727" y="329" width="340" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="1087" y="329" width="340" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="1087" y="297" width="340" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="1087" y="265" width="340" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="1087" y="232" width="340" height="31" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="1087" y="200" width="340" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="1087" y="168" width="340" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="1087" y="136" width="340" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="1087" y="104" width="340" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="727" y="424" width="700" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="727" y="456" width="700" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="727" y="488" width="700" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="727" y="520" width="700" height="31" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="727" y="553" width="700" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="727" y="649" width="340" height="31" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="727" y="682" width="340" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="727" y="714" width="340" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="727" y="746" width="340" height="31" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="727" y="779" width="340" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="727" y="811" width="340" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="727" y="843" width="340" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="727" y="875" width="340" height="31" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="727" y="908" width="340" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="727" y="972" width="340" height="31" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="727" y="940" width="340" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="727" y="1005" width="340" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="727" y="1037" width="340" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="1087" y="649" width="340" height="31" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="1087" y="682" width="340" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="1087" y="714" width="340" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="1087" y="746" width="340" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="1087" y="778" width="340" height="31" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="1087" y="843" width="340" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="1087" y="875" width="340" height="31" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="1087" y="908" width="340" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="1087" y="940" width="340" height="30" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="21" y="138" width="120" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-25-143-112-26-0)" font-size="12px"><text x="26.5" y="155.5">(Shift+)Enter</text></g><rect x="21" y="170" width="120" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-25-175-112-26-0)" font-size="12px"><text x="26.5" y="187.5">Enter / Tab</text></g><rect x="21" y="202" width="120" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-25-207-112-26-0)" font-size="12px"><text x="26.5" y="219.5">Enter / F2</text></g><rect x="21" y="234.5" width="235" height="34.5" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-25-240-227-35-0)" font-size="12px"><text x="26.5" y="252">Ctrl+Enter / Shift+Tab / F2 / Esc</text></g><rect x="21" y="267" width="120" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-25-272-112-26-0)" font-size="12px"><text x="26.5" y="284.5">Ctrl+B / I</text></g><rect x="21" y="299" width="120" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-25-304-112-26-0)" font-size="12px"><text x="26.5" y="316.5">Ctrl+U</text></g><rect x="21" y="331" width="120" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-25-336-112-26-0)" font-size="12px"><text x="26.5" y="348.5">Ctrl+. / ,</text></g><rect x="202" y="234.5" width="10" height="16" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-202-236-10-20-0)" font-size="10px"><text x="203.5" y="246">1</text></g><rect x="128" y="138" width="200" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-132-143-192-26-0)" font-size="12px"><text x="133.5" y="155.5">New line in formatted labels</text></g><rect x="128" y="170" width="203" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-132-175-195-26-0)" font-size="12px"><text x="133.5" y="187.5">Line break / Tab / Indent in labels</text></g><rect x="128" y="202" width="200" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-132-207-192-26-0)" font-size="12px"><text x="133.5" y="219.5">Start editing label of selected cell</text></g><rect x="222" y="234.5" width="117" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-226-240-109-26-0)" font-size="12px"><text x="227.5" y="252">Stop editing</text></g><rect x="128" y="267" width="200" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-132-272-192-26-0)" font-size="12px"><text x="133.5" y="284.5">Toggle bold / italic on selected text</text></g><rect x="128" y="299" width="200" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-132-304-192-26-0)" font-size="12px"><text x="133.5" y="316.5">Toggle underline on selected text</text></g><rect x="128" y="331" width="219" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-132-336-211-26-0)" font-size="12px"><text x="133.5" y="348.5">Superscript / Subscript on selected text</text></g><rect x="381" y="106" width="90" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-385-111-82-26-0)" font-size="12px"><text x="386.5" y="123.5">(Shift+)Tab</text></g><rect x="381" y="138" width="132" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-385-143-124-26-0)" font-size="12px"><text x="386.5" y="155.5">Alt+Shift+P / Alt+Tab</text></g><rect x="381" y="170" width="114" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-385-175-106-26-0)" font-size="12px"><text x="386.5" y="187.5">Alt+Shift+X / C / S</text></g><rect x="381" y="202" width="100" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-385-207-92-26-0)" font-size="12px"><text x="386.5" y="219.5">Alt+Drag</text></g><rect x="381" y="234.5" width="90" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-385-240-82-26-0)" font-size="12px"><text x="386.5" y="252">Ctrl+(Shift+)A</text></g><rect x="381" y="267" width="136" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-385-272-128-26-0)" font-size="12px"><text x="386.5" y="284.5">Ctrl+Shift+I / E</text></g><rect x="381" y="299" width="161" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-385-304-153-26-0)" font-size="12px"><text x="386.5" y="316.5">Ctrl / Shift+Select / Drag</text></g><rect x="381" y="331" width="110" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-385-336-102-26-0)" font-size="12px"><text x="386.5" y="348.5">Alt+Click</text></g><rect x="502" y="106" width="155" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-506-111-147-26-0)" font-size="12px"><text x="507.5" y="123.5">Select next / previous</text></g><rect x="512" y="138" width="155" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-516-143-147-26-0)" font-size="12px"><text x="517.5" y="155.5">Select parent</text></g><rect x="502" y="170" width="189" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-506-175-181-26-0)" font-size="12px"><text x="507.5" y="187.5">Select (all) children / siblings</text></g><rect x="502" y="202" width="212" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-506-207-204-26-0)" font-size="12px"><text x="507.5" y="219.5">Start selection / Select intersection</text></g><rect x="502" y="234.5" width="110" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-506-240-102-26-0)" font-size="12px"><text x="507.5" y="252">Select all / none</text></g><rect x="502" y="267" width="186" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-506-272-178-26-0)" font-size="12px"><text x="507.5" y="284.5">Select vertices / edges</text></g><rect x="532" y="299" width="145" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-536-304-137-26-0)" font-size="12px"><text x="537.5" y="316.5">Toggle selection state</text></g><rect x="502" y="331" width="136" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-506-336-128-26-0)" font-size="12px"><text x="507.5" y="348.5">Select cell below</text></g><rect x="374" y="426" width="166" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-378-431-158-26-0)" font-size="12px"><text x="379.5" y="443.5">Alt+Wheel</text></g><rect x="374" y="490.5" width="130" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-378-495-122-26-0)" font-size="12px"><text x="379.5" y="508">Wheel</text></g><rect x="374" y="523" width="130" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-378-528-122-26-0)" font-size="12px"><text x="379.5" y="540.5">Shift+Wheel</text></g><rect x="374" y="555" width="160" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-378-560-152-26-0)" font-size="12px"><text x="379.5" y="572.5">Space / Right-click+Drag</text></g><rect x="374" y="587" width="130" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-378-592-122-26-0)" font-size="12px"><text x="379.5" y="604.5">Alt+Ctrl+Shift+Drag</text></g><rect x="374" y="619.5" width="133" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-378-625-125-26-0)" font-size="12px"><text x="379.5" y="637">Alt+Resize / Move</text></g><rect x="374" y="652" width="130" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-378-657-122-26-0)" font-size="12px"><text x="379.5" y="669.5">Shift+Home</text></g><rect x="374" y="684" width="130" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-378-689-122-26-0)" font-size="12px"><text x="379.5" y="701.5">End</text></g><rect x="374" y="716" width="130" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-378-721-122-26-0)" font-size="12px"><text x="379.5" y="733.5">Enter / Home</text></g><rect x="374" y="748.5" width="130" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-378-754-122-26-0)" font-size="12px"><text x="379.5" y="766">Ctrl+Shift+H</text></g><rect x="374" y="781" width="130" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-378-786-122-26-0)" font-size="12px"><text x="379.5" y="798.5">Ctrl+J</text></g><rect x="374" y="813" width="130" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-378-818-122-26-0)" font-size="12px"><text x="379.5" y="830.5">Ctrl+Shift+J</text></g><rect x="374" y="877.5" width="130" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-378-882-122-26-0)" font-size="12px"><text x="379.5" y="895">Ctrl + (Numpad)</text></g><rect x="374" y="910" width="130" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-378-915-122-26-0)" font-size="12px"><text x="379.5" y="927.5">Ctrl - (Numpad)</text></g><rect x="374" y="845" width="130" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-378-850-122-26-0)" font-size="12px"><text x="379.5" y="862.5">Ctrl+0</text></g><rect x="544" y="426" width="125" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-548-431-117-26-0)" font-size="12px"><text x="549.5" y="443.5">Canvas zoom in/out</text></g><rect x="544" y="490.5" width="150" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-548-496-142-26-0)" font-size="12px"><text x="549.5" y="508">Canvas vertical scroll</text></g><rect x="544" y="523" width="150" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-548-528-142-26-0)" font-size="12px"><text x="549.5" y="540.5">Canvas horizontal scroll</text></g><rect x="544" y="555" width="130" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-548-560-122-26-0)" font-size="12px"><text x="549.5" y="572.5">Pan canvas</text></g><rect x="544" y="587" width="150" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-548-592-142-26-0)" font-size="12px"><text x="549.5" y="604.5">Create / Remove space</text></g><rect x="544" y="619.5" width="150" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-548-625-142-26-0)" font-size="12px"><text x="549.5" y="637">Ignore grid</text></g><rect x="544" y="652" width="150" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-548-657-142-26-0)" font-size="12px"><text x="549.5" y="669.5">Home</text></g><rect x="544" y="684" width="150" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-548-689-142-26-0)" font-size="12px"><text x="549.5" y="701.5">Refresh</text></g><rect x="544" y="716" width="150" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-548-721-142-26-0)" font-size="12px"><text x="549.5" y="733.5">Reset view</text></g><rect x="544" y="748.5" width="150" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-548-754-142-26-0)" font-size="12px"><text x="549.5" y="766">Fit window</text></g><rect x="544" y="781" width="150" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-548-786-142-26-0)" font-size="12px"><text x="549.5" y="798.5">Fit page</text></g><rect x="544" y="813" width="150" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-548-818-142-26-0)" font-size="12px"><text x="549.5" y="830.5">Fit two pages</text></g><rect x="544" y="845" width="150" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-548-850-142-26-0)" font-size="12px"><text x="549.5" y="862.5">Custom zoom</text></g><rect x="544" y="877.5" width="150" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-548-882-142-26-0)" font-size="12px"><text x="549.5" y="895">Zoom in</text></g><rect x="544" y="910" width="150" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-548-915-142-26-0)" font-size="12px"><text x="549.5" y="927.5">Zoom out</text></g><rect x="425" y="950" width="248" height="48" rx="7.2" ry="7.2" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-size="12px"><text x="426.5" y="964.5">Click to connect and clone (Ctrl+Click to clone,</text><text x="426.5" y="978.5">Shift+Click to connect), Drag to connect</text><text x="426.5" y="992.5">(Ctrl+Drag to clone)</text></g><image x="391.99" y="1033.5" width="15" height="25" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA8AAAAZAgMAAACTN5xfAAAACVBMVEX///////8AAACO9MPsAAAAAXRSTlMAQObYZgAAAFRJREFUeF5VyjEKgDAQRNGPRSA38AB6JEWsUkggt9hLGDbdlh5Tp7R5DPzBAEK46OI2UcQhdrGZKB/nA13B9Q5hqwEij+akVi/yFDMJFjL8VlUdw1+gHxbW+YsglAAAAABJRU5ErkJggg=="/><rect x="425" y="1005" width="239" height="20" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-425-1005-239-20-0)" font-size="12px"><text x="426.5" y="1019.5">Click to rotate 90° clockwise, Drag to rotate</text></g><rect x="425" y="1036" width="258" height="20" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-425-1036-258-20-0)" font-size="12px"><text x="426.5" y="1050.5">Ctrl: Ask   Alt: Origin   Shift: As Image</text></g><rect x="741" y="106" width="100" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-745-111-92-26-0)" font-size="12px"><text x="746.5" y="123.5">Ctrl+Shift+L</text></g><rect x="741" y="138" width="100" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-745-143-92-26-0)" font-size="12px"><text x="746.5" y="155.5">Ctrl+Shift+O</text></g><rect x="741" y="299" width="80" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-745-304-72-26-0)" font-size="12px"><text x="746.5" y="316.5">Ctrl+M</text></g><rect x="741" y="170" width="100" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-745-175-92-26-0)" font-size="12px"><text x="746.5" y="187.5">Ctrl+Shift+P</text></g><rect x="741" y="267" width="100" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-745-272-92-26-0)" font-size="12px"><text x="746.5" y="284.5">Ctrl+Shift+M</text></g><rect x="741" y="234.5" width="86" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-745-240-78-26-0)" font-size="12px"><text x="746.5" y="252">Ctrl+K</text></g><rect x="741" y="331" width="80" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-745-336-72-26-0)" font-size="12px"><text x="746.5" y="348.5">Ctrl+F</text></g><rect x="850" y="106" width="170" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-854-111-162-26-0)" font-size="12px"><text x="855.5" y="123.5">Toggle Layers</text></g><rect x="850" y="138" width="170" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-854-143-162-26-0)" font-size="12px"><text x="855.5" y="155.5">Toggle Outline</text></g><rect x="850" y="299" width="170" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-854-304-162-26-0)" font-size="12px"><text x="855.5" y="316.5">Edit metadata</text></g><rect x="850" y="170" width="170" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-854-175-162-26-0)" font-size="12px"><text x="855.5" y="187.5">Toggle Format</text></g><rect x="850" y="267" width="170" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-854-272-162-26-0)" font-size="12px"><text x="855.5" y="284.5">Edit vertex geometry</text></g><rect x="850" y="234.5" width="160" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-854-240-152-26-0)" font-size="12px"><text x="855.5" y="252">Toggle Tags</text></g><rect x="850" y="331" width="93" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-854-336-85-26-0)" font-size="12px"><text x="855.5" y="348.5">Find/Replace</text></g><rect x="741" y="426" width="230" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-745-431-222-26-0)" font-size="12px"><text x="746.5" y="443.5">Alt+(Shift+)Drag shape from library</text></g><rect x="741" y="458" width="240" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-745-463-232-26-0)" font-size="12px"><text x="746.5" y="475.5">Alt+(Shift / Ctrl)+Click on a library shape</text></g><rect x="741" y="490" width="230" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-745-495-222-26-0)" font-size="12px"><text x="746.5" y="507.5">Shift+Click on a library shape</text></g><rect x="741" y="522.5" width="230" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-745-528-222-26-0)" font-size="12px"><text x="746.5" y="540">Click on a library shape</text></g><rect x="741" y="555" width="230" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-745-560-222-26-0)" font-size="12px"><text x="746.5" y="572.5">Alt / Shift+Connect</text></g><rect x="1001" y="426" width="416" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-1005-431-408-26-0)" font-size="12px"><text x="1006.5" y="443.5">Disable replace and connect on drop targets (Shift ignores current style)</text></g><rect x="1001" y="458" width="416" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-1005-463-408-26-0)" font-size="12px"><text x="1006.5" y="475.5">Insert and connect the selected item (Shift ignores current style)</text></g><rect x="1001" y="490" width="270" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-1005-495-262-26-0)" font-size="12px"><text x="1006.5" y="507.5">Replace the selected item with the clicked item</text></g><rect x="1001" y="522.5" width="270" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-1005-527-262-26-0)" font-size="12px"><text x="1006.5" y="540">Connect to unconnected side of selected edge</text></g><rect x="1001" y="555" width="306" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-1005-560-298-26-0)" font-size="12px"><text x="1006.5" y="572.5">Ignore shape / Connect to a fixed point</text></g><rect x="741" y="651.5" width="90" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-745-657-82-26-0)" font-size="12px"><text x="746.5" y="669">Ctrl+(Shift+)S</text></g><rect x="741" y="684" width="90" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-745-689-82-26-0)" font-size="12px"><text x="746.5" y="701.5">Ctrl+Z</text></g><rect x="741" y="716" width="90" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-745-721-82-26-0)" font-size="12px"><text x="746.5" y="733.5">Alt+Shift+A</text></g><rect x="741" y="780.5" width="90" height="27" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-745-786-82-27-0)" font-size="12px"><text x="746.5" y="798">Hold Alt</text></g><rect x="741" y="813" width="90" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-745-818-82-26-0)" font-size="12px"><text x="746.5" y="830.5">Ctrl+Shift+G</text></g><rect x="741" y="845" width="90" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-745-850-82-26-0)" font-size="12px"><text x="746.5" y="862.5">Ctrl+P</text></g><rect x="741" y="877.5" width="90" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-745-883-82-26-0)" font-size="12px"><text x="746.5" y="895">Ctrl+Y</text></g><rect x="741" y="910" width="90" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-745-915-82-26-0)" font-size="12px"><text x="746.5" y="927.5">Ctrl+Shift+Z</text></g><rect x="741" y="942" width="110" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-745-947-102-26-0)" font-size="12px"><text x="746.5" y="959.5">A / S / D / F / C</text></g><rect x="741" y="974.5" width="90" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-745-980-82-26-0)" font-size="12px"><text x="746.5" y="992">X</text></g><rect x="741" y="1007" width="106" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-745-1012-98-26-0)" font-size="12px"><text x="746.5" y="1024.5">Ctrl / Right click</text></g><rect x="741" y="1039" width="109" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-745-1044-101-26-0)" font-size="12px"><text x="746.5" y="1056.5">(Ctrl / Shift)+Esc</text></g><rect x="851" y="651.5" width="140" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-855-657-132-26-0)" font-size="12px"><text x="856.5" y="669">Save (as)</text></g><rect x="851" y="684" width="140" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-855-689-132-26-0)" font-size="12px"><text x="856.5" y="701.5">Undo</text></g><rect x="851" y="716" width="140" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-855-721-132-26-0)" font-size="12px"><text x="856.5" y="733.5">Connection arrows</text></g><rect x="851" y="780" width="203" height="28" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-855-785-195-28-0)" font-size="12px"><text x="856.5" y="797.5">Ignore handles under the mouse</text></g><rect x="851" y="813" width="140" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-855-818-132-26-0)" font-size="12px"><text x="856.5" y="830.5">Toggle grid</text></g><rect x="851" y="845" width="140" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-855-850-132-26-0)" font-size="12px"><text x="856.5" y="862.5">Print</text></g><rect x="851" y="877.5" width="140" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-855-883-132-26-0)" font-size="12px"><text x="856.5" y="895">Redo (Windows)</text></g><rect x="851" y="910" width="140" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-855-915-132-26-0)" font-size="12px"><text x="856.5" y="927.5">Redo (Linux/macOS)</text></g><rect x="851" y="942" width="210" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-855-947-202-26-0)" font-size="12px"><text x="856.5" y="959.5">Add Text/Note/Rectangle/Ellipse/Line</text></g><rect x="851" y="974.5" width="140" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-855-980-132-26-0)" font-size="12px"><text x="856.5" y="992">Toggle Freehand Mode</text></g><rect x="851" y="1007" width="214" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-855-1012-206-26-0)" font-size="12px"><text x="856.5" y="1024.5">Context Menu</text></g><rect x="851" y="1039" width="140" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-855-1044-132-26-0)" font-size="12px"><text x="856.5" y="1056.5">Cancel editing / action</text></g><rect x="1101" y="845" width="110" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-1105-850-102-26-0)" font-size="12px"><text x="1106.5" y="862.5">Drag</text></g><rect x="1101" y="877.5" width="110" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-1105-883-102-26-0)" font-size="12px"><text x="1106.5" y="895">Tap and hold</text></g><rect x="1101" y="910" width="110" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-1105-915-102-26-0)" font-size="12px"><text x="1106.5" y="927.5">Pinch</text></g><rect x="1101" y="942" width="110" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-1105-947-102-26-0)" font-size="12px"><text x="1106.5" y="959.5">Tap selected cell</text></g><rect x="1211" y="845" width="140" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-1215-850-132-26-0)" font-size="12px"><text x="1216.5" y="862.5">Move cell / Pan canvas</text></g><rect x="1211" y="878" width="196" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-1215-883-188-26-0)" font-size="12px"><text x="1216.5" y="895.5">Toggle selection / Rubberband</text></g><rect x="1211" y="910" width="120" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-1215-915-112-26-0)" font-size="12px"><text x="1216.5" y="927.5">Zoom</text></g><rect x="1211" y="942" width="120" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-1215-947-112-26-0)" font-size="12px"><text x="1216.5" y="959.5">Context menu</text></g><rect x="1101" y="106" width="120" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-1105-111-112-26-0)" font-size="12px"><text x="1106.5" y="123.5">Cursor</text></g><rect x="1101" y="138" width="120" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-1105-143-112-26-0)" font-size="12px"><text x="1106.5" y="155.5">Shift+Cursor</text></g><rect x="1101" y="170" width="120" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-1105-175-112-26-0)" font-size="12px"><text x="1106.5" y="187.5">Ctrl+Cursor</text></g><rect x="1101" y="202" width="120" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-1105-207-112-26-0)" font-size="12px"><text x="1106.5" y="219.5">Ctrl+Shift+Cursor</text></g><rect x="1101" y="234.5" width="120" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-1105-240-112-26-0)" font-size="12px"><text x="1106.5" y="252">Alt+Shift+Cursor</text></g><rect x="1101" y="267" width="120" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-1105-272-112-26-0)" font-size="12px"><text x="1106.5" y="284.5">Alt+Cursor</text></g><rect x="1101" y="299" width="120" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-1105-304-112-26-0)" font-size="12px"><text x="1106.5" y="316.5">Ctrl+Shift+Pg Up</text></g><rect x="1101" y="331" width="140" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-1105-336-132-26-0)" font-size="12px"><text x="1106.5" y="348.5">Ctrl+Shift+Pg Down</text></g><rect x="1241" y="106" width="149" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-1245-111-141-26-0)" font-size="12px"><text x="1246.5" y="123.5">Scroll / Move cell (pt)</text></g><rect x="1241" y="138" width="178" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-1245-143-170-26-0)" font-size="12px"><text x="1246.5" y="155.5">Move cell (grid) or page</text></g><rect x="1241" y="170" width="185" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-1245-175-177-26-0)" font-size="12px"><text x="1246.5" y="187.5">Resize cell (pt) or select page</text></g><rect x="1241" y="202" width="176" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-1245-207-168-26-0)" font-size="12px"><text x="1246.5" y="219.5">Resize cell (grid size)</text></g><rect x="1241" y="234.5" width="149" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-1245-240-141-26-0)" font-size="12px"><text x="1246.5" y="252">Clone and connect</text></g><rect x="1241" y="267" width="149" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-1245-272-141-26-0)" font-size="12px"><text x="1246.5" y="284.5">Scroll page</text></g><rect x="1241" y="299" width="130" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-1245-304-122-26-0)" font-size="12px"><text x="1246.5" y="316.5">Previous page</text></g><rect x="1241" y="331" width="140" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-1245-336-132-26-0)" font-size="12px"><text x="1246.5" y="348.5">Next page</text></g><rect x="1101" y="651" width="100" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-1105-656-92-26-0)" font-size="12px"><text x="1106.5" y="668.5">Ctrl+Shift+R</text></g><rect x="1101" y="684" width="100" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-1105-689-92-26-0)" font-size="12px"><text x="1106.5" y="701.5">Ctrl+E</text></g><rect x="1101" y="716" width="100" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-1105-721-92-26-0)" font-size="12px"><text x="1106.5" y="733.5">Ctrl+Shift+D</text></g><rect x="1211" y="651" width="120" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-1215-656-112-26-0)" font-size="12px"><text x="1216.5" y="668.5">Clear default style</text></g><rect x="1211" y="684" width="120" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-1215-689-112-26-0)" font-size="12px"><text x="1216.5" y="701.5">Edit style</text></g><rect x="1211" y="716" width="120" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-1215-721-112-26-0)" font-size="12px"><text x="1216.5" y="733.5">Set as default style</text></g><rect x="1080" y="70" width="173" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-1084-75-165-26-0)" font-size="14px"><text x="1085.5" y="89.5">CURSOR / PAGE KEYS</text></g><rect x="721" y="70" width="173" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-725-75-165-26-0)" font-size="14px"><text x="726.5" y="89.5">TOOLS</text></g><rect x="365" y="70" width="173" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-369-75-165-26-0)" font-size="14px"><text x="370.5" y="89.5">SELECTION</text></g><rect x="1" y="70" width="173" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-5-75-165-26-0)" font-size="14px"><text x="6.5" y="89.5">LABELS</text></g><rect x="0" y="391" width="173" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-4-396-165-26-0)" font-size="14px"><text x="5.5" y="410.5">CANVAS</text></g><rect x="361" y="391" width="173" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-365-396-165-26-0)" font-size="14px"><text x="366.5" y="410.5">VIEW</text></g><rect x="721" y="391" width="173" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-725-396-165-26-0)" font-size="14px"><text x="726.5" y="410.5">LIBRARY / CONNECT</text></g><rect x="1081" y="615" width="173" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-1085-620-165-26-0)" font-size="14px"><text x="1086.5" y="634.5">STYLES</text></g><rect x="721" y="615" width="173" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-725-620-165-26-0)" font-size="14px"><text x="726.5" y="634.5">DOCUMENT</text></g><rect x="167" y="-0.5" width="281" height="51.5" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-171-0-273-52-0)" font-size="27px"><text x="172.5" y="37.25">Keyboard Shortcuts</text></g><rect x="741" y="202" width="88" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-745-207-80-26-0)" font-size="12px"><text x="746.5" y="219.5">Ctrl+Shift+K</text></g><rect x="850" y="202" width="188" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-854-207-180-26-0)" font-size="12px"><text x="855.5" y="219.5">Toggle Shapes</text></g><a xlink:href="https://app.diagrams.net/#Uhttps%3A%2F%2Fapp.diagrams.net%2Fshortcuts.svg" target="_blank"><rect x="1111.67" y="1023" width="132.33" height="18" fill="none" stroke="none" pointer-events="all"/><g fill="#F08705" font-family="Helvetica" text-decoration="underline" font-size="12px"><text x="1113.17" y="1036.5">Open in draw.io...</text></g></a><rect x="374" y="458" width="150" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-378-463-142-26-0)" font-size="12px"><text x="379.5" y="475.5">Ctrl+Shift+Wheel</text></g><rect x="544" y="458" width="123" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-548-463-115-26-0)" font-size="12px"><text x="549.5" y="475.5">Canvas zoom in/out</text></g><rect x="851" y="748.5" width="140" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-855-754-132-26-0)" font-size="12px"><text x="856.5" y="766">Connection points</text></g><rect x="741" y="748.5" width="90" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-745-753-82-26-0)" font-size="12px"><text x="746.5" y="766">Alt+Shift+O</text></g><rect x="128" y="106" width="200" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-132-111-192-26-0)" font-size="12px"><text x="133.5" y="123.5">Insert text or add an edge label</text></g><rect x="21" y="106" width="120" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-25-111-112-26-0)" font-size="12px"><text x="26.5" y="123.5">Double-click</text></g><rect x="100" y="138" width="10" height="16" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-100-139-10-20-0)" font-size="10px"><text x="101.5" y="149.5">1</text></g><rect x="1112" y="1000" width="255" height="19" rx="2.85" ry="2.85" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-size="12px"><text x="1113.5" y="1014">Ctrl / Shift+Enter: New Line / Apply in Safari</text></g><rect x="1105.33" y="994" width="20" height="10" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-size="10px"><text x="1106.83" y="1005.5">1</text></g><rect x="21" y="426" width="140" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-25-431-132-26-0)" font-size="12px"><text x="26.5" y="443.5">Ctrl+X / C / Ctrl+Alt+X</text></g><rect x="21" y="458" width="140" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-25-463-132-26-0)" font-size="12px"><text x="26.5" y="475.5">Ctrl+V / Ctrl+Shift+V</text></g><rect x="21" y="490.5" width="140" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-25-496-132-26-0)" font-size="12px"><text x="26.5" y="508">Ctrl+G</text></g><rect x="21" y="523" width="140" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-25-528-132-26-0)" font-size="12px"><text x="26.5" y="540.5">Ctrl+Shift+U</text></g><rect x="21" y="555" width="140" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-25-560-132-26-0)" font-size="12px"><text x="26.5" y="572.5">Ctrl+L / Alt+Shift+L</text></g><rect x="21" y="587" width="140" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-25-592-132-26-0)" font-size="12px"><text x="26.5" y="604.5">Ctrl+Enter / D</text></g><rect x="21" y="619.5" width="140" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-25-625-132-26-0)" font-size="12px"><text x="26.5" y="637">Backspace or Delete</text></g><rect x="21" y="652" width="140" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-25-657-132-26-0)" font-size="12px"><text x="26.5" y="669.5">Ctrl / Shift+Delete</text></g><rect x="21" y="684" width="140" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-25-689-132-26-0)" font-size="12px"><text x="26.5" y="701.5">Ctrl+R</text></g><rect x="21" y="716" width="140" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-25-721-132-26-0)" font-size="12px"><text x="26.5" y="733.5">Shift+Resize</text></g><rect x="21" y="748.5" width="140" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-25-754-132-26-0)" font-size="12px"><text x="26.5" y="766">Ctrl+Resize</text></g><rect x="21" y="781" width="140" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-25-786-132-26-0)" font-size="12px"><text x="26.5" y="798.5">Ctrl+Home</text></g><rect x="21" y="813" width="140" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-25-818-132-26-0)" font-size="12px"><text x="26.5" y="830.5">Ctrl+End</text></g><rect x="21" y="845" width="140" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-25-850-132-26-0)" font-size="12px"><text x="26.5" y="862.5">Ctrl+Shift+Home</text></g><rect x="21" y="877.5" width="140" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-25-883-132-26-0)" font-size="12px"><text x="26.5" y="895">Ctrl+Shift+End</text></g><rect x="21" y="910" width="140" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-25-915-132-26-0)" font-size="12px"><text x="26.5" y="927.5">Ctrl+Shift+F / B</text></g><rect x="21" y="942" width="140" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-25-947-132-26-0)" font-size="12px"><text x="26.5" y="959.5">Alt+Shift+F/V / B/E</text></g><rect x="21" y="974.5" width="140" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-25-980-132-26-0)" font-size="12px"><text x="26.5" y="992">Alt+Shift+R / T</text></g><rect x="21" y="1007" width="140" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-25-1012-132-26-0)" font-size="12px"><text x="26.5" y="1024.5">Ctrl+Shift+Y</text></g><rect x="21" y="1039" width="140" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-25-1044-132-26-0)" font-size="12px"><text x="26.5" y="1056.5">Ctrl / Shift+Drag</text></g><rect x="161" y="426" width="172" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-165-431-164-26-0)" font-size="12px"><text x="166.5" y="443.5">Cut / Copy / Copy as Image</text></g><rect x="161" y="458" width="174" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-165-463-166-26-0)" font-size="12px"><text x="166.5" y="475.5">Paste / Paste unformatted text</text></g><rect x="161" y="490.5" width="150" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-165-496-142-26-0)" font-size="12px"><text x="166.5" y="508">Group</text></g><rect x="161" y="523" width="150" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-165-528-142-26-0)" font-size="12px"><text x="166.5" y="540.5">Ungroup</text></g><rect x="161" y="555" width="150" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-165-560-142-26-0)" font-size="12px"><text x="166.5" y="572.5">Lock/Unlock / Edit link</text></g><rect x="161" y="587" width="150" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-165-592-142-26-0)" font-size="12px"><text x="166.5" y="604.5">Duplicate</text></g><rect x="161" y="619.5" width="150" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-165-625-142-26-0)" font-size="12px"><text x="166.5" y="637">Delete selected cells</text></g><rect x="161" y="652" width="187" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-165-657-179-26-0)" font-size="12px"><text x="166.5" y="669.5">...with connections / labels</text></g><rect x="161" y="684" width="159" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-165-689-151-26-0)" font-size="12px"><text x="166.5" y="701.5">Turn / Rotate 90° clockwise</text></g><rect x="161" y="716" width="150" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-165-721-142-26-0)" font-size="12px"><text x="166.5" y="733.5">Maintain proportions</text></g><rect x="161" y="910" width="181" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-165-915-173-26-0)" font-size="12px"><text x="166.5" y="927.5">Bring to front / Send to back</text></g><rect x="161" y="781" width="150" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-165-786-142-26-0)" font-size="12px"><text x="166.5" y="798.5">Collapse container</text></g><rect x="161" y="813" width="150" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-165-818-142-26-0)" font-size="12px"><text x="166.5" y="830.5">Expand container</text></g><rect x="161" y="845" width="150" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-165-850-142-26-0)" font-size="12px"><text x="166.5" y="862.5">Exit group</text></g><rect x="161" y="877.5" width="150" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-165-883-142-26-0)" font-size="12px"><text x="166.5" y="895">Enter group</text></g><rect x="161" y="942" width="150" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-165-947-142-26-0)" font-size="12px"><text x="166.5" y="959.5">Copy/paste size / data</text></g><rect x="161" y="748.5" width="150" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-165-754-142-26-0)" font-size="12px"><text x="166.5" y="766">Centered resize</text></g><rect x="161" y="975" width="176" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-165-980-168-26-0)" font-size="12px"><text x="166.5" y="992.5">Clear waypoints / Edit tooltip</text></g><rect x="161" y="1007" width="150" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-165-1012-142-26-0)" font-size="12px"><text x="166.5" y="1024.5">Autosize</text></g><rect x="161" y="1039" width="156" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-165-1044-148-26-0)" font-size="12px"><text x="166.5" y="1056.5">Clone / Swap / Constrain</text></g><rect x="481" y="12.25" width="456" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#777777" font-family="Helvetica" clip-path="url(#mx-clip-485-17-448-26-0)" font-size="18px"><text x="486.5" y="35.75">Cmd instead of Ctrl, Option instead of Alt for macOS </text></g><rect x="1101" y="748" width="117" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-1105-753-109-26-0)" font-size="12px"><text x="1106.5" y="765.5">Alt+C</text></g><rect x="1101" y="780.5" width="116" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" font-weight="bold" clip-path="url(#mx-clip-1105-786-108-26-0)" font-size="12px"><text x="1106.5" y="798">Alt+V</text></g><rect x="1211" y="748" width="154" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-1215-753-146-26-0)" font-size="12px"><text x="1216.5" y="765.5">Copy style</text></g><rect x="1211" y="780.5" width="146" height="26" fill="none" stroke="none" pointer-events="all"/><g fill="#333333" font-family="Helvetica" clip-path="url(#mx-clip-1215-786-138-26-0)" font-size="12px"><text x="1216.5" y="798">Paste style</text></g><image x="386.49" y="955.5" width="26" height="18" xlink:href="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB3aWR0aD0iMjZweCIgaGVpZ2h0PSIxOHB4IiB2ZXJzaW9uPSIxLjEiIHZpZXdCb3g9IjAgMCAyNiAxOCI+PHBhdGggZD0ibSAxIDYgTCAxNCA2IEwgMTQgMSBMIDI2IDkgTCAxNCAxOCBMIDE0IDEyIEwgMSAxMiB6IiBzdHJva2U9IiNmZmYiIGZpbGw9IiMyOWI2ZjIiLz48L3N2Zz4=" preserveAspectRatio="none"/><path d="M 42 44.62 C 42 45.18 41.4 45.69 40.97 45.69 L 7.93 45.69 C 7.47 45.69 7 45.18 7 44.76 L 7 11.9 C 7 11.28 7.33 10.75 8.02 10.75 L 41.17 10.78 C 41.67 10.78 41.99 11.23 42 11.82 Z" fill="#f08707" stroke="none" pointer-events="all"/><path d="M 19.94 45.69 L 14.54 37.6 L 27.06 18.97 L 28.5 18.71 L 42 32.48 L 42 44.66 C 42 45.1 41.44 45.75 41 45.69 Z" fill="#db6112" stroke="none" pointer-events="all"/><path d="M 15.13 37.69 C 14.28 37.69 13.88 36.99 13.88 36.42 L 13.88 31.83 C 13.88 31.28 14.3 30.6 14.89 30.6 L 18.5 30.6 L 21.72 25.27 L 21.29 25.27 C 21.04 25.27 20.27 24.77 20.27 24.24 L 20.27 19.34 C 20.27 18.79 20.96 18.09 21.48 18.09 L 27.31 18.09 C 27.84 18.09 28.7 18.39 28.7 19.39 L 28.7 24.1 C 28.7 24.65 28.21 25.27 27.7 25.27 L 27.28 25.27 L 30.51 30.6 L 33.95 30.6 C 34.76 30.6 35.16 31.05 35.16 31.71 L 35.16 36.42 C 35.16 37.14 34.54 37.69 33.93 37.69 L 28.14 37.69 C 27.4 37.69 26.76 37.21 26.76 36.48 L 26.76 31.53 C 26.84 31.03 27.31 30.6 27.87 30.6 L 28.96 30.6 L 25.86 25.27 L 23.19 25.27 L 20.1 30.6 L 21.29 30.6 C 21.81 30.6 22.26 31.1 22.26 31.69 L 22.26 36.71 C 22.26 37.12 21.78 37.69 21.23 37.69 Z" fill="#ffffff" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 28px; margin-left: 48px;"><div data-drawio-colors="color: #1A5BA3; " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 15px; font-family: Helvetica; color: rgb(26, 91, 163); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: nowrap;"><font style="font-size: 20px">draw.<font style="font-size: 20px" color="#f08707">io</font></font></div></div></div></foreignObject><text x="48" y="33" fill="#1A5BA3" font-family="Helvetica" font-size="15px" font-weight="bold">draw....</text></switch></g></g><switch><g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/><a transform="translate(0,-5)" xlink:href="https://www.drawio.com/doc/faq/svg-export-text-problems" target="_blank"><text text-anchor="middle" font-size="10px" x="50%" y="100%">Text is not SVG - cannot display</text></a></switch></svg>