{"name": "rollup", "version": "2.56.3", "description": "Next-generation ES module bundler", "main": "dist/rollup.js", "module": "dist/es/rollup.js", "typings": "dist/rollup.d.ts", "bin": {"rollup": "dist/bin/rollup"}, "scripts": {"build": "shx rm -rf dist && git rev-parse HEAD > .commithash && rollup --config rollup.config.ts --configPlugin typescript && shx cp src/rollup/types.d.ts dist/rollup.d.ts && shx chmod a+x dist/bin/rollup", "build:cjs": "shx rm -rf dist && rollup --config rollup.config.ts --configPlugin typescript --configTest && shx cp src/rollup/types.d.ts dist/rollup.d.ts && shx chmod a+x dist/bin/rollup", "build:bootstrap": "node dist/bin/rollup --config rollup.config.ts --configPlugin typescript && shx cp src/rollup/types.d.ts dist/rollup.d.ts && shx chmod a+x dist/bin/rollup", "ci:lint": "npm run lint:nofix", "ci:test": "npm run build:cjs && npm run build:bootstrap && npm run test:all", "ci:test:only": "npm run build:cjs && npm run build:bootstrap && npm run test:only", "ci:coverage": "npm run build:cjs && npm run build:bootstrap && nyc --reporter l<PERSON><PERSON><PERSON> mocha", "lint": "eslint . --fix --cache && prettier --write \"**/*.md\"", "lint:nofix": "eslint . && prettier --check \"**/*.md\"", "lint:markdown": "prettier --write \"**/*.md\"", "perf": "npm run build:cjs && node --expose-gc scripts/perf.js", "perf:debug": "node --inspect-brk scripts/perf-debug.js", "perf:init": "node scripts/perf-init.js", "_postinstall": "husky install", "postpublish": "pinst --enable && git push && git push --tags", "prepare": "husky install && npm run build", "prepublishOnly": "pinst --disable && npm ci && npm run lint:nofix && npm run security && npm run build:bootstrap && npm run test:all", "security": "npm audit", "test": "npm run build && npm run test:all", "test:cjs": "npm run build:cjs && npm run test:only", "test:quick": "mocha -b test/test.js", "test:all": "npm run test:only && npm run test:browser && npm run test:typescript && npm run test:leak && npm run test:package", "test:coverage": "npm run build:cjs && shx rm -rf coverage/* && nyc --reporter html mocha test/test.js", "test:coverage:browser": "npm run build && shx rm -rf coverage/* && nyc mocha test/browser/index.js", "test:leak": "node --expose-gc test/leak/index.js", "test:package": "node scripts/test-package.js", "test:only": "mocha test/test.js", "test:typescript": "shx rm -rf test/typescript/dist && shx cp -r dist test/typescript/ && tsc --noEmit -p test/typescript && tsc --noEmit", "test:browser": "mocha test/browser/index.js", "watch": "rollup --config rollup.config.ts --configPlugin typescript --watch"}, "repository": "rollup/rollup", "keywords": ["modules", "bundler", "bundling", "es6", "optimizer"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "homepage": "https://rollupjs.org/", "optionalDependencies": {"fsevents": "~2.3.2"}, "devDependencies": {"@rollup/plugin-alias": "^3.1.2", "@rollup/plugin-buble": "^0.21.3", "@rollup/plugin-commonjs": "19.0.0", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-node-resolve": "^13.0.0", "@rollup/plugin-replace": "^2.4.2", "@types/micromatch": "^4.0.1", "@types/node": "^10.17.60", "@types/require-relative": "^0.8.0", "@types/signal-exit": "^3.0.0", "@types/yargs-parser": "^20.2.0", "@typescript-eslint/eslint-plugin": "^4.27.0", "@typescript-eslint/parser": "^4.27.0", "acorn": "^8.4.0", "acorn-jsx": "^5.3.1", "acorn-walk": "^8.1.0", "buble": "^0.20.0", "chokidar": "^3.5.2", "colorette": "^1.2.2", "core-js": "^3.14.0", "date-time": "^4.0.0", "es5-shim": "^4.5.15", "es6-shim": "^0.35.6", "eslint": "^7.28.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-import": "^2.23.4", "eslint-plugin-prettier": "^3.4.0", "execa": "^5.1.1", "fixturify": "^2.1.1", "hash.js": "^1.1.7", "husky": "^6.0.0", "is-reference": "^3.0.0", "lint-staged": "^10.5.4", "locate-character": "^2.0.5", "magic-string": "^0.25.7", "micromatch": "^4.0.4", "mocha": "^8.4.0", "nyc": "^15.1.0", "pinst": "^2.1.6", "prettier": "^2.3.1", "pretty-bytes": "^5.6.0", "pretty-ms": "^7.0.1", "require-relative": "^0.8.7", "requirejs": "^2.3.6", "rollup": "^2.52.0", "rollup-plugin-license": "^2.5.0", "rollup-plugin-string": "^3.0.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-thatworks": "^1.0.4", "rollup-plugin-typescript": "^1.0.1", "rollup-pluginutils": "^2.8.2", "sander": "^0.6.0", "shx": "^0.3.3", "signal-exit": "^3.0.3", "source-map": "^0.7.3", "source-map-support": "^0.5.19", "sourcemap-codec": "^1.4.8", "systemjs": "^6.10.1", "terser": "^5.7.0", "tslib": "^2.3.0", "typescript": "^4.3.2", "weak-napi": "^2.0.2", "yargs-parser": "^20.2.7"}, "files": ["dist/**/*.js", "dist/*.d.ts", "dist/bin/rollup", "dist/rollup.browser.js.map"], "engines": {"node": ">=10.0.0"}, "exports": {".": {"node": {"require": "./dist/rollup.js", "import": "./dist/es/rollup.js"}, "default": "./dist/es/rollup.browser.js"}, "./dist/": "./dist/"}}