<shapes name="mxgraph.cisco.servers">
<shape name="Cisco Unified Presence Server" h="42.33" w="34.34" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
<constraint x="0.13" y="0.04" perimeter="0" name="NW"/>
<constraint x="0" y="1" perimeter="0" name="SW"/>
<constraint x="0.81" y="0" perimeter="0" name="NE"/>
<constraint x="0.815" y="0.875" perimeter="0" name="SE"/>
</connections>
<background>
<save/>
<path>
<move x="21.34" y="42.33"/>
<line x="21.34" y="4.67"/>
<line x="0" y="4.67"/>
<line x="0" y="42.33"/>
<close/>
<move x="0" y="4.67"/>
<line x="7.34" y="0"/>
<line x="28" y="0"/>
<line x="21.34" y="4.67"/>
<close/>
<move x="21.34" y="42.33"/>
<line x="28" y="37"/>
<line x="28" y="0"/>
<line x="21.34" y="4.67"/>
<close/>
</path>
</background>
<foreground>
<linejoin join="round"/>
<fillstroke/>
<path>
<move x="0" y="39.67"/>
<line x="21.34" y="39.67"/>
</path>
<stroke/>
<strokecolor color="#ffffff"/>
<path>
<move x="20.34" y="33.67"/>
<curve x1="20.34" y1="36" x2="18.67" y2="37.67" x3="16.34" y3="37.67"/>
<curve x1="14" y1="37.67" x2="12" y2="36" x3="12" y3="33.67"/>
<curve x1="12" y1="31.33" x2="14" y2="29.33" x3="16.34" y3="29.33"/>
<curve x1="18.67" y1="29.33" x2="20.34" y2="31.33" x3="20.34" y3="33.67"/>
<close/>
</path>
<stroke/>
<fillcolor color="#ffffff"/>
<path>
<move x="17.34" y="33.67"/>
<curve x1="17.34" y1="34.33" x2="17" y2="34.67" x3="16.34" y3="34.67"/>
<curve x1="15.67" y1="34.67" x2="15" y2="34.33" x3="15" y3="33.67"/>
<curve x1="15" y1="33" x2="15.67" y2="32.33" x3="16.34" y3="32.33"/>
<curve x1="17" y1="32.33" x2="17.34" y2="33" x3="17.34" y3="33.67"/>
<close/>
</path>
<fillstroke/>
<save/>
<path>
<move x="9.34" y="33.67"/>
<curve x1="9.34" y1="36" x2="7.34" y2="37.67" x3="5" y3="37.67"/>
<curve x1="3" y1="37.67" x2="1" y2="36" x3="1" y3="33.67"/>
<curve x1="1" y1="31.33" x2="3" y2="29.33" x3="5" y3="29.33"/>
<curve x1="7.34" y1="29.33" x2="9.34" y2="31.33" x3="9.34" y3="33.67"/>
<close/>
</path>
<stroke/>
<linecap cap="round"/>
<strokewidth width="1"/>
<path>
<move x="3.34" y="35.33"/>
<line x="6.67" y="32"/>
<move x="3.34" y="32"/>
<line x="6.67" y="35.33"/>
</path>
<stroke/>
<restore/>
<linecap cap="butt"/>
<path>
<move x="14.67" y="24.67"/>
<curve x1="14.67" y1="26.67" x2="13" y2="28.67" x3="10.67" y3="28.67"/>
<curve x1="8.34" y1="28.67" x2="6.34" y2="26.67" x3="6.34" y3="24.67"/>
<curve x1="6.34" y1="22.33" x2="8.34" y2="20.33" x3="10.67" y3="20.33"/>
<curve x1="13" y1="20.33" x2="14.67" y2="22.33" x3="14.67" y3="24.67"/>
<close/>
</path>
<stroke/>
<strokewidth width="1"/>
<linecap cap="round"/>
<path>
<move x="9.67" y="26.33"/>
<line x="8.34" y="25"/>
<move x="13" y="23.33"/>
<line x="9.67" y="26.33"/>
</path>
<stroke/>
<restore/>
<rect/>
<stroke/>
<path>
<move x="0" y="19"/>
<line x="21.34" y="19"/>
</path>
<stroke/>
<path>
<move x="11.67" y="11.33"/>
<line x="11.67" y="7.33"/>
<line x="1.67" y="7.33"/>
<line x="1.67" y="11.33"/>
<close/>
</path>
<fillstroke/>
<path>
<move x="2.67" y="9.67"/>
<line x="10.67" y="9.67"/>
</path>
<stroke/>
<strokewidth width="0.67"/>
<strokecolor color="#000000"/>
<fillcolor color="#ffffff"/>
<save/>
<linejoin join="round"/>
<path>
<move x="28" y="17.33"/>
<curve x1="28.67" y1="17.33" x2="28.67" y2="17.33" x3="28.67" y3="17.33"/>
<curve x1="33.34" y1="17.33" x2="34.34" y2="21.67" x3="34.34" y3="21.67"/>
<curve x1="34.34" y1="17" x2="34.34" y2="17" x3="34.34" y3="17"/>
<curve x1="34.34" y1="17" x2="33.34" y2="12.67" x3="28.67" y3="12.67"/>
<curve x1="28" y1="12.67" x2="28" y2="12.67" x3="28" y3="12.67"/>
</path>
<fillstroke/>
<restore/>
<path>
<move x="34.34" y="16.67"/>
<curve x1="34.34" y1="22" x2="34.34" y2="22" x3="34.34" y3="22"/>
<curve x1="33.67" y1="28.33" x2="22.34" y2="28" x3="22.34" y3="28"/>
<curve x1="22.34" y1="28.33" x2="22.34" y2="28.33" x3="22.34" y3="28.33"/>
<curve x1="22.34" y1="32.67" x2="22.34" y2="32.67" x3="22.34" y3="32.67"/>
<curve x1="15" y1="25.33" x2="15" y2="25.33" x3="15" y3="25.33"/>
<curve x1="22.34" y1="18" x2="22.34" y2="18" x3="22.34" y3="18"/>
<curve x1="22.34" y1="22.67" x2="22.34" y2="22.67" x3="22.34" y3="22.67"/>
<curve x1="22.34" y1="22.67" x2="34" y2="23" x3="34.34" y3="16.67"/>
</path>
<fillstroke/>
</foreground>
</shape>
<shape name="Communications Server" h="35.33" w="34.66" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
<constraint x="0.12" y="0.07" perimeter="0" name="NW"/>
<constraint x="0" y="1" perimeter="0" name="SW"/>
<constraint x="1" y="0" perimeter="0" name="NE"/>
<constraint x="0.92" y="0.91" perimeter="0" name="SE"/>
</connections>
<background>
<path>
<move x="28.66" y="35.33"/>
<line x="28.66" y="6"/>
<line x="0" y="6"/>
<line x="0" y="35.33"/>
<close/>
<move x="28.66" y="6"/>
<line x="0" y="6"/>
<line x="7.33" y="0"/>
<line x="34.66" y="0"/>
<line x="34.66" y="0.33"/>
<line x="34.66" y="29.33"/>
<line x="28.66" y="35.33"/>
<line x="28.66" y="6"/>
<close/>
<move x="28.66" y="6"/>
<line x="34.66" y="0"/>
</path>
</background>
<foreground>
<linejoin join="round"/>
<fillstroke/>
<strokecolor color="#ffffff"/>
<strokewidth width="2"/>
<path>
<move x="14.33" y="25.67"/>
<curve x1="17" y1="25.67" x2="19.33" y2="23.33" x3="19.33" y3="20.67"/>
<curve x1="19.33" y1="17.67" x2="17" y2="15.33" x3="14.33" y3="15.33"/>
<curve x1="11.66" y1="15.33" x2="9.33" y2="17.67" x3="9.33" y3="20.67"/>
<curve x1="9.33" y1="23.33" x2="11.66" y2="25.67" x3="14.33" y3="25.67"/>
<close/>
</path>
<stroke/>
<path>
<move x="14.33" y="15.33"/>
<line x="23.33" y="15.33"/>
</path>
<stroke/>
<strokecolor color="#ffffff"/>
<fillcolor color="#ffffff"/>
<strokewidth width="1"/>
<fillcolor color="#ffffff"/>
<path>
<move x="23.33" y="12"/>
<line x="23.33" y="18.67"/>
<line x="27" y="15.33"/>
<close/>
<move x="6" y="11.67"/>
<line x="12.33" y="11.67"/>
<line x="9.33" y="7.67"/>
<close/>
<move x="22.66" y="29.33"/>
<line x="16.33" y="29.33"/>
<line x="19.33" y="33.33"/>
<close/>
<move x="5.33" y="29"/>
<line x="5.33" y="22.67"/>
<line x="1.66" y="25.67"/>
<close/>
</path>
<fill/>
<strokewidth width="2"/>
<path>
<move x="9.33" y="20.67"/>
<line x="9.33" y="11.33"/>
<move x="19.33" y="20.67"/>
<line x="19.33" y="29.33"/>
<move x="14.33" y="25.67"/>
<line x="5" y="25.67"/>
</path>
<stroke/>
</foreground>
</shape>
<shape name="Directory Server" h="37.34" w="45" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.54" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="0.99" perimeter="0" name="S"/>
<constraint x="0.23" y="0.5" perimeter="0" name="W"/>
<constraint x="0.795" y="0.5" perimeter="0" name="E"/>
<constraint x="0" y="0.99" perimeter="0" name="SW"/>
<constraint x="1" y="0.9" perimeter="0" name="SE"/>
</connections>
<background>
<save/>
<path>
<move x="40" y="37.34"/>
<line x="45" y="33.67"/>
<line x="24.33" y="0"/>
<line x="19" y="3"/>
<close/>
<move x="0" y="37.34"/>
<line x="40" y="37.34"/>
<line x="19" y="3"/>
<line x="0" y="37"/>
<close/>
</path>
</background>
<foreground>
<linejoin join="round"/>
<fillstroke/>
<restore/>
<path>
</path>
<fillstroke/>
<fillcolor color="#ffffff"/>
<path>
<move x="19.33" y="26.67"/>
<curve x1="20.67" y1="26.67" x2="22" y2="25.34" x3="22" y3="23.67"/>
<curve x1="22" y1="22.34" x2="20.67" y2="21" x3="19.33" y3="21"/>
<curve x1="17.67" y1="21" x2="16.33" y2="22.34" x3="16.33" y3="23.67"/>
<curve x1="16.33" y1="25.34" x2="17.67" y2="26.67" x3="19.33" y3="26.67"/>
<close/>
</path>
<fill/>
<path>
<move x="19.33" y="16"/>
<curve x1="20.67" y1="16" x2="22" y2="14.67" x3="22" y3="13.34"/>
<curve x1="22" y1="11.67" x2="20.67" y2="10.34" x3="19.33" y3="10.34"/>
<curve x1="17.67" y1="10.34" x2="16.33" y2="11.67" x3="16.33" y3="13.34"/>
<curve x1="16.33" y1="14.67" x2="17.67" y2="16" x3="19.33" y3="16"/>
<close/>
</path>
<fill/>
<path>
<move x="27" y="34.34"/>
<curve x1="28.67" y1="34.34" x2="30" y2="33.34" x3="30" y3="31.67"/>
<curve x1="30" y1="30" x2="28.67" y2="28.67" x3="27" y3="28.67"/>
<curve x1="25.33" y1="28.67" x2="24" y2="30" x3="24" y3="31.67"/>
<curve x1="24" y1="33.34" x2="25.33" y2="34.34" x3="27" y3="34.34"/>
<close/>
</path>
<fill/>
<path>
<move x="11.33" y="34.34"/>
<curve x1="13" y1="34.34" x2="14.33" y2="33" x3="14.33" y3="31.67"/>
<curve x1="14.33" y1="30" x2="13" y2="28.67" x3="11.33" y3="28.67"/>
<curve x1="10" y1="28.67" x2="8.67" y2="30" x3="8.67" y3="31.67"/>
<curve x1="8.67" y1="33" x2="10" y2="34.34" x3="11.33" y3="34.34"/>
<close/>
</path>
<fill/>
<strokecolor color="#ffffff"/>
<strokewidth width="1.33"/>
<path>
<move x="19" y="13.67"/>
<line x="19" y="23.67"/>
<line x="11.33" y="31.67"/>
<move x="19" y="24"/>
<line x="26.33" y="31.34"/>
</path>
<stroke/>
</foreground>
</shape>
<shape name="Fileserver" h="39.33" w="27" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
<constraint x="0.13" y="0.08" perimeter="0" name="NW"/>
<constraint x="0" y="1" perimeter="0" name="SW"/>
<constraint x="1" y="0" perimeter="0" name="NE"/>
<constraint x="0.88" y="0.92" perimeter="0" name="SE"/>
</connections>
<background>
<save/>
<path>
<move x="20.33" y="6.66"/>
<line x="0" y="6.66"/>
<line x="0" y="39.33"/>
<line x="20.33" y="39.33"/>
<close/>
<move x="20.33" y="39.33"/>
<line x="27" y="33"/>
<line x="27" y="0"/>
<line x="6.66" y="0"/>
<line x="0" y="6.66"/>
<line x="20.33" y="6.66"/>
<line x="20.33" y="39.33"/>
<close/>
</path>
</background>
<foreground>
<linejoin join="round"/>
<fillstroke/>
<restore/>
<rect/>
<stroke/>
<path>
<move x="20.33" y="6.66"/>
<line x="27" y="0"/>
<move x="0" y="19.33"/>
<line x="20.33" y="19.33"/>
<move x="0" y="34.33"/>
<line x="20.33" y="34.33"/>
<move x="5" y="11.66"/>
<line x="10.66" y="11.66"/>
<move x="13" y="13.66"/>
<line x="13" y="9.66"/>
<line x="2.66" y="9.66"/>
<line x="2.66" y="13.66"/>
<line x="13" y="13.66"/>
<close/>
</path>
<stroke/>
</foreground>
</shape>
<shape name="File Server" h="36" w="27.33" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
<constraint x="0.14" y="0.08" perimeter="0" name="NW"/>
<constraint x="0" y="1" perimeter="0" name="SW"/>
<constraint x="1" y="0" perimeter="0" name="NE"/>
<constraint x="0.89" y="0.9" perimeter="0" name="SE"/>
</connections>
<background>
<save/>
<path>
<move x="20.67" y="6.67"/>
<line x="0" y="6.67"/>
<line x="0" y="36"/>
<line x="20.67" y="36"/>
<close/>
<move x="20.67" y="36"/>
<line x="27.33" y="29.67"/>
<line x="27.33" y="0"/>
<line x="6.67" y="0"/>
<line x="0" y="6.67"/>
<line x="20.67" y="6.67"/>
<line x="20.67" y="36"/>
<close/>
</path>
</background>
<foreground>
<linejoin join="round"/>
<fillstroke/>
<restore/>
<rect/>
<stroke/>
<path>
<move x="20.67" y="6.67"/>
<line x="27.33" y="0"/>
<move x="0" y="14.67"/>
<line x="20.67" y="14.67"/>
<move x="0" y="24"/>
<line x="20.67" y="24"/>
<move x="7.33" y="20.34"/>
<line x="13" y="20.34"/>
<move x="7.33" y="18.67"/>
<line x="13" y="18.67"/>
<move x="10" y="20.34"/>
<line x="10" y="20.34"/>
<move x="15.33" y="22.34"/>
<line x="15.33" y="16.67"/>
<line x="5" y="16.67"/>
<line x="5" y="22.34"/>
<close/>
</path>
<stroke/>
</foreground>
</shape>
<shape name="Host" h="32.66" w="65.34" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
<constraint x="0.045" y="0.09" perimeter="0" name="NW"/>
<constraint x="0" y="1" perimeter="0" name="SW"/>
<constraint x="1" y="0" perimeter="0" name="NE"/>
<constraint x="0.96" y="0.9" perimeter="0" name="SE"/>
</connections>
<background>
<path>
<move x="65.34" y="26.66"/>
<line x="65.34" y="0"/>
<line x="59.34" y="6"/>
<line x="59.34" y="32.66"/>
<close/>
<move x="0" y="32.66"/>
<line x="0" y="6"/>
<line x="59.34" y="6"/>
<line x="59.34" y="32.66"/>
<close/>
</path>
</background>
<foreground>
<linejoin join="round"/>
<fillstroke/>
<fontcolor color="#ffffff"/>
<fontsize size="9"/>
<text str="X.25 Host" x="29" y="18" valign="middle" align="center"/>
<strokewidth width="0.67"/>
<path>
<move x="6" y="0"/>
<line x="0" y="6"/>
<line x="59.34" y="6"/>
<line x="65.34" y="0"/>
<line x="6" y="0"/>
<close/>
</path>
<fillstroke/>
</foreground>
</shape>
<shape name="IPTV Server" h="32" w="48.67" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0.01" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
<constraint x="0.1" y="0.14" perimeter="0" name="NW"/>
<constraint x="0" y="1" perimeter="0" name="SW"/>
<constraint x="1" y="0.01" perimeter="0" name="NE"/>
<constraint x="0.92" y="0.85" perimeter="0" name="SE"/>
</connections>
<background>
<save/>
<path>
<move x="0" y="32"/>
<line x="0" y="9"/>
<line x="40.33" y="9"/>
<line x="40.33" y="32"/>
<close/>
<move x="40.33" y="32"/>
<line x="40.33" y="9"/>
<line x="48.67" y="0"/>
<line x="48.67" y="22.67"/>
<close/>
<move x="40.33" y="9"/>
<line x="0" y="9"/>
<line x="10" y="0"/>
<line x="48.67" y="0"/>
<close/>
</path>
</background>
<foreground>
<linejoin join="round"/>
<fillstroke/>
<restore/>
<rect/>
<stroke/>
<fillcolor color="#ffffff"/>
<path>
<move x="8.33" y="17.67"/>
<line x="1" y="17.67"/>
<line x="1" y="16"/>
<line x="8.33" y="16"/>
<line x="8.33" y="14"/>
<line x="12.67" y="16.67"/>
<line x="8.33" y="19.67"/>
<close/>
<move x="34.67" y="14"/>
<line x="28.67" y="14"/>
<line x="28.67" y="12.67"/>
<line x="34.67" y="12.67"/>
<line x="34.67" y="11"/>
<line x="38.33" y="13.34"/>
<line x="34.67" y="15.67"/>
<close/>
<move x="34.67" y="21"/>
<line x="28.67" y="21"/>
<line x="28.67" y="20"/>
<line x="34.67" y="20"/>
<line x="34.67" y="18"/>
<line x="38.33" y="20.67"/>
<line x="34.67" y="23"/>
<close/>
<move x="34.67" y="28.34"/>
<line x="28.67" y="28.34"/>
<line x="28.67" y="27"/>
<line x="34.67" y="27"/>
<line x="34.67" y="25.34"/>
<line x="38.33" y="27.67"/>
<line x="34.67" y="30"/>
<close/>
<move x="24" y="13"/>
<line x="24" y="21.67"/>
<line x="13.67" y="21.67"/>
<line x="13.67" y="13"/>
<close/>
</path>
<fill/>
<strokecolor color="#000000"/>
<strokewidth width="0.4"/>
<path>
<move x="26.67" y="14.34"/>
<line x="26.67" y="18"/>
<line x="24.67" y="18"/>
<line x="24.67" y="14.34"/>
<close/>
</path>
<fillstroke/>
<strokecolor color="#ffffff"/>
<strokewidth width="1.33"/>
<linecap cap="round"/>
<path>
<move x="15" y="27.34"/>
<line x="18.67" y="17.34"/>
<line x="22.67" y="27.34"/>
</path>
<stroke/>
<strokewidth width="2"/>
<path>
<move x="21.67" y="16.34"/>
<line x="24.67" y="16.34"/>
</path>
<stroke/>
</foreground>
</shape>
<shape name="Microwebserver" h="32.67" w="48.67" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
<constraint x="0.12" y="0.1" perimeter="0" name="NW"/>
<constraint x="0.12" y="0.9" perimeter="0" name="SW"/>
<constraint x="0.88" y="0.1" perimeter="0" name="NE"/>
<constraint x="0.88" y="0.9" perimeter="0" name="SE"/>
</connections>
<background>
<save/>
<path>
<move x="48.67" y="9.34"/>
<curve x1="48.67" y1="14.67" x2="37.67" y2="18.67" x3="24.33" y3="18.67"/>
<curve x1="11" y1="18.67" x2="0" y2="14.67" x3="0" y3="9.34"/>
<curve x1="0" y1="23.34" x2="0" y2="23.34" x3="0" y3="23.34"/>
<curve x1="0" y1="28.34" x2="11" y2="32.67" x3="24.33" y3="32.67"/>
<curve x1="37.67" y1="32.67" x2="48.67" y2="28.34" x3="48.67" y3="23.34"/>
<close/>
<move x="24.33" y="18.67"/>
<curve x1="37.67" y1="18.67" x2="48.67" y2="14.67" x3="48.67" y3="9.34"/>
<curve x1="48.67" y1="4.34" x2="37.67" y2="0" x3="24.33" y3="0"/>
<curve x1="11" y1="0" x2="0" y2="4.34" x3="0" y3="9.34"/>
<curve x1="0" y1="14.67" x2="11" y2="18.67" x3="24.33" y3="18.67"/>
<close/>
</path>
</background>
<foreground>
<linejoin join="round"/>
<fillstroke/>
<restore/>
<rect/>
<stroke/>
<strokewidth width="1"/>
<strokecolor color="#ffffff"/>
<path>
<move x="35.33" y="11.5"/>
<line x="40.67" y="14"/>
<line x="35.33" y="16.67"/>
<line x="35.33" y="15"/>
<line x="9" y="15"/>
<line x="9" y="13"/>
<line x="35.33" y="13"/>
<close/>
</path>
<stroke/>
<fillcolor color="#ffffff"/>
<path>
<move x="14.33" y="6.67"/>
<curve x1="15.33" y1="6.67" x2="15.33" y2="6.67" x3="15.33" y3="6.67"/>
<curve x1="16.33" y1="9.67" x2="16.33" y2="10" x3="16.33" y3="10.67"/>
<curve x1="16.33" y1="10.67" x2="16.33" y2="10.67" x3="16.33" y3="10.67"/>
<curve x1="16.33" y1="10.34" x2="16.67" y2="9.67" x3="17" y3="6.67"/>
<curve x1="18.33" y1="6.67" x2="18.33" y2="6.67" x3="18.33" y3="6.67"/>
<curve x1="19" y1="9.67" x2="19" y2="10" x3="19" y3="10.67"/>
<curve x1="19" y1="10.67" x2="19" y2="10.67" x3="19" y3="10.67"/>
<curve x1="19.33" y1="10.34" x2="19.33" y2="9.67" x3="20" y3="6.67"/>
<curve x1="21" y1="6.67" x2="21" y2="6.67" x3="21" y3="6.67"/>
<curve x1="19.67" y1="12.34" x2="19.67" y2="12.34" x3="19.67" y3="12.34"/>
<curve x1="18.67" y1="12.34" x2="18.67" y2="12.34" x3="18.67" y3="12.34"/>
<curve x1="18" y1="9.67" x2="18" y2="9" x3="17.67" y3="8"/>
<curve x1="17.67" y1="8" x2="17.67" y2="8" x3="17.67" y3="8"/>
<curve x1="17.67" y1="9" x2="17.33" y2="9.34" x3="17" y3="12.34"/>
<curve x1="15.67" y1="12.34" x2="15.67" y2="12.34" x3="15.67" y3="12.34"/>
<close/>
</path>
<fill/>
<path>
<move x="21.33" y="6.67"/>
<curve x1="22.67" y1="6.67" x2="22.67" y2="6.67" x3="22.67" y3="6.67"/>
<curve x1="23.33" y1="9.67" x2="23.33" y2="10" x3="23.33" y3="10.67"/>
<curve x1="23.33" y1="10.67" x2="23.33" y2="10.67" x3="23.33" y3="10.67"/>
<curve x1="23.67" y1="10.34" x2="23.67" y2="9.67" x3="24.33" y3="6.67"/>
<curve x1="25.33" y1="6.67" x2="25.33" y2="6.67" x3="25.33" y3="6.67"/>
<curve x1="26" y1="9.67" x2="26" y2="10" x3="26.33" y3="10.67"/>
<curve x1="26.33" y1="10.67" x2="26.33" y2="10.67" x3="26.33" y3="10.67"/>
<curve x1="26.33" y1="10.34" x2="26.33" y2="9.67" x3="27" y3="6.67"/>
<curve x1="28.33" y1="6.67" x2="28.33" y2="6.67" x3="28.33" y3="6.67"/>
<curve x1="26.67" y1="12.34" x2="26.67" y2="12.34" x3="26.67" y3="12.34"/>
<curve x1="25.67" y1="12.34" x2="25.67" y2="12.34" x3="25.67" y3="12.34"/>
<curve x1="25" y1="9.67" x2="25" y2="9" x3="24.67" y3="8"/>
<curve x1="24.67" y1="8" x2="24.67" y2="8" x3="24.67" y3="8"/>
<curve x1="24.67" y1="9" x2="24.67" y2="9.34" x3="24" y3="12.34"/>
<curve x1="23" y1="12.34" x2="23" y2="12.34" x3="23" y3="12.34"/>
<close/>
</path>
<fill/>
<path>
<move x="28.33" y="6.67"/>
<curve x1="29.67" y1="6.67" x2="29.67" y2="6.67" x3="29.67" y3="6.67"/>
<curve x1="30.33" y1="9.67" x2="30.33" y2="10" x3="30.67" y3="10.67"/>
<curve x1="30.67" y1="10.67" x2="30.67" y2="10.67" x3="30.67" y3="10.67"/>
<curve x1="30.67" y1="10.34" x2="30.67" y2="9.67" x3="31.33" y3="6.67"/>
<curve x1="32.33" y1="6.67" x2="32.33" y2="6.67" x3="32.33" y3="6.67"/>
<curve x1="33" y1="9.67" x2="33" y2="10" x3="33.33" y3="10.67"/>
<curve x1="33.33" y1="10.67" x2="33.33" y2="10.67" x3="33.33" y3="10.67"/>
<curve x1="33.33" y1="10.34" x2="33.33" y2="9.67" x3="34.33" y3="6.67"/>
<curve x1="35.33" y1="6.67" x2="35.33" y2="6.67" x3="35.33" y3="6.67"/>
<curve x1="33.67" y1="12.34" x2="33.67" y2="12.34" x3="33.67" y3="12.34"/>
<curve x1="32.67" y1="12.34" x2="32.67" y2="12.34" x3="32.67" y3="12.34"/>
<curve x1="32" y1="9.67" x2="32" y2="9" x3="32" y3="8"/>
<curve x1="32" y1="8" x2="32" y2="8" x3="32" y3="8"/>
<curve x1="31.67" y1="9" x2="31.67" y2="9.34" x3="31" y3="12.34"/>
<curve x1="30" y1="12.34" x2="30" y2="12.34" x3="30" y3="12.34"/>
<close/>
</path>
<fill/>
</foreground>
</shape>
<shape name="MOH Server" h="39.33" w="27.33" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
<constraint x="0.12" y="0.085" perimeter="0" name="NW"/>
<constraint x="0" y="1" perimeter="0" name="SW"/>
<constraint x="1" y="0" perimeter="0" name="NE"/>
<constraint x="0.88" y="0.915" perimeter="0" name="SE"/>
</connections>
<background>
<save/>
<path>
<move x="20.66" y="6.66"/>
<line x="0" y="6.66"/>
<line x="0" y="39.33"/>
<line x="20.66" y="39.33"/>
<move x="27.33" y="0"/>
<line x="6.66" y="0"/>
<line x="0" y="6.66"/>
<line x="20.66" y="6.66"/>
<line x="27.33" y="0"/>
<close/>
<move x="20.66" y="6.66"/>
<line x="20.66" y="39.33"/>
<line x="27.33" y="32.66"/>
<line x="27.33" y="0"/>
<line x="20.66" y="6.66"/>
<close/>
<move x="20.66" y="6.66"/>
<line x="27.33" y="0"/>
</path>
<fillstroke/>
</background>
<foreground>
<linejoin join="round"/>
<fillstroke/>
<restore/>
<rect/>
<stroke/>
<path>
<move x="20.66" y="6.66"/>
<line x="27.33" y="0"/>
<move x="0.33" y="19.33"/>
<line x="20.33" y="19.33"/>
<move x="0.33" y="34.33"/>
<line x="20.33" y="34.33"/>
<move x="5" y="11.66"/>
<line x="11" y="11.66"/>
<move x="13" y="13.66"/>
<line x="13" y="9.66"/>
<line x="2.66" y="9.66"/>
<line x="2.66" y="13.66"/>
<line x="13" y="13.66"/>
<close/>
</path>
<stroke/>
<fillcolor color="#ffffff"/>
<path>
<move x="7" y="33.66"/>
<line x="6.66" y="18.33"/>
<line x="17.66" y="14.33"/>
<line x="17.66" y="31.33"/>
<line x="17" y="31.33"/>
<line x="16.33" y="21.66"/>
<line x="7.66" y="24.66"/>
<line x="7.66" y="33.33"/>
<line x="7" y="33.66"/>
<close/>
<move x="17.66" y="31.66"/>
<curve x1="17.66" y1="32.66" x2="16.66" y2="33.66" x3="15" y3="34"/>
<curve x1="13.33" y1="34" x2="12" y2="33" x3="11.66" y3="32"/>
<curve x1="11.66" y1="31" x2="13" y2="30" x3="14.66" y3="30"/>
<curve x1="16.33" y1="30" x2="17.66" y2="30.66" x3="17.66" y3="31.66"/>
<close/>
<move x="7.66" y="34"/>
<curve x1="7.66" y1="35" x2="6.33" y2="36" x3="4.66" y3="36"/>
<curve x1="3" y1="36.33" x2="1.66" y2="35.33" x3="1.66" y3="34.33"/>
<curve x1="1.33" y1="33.33" x2="2.66" y2="32.33" x3="4.33" y3="32.33"/>
<curve x1="6" y1="32.33" x2="7.33" y2="33" x3="7.66" y3="34"/>
<close/>
</path>
<fill/>
<fillcolor color="#808080"/>
<path>
<move x="16.33" y="20"/>
<line x="7.66" y="23"/>
<line x="7.66" y="21.33"/>
<line x="16.33" y="18.33"/>
<close/>
</path>
<fill/>
</foreground>
</shape>
<shape name="Server With Router" h="40.67" w="24.66" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
<constraint x="0.08" y="0.04" perimeter="0" name="NW"/>
<constraint x="0" y="1" perimeter="0" name="SW"/>
<constraint x="1" y="0" perimeter="0" name="NE"/>
<constraint x="0.93" y="0.95" perimeter="0" name="SE"/>
</connections>
<background>
<save/>
<path>
<move x="21.33" y="40.67"/>
<line x="21.33" y="3.34"/>
<line x="0" y="3.34"/>
<line x="0" y="40.67"/>
<close/>
<move x="0" y="3.34"/>
<line x="4" y="0"/>
<line x="24.66" y="0"/>
<line x="21.33" y="3.34"/>
<close/>
<move x="21.33" y="40.67"/>
<line x="24.66" y="36.67"/>
<line x="24.66" y="0"/>
<line x="21.33" y="3.34"/>
<close/>
</path>
</background>
<foreground>
<linejoin join="round"/>
<fillstroke/>
<path>
<move x="0" y="18"/>
<line x="21.33" y="18"/>
</path>
<stroke/>
<path>
<move x="0" y="38.34"/>
<line x="21.33" y="38.34"/>
</path>
<stroke/>
<fillcolor color="#ffffff"/>
<path>
<move x="9" y="30"/>
<line x="9" y="33"/>
<line x="7.66" y="33"/>
<line x="10.33" y="36"/>
<line x="12.66" y="33"/>
<line x="11.66" y="33"/>
<line x="11.66" y="30"/>
<close/>
<move x="9" y="27.34"/>
<line x="9" y="24.34"/>
<line x="7.66" y="24.34"/>
<line x="10.33" y="21.34"/>
<line x="12.66" y="24.34"/>
<line x="11.66" y="24.34"/>
<line x="11.66" y="27.34"/>
<close/>
<move x="2.66" y="27.67"/>
<line x="5.66" y="27.67"/>
<line x="5.66" y="26.34"/>
<line x="9" y="29"/>
<line x="5.66" y="31.34"/>
<line x="5.66" y="30"/>
<line x="2.66" y="30"/>
<close/>
<move x="17.66" y="27.67"/>
<line x="14.66" y="27.67"/>
<line x="14.66" y="26.34"/>
<line x="11.66" y="29"/>
<line x="14.66" y="31.34"/>
<line x="14.66" y="30"/>
<line x="17.66" y="30"/>
<close/>
</path>
<fill/>
<restore/>
<rect/>
<stroke/>
<rect x="1.66" y="6" w="10" h="4"/>
<stroke/>
<path>
<move x="2.66" y="8"/>
<line x="10.66" y="8"/>
</path>
<stroke/>
</foreground>
</shape>
<shape name="SIP Proxy Server" h="44.33" w="28" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
<constraint x="0.13" y="0.07" perimeter="0" name="NW"/>
<constraint x="0" y="1" perimeter="0" name="SW"/>
<constraint x="1" y="0" perimeter="0" name="NE"/>
<constraint x="0.88" y="0.925" perimeter="0" name="SE"/>
</connections>
<background>
<path>
<move x="21.33" y="6.67"/>
<line x="0" y="6.67"/>
<line x="0" y="44.33"/>
<line x="21.33" y="44.33"/>
<close/>
<move x="21.33" y="44.33"/>
<line x="28" y="37.67"/>
<line x="28" y="0"/>
<line x="6.67" y="0"/>
<line x="0" y="6.67"/>
<line x="21.33" y="6.67"/>
<close/>
<move x="21.33" y="6.67"/>
<line x="28" y="0"/>
<move x="0" y="19.33"/>
<line x="21.33" y="19.33"/>
<move x="0" y="39.33"/>
<line x="21.33" y="39.33"/>
</path>
</background>
<foreground>
<linejoin join="round"/>
<fillstroke/>
<path>
<move x="14" y="13.67"/>
<line x="14" y="9.67"/>
<line x="3.67" y="9.67"/>
<line x="3.67" y="13.67"/>
<close/>
</path>
<stroke/>
<fontcolor color="#ffffff"/>
<fontsize size="11"/>
<text str="IP" x="15" y="29" valign="middle" align="center"/>
<strokewidth width="1.67"/>
<strokecolor color="#ffffff"/>
<fillcolor color="#ffffff"/>
<path>
<move x="19" y="23.67"/>
<curve x1="4.67" y1="23.67" x2="4.67" y2="23.67" x3="4.67" y3="23.67"/>
<curve x1="3" y1="23.67" x2="1.67" y2="25" x3="1.67" y3="26.33"/>
<curve x1="1.67" y1="28" x2="3.33" y2="29.33" x3="4.67" y3="29.33"/>
<curve x1="7.33" y1="29.33" x2="7.33" y2="29.33" x3="7.33" y3="29.33"/>
<curve x1="9" y1="29.33" x2="10" y2="30.67" x3="10" y3="32.33"/>
<curve x1="10" y1="34" x2="8.67" y2="35.33" x3="7" y3="35.33"/>
<curve x1="2.67" y1="35.33" x2="2.67" y2="35.33" x3="2.67" y3="35.33"/>
</path>
<stroke/>
<path>
<move x="18.33" y="21.67"/>
<line x="18.33" y="25.33"/>
<line x="20.67" y="23.67"/>
<close/>
<move x="2.67" y="37.33"/>
<line x="2.67" y="33.67"/>
<line x="0.67" y="35.33"/>
<close/>
</path>
<fill/>
</foreground>
</shape>
<shape name="Software Based Server" h="48.66" w="42" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
<constraint x="0.16" y="0.19" perimeter="0" name="NW"/>
<constraint x="0" y="1" perimeter="0" name="SW"/>
<constraint x="1" y="0" perimeter="0" name="NE"/>
</connections>
<background>
<save/>
<save/>
<path>
<move x="20.67" y="16"/>
<line x="0" y="16"/>
<line x="0" y="48.66"/>
<line x="20.67" y="48.66"/>
<close/>
<move x="20.67" y="48.66"/>
<line x="27" y="42"/>
<line x="27" y="9.33"/>
<line x="6.67" y="9.33"/>
<line x="0" y="16"/>
<line x="20.67" y="16"/>
<close/>
</path>
</background>
<foreground>
<linejoin join="round"/>
<fillstroke/>
<restore/>
<rect/>
<stroke/>
<path>
<move x="20.67" y="16"/>
<line x="27" y="9.33"/>
<move x="0" y="28.66"/>
<line x="20.67" y="28.66"/>
<move x="0" y="43.66"/>
<line x="20.67" y="43.66"/>
</path>
<stroke/>
<path>
<move x="13" y="23"/>
<line x="13" y="18.66"/>
<line x="2.67" y="18.66"/>
<line x="2.67" y="23"/>
<close/>
</path>
<stroke/>
<linejoin join="round"/>
<path>
<move x="42" y="0"/>
<line x="41.33" y="0.33"/>
<line x="15.33" y="0.33"/>
<line x="16" y="0"/>
<close/>
<move x="42" y="25.66"/>
<line x="42" y="0"/>
<line x="41.33" y="0.66"/>
<line x="41.33" y="26.33"/>
<close/>
</path>
<fillstroke/>
<path>
<move x="16.33" y="26.33"/>
<line x="41.33" y="26.33"/>
<line x="41.33" y="0.33"/>
<line x="15.33" y="0.33"/>
<line x="15.33" y="25.33"/>
<close/>
</path>
<fillstroke/>
<save/>
<path>
<move x="39.33" y="0.33"/>
<curve x1="39.33" y1="15.33" x2="39.33" y2="15.33" x3="39.33" y3="15.33"/>
<curve x1="39.33" y1="16.66" x2="38.33" y2="16.33" x3="38.33" y3="16.33"/>
<curve x1="18" y1="16.33" x2="18" y2="16.33" x3="18" y3="16.33"/>
<curve x1="18" y1="16.33" x2="17.33" y2="16.33" x3="17.33" y3="15.66"/>
<curve x1="17.33" y1="0.33" x2="17.33" y2="0.33" x3="17.33" y3="0.33"/>
</path>
<stroke/>
<path>
<move x="17" y="3"/>
<line x="17" y="2"/>
<line x="15.67" y="2"/>
<line x="15.67" y="3"/>
<close/>
<move x="41" y="3"/>
<line x="41" y="2"/>
<line x="39.67" y="2"/>
<line x="39.67" y="3"/>
<close/>
</path>
<stroke/>
<strokewidth width="0.67"/>
<fillcolor color="#ffffff"/>
<path>
<move x="35" y="19"/>
<curve x1="35" y1="26.33" x2="35" y2="26.33" x3="35" y3="26.33"/>
<curve x1="19.67" y1="26.33" x2="19.67" y2="26.33" x3="19.67" y3="26.33"/>
<curve x1="19.67" y1="18.66" x2="19.67" y2="18.66" x3="19.67" y3="18.66"/>
<curve x1="19.67" y1="18" x2="20.67" y2="18" x3="20.67" y3="18"/>
<curve x1="34.33" y1="18" x2="34.33" y2="18" x3="34.33" y3="18"/>
<curve x1="35.33" y1="18" x2="35" y2="19" x3="35" y3="19"/>
<close/>
</path>
<fillstroke/>
<restore/>
<path>
<move x="39.33" y="26.33"/>
<curve x1="39.33" y1="19" x2="39.33" y2="19" x3="39.33" y3="19"/>
<curve x1="39.33" y1="19" x2="39.33" y2="18" x3="38.33" y3="18"/>
<curve x1="20.67" y1="18" x2="20.67" y2="18" x3="20.67" y3="18"/>
<curve x1="20.67" y1="18" x2="19.67" y2="18" x3="19.67" y3="18.66"/>
<curve x1="19.67" y1="26" x2="19.67" y2="26" x3="19.67" y3="26"/>
</path>
<stroke/>
<path>
<move x="25.67" y="25.66"/>
<line x="25.67" y="18.66"/>
<line x="22.67" y="18.66"/>
<line x="22.67" y="25.66"/>
<close/>
</path>
<fill/>
<fillcolor color="#ffffff"/>
<path>
<move x="33.33" y="13.66"/>
<curve x1="36" y1="10.66" x2="36" y2="6.33" x3="33.33" y3="3.33"/>
<curve x1="30.67" y1="0.66" x2="26" y2="0.66" x3="23.33" y3="3.33"/>
<curve x1="20.67" y1="6.33" x2="20.67" y2="10.66" x3="23.33" y3="13.66"/>
<curve x1="26" y1="16.33" x2="30.67" y2="16.33" x3="33.33" y3="13.66"/>
<close/>
</path>
<fill/>
<restore/>
<rect/>
<stroke/>
<path>
<move x="29" y="11.33"/>
<line x="31" y="9"/>
<line x="28.67" y="8.66"/>
<close/>
<move x="25.67" y="7.66"/>
<line x="27.67" y="5.66"/>
<line x="28" y="8"/>
<close/>
</path>
<fill/>
<path>
<move x="30.33" y="4.33"/>
<line x="32.33" y="6.33"/>
<line x="32.67" y="4"/>
<close/>
<move x="24" y="10.33"/>
<line x="26.33" y="12.66"/>
<line x="24" y="13"/>
<close/>
</path>
<fill/>
<strokecolor color="#126d99"/>
<path>
<move x="27.33" y="7.33"/>
<line x="24" y="4"/>
</path>
<stroke/>
<path>
<move x="33" y="13"/>
<line x="29.67" y="9.66"/>
<move x="29" y="7.66"/>
<line x="31.33" y="5.33"/>
</path>
<stroke/>
<path>
<move x="25" y="11.66"/>
<line x="27.67" y="9"/>
</path>
<stroke/>
</foreground>
</shape>
<shape name="Standard Host" h="39.33" w="27.67" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
<constraint x="0.17" y="0.07" perimeter="0" name="NW"/>
<constraint x="0" y="1" perimeter="0" name="SW"/>
<constraint x="1" y="0" perimeter="0" name="NE"/>
<constraint x="0.89" y="0.91" perimeter="0" name="SE"/>
</connections>
<background>
<save/>
<path>
<move x="21" y="6.33"/>
<line x="0" y="6.33"/>
<line x="0" y="39.33"/>
<line x="21" y="39.33"/>
<close/>
<move x="21" y="39.33"/>
<line x="27.67" y="32.67"/>
<line x="27.67" y="0"/>
<line x="8.33" y="0"/>
<line x="0" y="6.33"/>
<line x="21" y="6.33"/>
<close/>
</path>
</background>
<foreground>
<linejoin join="round"/>
<fillstroke/>
<path>
<move x="21" y="6.33"/>
<line x="27.67" y="0"/>
<move x="10.33" y="6.33"/>
<line x="10.33" y="39.33"/>
<move x="10.33" y="6.33"/>
<line x="18" y="0"/>
</path>
<stroke/>
<restore/>
<rect/>
<stroke/>
<rect x="1.67" y="9.33" w="7.33" h="4.33"/>
<stroke/>
<rect x="12" y="9.33" w="7.33" h="4.33"/>
<stroke/>
<rect x="1.67" y="25.33" w="7.33" h="4.33"/>
<stroke/>
<rect x="12" y="25.33" w="7.33" h="4.33"/>
<stroke/>
<rect x="1.67" y="32.33" w="7.33" h="4.33"/>
<stroke/>
<rect x="12" y="32.33" w="7.33" h="4.33"/>
<stroke/>
</foreground>
</shape>
<shape name="Storage Server" h="52.34" w="34.67" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0.22" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
<constraint x="0" y="0.23" perimeter="0" name="NW"/>
<constraint x="0.22" y="1" perimeter="0" name="SW"/>
<constraint x="1" y="0" perimeter="0" name="NE"/>
<constraint x="0.9" y="0.94" perimeter="0" name="SE"/>
</connections>
<background>
<save/>
<path>
<move x="28" y="6.67"/>
<line x="7.67" y="6.67"/>
<line x="7.67" y="52.34"/>
<line x="28" y="52.34"/>
<close/>
<move x="28" y="52.34"/>
<line x="34.67" y="45.67"/>
<line x="34.67" y="0"/>
<line x="14" y="0"/>
<line x="7.67" y="6.67"/>
<line x="28" y="6.67"/>
<close/>
<move x="28" y="6.67"/>
<line x="34.67" y="0"/>
</path>
</background>
<foreground>
<linejoin join="round"/>
<fillstroke/>
<strokewidth width="0.67"/>
<restore/>
<rect/>
<stroke/>
<rect x="9.34" y="26" w="10.33" h="1.67"/>
<stroke/>
<rect x="9.34" y="29" w="10.33" h="1.67"/>
<stroke/>
<rect x="9.34" y="32.34" w="10.33" h="1.67"/>
<stroke/>
<rect x="9.34" y="35.34" w="10.33" h="1.67"/>
<stroke/>
<rect x="9.34" y="38.34" w="10.33" h="1.67"/>
<stroke/>
<rect x="9.34" y="41.67" w="10.33" h="1.67"/>
<stroke/>
<rect x="9.34" y="44.67" w="10.33" h="1.67"/>
<stroke/>
<rect x="9.34" y="47.67" w="10.33" h="1.67"/>
<stroke/>
<linejoin join="round"/>
<path>
<move x="21" y="12"/>
<curve x1="21" y1="14" x2="16.34" y2="16" x3="10.34" y3="16"/>
<curve x1="4.67" y1="16" x2="0" y2="14" x3="0" y3="12"/>
<curve x1="0" y1="21" x2="0" y2="21" x3="0" y3="21"/>
<curve x1="0" y1="23" x2="4.67" y2="24.67" x3="10.34" y3="24.67"/>
<curve x1="16.34" y1="24.67" x2="21" y2="23" x3="21" y3="21"/>
<close/>
</path>
<fillstroke/>
<path>
<move x="10.34" y="16"/>
<curve x1="16.34" y1="16" x2="21" y2="14" x3="21" y3="12"/>
<curve x1="21" y1="10" x2="16.34" y2="8" x3="10.34" y3="8"/>
<curve x1="4.67" y1="8" x2="0" y2="10" x3="0" y3="12"/>
<curve x1="0" y1="14" x2="4.67" y2="16" x3="10.34" y3="16"/>
<close/>
</path>
<fillstroke/>
<path>
<move x="32" y="20"/>
<curve x1="32" y1="22" x2="27.34" y2="24" x3="21.67" y3="24"/>
<curve x1="16" y1="24" x2="11.34" y2="22" x3="11.34" y3="20"/>
<curve x1="11.34" y1="29" x2="11.34" y2="29" x3="11.34" y3="29"/>
<curve x1="11.34" y1="31" x2="16" y2="32.67" x3="21.67" y3="32.67"/>
<curve x1="27.34" y1="32.67" x2="32" y2="31" x3="32" y3="29"/>
<close/>
</path>
<fillstroke/>
<path>
<move x="21.67" y="24"/>
<curve x1="27.34" y1="24" x2="32" y2="22" x3="32" y3="20"/>
<curve x1="32" y1="18" x2="27.34" y2="16" x3="21.67" y3="16"/>
<curve x1="16" y1="16" x2="11.34" y2="18" x3="11.34" y3="20"/>
<curve x1="11.34" y1="22" x2="16" y2="24" x3="21.67" y3="24"/>
<close/>
</path>
<fillstroke/>
</foreground>
</shape>
<shape name="Unity Server" h="43.67" w="31" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
<constraint x="0.12" y="0.07" perimeter="0" name="NW"/>
<constraint x="0" y="1" perimeter="0" name="SW"/>
<constraint x="1" y="0" perimeter="0" name="NE"/>
<constraint x="0.89" y="0.92" perimeter="0" name="SE"/>
</connections>
<background>
<save/>
<path>
<move x="24.33" y="6.67"/>
<line x="0" y="6.67"/>
<line x="0" y="43.67"/>
<line x="24.33" y="43.67"/>
<close/>
</path>
</background>
<foreground>
<linejoin join="round"/>
<fillstroke/>
<path>
<move x="24.33" y="43.67"/>
<line x="31" y="37"/>
<line x="31" y="0"/>
<line x="6.67" y="0"/>
<line x="0" y="6.67"/>
<line x="24.33" y="6.67"/>
<close/>
</path>
<fillstroke/>
<restore/>
<rect/>
<stroke/>
<save/>
<path>
<move x="24.33" y="6.67"/>
<line x="31" y="0"/>
<move x="0" y="17"/>
<line x="24.33" y="17"/>
<move x="0" y="40"/>
<line x="24.33" y="40"/>
<move x="5" y="11.67"/>
<line x="10.67" y="11.67"/>
<move x="13" y="13.67"/>
<line x="13" y="9.34"/>
<line x="2.67" y="9.34"/>
<line x="2.67" y="13.67"/>
<line x="13" y="13.67"/>
<close/>
</path>
<stroke/>
<restore/>
<fillcolor color="#ffffff"/>
<path>
<move x="14" y="29.34"/>
<curve x1="14" y1="30.67" x2="13" y2="31.34" x3="11.67" y3="31.34"/>
<curve x1="11.33" y1="31.34" x2="10.33" y2="31.34" x3="10" y3="30.67"/>
<curve x1="9.67" y1="30.34" x2="9.67" y2="30" x3="9.67" y3="29.34"/>
<curve x1="9.67" y1="26" x2="9.67" y2="26" x3="9.67" y3="26"/>
<curve x1="10.67" y1="26" x2="10.67" y2="26" x3="10.67" y3="26"/>
<curve x1="10.67" y1="29.34" x2="10.67" y2="29.34" x3="10.67" y3="29.34"/>
<curve x1="10.67" y1="30" x2="11" y2="30.34" x3="11.67" y3="30.34"/>
<curve x1="12.33" y1="30.34" x2="12.67" y2="30" x3="12.67" y3="29.34"/>
<curve x1="12.67" y1="26" x2="12.67" y2="26" x3="12.67" y3="26"/>
<curve x1="14" y1="26" x2="14" y2="26" x3="14" y3="26"/>
<close/>
</path>
<fill/>
<save/>
<strokecolor color="#ffffff"/>
<strokewidth width="1"/>
<path>
<move x="11.67" y="32.34"/>
<curve x1="14" y1="32.34" x2="16" y2="30.67" x3="16" y3="28.34"/>
<curve x1="16" y1="26" x2="14" y2="24.34" x3="11.67" y3="24.34"/>
<curve x1="9.67" y1="24.34" x2="7.67" y2="26" x3="7.67" y3="28.34"/>
<curve x1="7.67" y1="30.67" x2="9.67" y2="32.34" x3="11.67" y3="32.34"/>
<close/>
</path>
<stroke/>
<path>
<move x="11.67" y="24.34"/>
<line x="19" y="24.34"/>
</path>
<stroke/>
<restore/>
<fillcolor color="#ffffff"/>
<strokewidth width="1"/>
<strokecolor color="#ffffff"/>
<path>
<move x="19.33" y="22"/>
<line x="19.33" y="26.34"/>
<line x="21.67" y="24.34"/>
<close/>
</path>
<fillstroke/>
<save/>
<strokewidth width="1"/>
<path>
<move x="7.67" y="28.34"/>
<line x="7.67" y="21.34"/>
</path>
<stroke/>
<restore/>
<fillcolor color="#ffffff"/>
<strokewidth width="1"/>
<fillcolor color="#ffffff"/>
<path>
<move x="5.67" y="21"/>
<line x="9.67" y="21"/>
<line x="7.67" y="18.34"/>
<close/>
</path>
<fillstroke/>
<save/>
<strokecolor color="#ffffff"/>
<strokewidth width="1"/>
<path>
<move x="16" y="28.34"/>
<line x="16" y="35.34"/>
</path>
<stroke/>
<restore/>
<fillcolor color="#ffffff"/>
<strokewidth width="1"/>
<fillcolor color="#ffffff"/>
<path>
<move x="18" y="35.67"/>
<line x="14" y="35.67"/>
<line x="15.67" y="38.34"/>
<close/>
</path>
<fillstroke/>
<save/>
<strokecolor color="#ffffff"/>
<strokewidth width="1"/>
<path>
<move x="12" y="32.34"/>
<line x="4.67" y="32.34"/>
</path>
<stroke/>
<restore/>
<fillcolor color="#ffffff"/>
<strokewidth width="1"/>
<fillcolor color="#ffffff"/>
<path>
<move x="4.67" y="34.67"/>
<line x="4.67" y="30.34"/>
<line x="2.33" y="32.34"/>
<close/>
</path>
<fillstroke/>
</foreground>
</shape>
<shape name="Voice Commserver" h="35" w="34.66" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
<constraint x="0.11" y="0.08" perimeter="0" name="NW"/>
<constraint x="0" y="1" perimeter="0" name="SW"/>
<constraint x="1" y="0" perimeter="0" name="NE"/>
<constraint x="0.92" y="0.91" perimeter="0" name="SE"/>
</connections>
<background>
<path>
<move x="28.66" y="35"/>
<line x="28.66" y="5.66"/>
<line x="0" y="5.66"/>
<line x="0" y="35"/>
<close/>
</path>
</background>
<foreground>
<linejoin join="round"/>
<fillstroke/>
<path>
<move x="28.66" y="5.66"/>
<line x="0" y="5.66"/>
<line x="7.33" y="0"/>
<line x="34.66" y="0"/>
<line x="34.66" y="0"/>
<line x="34.66" y="29"/>
<line x="28.66" y="35"/>
<line x="28.66" y="6"/>
<close/>
<move x="28.66" y="5.66"/>
<line x="34.66" y="0"/>
</path>
<fillstroke/>
<strokecolor color="#ffffff"/>
<fillcolor color="#ffffff"/>
<strokewidth width="1"/>
<path>
<move x="14.33" y="26.66"/>
<curve x1="17.66" y1="26.66" x2="20.66" y2="24" x3="20.66" y3="20.33"/>
<curve x1="20.66" y1="17" x2="17.66" y2="14.33" x3="14.33" y3="14.33"/>
<curve x1="11" y1="14.33" x2="8.33" y2="17" x3="8.33" y3="20.33"/>
<curve x1="8.33" y1="24" x2="11" y2="26.66" x3="14.33" y3="26.66"/>
<close/>
</path>
<stroke/>
<path>
<move x="14.33" y="14.33"/>
<line x="25" y="14.33"/>
<move x="8.33" y="20.33"/>
<line x="8.33" y="9.66"/>
<move x="20.66" y="20.33"/>
<line x="20.66" y="31.33"/>
<move x="14.66" y="26.66"/>
<line x="3.66" y="26.66"/>
</path>
<stroke/>
<path>
<move x="23.33" y="11"/>
<line x="23.33" y="17.33"/>
<line x="27.33" y="14.33"/>
<close/>
<move x="5" y="11.66"/>
<line x="11.33" y="11.66"/>
<line x="8.33" y="7.66"/>
<close/>
<move x="24" y="29.33"/>
<line x="17.33" y="29.33"/>
<line x="20.66" y="33"/>
<close/>
<move x="5.66" y="29.66"/>
<line x="5.66" y="23.33"/>
<line x="1.66" y="26.33"/>
<close/>
</path>
<fill/>
<fontcolor color="#ffffff"/>
<fontsize size="11"/>
<text str="V" x="14.5" y="20" valign="middle" align="center"/>
</foreground>
</shape>
<shape name="WWW Server" h="42.33" w="41.34" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0.13" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
<constraint x="0.2" y="0.06" perimeter="0" name="NW"/>
<constraint x="0" y="1" perimeter="0" name="SW"/>
<constraint x="1" y="0" perimeter="0" name="NE"/>
<constraint x="0.85" y="1" perimeter="0" name="SE"/>
</connections>
<background>
<path>
<move x="41.34" y="24.33"/>
<line x="37.34" y="29.33"/>
<line x="5.67" y="29.33"/>
<line x="10.67" y="24"/>
<close/>
</path>
</background>
<foreground>
<linejoin join="round"/>
<fillstroke/>
<path>
<move x="5.67" y="35"/>
<line x="5.67" y="29.33"/>
<line x="36" y="29.33"/>
<line x="36" y="35"/>
<close/>
</path>
<fillstroke/>
<path>
<move x="5.34" y="5.33"/>
<line x="36" y="5.33"/>
<line x="36" y="28"/>
<line x="5.34" y="28"/>
<close/>
</path>
<fillstroke/>
<path>
<move x="36" y="28"/>
<line x="41.34" y="23"/>
<line x="41.34" y="0"/>
<line x="36" y="5.33"/>
<close/>
</path>
<fillstroke/>
<strokewidth width="0.67"/>
<path>
<move x="36" y="35"/>
<line x="36" y="29.33"/>
<line x="41.34" y="24.33"/>
<line x="41.34" y="30"/>
<line x="36" y="35"/>
<close/>
</path>
<fillstroke/>
<path>
<move x="10.34" y="7.66"/>
<curve x1="30.67" y1="7.66" x2="30.67" y2="7.66" x3="30.67" y3="7.66"/>
<curve x1="32" y1="7.66" x2="33.34" y2="9" x3="33.34" y3="10.33"/>
<curve x1="33.34" y1="23" x2="33.34" y2="23" x3="33.34" y3="23"/>
<curve x1="33.34" y1="24.33" x2="32" y2="25.33" x3="30.67" y3="25.33"/>
<curve x1="10.34" y1="25.33" x2="10.34" y2="25.33" x3="10.34" y3="25.33"/>
<curve x1="9" y1="25.33" x2="8" y2="24.33" x3="8" y3="23"/>
<curve x1="8" y1="10.33" x2="8" y2="10.33" x3="8" y3="10.33"/>
<curve x1="8" y1="9" x2="9" y2="7.66" x3="10.34" y3="7.66"/>
<close/>
</path>
<stroke/>
<path>
<move x="41.34" y="0"/>
<line x="11.34" y="0"/>
<line x="5.34" y="5.33"/>
<line x="36" y="5.33"/>
<close/>
<move x="35" y="42.33"/>
<line x="35" y="40.66"/>
<line x="39" y="35"/>
<line x="39" y="38.66"/>
<close/>
<move x="35" y="40.66"/>
<line x="0" y="40.66"/>
<line x="4" y="35"/>
<line x="39" y="35"/>
<close/>
<move x="35" y="40.66"/>
<line x="35" y="42.33"/>
<line x="0" y="42.33"/>
<line x="0" y="40.66"/>
<close/>
</path>
<fillstroke/>
<path>
<move x="17.67" y="11.66"/>
<curve x1="14" y1="11" x2="12" y2="13" x3="12.34" y3="14.33"/>
<curve x1="12.34" y1="14.66" x2="12.34" y2="14.66" x3="12.34" y3="14.66"/>
<curve x1="9.67" y1="15" x2="10.34" y2="18.33" x3="11.67" y3="18.33"/>
<curve x1="11.67" y1="18.33" x2="11.67" y2="18.33" x3="11.67" y3="18.33"/>
<curve x1="11.34" y1="20.66" x2="14.67" y2="22" x3="16.67" y3="20.66"/>
<curve x1="16.67" y1="20.33" x2="16.67" y2="20.33" x3="16.67" y3="20.33"/>
<curve x1="17" y1="22.66" x2="24.34" y2="22.66" x3="25.67" y3="21.33"/>
<curve x1="26" y1="21.33" x2="26" y2="21.33" x3="26" y3="21.33"/>
<curve x1="29" y1="21.33" x2="30.34" y2="19.33" x3="29.67" y3="17.66"/>
<curve x1="30" y1="17.66" x2="30" y2="17.66" x3="30" y3="17.66"/>
<curve x1="31" y1="17.33" x2="31" y2="15" x3="29.34" y3="14.66"/>
<curve x1="29.34" y1="14.33" x2="29.34" y2="14.33" x3="29.34" y3="14.33"/>
<curve x1="30" y1="13" x2="28.34" y2="11.33" x3="25.67" y3="11.66"/>
<curve x1="25.34" y1="11.66" x2="25.34" y2="11.66" x3="25.34" y3="11.66"/>
<curve x1="23.67" y1="10" x2="18.34" y2="10" x3="17.67" y3="11.66"/>
<close/>
</path>
<fillstroke/>
</foreground>
</shape>
</shapes>