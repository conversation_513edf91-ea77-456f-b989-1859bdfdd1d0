html body.theme-dark .geDiagramContainer,
html body.theme-dark div.geMenubarContainer,
html body.theme-dark td.mxPopupMenuIcon,
html body.theme-dark .geFormatContainer,
html body.theme-dark div.geMenubarContainer .geStatus:hover {
  background-color: #2a2a2a;
}
html body.theme-dark .geSidebarContainer button {
  border: 1px solid #505759;
  border-radius: 3px;
}

body.theme-dark div.mxPopupMenu {
  background: #2a2a2a;
  border: 1px solid #333;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}
body.theme-dark div.mxPopupMenu hr {
  background-color: #505759;
}
html body.theme-dark .geTabContainer {
  border-top: 1px solid #505759;
  background-color: #2a2a2a;
}
html body.theme-dark .geDragPreview {
  border: 1px dashed #cccccc;
}
html body.theme-dark .geMenubarContainer .geItem:active,
html body.theme-dark .geSidebarContainer button:active {
  opacity: 0.7;
}
html body.theme-dark,
html body.theme-dark .geFooterContainer,
html body.theme-dark #geFooterItem1,
html body.theme-dark textarea,
html body.theme-dark .mxWindowTitle,
html body.theme-dark .geDialogTitle,
html body.theme-dark .geDialogFooter,
html body.theme-dark select,
html body.theme-dark .geEditor div.mxTooltip,
html body.theme-dark .geHint {
  background: #2a2a2a;
  color: #cccccc;
}
/* html body.theme-dark a {
  color: #337ab7;
} */
html body.theme-dark div.mxRubberband {
  border: 1px dashed #ffffff !important;
  background: #505759 !important;
}
html body.theme-dark .geTemplate {
  color: #000000;
}
html body.theme-dark .geToolbarContainer,
html body.theme-dark .geSidebar,
html body.theme-dark .geSidebarContainer .geTitle,
html body.theme-dark input,
html body.theme-dark textarea,
html body.theme-dark button,
html body.theme-dark .geColorBtn,
html body.theme-dark .geBaseButton,
html body.theme-dark .geSidebarTooltip,
html body.theme-dark .geBaseButton,
html body.theme-dark .geSidebarContainer button {
  background: #2a2a2a;
  border-color: #505759;
  box-shadow: none;
  color: #cccccc;
}
html body.theme-dark .geSidebarTooltip {
  border: 1px solid #505759;
}
html body.theme-dark .geSprite,
html body.theme-dark .geSocialFooter img,
html body.theme-dark .mxPopupMenuItem > img {
  filter: invert(100%);
}
html body.theme-dark .geFormatContainer {
  background-color: #2a2a2a !important;
  border-left: 1px solid #505759;
}
html body.theme-dark .geSidebarFooter {
  border-top: 1px solid #505759;
}
html body.theme-dark .geFormatSection {
  border-bottom: 1px solid #505759;
  border-color: #505759;
}
html body.theme-dark .geDiagramContainer {
  border-color: #505759;
}
html body.theme-dark .geSidebarContainer a,
html body.theme-dark .geMenubarContainer a,
html body.theme-dark .geToolbar a {
  color: #cccccc;
}
html body.theme-dark .geMenubarMenu {
  border-color: #505759 !important;
}
html body.theme-dark .geToolbarMenu,
html body.theme-dark .geFooterContainer,
html body.theme-dark .geFooterContainer td {
  border-color: #505759;
}
html body.theme-dark .geFooterContainer a {
  background-color: none;
}
html body.theme-dark .geFooterContainer td:hover,
html body.theme-dark #geFooterItem1:hover {
  background-color: #000000;
}
html body.theme-dark .geSidebarContainer,
html body.theme-dark .geDiagramBackdrop {
  background-color: #2a2a2a;
}
html body.theme-dark .geBackgroundPage {
  box-shadow: none;
}
html body.theme-dark .geBtn {
  background: #2a2a2a !important;
  border-color: #505759 !important;
  color: #cccccc !important;
}
html body.theme-dark .gePrimaryBtn {
  background: #505759 !important;
  border-color: #cccccc !important;
  color: #cccccc !important;
}
html body.theme-dark .gePropHeader,
html body.theme-dark .gePropRow,
html body.theme-dark .gePropRowDark,
html body.theme-dark .gePropRowCell,
html body.theme-dark .gePropRow > .gePropRowCell,
html body.theme-dark .gePropRowAlt > .gePropRowCell,
html body.theme-dark .gePropRowDark > .gePropRowCell,
html body.theme-dark .gePropRowDarkAlt > .gePropRowCell {
  background: #2a2a2a !important;
  border-color: #2a2a2a !important;
  color: #cccccc !important;
  font-weight: normal !important;
}
html body.theme-dark .geBtn:hover {
  background: #000000 !important;
}
html body.theme-dark tr.mxPopupMenuItem {
  color: #cccccc;
}
html body.theme-dark tr.mxPopupMenuItemHover {
  background: #000000;
  color: #cccccc;
}
html body.theme-dark .geSidebarContainer .geTitle:hover,
html body.theme-dark .geSidebarContainer .geItem:hover,
html body.theme-dark .geMenubarContainer .geItem:hover,
html body.theme-dark .geBaseButton:hover,
html body.theme-dark .geSidebarContainer button:hover {
  background: #000000;
}
html body.theme-dark .geToolbarContainer .geSeparator {
  background-color: #505759;
}
html body.theme-dark .geHsplit,
html body.theme-dark .geVsplit,
html body.theme-dark table.mxPopupMenu hr {
  border-color: #505759;
  background-color: #2a2a2a;
}
html body.theme-dark .geToolbarContainer .geButton:hover,
html body.theme-dark .geToolbarContainer .geButton:active,
html body.theme-dark .geToolbarContainer .geLabel:hover,
html body.theme-dark .geToolbarContainer .geLabel:active,
html body.theme-dark .geHsplit:hover,
html body.theme-dark .geVsplit:hover,
html body.theme-dark .geSidebarContainer button:active {
  background-color: #000;
}
html body.theme-dark .geToolbar {
  border-color: #505759;
  box-shadow: none;
}
html body.theme-dark .geDialog,
html body.theme-dark div.mxWindow {
  background: #2a2a2a;
  border-color: #c0c0c0;
  box-shadow: none;
  color: #cccccc;
}
body.theme-dark .geHint {
  -webkit-box-shadow: 1px 1px 1px 0px #ccc;
  -moz-box-shadow: 1px 1px 1px 0px #ccc;
  box-shadow: 1px 1px 1px 0px #ccc;
}
html body.theme-dark .geEditor ::-webkit-scrollbar-thumb {
  background-color: #505759;
}
html body.theme-dark .geEditor ::-webkit-scrollbar-thumb:hover,
.geHsplit:hover,
.geVsplit:hover {
  background-color: #a0a0a0;
}
html body.theme-dark .geStatusAlert {
  background-color: #a20025;
  border: 1px solid #bd002b;
  color: #fff !important;
}
html body.theme-dark .geStatusAlert:hover {
  background-color: #a20025;
  border-color: #bd002b;
}
html body.theme-dark .geCommentContainer {
  background-color: transparent;
  border-width: 1px;
  box-shadow: none;
  color: inherit;
}

html body.theme-dark .geNotification-bell * {
  display: block;
  margin: 0 auto;
  background-color: #aaa !important;
  box-shadow: 0px 0px 10px #aaa !important;
}

html body.theme-dark .geNotification-count {
  position: absolute;
  z-index: 1;
  top: -5px;
  right: 7px;
  width: 15px;
  height: 15px;
  line-height: 15px;
  font-size: 10px;
  border-radius: 50%;
  background-color: #ff4927;
  color: #deebff !important;
  animation: geZoomAnim 1s 1s both;
}

html body.theme-dark .geNotifPanel .header {
  height: 30px;
  width: 100%;
  background: #424242 !important;
  color: #ccc !important;
  font-size: 15px;
}

html body.theme-dark .geNotifPanel .notifications {
  position: relative;
  height: 270px;
  overflow-x: hidden;
  overflow-y: auto;
  background-color: #707070;
}

/*Using !important here since the diagram puts inline style on svg element*/
html body.theme-dark .geDiagramContainer>svg {
  background-color: #2a2a2a !important;
}