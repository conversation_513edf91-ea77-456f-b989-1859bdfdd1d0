{"version": 3, "file": "workbox-7a2a8380.js", "sources": ["../../../../../../../opt/hostedtoolcache/node/14.19.3/x64/lib/node_modules/workbox-cli/node_modules/workbox-core/_version.js", "../../../../../../../opt/hostedtoolcache/node/14.19.3/x64/lib/node_modules/workbox-cli/node_modules/workbox-core/_private/logger.js", "../../../../../../../opt/hostedtoolcache/node/14.19.3/x64/lib/node_modules/workbox-cli/node_modules/workbox-core/models/messages/messageGenerator.js", "../../../../../../../opt/hostedtoolcache/node/14.19.3/x64/lib/node_modules/workbox-cli/node_modules/workbox-core/_private/WorkboxError.js", "../../../../../../../opt/hostedtoolcache/node/14.19.3/x64/lib/node_modules/workbox-cli/node_modules/workbox-routing/_version.js", "../../../../../../../opt/hostedtoolcache/node/14.19.3/x64/lib/node_modules/workbox-cli/node_modules/workbox-routing/utils/constants.js", "../../../../../../../opt/hostedtoolcache/node/14.19.3/x64/lib/node_modules/workbox-cli/node_modules/workbox-routing/utils/normalizeHandler.js", "../../../../../../../opt/hostedtoolcache/node/14.19.3/x64/lib/node_modules/workbox-cli/node_modules/workbox-routing/Route.js", "../../../../../../../opt/hostedtoolcache/node/14.19.3/x64/lib/node_modules/workbox-cli/node_modules/workbox-routing/RegExpRoute.js", "../../../../../../../opt/hostedtoolcache/node/14.19.3/x64/lib/node_modules/workbox-cli/node_modules/workbox-routing/Router.js", "../../../../../../../opt/hostedtoolcache/node/14.19.3/x64/lib/node_modules/workbox-cli/node_modules/workbox-routing/utils/getOrCreateDefaultRouter.js", "../../../../../../../opt/hostedtoolcache/node/14.19.3/x64/lib/node_modules/workbox-cli/node_modules/workbox-core/_private/cacheNames.js", "../../../../../../../opt/hostedtoolcache/node/14.19.3/x64/lib/node_modules/workbox-cli/node_modules/workbox-core/_private/waitUntil.js", "../../../../../../../opt/hostedtoolcache/node/14.19.3/x64/lib/node_modules/workbox-cli/node_modules/workbox-precaching/_version.js", "../../../../../../../opt/hostedtoolcache/node/14.19.3/x64/lib/node_modules/workbox-cli/node_modules/workbox-precaching/utils/createCacheKey.js", "../../../../../../../opt/hostedtoolcache/node/14.19.3/x64/lib/node_modules/workbox-cli/node_modules/workbox-precaching/utils/PrecacheInstallReportPlugin.js", "../../../../../../../opt/hostedtoolcache/node/14.19.3/x64/lib/node_modules/workbox-cli/node_modules/workbox-precaching/utils/PrecacheCacheKeyPlugin.js", "../../../../../../../opt/hostedtoolcache/node/14.19.3/x64/lib/node_modules/workbox-cli/node_modules/workbox-core/_private/canConstructResponseFromBodyStream.js", "../../../../../../../opt/hostedtoolcache/node/14.19.3/x64/lib/node_modules/workbox-cli/node_modules/workbox-core/copyResponse.js", "../../../../../../../opt/hostedtoolcache/node/14.19.3/x64/lib/node_modules/workbox-cli/node_modules/workbox-core/_private/cacheMatchIgnoreParams.js", "../../../../../../../opt/hostedtoolcache/node/14.19.3/x64/lib/node_modules/workbox-cli/node_modules/workbox-core/_private/Deferred.js", "../../../../../../../opt/hostedtoolcache/node/14.19.3/x64/lib/node_modules/workbox-cli/node_modules/workbox-core/models/quotaErrorCallbacks.js", "../../../../../../../opt/hostedtoolcache/node/14.19.3/x64/lib/node_modules/workbox-cli/node_modules/workbox-strategies/_version.js", "../../../../../../../opt/hostedtoolcache/node/14.19.3/x64/lib/node_modules/workbox-cli/node_modules/workbox-strategies/StrategyHandler.js", "../../../../../../../opt/hostedtoolcache/node/14.19.3/x64/lib/node_modules/workbox-cli/node_modules/workbox-core/_private/timeout.js", "../../../../../../../opt/hostedtoolcache/node/14.19.3/x64/lib/node_modules/workbox-cli/node_modules/workbox-core/_private/getFriendlyURL.js", "../../../../../../../opt/hostedtoolcache/node/14.19.3/x64/lib/node_modules/workbox-cli/node_modules/workbox-core/_private/executeQuotaErrorCallbacks.js", "../../../../../../../opt/hostedtoolcache/node/14.19.3/x64/lib/node_modules/workbox-cli/node_modules/workbox-precaching/PrecacheStrategy.js", "../../../../../../../opt/hostedtoolcache/node/14.19.3/x64/lib/node_modules/workbox-cli/node_modules/workbox-strategies/Strategy.js", "../../../../../../../opt/hostedtoolcache/node/14.19.3/x64/lib/node_modules/workbox-cli/node_modules/workbox-precaching/PrecacheController.js", "../../../../../../../opt/hostedtoolcache/node/14.19.3/x64/lib/node_modules/workbox-cli/node_modules/workbox-precaching/utils/getOrCreatePrecacheController.js", "../../../../../../../opt/hostedtoolcache/node/14.19.3/x64/lib/node_modules/workbox-cli/node_modules/workbox-precaching/PrecacheRoute.js", "../../../../../../../opt/hostedtoolcache/node/14.19.3/x64/lib/node_modules/workbox-cli/node_modules/workbox-precaching/utils/generateURLVariations.js", "../../../../../../../opt/hostedtoolcache/node/14.19.3/x64/lib/node_modules/workbox-cli/node_modules/workbox-precaching/utils/removeIgnoredSearchParams.js", "../../../../../../../opt/hostedtoolcache/node/14.19.3/x64/lib/node_modules/workbox-cli/node_modules/workbox-precaching/addRoute.js", "../../../../../../../opt/hostedtoolcache/node/14.19.3/x64/lib/node_modules/workbox-cli/node_modules/workbox-routing/registerRoute.js", "../../../../../../../opt/hostedtoolcache/node/14.19.3/x64/lib/node_modules/workbox-cli/node_modules/workbox-precaching/precacheAndRoute.js", "../../../../../../../opt/hostedtoolcache/node/14.19.3/x64/lib/node_modules/workbox-cli/node_modules/workbox-precaching/precache.js"], "sourcesContent": ["\"use strict\";\n// @ts-ignore\ntry {\n    self['workbox:core:6.2.4'] && _();\n}\ncatch (e) { }\n", "/*\n  Copyright 2019 Google LLC\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\nconst logger = (process.env.NODE_ENV === 'production' ? null : (() => {\n    // Don't overwrite this value if it's already set.\n    // See https://github.com/GoogleChrome/workbox/pull/2284#issuecomment-*********\n    if (!('__WB_DISABLE_DEV_LOGS' in self)) {\n        self.__WB_DISABLE_DEV_LOGS = false;\n    }\n    let inGroup = false;\n    const methodToColorMap = {\n        debug: `#7f8c8d`,\n        log: `#2ecc71`,\n        warn: `#f39c12`,\n        error: `#c0392b`,\n        groupCollapsed: `#3498db`,\n        groupEnd: null,\n    };\n    const print = function (method, args) {\n        if (self.__WB_DISABLE_DEV_LOGS) {\n            return;\n        }\n        if (method === 'groupCollapsed') {\n            // Safari doesn't print all console.groupCollapsed() arguments:\n            // https://bugs.webkit.org/show_bug.cgi?id=182754\n            if (/^((?!chrome|android).)*safari/i.test(navigator.userAgent)) {\n                console[method](...args);\n                return;\n            }\n        }\n        const styles = [\n            `background: ${methodToColorMap[method]}`,\n            `border-radius: 0.5em`,\n            `color: white`,\n            `font-weight: bold`,\n            `padding: 2px 0.5em`,\n        ];\n        // When in a group, the workbox prefix is not displayed.\n        const logPrefix = inGroup ? [] : ['%cworkbox', styles.join(';')];\n        console[method](...logPrefix, ...args);\n        if (method === 'groupCollapsed') {\n            inGroup = true;\n        }\n        if (method === 'groupEnd') {\n            inGroup = false;\n        }\n    };\n    // eslint-disable-next-line @typescript-eslint/ban-types\n    const api = {};\n    const loggerMethods = Object.keys(methodToColorMap);\n    for (const key of loggerMethods) {\n        const method = key;\n        api[method] = (...args) => {\n            print(method, args);\n        };\n    }\n    return api;\n})());\nexport { logger };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { messages } from './messages.js';\nimport '../../_version.js';\nconst fallback = (code, ...args) => {\n    let msg = code;\n    if (args.length > 0) {\n        msg += ` :: ${JSON.stringify(args)}`;\n    }\n    return msg;\n};\nconst generatorFunction = (code, details = {}) => {\n    const message = messages[code];\n    if (!message) {\n        throw new Error(`Unable to find message for code '${code}'.`);\n    }\n    return message(details);\n};\nexport const messageGenerator = (process.env.NODE_ENV === 'production') ?\n    fallback : generatorFunction;\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { messageGenerator } from '../models/messages/messageGenerator.js';\nimport '../_version.js';\n/**\n * Workbox errors should be thrown with this class.\n * This allows use to ensure the type easily in tests,\n * helps developers identify errors from workbox\n * easily and allows use to optimise error\n * messages correctly.\n *\n * @private\n */\nclass WorkboxError extends Error {\n    /**\n     *\n     * @param {string} errorCode The error code that\n     * identifies this particular error.\n     * @param {Object=} details Any relevant arguments\n     * that will help developers identify issues should\n     * be added as a key on the context object.\n     */\n    constructor(errorCode, details) {\n        const message = messageGenerator(errorCode, details);\n        super(message);\n        this.name = errorCode;\n        this.details = details;\n    }\n}\nexport { WorkboxError };\n", "\"use strict\";\n// @ts-ignore\ntry {\n    self['workbox:routing:6.2.4'] && _();\n}\ncatch (e) { }\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * The default HTTP method, 'GET', used when there's no specific method\n * configured for a route.\n *\n * @type {string}\n *\n * @private\n */\nexport const defaultMethod = 'GET';\n/**\n * The list of valid HTTP methods associated with requests that could be routed.\n *\n * @type {Array<string>}\n *\n * @private\n */\nexport const validMethods = [\n    'DELETE',\n    'GET',\n    'HEAD',\n    'PATCH',\n    'POST',\n    'PUT',\n];\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport '../_version.js';\n/**\n * @param {function()|Object} handler Either a function, or an object with a\n * 'handle' method.\n * @return {Object} An object with a handle method.\n *\n * @private\n */\nexport const normalizeHandler = (handler) => {\n    if (handler && typeof handler === 'object') {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.hasMethod(handler, 'handle', {\n                moduleName: 'workbox-routing',\n                className: 'Route',\n                funcName: 'constructor',\n                paramName: 'handler',\n            });\n        }\n        return handler;\n    }\n    else {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isType(handler, 'function', {\n                moduleName: 'workbox-routing',\n                className: 'Route',\n                funcName: 'constructor',\n                paramName: 'handler',\n            });\n        }\n        return { handle: handler };\n    }\n};\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { defaultMethod, validMethods } from './utils/constants.js';\nimport { normalizeHandler } from './utils/normalizeHandler.js';\nimport './_version.js';\n/**\n * A `Route` consists of a pair of callback functions, \"match\" and \"handler\".\n * The \"match\" callback determine if a route should be used to \"handle\" a\n * request by returning a non-falsy value if it can. The \"handler\" callback\n * is called when there is a match and should return a Promise that resolves\n * to a `Response`.\n *\n * @memberof module:workbox-routing\n */\nclass Route {\n    /**\n     * Constructor for Route class.\n     *\n     * @param {module:workbox-routing~matchCallback} match\n     * A callback function that determines whether the route matches a given\n     * `fetch` event by returning a non-falsy value.\n     * @param {module:workbox-routing~handlerCallback} handler A callback\n     * function that returns a Promise resolving to a Response.\n     * @param {string} [method='GET'] The HTTP method to match the Route\n     * against.\n     */\n    constructor(match, handler, method = defaultMethod) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isType(match, 'function', {\n                moduleName: 'workbox-routing',\n                className: 'Route',\n                funcName: 'constructor',\n                paramName: 'match',\n            });\n            if (method) {\n                assert.isOneOf(method, validMethods, { paramName: 'method' });\n            }\n        }\n        // These values are referenced directly by Router so cannot be\n        // altered by minificaton.\n        this.handler = normalizeHandler(handler);\n        this.match = match;\n        this.method = method;\n    }\n    /**\n     *\n     * @param {module:workbox-routing-handlerCallback} handler A callback\n     * function that returns a Promise resolving to a Response\n     */\n    setCatchHandler(handler) {\n        this.catchHandler = normalizeHandler(handler);\n    }\n}\nexport { Route };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { Route } from './Route.js';\nimport './_version.js';\n/**\n * RegExpRoute makes it easy to create a regular expression based\n * [Route]{@link module:workbox-routing.Route}.\n *\n * For same-origin requests the RegExp only needs to match part of the URL. For\n * requests against third-party servers, you must define a RegExp that matches\n * the start of the URL.\n *\n * [See the module docs for info.]{@link https://developers.google.com/web/tools/workbox/modules/workbox-routing}\n *\n * @memberof module:workbox-routing\n * @extends module:workbox-routing.Route\n */\nclass RegExpRoute extends Route {\n    /**\n     * If the regular expression contains\n     * [capture groups]{@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/RegExp#grouping-back-references},\n     * the captured values will be passed to the\n     * [handler's]{@link module:workbox-routing~handlerCallback} `params`\n     * argument.\n     *\n     * @param {RegExp} regExp The regular expression to match against URLs.\n     * @param {module:workbox-routing~handlerCallback} handler A callback\n     * function that returns a Promise resulting in a Response.\n     * @param {string} [method='GET'] The HTTP method to match the Route\n     * against.\n     */\n    constructor(regExp, handler, method) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isInstance(regExp, RegExp, {\n                moduleName: 'workbox-routing',\n                className: 'RegExpRoute',\n                funcName: 'constructor',\n                paramName: 'pattern',\n            });\n        }\n        const match = ({ url }) => {\n            const result = regExp.exec(url.href);\n            // Return immediately if there's no match.\n            if (!result) {\n                return;\n            }\n            // Require that the match start at the first character in the URL string\n            // if it's a cross-origin request.\n            // See https://github.com/GoogleChrome/workbox/issues/281 for the context\n            // behind this behavior.\n            if ((url.origin !== location.origin) && (result.index !== 0)) {\n                if (process.env.NODE_ENV !== 'production') {\n                    logger.debug(`The regular expression '${regExp.toString()}' only partially matched ` +\n                        `against the cross-origin URL '${url.toString()}'. RegExpRoute's will only ` +\n                        `handle cross-origin requests if they match the entire URL.`);\n                }\n                return;\n            }\n            // If the route matches, but there aren't any capture groups defined, then\n            // this will return [], which is truthy and therefore sufficient to\n            // indicate a match.\n            // If there are capture groups, then it will return their values.\n            return result.slice(1);\n        };\n        super(match, handler, method);\n    }\n}\nexport { RegExpRoute };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { getFriendlyURL } from 'workbox-core/_private/getFriendlyURL.js';\nimport { defaultMethod } from './utils/constants.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { normalizeHandler } from './utils/normalizeHandler.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport './_version.js';\n/**\n * The Router can be used to process a FetchEvent through one or more\n * [Routes]{@link module:workbox-routing.Route} responding  with a Request if\n * a matching route exists.\n *\n * If no route matches a given a request, the Router will use a \"default\"\n * handler if one is defined.\n *\n * Should the matching Route throw an error, the Router will use a \"catch\"\n * handler if one is defined to gracefully deal with issues and respond with a\n * Request.\n *\n * If a request matches multiple routes, the **earliest** registered route will\n * be used to respond to the request.\n *\n * @memberof module:workbox-routing\n */\nclass Router {\n    /**\n     * Initializes a new Router.\n     */\n    constructor() {\n        this._routes = new Map();\n        this._defaultHandlerMap = new Map();\n    }\n    /**\n     * @return {Map<string, Array<module:workbox-routing.Route>>} routes A `Map` of HTTP\n     * method name ('GET', etc.) to an array of all the corresponding `Route`\n     * instances that are registered.\n     */\n    get routes() {\n        return this._routes;\n    }\n    /**\n     * Adds a fetch event listener to respond to events when a route matches\n     * the event's request.\n     */\n    addFetchListener() {\n        // See https://github.com/Microsoft/TypeScript/issues/28357#issuecomment-*********\n        self.addEventListener('fetch', ((event) => {\n            const { request } = event;\n            const responsePromise = this.handleRequest({ request, event });\n            if (responsePromise) {\n                event.respondWith(responsePromise);\n            }\n        }));\n    }\n    /**\n     * Adds a message event listener for URLs to cache from the window.\n     * This is useful to cache resources loaded on the page prior to when the\n     * service worker started controlling it.\n     *\n     * The format of the message data sent from the window should be as follows.\n     * Where the `urlsToCache` array may consist of URL strings or an array of\n     * URL string + `requestInit` object (the same as you'd pass to `fetch()`).\n     *\n     * ```\n     * {\n     *   type: 'CACHE_URLS',\n     *   payload: {\n     *     urlsToCache: [\n     *       './script1.js',\n     *       './script2.js',\n     *       ['./script3.js', {mode: 'no-cors'}],\n     *     ],\n     *   },\n     * }\n     * ```\n     */\n    addCacheListener() {\n        // See https://github.com/Microsoft/TypeScript/issues/28357#issuecomment-*********\n        self.addEventListener('message', ((event) => {\n            // event.data is type 'any'\n            if (event.data && event.data.type === 'CACHE_URLS') { // eslint-disable-line\n                const { payload } = event.data; // eslint-disable-line\n                if (process.env.NODE_ENV !== 'production') {\n                    logger.debug(`Caching URLs from the window`, payload.urlsToCache);\n                }\n                const requestPromises = Promise.all(payload.urlsToCache.map((entry) => {\n                    if (typeof entry === 'string') {\n                        entry = [entry];\n                    }\n                    const request = new Request(...entry);\n                    return this.handleRequest({ request, event });\n                    // TODO(philipwalton): TypeScript errors without this typecast for\n                    // some reason (probably a bug). The real type here should work but\n                    // doesn't: `Array<Promise<Response> | undefined>`.\n                })); // TypeScript\n                event.waitUntil(requestPromises);\n                // If a MessageChannel was used, reply to the message on success.\n                if (event.ports && event.ports[0]) {\n                    void requestPromises.then(() => event.ports[0].postMessage(true));\n                }\n            }\n        }));\n    }\n    /**\n     * Apply the routing rules to a FetchEvent object to get a Response from an\n     * appropriate Route's handler.\n     *\n     * @param {Object} options\n     * @param {Request} options.request The request to handle.\n     * @param {ExtendableEvent} options.event The event that triggered the\n     *     request.\n     * @return {Promise<Response>|undefined} A promise is returned if a\n     *     registered route can handle the request. If there is no matching\n     *     route and there's no `defaultHandler`, `undefined` is returned.\n     */\n    handleRequest({ request, event }) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isInstance(request, Request, {\n                moduleName: 'workbox-routing',\n                className: 'Router',\n                funcName: 'handleRequest',\n                paramName: 'options.request',\n            });\n        }\n        const url = new URL(request.url, location.href);\n        if (!url.protocol.startsWith('http')) {\n            if (process.env.NODE_ENV !== 'production') {\n                logger.debug(`Workbox Router only supports URLs that start with 'http'.`);\n            }\n            return;\n        }\n        const sameOrigin = url.origin === location.origin;\n        const { params, route } = this.findMatchingRoute({\n            event,\n            request,\n            sameOrigin,\n            url,\n        });\n        let handler = route && route.handler;\n        const debugMessages = [];\n        if (process.env.NODE_ENV !== 'production') {\n            if (handler) {\n                debugMessages.push([\n                    `Found a route to handle this request:`, route,\n                ]);\n                if (params) {\n                    debugMessages.push([\n                        `Passing the following params to the route's handler:`, params,\n                    ]);\n                }\n            }\n        }\n        // If we don't have a handler because there was no matching route, then\n        // fall back to defaultHandler if that's defined.\n        const method = request.method;\n        if (!handler && this._defaultHandlerMap.has(method)) {\n            if (process.env.NODE_ENV !== 'production') {\n                debugMessages.push(`Failed to find a matching route. Falling ` +\n                    `back to the default handler for ${method}.`);\n            }\n            handler = this._defaultHandlerMap.get(method);\n        }\n        if (!handler) {\n            if (process.env.NODE_ENV !== 'production') {\n                // No handler so Workbox will do nothing. If logs is set of debug\n                // i.e. verbose, we should print out this information.\n                logger.debug(`No route found for: ${getFriendlyURL(url)}`);\n            }\n            return;\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            // We have a handler, meaning Workbox is going to handle the route.\n            // print the routing details to the console.\n            logger.groupCollapsed(`Router is responding to: ${getFriendlyURL(url)}`);\n            debugMessages.forEach((msg) => {\n                if (Array.isArray(msg)) {\n                    logger.log(...msg);\n                }\n                else {\n                    logger.log(msg);\n                }\n            });\n            logger.groupEnd();\n        }\n        // Wrap in try and catch in case the handle method throws a synchronous\n        // error. It should still callback to the catch handler.\n        let responsePromise;\n        try {\n            responsePromise = handler.handle({ url, request, event, params });\n        }\n        catch (err) {\n            responsePromise = Promise.reject(err);\n        }\n        // Get route's catch handler, if it exists\n        const catchHandler = route && route.catchHandler;\n        if (responsePromise instanceof Promise && (this._catchHandler || catchHandler)) {\n            responsePromise = responsePromise.catch(async (err) => {\n                // If there's a route catch handler, process that first\n                if (catchHandler) {\n                    if (process.env.NODE_ENV !== 'production') {\n                        // Still include URL here as it will be async from the console group\n                        // and may not make sense without the URL\n                        logger.groupCollapsed(`Error thrown when responding to: ` +\n                            ` ${getFriendlyURL(url)}. Falling back to route's Catch Handler.`);\n                        logger.error(`Error thrown by:`, route);\n                        logger.error(err);\n                        logger.groupEnd();\n                    }\n                    try {\n                        return await catchHandler.handle({ url, request, event, params });\n                    }\n                    catch (catchErr) {\n                        if (catchErr instanceof Error) {\n                            err = catchErr;\n                        }\n                    }\n                }\n                if (this._catchHandler) {\n                    if (process.env.NODE_ENV !== 'production') {\n                        // Still include URL here as it will be async from the console group\n                        // and may not make sense without the URL\n                        logger.groupCollapsed(`Error thrown when responding to: ` +\n                            ` ${getFriendlyURL(url)}. Falling back to global Catch Handler.`);\n                        logger.error(`Error thrown by:`, route);\n                        logger.error(err);\n                        logger.groupEnd();\n                    }\n                    return this._catchHandler.handle({ url, request, event });\n                }\n                throw err;\n            });\n        }\n        return responsePromise;\n    }\n    /**\n     * Checks a request and URL (and optionally an event) against the list of\n     * registered routes, and if there's a match, returns the corresponding\n     * route along with any params generated by the match.\n     *\n     * @param {Object} options\n     * @param {URL} options.url\n     * @param {boolean} options.sameOrigin The result of comparing `url.origin`\n     *     against the current origin.\n     * @param {Request} options.request The request to match.\n     * @param {Event} options.event The corresponding event.\n     * @return {Object} An object with `route` and `params` properties.\n     *     They are populated if a matching route was found or `undefined`\n     *     otherwise.\n     */\n    findMatchingRoute({ url, sameOrigin, request, event }) {\n        const routes = this._routes.get(request.method) || [];\n        for (const route of routes) {\n            let params;\n            // route.match returns type any, not possible to change right now.\n            // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n            const matchResult = route.match({ url, sameOrigin, request, event });\n            if (matchResult) {\n                if (process.env.NODE_ENV !== 'production') {\n                    // Warn developers that using an async matchCallback is almost always\n                    // not the right thing to do.\n                    if (matchResult instanceof Promise) {\n                        logger.warn(`While routing ${getFriendlyURL(url)}, an async ` +\n                            `matchCallback function was used. Please convert the ` +\n                            `following route to use a synchronous matchCallback function:`, route);\n                    }\n                }\n                // See https://github.com/GoogleChrome/workbox/issues/2079\n                // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n                params = matchResult;\n                if (Array.isArray(params) && params.length === 0) {\n                    // Instead of passing an empty array in as params, use undefined.\n                    params = undefined;\n                }\n                else if ((matchResult.constructor === Object && // eslint-disable-line\n                    Object.keys(matchResult).length === 0)) {\n                    // Instead of passing an empty object in as params, use undefined.\n                    params = undefined;\n                }\n                else if (typeof matchResult === 'boolean') {\n                    // For the boolean value true (rather than just something truth-y),\n                    // don't set params.\n                    // See https://github.com/GoogleChrome/workbox/pull/2134#issuecomment-513924353\n                    params = undefined;\n                }\n                // Return early if have a match.\n                return { route, params };\n            }\n        }\n        // If no match was found above, return and empty object.\n        return {};\n    }\n    /**\n     * Define a default `handler` that's called when no routes explicitly\n     * match the incoming request.\n     *\n     * Each HTTP method ('GET', 'POST', etc.) gets its own default handler.\n     *\n     * Without a default handler, unmatched requests will go against the\n     * network as if there were no service worker present.\n     *\n     * @param {module:workbox-routing~handlerCallback} handler A callback\n     * function that returns a Promise resulting in a Response.\n     * @param {string} [method='GET'] The HTTP method to associate with this\n     * default handler. Each method has its own default.\n     */\n    setDefaultHandler(handler, method = defaultMethod) {\n        this._defaultHandlerMap.set(method, normalizeHandler(handler));\n    }\n    /**\n     * If a Route throws an error while handling a request, this `handler`\n     * will be called and given a chance to provide a response.\n     *\n     * @param {module:workbox-routing~handlerCallback} handler A callback\n     * function that returns a Promise resulting in a Response.\n     */\n    setCatchHandler(handler) {\n        this._catchHandler = normalizeHandler(handler);\n    }\n    /**\n     * Registers a route with the router.\n     *\n     * @param {module:workbox-routing.Route} route The route to register.\n     */\n    registerRoute(route) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isType(route, 'object', {\n                moduleName: 'workbox-routing',\n                className: 'Router',\n                funcName: 'registerRoute',\n                paramName: 'route',\n            });\n            assert.hasMethod(route, 'match', {\n                moduleName: 'workbox-routing',\n                className: 'Router',\n                funcName: 'registerRoute',\n                paramName: 'route',\n            });\n            assert.isType(route.handler, 'object', {\n                moduleName: 'workbox-routing',\n                className: 'Router',\n                funcName: 'registerRoute',\n                paramName: 'route',\n            });\n            assert.hasMethod(route.handler, 'handle', {\n                moduleName: 'workbox-routing',\n                className: 'Router',\n                funcName: 'registerRoute',\n                paramName: 'route.handler',\n            });\n            assert.isType(route.method, 'string', {\n                moduleName: 'workbox-routing',\n                className: 'Router',\n                funcName: 'registerRoute',\n                paramName: 'route.method',\n            });\n        }\n        if (!this._routes.has(route.method)) {\n            this._routes.set(route.method, []);\n        }\n        // Give precedence to all of the earlier routes by adding this additional\n        // route to the end of the array.\n        this._routes.get(route.method).push(route);\n    }\n    /**\n     * Unregisters a route with the router.\n     *\n     * @param {module:workbox-routing.Route} route The route to unregister.\n     */\n    unregisterRoute(route) {\n        if (!this._routes.has(route.method)) {\n            throw new WorkboxError('unregister-route-but-not-found-with-method', {\n                method: route.method,\n            });\n        }\n        const routeIndex = this._routes.get(route.method).indexOf(route);\n        if (routeIndex > -1) {\n            this._routes.get(route.method).splice(routeIndex, 1);\n        }\n        else {\n            throw new WorkboxError('unregister-route-route-not-registered');\n        }\n    }\n}\nexport { Router };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { Router } from '../Router.js';\nimport '../_version.js';\nlet defaultRouter;\n/**\n * Creates a new, singleton Router instance if one does not exist. If one\n * does already exist, that instance is returned.\n *\n * @private\n * @return {Router}\n */\nexport const getOrCreateDefaultRouter = () => {\n    if (!defaultRouter) {\n        defaultRouter = new Router();\n        // The helpers that use the default Router assume these listeners exist.\n        defaultRouter.addFetchListener();\n        defaultRouter.addCacheListener();\n    }\n    return defaultRouter;\n};\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\nconst _cacheNameDetails = {\n    googleAnalytics: 'googleAnalytics',\n    precache: 'precache-v2',\n    prefix: 'workbox',\n    runtime: 'runtime',\n    suffix: typeof registration !== 'undefined' ? registration.scope : '',\n};\nconst _createCacheName = (cacheName) => {\n    return [_cacheNameDetails.prefix, cacheName, _cacheNameDetails.suffix]\n        .filter((value) => value && value.length > 0)\n        .join('-');\n};\nconst eachCacheNameDetail = (fn) => {\n    for (const key of Object.keys(_cacheNameDetails)) {\n        fn(key);\n    }\n};\nexport const cacheNames = {\n    updateDetails: (details) => {\n        eachCacheNameDetail((key) => {\n            if (typeof details[key] === 'string') {\n                _cacheNameDetails[key] = details[key];\n            }\n        });\n    },\n    getGoogleAnalyticsName: (userCacheName) => {\n        return userCacheName || _createCacheName(_cacheNameDetails.googleAnalytics);\n    },\n    getPrecacheName: (userCacheName) => {\n        return userCacheName || _createCacheName(_cacheNameDetails.precache);\n    },\n    getPrefix: () => {\n        return _cacheNameDetails.prefix;\n    },\n    getRuntimeName: (userCacheName) => {\n        return userCacheName || _createCacheName(_cacheNameDetails.runtime);\n    },\n    getSuffix: () => {\n        return _cacheNameDetails.suffix;\n    },\n};\n", "/*\n  Copyright 2020 Google LLC\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * A utility method that makes it easier to use `event.waitUntil` with\n * async functions and return the result.\n *\n * @param {ExtendableEvent} event\n * @param {Function} asyncFn\n * @return {Function}\n * @private\n */\nfunction waitUntil(event, asyncFn) {\n    const returnPromise = asyncFn();\n    event.waitUntil(returnPromise);\n    return returnPromise;\n}\nexport { waitUntil };\n", "\"use strict\";\n// @ts-ignore\ntry {\n    self['workbox:precaching:6.2.4'] && _();\n}\ncatch (e) { }\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport '../_version.js';\n// Name of the search parameter used to store revision info.\nconst REVISION_SEARCH_PARAM = '__WB_REVISION__';\n/**\n * Converts a manifest entry into a versioned URL suitable for precaching.\n *\n * @param {Object|string} entry\n * @return {string} A URL with versioning info.\n *\n * @private\n * @memberof module:workbox-precaching\n */\nexport function createCacheKey(entry) {\n    if (!entry) {\n        throw new WorkboxError('add-to-cache-list-unexpected-type', { entry });\n    }\n    // If a precache manifest entry is a string, it's assumed to be a versioned\n    // URL, like '/app.abcd1234.js'. Return as-is.\n    if (typeof entry === 'string') {\n        const urlObject = new URL(entry, location.href);\n        return {\n            cacheKey: urlObject.href,\n            url: urlObject.href,\n        };\n    }\n    const { revision, url } = entry;\n    if (!url) {\n        throw new WorkboxError('add-to-cache-list-unexpected-type', { entry });\n    }\n    // If there's just a URL and no revision, then it's also assumed to be a\n    // versioned URL.\n    if (!revision) {\n        const urlObject = new URL(url, location.href);\n        return {\n            cacheKey: urlObject.href,\n            url: urlObject.href,\n        };\n    }\n    // Otherwise, construct a properly versioned URL using the custom Workbox\n    // search parameter along with the revision info.\n    const cacheKeyURL = new URL(url, location.href);\n    const originalURL = new URL(url, location.href);\n    cacheKeyURL.searchParams.set(REVISION_SEARCH_PARAM, revision);\n    return {\n        cacheKey: cacheKeyURL.href,\n        url: originalURL.href,\n    };\n}\n", "/*\n  Copyright 2020 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * A plugin, designed to be used with PrecacheController, to determine the\n * of assets that were updated (or not updated) during the install event.\n *\n * @private\n */\nclass PrecacheInstallReportPlugin {\n    constructor() {\n        this.updatedURLs = [];\n        this.notUpdatedURLs = [];\n        this.handlerWillStart = async ({ request, state, }) => {\n            // TODO: `state` should never be undefined...\n            if (state) {\n                state.originalRequest = request;\n            }\n        };\n        this.cachedResponseWillBeUsed = async ({ event, state, cachedResponse, }) => {\n            if (event.type === 'install') {\n                if (state && state.originalRequest\n                    && state.originalRequest instanceof Request) {\n                    // TODO: `state` should never be undefined...\n                    const url = state.originalRequest.url;\n                    if (cachedResponse) {\n                        this.notUpdatedURLs.push(url);\n                    }\n                    else {\n                        this.updatedURLs.push(url);\n                    }\n                }\n            }\n            return cachedResponse;\n        };\n    }\n}\nexport { PrecacheInstallReportPlugin };\n", "/*\n  Copyright 2020 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * A plugin, designed to be used with PrecacheController, to translate URLs into\n * the corresponding cache key, based on the current revision info.\n *\n * @private\n */\nclass PrecacheCacheKeyPlugin {\n    constructor({ precacheController }) {\n        this.cacheKeyWillBeUsed = async ({ request, params, }) => {\n            // Params is type any, can't change right now.\n            /* eslint-disable */\n            const cacheKey = (params === null || params === void 0 ? void 0 : params.cacheKey) ||\n                this._precacheController.getCacheKeyForURL(request.url);\n            /* eslint-enable */\n            return cacheKey\n                ? new Request(cacheKey, { headers: request.headers })\n                : request;\n        };\n        this._precacheController = precacheController;\n    }\n}\nexport { PrecacheCacheKeyPlugin };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\nlet supportStatus;\n/**\n * A utility function that determines whether the current browser supports\n * constructing a new `Response` from a `response.body` stream.\n *\n * @return {boolean} `true`, if the current browser can successfully\n *     construct a `Response` from a `response.body` stream, `false` otherwise.\n *\n * @private\n */\nfunction canConstructResponseFromBodyStream() {\n    if (supportStatus === undefined) {\n        const testResponse = new Response('');\n        if ('body' in testResponse) {\n            try {\n                new Response(testResponse.body);\n                supportStatus = true;\n            }\n            catch (error) {\n                supportStatus = false;\n            }\n        }\n        supportStatus = false;\n    }\n    return supportStatus;\n}\nexport { canConstructResponseFromBodyStream };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { canConstructResponseFromBodyStream } from './_private/canConstructResponseFromBodyStream.js';\nimport { WorkboxError } from './_private/WorkboxError.js';\nimport './_version.js';\n/**\n * Allows developers to copy a response and modify its `headers`, `status`,\n * or `statusText` values (the values settable via a\n * [`ResponseInit`]{@link https://developer.mozilla.org/en-US/docs/Web/API/Response/Response#Syntax}\n * object in the constructor).\n * To modify these values, pass a function as the second argument. That\n * function will be invoked with a single object with the response properties\n * `{headers, status, statusText}`. The return value of this function will\n * be used as the `ResponseInit` for the new `Response`. To change the values\n * either modify the passed parameter(s) and return it, or return a totally\n * new object.\n *\n * This method is intentionally limited to same-origin responses, regardless of\n * whether CORS was used or not.\n *\n * @param {Response} response\n * @param {Function} modifier\n * @memberof module:workbox-core\n */\nasync function copyResponse(response, modifier) {\n    let origin = null;\n    // If response.url isn't set, assume it's cross-origin and keep origin null.\n    if (response.url) {\n        const responseURL = new URL(response.url);\n        origin = responseURL.origin;\n    }\n    if (origin !== self.location.origin) {\n        throw new WorkboxError('cross-origin-copy-response', { origin });\n    }\n    const clonedResponse = response.clone();\n    // Create a fresh `ResponseInit` object by cloning the headers.\n    const responseInit = {\n        headers: new Headers(clonedResponse.headers),\n        status: clonedResponse.status,\n        statusText: clonedResponse.statusText,\n    };\n    // Apply any user modifications.\n    const modifiedResponseInit = modifier ? modifier(responseInit) : responseInit;\n    // Create the new response from the body stream and `ResponseInit`\n    // modifications. Note: not all browsers support the Response.body stream,\n    // so fall back to reading the entire body into memory as a blob.\n    const body = canConstructResponseFromBodyStream() ?\n        clonedResponse.body : await clonedResponse.blob();\n    return new Response(body, modifiedResponseInit);\n}\nexport { copyResponse };\n", "/*\n  Copyright 2020 Google LLC\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\nfunction stripParams(fullURL, ignoreParams) {\n    const strippedURL = new URL(fullURL);\n    for (const param of ignoreParams) {\n        strippedURL.searchParams.delete(param);\n    }\n    return strippedURL.href;\n}\n/**\n * Matches an item in the cache, ignoring specific URL params. This is similar\n * to the `ignoreSearch` option, but it allows you to ignore just specific\n * params (while continuing to match on the others).\n *\n * @private\n * @param {Cache} cache\n * @param {Request} request\n * @param {Object} matchOptions\n * @param {Array<string>} ignoreParams\n * @return {Promise<Response|undefined>}\n */\nasync function cacheMatchIgnoreParams(cache, request, ignoreParams, matchOptions) {\n    const strippedRequestURL = stripParams(request.url, ignoreParams);\n    // If the request doesn't include any ignored params, match as normal.\n    if (request.url === strippedRequestURL) {\n        return cache.match(request, matchOptions);\n    }\n    // Otherwise, match by comparing keys\n    const keysOptions = Object.assign(Object.assign({}, matchOptions), { ignoreSearch: true });\n    const cacheKeys = await cache.keys(request, keysOptions);\n    for (const cacheKey of cacheKeys) {\n        const strippedCacheKeyURL = stripParams(cacheKey.url, ignoreParams);\n        if (strippedRequestURL === strippedCacheKeyURL) {\n            return cache.match(cacheKey, matchOptions);\n        }\n    }\n    return;\n}\nexport { cacheMatchIgnoreParams };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * The Deferred class composes Promises in a way that allows for them to be\n * resolved or rejected from outside the constructor. In most cases promises\n * should be used directly, but Deferreds can be necessary when the logic to\n * resolve a promise must be separate.\n *\n * @private\n */\nclass Deferred {\n    /**\n     * Creates a promise and exposes its resolve and reject functions as methods.\n     */\n    constructor() {\n        this.promise = new Promise((resolve, reject) => {\n            this.resolve = resolve;\n            this.reject = reject;\n        });\n    }\n}\nexport { Deferred };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n// Callbacks to be executed whenever there's a quota error.\n// Can't change Function type right now.\n// eslint-disable-next-line @typescript-eslint/ban-types\nconst quotaErrorCallbacks = new Set();\nexport { quotaErrorCallbacks };\n", "\"use strict\";\n// @ts-ignore\ntry {\n    self['workbox:strategies:6.2.4'] && _();\n}\ncatch (e) { }\n", "/*\n  Copyright 2020 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { cacheMatchIgnoreParams } from 'workbox-core/_private/cacheMatchIgnoreParams.js';\nimport { Deferred } from 'workbox-core/_private/Deferred.js';\nimport { executeQuotaErrorCallbacks } from 'workbox-core/_private/executeQuotaErrorCallbacks.js';\nimport { getFriendlyURL } from 'workbox-core/_private/getFriendlyURL.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { timeout } from 'workbox-core/_private/timeout.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport './_version.js';\nfunction toRequest(input) {\n    return (typeof input === 'string') ? new Request(input) : input;\n}\n/**\n * A class created every time a Strategy instance instance calls\n * [handle()]{@link module:workbox-strategies.Strategy~handle} or\n * [handleAll()]{@link module:workbox-strategies.Strategy~handleAll} that wraps all fetch and\n * cache actions around plugin callbacks and keeps track of when the strategy\n * is \"done\" (i.e. all added `event.waitUntil()` promises have resolved).\n *\n * @memberof module:workbox-strategies\n */\nclass StrategyHandler {\n    /**\n     * Creates a new instance associated with the passed strategy and event\n     * that's handling the request.\n     *\n     * The constructor also initializes the state that will be passed to each of\n     * the plugins handling this request.\n     *\n     * @param {module:workbox-strategies.Strategy} strategy\n     * @param {Object} options\n     * @param {Request|string} options.request A request to run this strategy for.\n     * @param {ExtendableEvent} options.event The event associated with the\n     *     request.\n     * @param {URL} [options.url]\n     * @param {*} [options.params]\n     *     [match callback]{@link module:workbox-routing~matchCallback},\n     *     (if applicable).\n     */\n    constructor(strategy, options) {\n        this._cacheKeys = {};\n        /**\n         * The request the strategy is performing (passed to the strategy's\n         * `handle()` or `handleAll()` method).\n         * @name request\n         * @instance\n         * @type {Request}\n         * @memberof module:workbox-strategies.StrategyHandler\n         */\n        /**\n         * The event associated with this request.\n         * @name event\n         * @instance\n         * @type {ExtendableEvent}\n         * @memberof module:workbox-strategies.StrategyHandler\n         */\n        /**\n         * A `URL` instance of `request.url` (if passed to the strategy's\n         * `handle()` or `handleAll()` method).\n         * Note: the `url` param will be present if the strategy was invoked\n         * from a workbox `Route` object.\n         * @name url\n         * @instance\n         * @type {URL|undefined}\n         * @memberof module:workbox-strategies.StrategyHandler\n         */\n        /**\n         * A `param` value (if passed to the strategy's\n         * `handle()` or `handleAll()` method).\n         * Note: the `param` param will be present if the strategy was invoked\n         * from a workbox `Route` object and the\n         * [match callback]{@link module:workbox-routing~matchCallback} returned\n         * a truthy value (it will be that value).\n         * @name params\n         * @instance\n         * @type {*|undefined}\n         * @memberof module:workbox-strategies.StrategyHandler\n         */\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isInstance(options.event, ExtendableEvent, {\n                moduleName: 'workbox-strategies',\n                className: 'StrategyHandler',\n                funcName: 'constructor',\n                paramName: 'options.event',\n            });\n        }\n        Object.assign(this, options);\n        this.event = options.event;\n        this._strategy = strategy;\n        this._handlerDeferred = new Deferred();\n        this._extendLifetimePromises = [];\n        // Copy the plugins list (since it's mutable on the strategy),\n        // so any mutations don't affect this handler instance.\n        this._plugins = [...strategy.plugins];\n        this._pluginStateMap = new Map();\n        for (const plugin of this._plugins) {\n            this._pluginStateMap.set(plugin, {});\n        }\n        this.event.waitUntil(this._handlerDeferred.promise);\n    }\n    /**\n     * Fetches a given request (and invokes any applicable plugin callback\n     * methods) using the `fetchOptions` (for non-navigation requests) and\n     * `plugins` defined on the `Strategy` object.\n     *\n     * The following plugin lifecycle methods are invoked when using this method:\n     * - `requestWillFetch()`\n     * - `fetchDidSucceed()`\n     * - `fetchDidFail()`\n     *\n     * @param {Request|string} input The URL or request to fetch.\n     * @return {Promise<Response>}\n     */\n    async fetch(input) {\n        const { event } = this;\n        let request = toRequest(input);\n        if (request.mode === 'navigate' &&\n            event instanceof FetchEvent &&\n            event.preloadResponse) {\n            const possiblePreloadResponse = await event.preloadResponse;\n            if (possiblePreloadResponse) {\n                if (process.env.NODE_ENV !== 'production') {\n                    logger.log(`Using a preloaded navigation response for ` +\n                        `'${getFriendlyURL(request.url)}'`);\n                }\n                return possiblePreloadResponse;\n            }\n        }\n        // If there is a fetchDidFail plugin, we need to save a clone of the\n        // original request before it's either modified by a requestWillFetch\n        // plugin or before the original request's body is consumed via fetch().\n        const originalRequest = this.hasCallback('fetchDidFail') ?\n            request.clone() : null;\n        try {\n            for (const cb of this.iterateCallbacks('requestWillFetch')) {\n                request = await cb({ request: request.clone(), event });\n            }\n        }\n        catch (err) {\n            if (err instanceof Error) {\n                throw new WorkboxError('plugin-error-request-will-fetch', { thrownErrorMessage: err.message });\n            }\n        }\n        // The request can be altered by plugins with `requestWillFetch` making\n        // the original request (most likely from a `fetch` event) different\n        // from the Request we make. Pass both to `fetchDidFail` to aid debugging.\n        const pluginFilteredRequest = request.clone();\n        try {\n            let fetchResponse;\n            // See https://github.com/GoogleChrome/workbox/issues/1796\n            fetchResponse = await fetch(request, request.mode === 'navigate' ?\n                undefined : this._strategy.fetchOptions);\n            if (process.env.NODE_ENV !== 'production') {\n                logger.debug(`Network request for ` +\n                    `'${getFriendlyURL(request.url)}' returned a response with ` +\n                    `status '${fetchResponse.status}'.`);\n            }\n            for (const callback of this.iterateCallbacks('fetchDidSucceed')) {\n                fetchResponse = await callback({\n                    event,\n                    request: pluginFilteredRequest,\n                    response: fetchResponse,\n                });\n            }\n            return fetchResponse;\n        }\n        catch (error) {\n            if (process.env.NODE_ENV !== 'production') {\n                logger.log(`Network request for ` +\n                    `'${getFriendlyURL(request.url)}' threw an error.`, error);\n            }\n            // `originalRequest` will only exist if a `fetchDidFail` callback\n            // is being used (see above).\n            if (originalRequest) {\n                await this.runCallbacks('fetchDidFail', {\n                    error: error,\n                    event,\n                    originalRequest: originalRequest.clone(),\n                    request: pluginFilteredRequest.clone(),\n                });\n            }\n            throw error;\n        }\n    }\n    /**\n     * Calls `this.fetch()` and (in the background) runs `this.cachePut()` on\n     * the response generated by `this.fetch()`.\n     *\n     * The call to `this.cachePut()` automatically invokes `this.waitUntil()`,\n     * so you do not have to manually call `waitUntil()` on the event.\n     *\n     * @param {Request|string} input The request or URL to fetch and cache.\n     * @return {Promise<Response>}\n     */\n    async fetchAndCachePut(input) {\n        const response = await this.fetch(input);\n        const responseClone = response.clone();\n        void this.waitUntil(this.cachePut(input, responseClone));\n        return response;\n    }\n    /**\n     * Matches a request from the cache (and invokes any applicable plugin\n     * callback methods) using the `cacheName`, `matchOptions`, and `plugins`\n     * defined on the strategy object.\n     *\n     * The following plugin lifecycle methods are invoked when using this method:\n     * - cacheKeyWillByUsed()\n     * - cachedResponseWillByUsed()\n     *\n     * @param {Request|string} key The Request or URL to use as the cache key.\n     * @return {Promise<Response|undefined>} A matching response, if found.\n     */\n    async cacheMatch(key) {\n        const request = toRequest(key);\n        let cachedResponse;\n        const { cacheName, matchOptions } = this._strategy;\n        const effectiveRequest = await this.getCacheKey(request, 'read');\n        const multiMatchOptions = Object.assign(Object.assign({}, matchOptions), { cacheName });\n        cachedResponse = await caches.match(effectiveRequest, multiMatchOptions);\n        if (process.env.NODE_ENV !== 'production') {\n            if (cachedResponse) {\n                logger.debug(`Found a cached response in '${cacheName}'.`);\n            }\n            else {\n                logger.debug(`No cached response found in '${cacheName}'.`);\n            }\n        }\n        for (const callback of this.iterateCallbacks('cachedResponseWillBeUsed')) {\n            cachedResponse = (await callback({\n                cacheName,\n                matchOptions,\n                cachedResponse,\n                request: effectiveRequest,\n                event: this.event,\n            })) || undefined;\n        }\n        return cachedResponse;\n    }\n    /**\n     * Puts a request/response pair in the cache (and invokes any applicable\n     * plugin callback methods) using the `cacheName` and `plugins` defined on\n     * the strategy object.\n     *\n     * The following plugin lifecycle methods are invoked when using this method:\n     * - cacheKeyWillByUsed()\n     * - cacheWillUpdate()\n     * - cacheDidUpdate()\n     *\n     * @param {Request|string} key The request or URL to use as the cache key.\n     * @param {Response} response The response to cache.\n     * @return {Promise<boolean>} `false` if a cacheWillUpdate caused the response\n     * not be cached, and `true` otherwise.\n     */\n    async cachePut(key, response) {\n        const request = toRequest(key);\n        // Run in the next task to avoid blocking other cache reads.\n        // https://github.com/w3c/ServiceWorker/issues/1397\n        await timeout(0);\n        const effectiveRequest = await this.getCacheKey(request, 'write');\n        if (process.env.NODE_ENV !== 'production') {\n            if (effectiveRequest.method && effectiveRequest.method !== 'GET') {\n                throw new WorkboxError('attempt-to-cache-non-get-request', {\n                    url: getFriendlyURL(effectiveRequest.url),\n                    method: effectiveRequest.method,\n                });\n            }\n            // See https://github.com/GoogleChrome/workbox/issues/2818\n            const vary = response.headers.get('Vary');\n            if (vary) {\n                logger.debug(`The response for ${getFriendlyURL(effectiveRequest.url)} ` +\n                    `has a 'Vary: ${vary}' header. ` +\n                    `Consider setting the {ignoreVary: true} option on your strategy ` +\n                    `to ensure cache matching and deletion works as expected.`);\n            }\n        }\n        if (!response) {\n            if (process.env.NODE_ENV !== 'production') {\n                logger.error(`Cannot cache non-existent response for ` +\n                    `'${getFriendlyURL(effectiveRequest.url)}'.`);\n            }\n            throw new WorkboxError('cache-put-with-no-response', {\n                url: getFriendlyURL(effectiveRequest.url),\n            });\n        }\n        const responseToCache = await this._ensureResponseSafeToCache(response);\n        if (!responseToCache) {\n            if (process.env.NODE_ENV !== 'production') {\n                logger.debug(`Response '${getFriendlyURL(effectiveRequest.url)}' ` +\n                    `will not be cached.`, responseToCache);\n            }\n            return false;\n        }\n        const { cacheName, matchOptions } = this._strategy;\n        const cache = await self.caches.open(cacheName);\n        const hasCacheUpdateCallback = this.hasCallback('cacheDidUpdate');\n        const oldResponse = hasCacheUpdateCallback ? await cacheMatchIgnoreParams(\n        // TODO(philipwalton): the `__WB_REVISION__` param is a precaching\n        // feature. Consider into ways to only add this behavior if using\n        // precaching.\n        cache, effectiveRequest.clone(), ['__WB_REVISION__'], matchOptions) :\n            null;\n        if (process.env.NODE_ENV !== 'production') {\n            logger.debug(`Updating the '${cacheName}' cache with a new Response ` +\n                `for ${getFriendlyURL(effectiveRequest.url)}.`);\n        }\n        try {\n            await cache.put(effectiveRequest, hasCacheUpdateCallback ?\n                responseToCache.clone() : responseToCache);\n        }\n        catch (error) {\n            if (error instanceof Error) {\n                // See https://developer.mozilla.org/en-US/docs/Web/API/DOMException#exception-QuotaExceededError\n                if (error.name === 'QuotaExceededError') {\n                    await executeQuotaErrorCallbacks();\n                }\n                throw error;\n            }\n        }\n        for (const callback of this.iterateCallbacks('cacheDidUpdate')) {\n            await callback({\n                cacheName,\n                oldResponse,\n                newResponse: responseToCache.clone(),\n                request: effectiveRequest,\n                event: this.event,\n            });\n        }\n        return true;\n    }\n    /**\n     * Checks the list of plugins for the `cacheKeyWillBeUsed` callback, and\n     * executes any of those callbacks found in sequence. The final `Request`\n     * object returned by the last plugin is treated as the cache key for cache\n     * reads and/or writes. If no `cacheKeyWillBeUsed` plugin callbacks have\n     * been registered, the passed request is returned unmodified\n     *\n     * @param {Request} request\n     * @param {string} mode\n     * @return {Promise<Request>}\n     */\n    async getCacheKey(request, mode) {\n        if (!this._cacheKeys[mode]) {\n            let effectiveRequest = request;\n            for (const callback of this.iterateCallbacks('cacheKeyWillBeUsed')) {\n                effectiveRequest = toRequest(await callback({\n                    mode,\n                    request: effectiveRequest,\n                    event: this.event,\n                    // params has a type any can't change right now.\n                    params: this.params,\n                }));\n            }\n            this._cacheKeys[mode] = effectiveRequest;\n        }\n        return this._cacheKeys[mode];\n    }\n    /**\n     * Returns true if the strategy has at least one plugin with the given\n     * callback.\n     *\n     * @param {string} name The name of the callback to check for.\n     * @return {boolean}\n     */\n    hasCallback(name) {\n        for (const plugin of this._strategy.plugins) {\n            if (name in plugin) {\n                return true;\n            }\n        }\n        return false;\n    }\n    /**\n     * Runs all plugin callbacks matching the given name, in order, passing the\n     * given param object (merged ith the current plugin state) as the only\n     * argument.\n     *\n     * Note: since this method runs all plugins, it's not suitable for cases\n     * where the return value of a callback needs to be applied prior to calling\n     * the next callback. See\n     * [`iterateCallbacks()`]{@link module:workbox-strategies.StrategyHandler#iterateCallbacks}\n     * below for how to handle that case.\n     *\n     * @param {string} name The name of the callback to run within each plugin.\n     * @param {Object} param The object to pass as the first (and only) param\n     *     when executing each callback. This object will be merged with the\n     *     current plugin state prior to callback execution.\n     */\n    async runCallbacks(name, param) {\n        for (const callback of this.iterateCallbacks(name)) {\n            // TODO(philipwalton): not sure why `any` is needed. It seems like\n            // this should work with `as WorkboxPluginCallbackParam[C]`.\n            await callback(param);\n        }\n    }\n    /**\n     * Accepts a callback and returns an iterable of matching plugin callbacks,\n     * where each callback is wrapped with the current handler state (i.e. when\n     * you call each callback, whatever object parameter you pass it will\n     * be merged with the plugin's current state).\n     *\n     * @param {string} name The name fo the callback to run\n     * @return {Array<Function>}\n     */\n    *iterateCallbacks(name) {\n        for (const plugin of this._strategy.plugins) {\n            if (typeof plugin[name] === 'function') {\n                const state = this._pluginStateMap.get(plugin);\n                const statefulCallback = (param) => {\n                    const statefulParam = Object.assign(Object.assign({}, param), { state });\n                    // TODO(philipwalton): not sure why `any` is needed. It seems like\n                    // this should work with `as WorkboxPluginCallbackParam[C]`.\n                    return plugin[name](statefulParam);\n                };\n                yield statefulCallback;\n            }\n        }\n    }\n    /**\n     * Adds a promise to the\n     * [extend lifetime promises]{@link https://w3c.github.io/ServiceWorker/#extendableevent-extend-lifetime-promises}\n     * of the event event associated with the request being handled (usually a\n     * `FetchEvent`).\n     *\n     * Note: you can await\n     * [`doneWaiting()`]{@link module:workbox-strategies.StrategyHandler~doneWaiting}\n     * to know when all added promises have settled.\n     *\n     * @param {Promise} promise A promise to add to the extend lifetime promises\n     *     of the event that triggered the request.\n     */\n    waitUntil(promise) {\n        this._extendLifetimePromises.push(promise);\n        return promise;\n    }\n    /**\n     * Returns a promise that resolves once all promises passed to\n     * [`waitUntil()`]{@link module:workbox-strategies.StrategyHandler~waitUntil}\n     * have settled.\n     *\n     * Note: any work done after `doneWaiting()` settles should be manually\n     * passed to an event's `waitUntil()` method (not this handler's\n     * `waitUntil()` method), otherwise the service worker thread my be killed\n     * prior to your work completing.\n     */\n    async doneWaiting() {\n        let promise;\n        while (promise = this._extendLifetimePromises.shift()) {\n            await promise;\n        }\n    }\n    /**\n     * Stops running the strategy and immediately resolves any pending\n     * `waitUntil()` promises.\n     */\n    destroy() {\n        this._handlerDeferred.resolve(null);\n    }\n    /**\n     * This method will call cacheWillUpdate on the available plugins (or use\n     * status === 200) to determine if the Response is safe and valid to cache.\n     *\n     * @param {Request} options.request\n     * @param {Response} options.response\n     * @return {Promise<Response|undefined>}\n     *\n     * @private\n     */\n    async _ensureResponseSafeToCache(response) {\n        let responseToCache = response;\n        let pluginsUsed = false;\n        for (const callback of this.iterateCallbacks('cacheWillUpdate')) {\n            responseToCache = (await callback({\n                request: this.request,\n                response: responseToCache,\n                event: this.event,\n            })) || undefined;\n            pluginsUsed = true;\n            if (!responseToCache) {\n                break;\n            }\n        }\n        if (!pluginsUsed) {\n            if (responseToCache && responseToCache.status !== 200) {\n                responseToCache = undefined;\n            }\n            if (process.env.NODE_ENV !== 'production') {\n                if (responseToCache) {\n                    if (responseToCache.status !== 200) {\n                        if (responseToCache.status === 0) {\n                            logger.warn(`The response for '${this.request.url}' ` +\n                                `is an opaque response. The caching strategy that you're ` +\n                                `using will not cache opaque responses by default.`);\n                        }\n                        else {\n                            logger.debug(`The response for '${this.request.url}' ` +\n                                `returned a status code of '${response.status}' and won't ` +\n                                `be cached as a result.`);\n                        }\n                    }\n                }\n            }\n        }\n        return responseToCache;\n    }\n}\nexport { StrategyHandler };\n", "/*\n  Copyright 2019 Google LLC\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * Returns a promise that resolves and the passed number of milliseconds.\n * This utility is an async/await-friendly version of `setTimeout`.\n *\n * @param {number} ms\n * @return {Promise}\n * @private\n */\nexport function timeout(ms) {\n    return new Promise((resolve) => setTimeout(resolve, ms));\n}\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\nconst getFriendlyURL = (url) => {\n    const urlObj = new URL(String(url), location.href);\n    // See https://github.com/GoogleChrome/workbox/issues/2323\n    // We want to include everything, except for the origin if it's same-origin.\n    return urlObj.href.replace(new RegExp(`^${location.origin}`), '');\n};\nexport { getFriendlyURL };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { logger } from '../_private/logger.js';\nimport { quotaErrorCallbacks } from '../models/quotaErrorCallbacks.js';\nimport '../_version.js';\n/**\n * Runs all of the callback functions, one at a time sequentially, in the order\n * in which they were registered.\n *\n * @memberof module:workbox-core\n * @private\n */\nasync function executeQuotaErrorCallbacks() {\n    if (process.env.NODE_ENV !== 'production') {\n        logger.log(`About to run ${quotaErrorCallbacks.size} ` +\n            `callbacks to clean up caches.`);\n    }\n    for (const callback of quotaErrorCallbacks) {\n        await callback();\n        if (process.env.NODE_ENV !== 'production') {\n            logger.log(callback, 'is complete.');\n        }\n    }\n    if (process.env.NODE_ENV !== 'production') {\n        logger.log('Finished running callbacks.');\n    }\n}\nexport { executeQuotaErrorCallbacks };\n", "/*\n  Copyright 2020 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { copyResponse } from 'workbox-core/copyResponse.js';\nimport { cacheNames } from 'workbox-core/_private/cacheNames.js';\nimport { getFriendlyURL } from 'workbox-core/_private/getFriendlyURL.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { Strategy } from 'workbox-strategies/Strategy.js';\nimport './_version.js';\n/**\n * A [Strategy]{@link module:workbox-strategies.Strategy} implementation\n * specifically designed to work with\n * [PrecacheController]{@link module:workbox-precaching.PrecacheController}\n * to both cache and fetch precached assets.\n *\n * Note: an instance of this class is created automatically when creating a\n * `PrecacheController`; it's generally not necessary to create this yourself.\n *\n * @extends module:workbox-strategies.Strategy\n * @memberof module:workbox-precaching\n */\nclass PrecacheStrategy extends Strategy {\n    /**\n     *\n     * @param {Object} [options]\n     * @param {string} [options.cacheName] Cache name to store and retrieve\n     * requests. Defaults to the cache names provided by\n     * [workbox-core]{@link module:workbox-core.cacheNames}.\n     * @param {Array<Object>} [options.plugins] [Plugins]{@link https://developers.google.com/web/tools/workbox/guides/using-plugins}\n     * to use in conjunction with this caching strategy.\n     * @param {Object} [options.fetchOptions] Values passed along to the\n     * [`init`]{@link https://developer.mozilla.org/en-US/docs/Web/API/WindowOrWorkerGlobalScope/fetch#Parameters}\n     * of all fetch() requests made by this strategy.\n     * @param {Object} [options.matchOptions] The\n     * [`CacheQueryOptions`]{@link https://w3c.github.io/ServiceWorker/#dictdef-cachequeryoptions}\n     * for any `cache.match()` or `cache.put()` calls made by this strategy.\n     * @param {boolean} [options.fallbackToNetwork=true] Whether to attempt to\n     * get the response from the network if there's a precache miss.\n     */\n    constructor(options = {}) {\n        options.cacheName = cacheNames.getPrecacheName(options.cacheName);\n        super(options);\n        this._fallbackToNetwork =\n            options.fallbackToNetwork === false ? false : true;\n        // Redirected responses cannot be used to satisfy a navigation request, so\n        // any redirected response must be \"copied\" rather than cloned, so the new\n        // response doesn't contain the `redirected` flag. See:\n        // https://bugs.chromium.org/p/chromium/issues/detail?id=669363&desc=2#c1\n        this.plugins.push(PrecacheStrategy.copyRedirectedCacheableResponsesPlugin);\n    }\n    /**\n     * @private\n     * @param {Request|string} request A request to run this strategy for.\n     * @param {module:workbox-strategies.StrategyHandler} handler The event that\n     *     triggered the request.\n     * @return {Promise<Response>}\n     */\n    async _handle(request, handler) {\n        const response = await handler.cacheMatch(request);\n        if (response) {\n            return response;\n        }\n        // If this is an `install` event for an entry that isn't already cached,\n        // then populate the cache.\n        if (handler.event && handler.event.type === 'install') {\n            return await this._handleInstall(request, handler);\n        }\n        // Getting here means something went wrong. An entry that should have been\n        // precached wasn't found in the cache.\n        return await this._handleFetch(request, handler);\n    }\n    async _handleFetch(request, handler) {\n        let response;\n        const params = (handler.params || {});\n        // Fall back to the network if we're configured to do so.\n        if (this._fallbackToNetwork) {\n            if (process.env.NODE_ENV !== 'production') {\n                logger.warn(`The precached response for ` +\n                    `${getFriendlyURL(request.url)} in ${this.cacheName} was not ` +\n                    `found. Falling back to the network.`);\n            }\n            const integrityInManifest = params.integrity;\n            const integrityInRequest = request.integrity;\n            const noIntegrityConflict = !integrityInRequest || integrityInRequest === integrityInManifest;\n            response = await handler.fetch(new Request(request, {\n                integrity: integrityInRequest || integrityInManifest,\n            }));\n            // It's only \"safe\" to repair the cache if we're using SRI to guarantee\n            // that the response matches the precache manifest's expectations,\n            // and there's either a) no integrity property in the incoming request\n            // or b) there is an integrity, and it matches the precache manifest.\n            // See https://github.com/GoogleChrome/workbox/issues/2858\n            if (integrityInManifest && noIntegrityConflict) {\n                this._useDefaultCacheabilityPluginIfNeeded();\n                const wasCached = await handler.cachePut(request, response.clone());\n                if (process.env.NODE_ENV !== 'production') {\n                    if (wasCached) {\n                        logger.log(`A response for ${getFriendlyURL(request.url)} ` +\n                            `was used to \"repair\" the precache.`);\n                    }\n                }\n            }\n        }\n        else {\n            // This shouldn't normally happen, but there are edge cases:\n            // https://github.com/GoogleChrome/workbox/issues/1441\n            throw new WorkboxError('missing-precache-entry', {\n                cacheName: this.cacheName,\n                url: request.url,\n            });\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            const cacheKey = params.cacheKey || (await handler.getCacheKey(request, 'read'));\n            // Workbox is going to handle the route.\n            // print the routing details to the console.\n            logger.groupCollapsed(`Precaching is responding to: ` + getFriendlyURL(request.url));\n            logger.log(`Serving the precached url: ${getFriendlyURL(cacheKey instanceof Request ? cacheKey.url : cacheKey)}`);\n            logger.groupCollapsed(`View request details here.`);\n            logger.log(request);\n            logger.groupEnd();\n            logger.groupCollapsed(`View response details here.`);\n            logger.log(response);\n            logger.groupEnd();\n            logger.groupEnd();\n        }\n        return response;\n    }\n    async _handleInstall(request, handler) {\n        this._useDefaultCacheabilityPluginIfNeeded();\n        const response = await handler.fetch(request);\n        // Make sure we defer cachePut() until after we know the response\n        // should be cached; see https://github.com/GoogleChrome/workbox/issues/2737\n        const wasCached = await handler.cachePut(request, response.clone());\n        if (!wasCached) {\n            // Throwing here will lead to the `install` handler failing, which\n            // we want to do if *any* of the responses aren't safe to cache.\n            throw new WorkboxError('bad-precaching-response', {\n                url: request.url,\n                status: response.status,\n            });\n        }\n        return response;\n    }\n    /**\n     * This method is complex, as there a number of things to account for:\n     *\n     * The `plugins` array can be set at construction, and/or it might be added to\n     * to at any time before the strategy is used.\n     *\n     * At the time the strategy is used (i.e. during an `install` event), there\n     * needs to be at least one plugin that implements `cacheWillUpdate` in the\n     * array, other than `copyRedirectedCacheableResponsesPlugin`.\n     *\n     * - If this method is called and there are no suitable `cacheWillUpdate`\n     * plugins, we need to add `defaultPrecacheCacheabilityPlugin`.\n     *\n     * - If this method is called and there is exactly one `cacheWillUpdate`, then\n     * we don't have to do anything (this might be a previously added\n     * `defaultPrecacheCacheabilityPlugin`, or it might be a custom plugin).\n     *\n     * - If this method is called and there is more than one `cacheWillUpdate`,\n     * then we need to check if one is `defaultPrecacheCacheabilityPlugin`. If so,\n     * we need to remove it. (This situation is unlikely, but it could happen if\n     * the strategy is used multiple times, the first without a `cacheWillUpdate`,\n     * and then later on after manually adding a custom `cacheWillUpdate`.)\n     *\n     * See https://github.com/GoogleChrome/workbox/issues/2737 for more context.\n     *\n     * @private\n     */\n    _useDefaultCacheabilityPluginIfNeeded() {\n        let defaultPluginIndex = null;\n        let cacheWillUpdatePluginCount = 0;\n        for (const [index, plugin] of this.plugins.entries()) {\n            // Ignore the copy redirected plugin when determining what to do.\n            if (plugin === PrecacheStrategy.copyRedirectedCacheableResponsesPlugin) {\n                continue;\n            }\n            // Save the default plugin's index, in case it needs to be removed.\n            if (plugin === PrecacheStrategy.defaultPrecacheCacheabilityPlugin) {\n                defaultPluginIndex = index;\n            }\n            if (plugin.cacheWillUpdate) {\n                cacheWillUpdatePluginCount++;\n            }\n        }\n        if (cacheWillUpdatePluginCount === 0) {\n            this.plugins.push(PrecacheStrategy.defaultPrecacheCacheabilityPlugin);\n        }\n        else if (cacheWillUpdatePluginCount > 1 && defaultPluginIndex !== null) {\n            // Only remove the default plugin; multiple custom plugins are allowed.\n            this.plugins.splice(defaultPluginIndex, 1);\n        }\n        // Nothing needs to be done if cacheWillUpdatePluginCount is 1\n    }\n}\nPrecacheStrategy.defaultPrecacheCacheabilityPlugin = {\n    async cacheWillUpdate({ response }) {\n        if (!response || response.status >= 400) {\n            return null;\n        }\n        return response;\n    },\n};\nPrecacheStrategy.copyRedirectedCacheableResponsesPlugin = {\n    async cacheWillUpdate({ response }) {\n        return response.redirected ? await copyResponse(response) : response;\n    },\n};\nexport { PrecacheStrategy };\n", "/*\n  Copyright 2020 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { cacheNames } from 'workbox-core/_private/cacheNames.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { getFriendlyURL } from 'workbox-core/_private/getFriendlyURL.js';\nimport { StrategyHandler } from './StrategyHandler.js';\nimport './_version.js';\n/**\n * An abstract base class that all other strategy classes must extend from:\n *\n * @memberof module:workbox-strategies\n */\nclass Strategy {\n    /**\n     * Creates a new instance of the strategy and sets all documented option\n     * properties as public instance properties.\n     *\n     * Note: if a custom strategy class extends the base Strategy class and does\n     * not need more than these properties, it does not need to define its own\n     * constructor.\n     *\n     * @param {Object} [options]\n     * @param {string} [options.cacheName] Cache name to store and retrieve\n     * requests. Defaults to the cache names provided by\n     * [workbox-core]{@link module:workbox-core.cacheNames}.\n     * @param {Array<Object>} [options.plugins] [Plugins]{@link https://developers.google.com/web/tools/workbox/guides/using-plugins}\n     * to use in conjunction with this caching strategy.\n     * @param {Object} [options.fetchOptions] Values passed along to the\n     * [`init`](https://developer.mozilla.org/en-US/docs/Web/API/WindowOrWorkerGlobalScope/fetch#Parameters)\n     * of [non-navigation](https://github.com/GoogleChrome/workbox/issues/1796)\n     * `fetch()` requests made by this strategy.\n     * @param {Object} [options.matchOptions] The\n     * [`CacheQueryOptions`]{@link https://w3c.github.io/ServiceWorker/#dictdef-cachequeryoptions}\n     * for any `cache.match()` or `cache.put()` calls made by this strategy.\n     */\n    constructor(options = {}) {\n        /**\n         * Cache name to store and retrieve\n         * requests. Defaults to the cache names provided by\n         * [workbox-core]{@link module:workbox-core.cacheNames}.\n         *\n         * @type {string}\n         */\n        this.cacheName = cacheNames.getRuntimeName(options.cacheName);\n        /**\n         * The list\n         * [Plugins]{@link https://developers.google.com/web/tools/workbox/guides/using-plugins}\n         * used by this strategy.\n         *\n         * @type {Array<Object>}\n         */\n        this.plugins = options.plugins || [];\n        /**\n         * Values passed along to the\n         * [`init`]{@link https://developer.mozilla.org/en-US/docs/Web/API/WindowOrWorkerGlobalScope/fetch#Parameters}\n         * of all fetch() requests made by this strategy.\n         *\n         * @type {Object}\n         */\n        this.fetchOptions = options.fetchOptions;\n        /**\n         * The\n         * [`CacheQueryOptions`]{@link https://w3c.github.io/ServiceWorker/#dictdef-cachequeryoptions}\n         * for any `cache.match()` or `cache.put()` calls made by this strategy.\n         *\n         * @type {Object}\n         */\n        this.matchOptions = options.matchOptions;\n    }\n    /**\n     * Perform a request strategy and returns a `Promise` that will resolve with\n     * a `Response`, invoking all relevant plugin callbacks.\n     *\n     * When a strategy instance is registered with a Workbox\n     * [route]{@link module:workbox-routing.Route}, this method is automatically\n     * called when the route matches.\n     *\n     * Alternatively, this method can be used in a standalone `FetchEvent`\n     * listener by passing it to `event.respondWith()`.\n     *\n     * @param {FetchEvent|Object} options A `FetchEvent` or an object with the\n     *     properties listed below.\n     * @param {Request|string} options.request A request to run this strategy for.\n     * @param {ExtendableEvent} options.event The event associated with the\n     *     request.\n     * @param {URL} [options.url]\n     * @param {*} [options.params]\n     */\n    handle(options) {\n        const [responseDone] = this.handleAll(options);\n        return responseDone;\n    }\n    /**\n     * Similar to [`handle()`]{@link module:workbox-strategies.Strategy~handle}, but\n     * instead of just returning a `Promise` that resolves to a `Response` it\n     * it will return an tuple of [response, done] promises, where the former\n     * (`response`) is equivalent to what `handle()` returns, and the latter is a\n     * Promise that will resolve once any promises that were added to\n     * `event.waitUntil()` as part of performing the strategy have completed.\n     *\n     * You can await the `done` promise to ensure any extra work performed by\n     * the strategy (usually caching responses) completes successfully.\n     *\n     * @param {FetchEvent|Object} options A `FetchEvent` or an object with the\n     *     properties listed below.\n     * @param {Request|string} options.request A request to run this strategy for.\n     * @param {ExtendableEvent} options.event The event associated with the\n     *     request.\n     * @param {URL} [options.url]\n     * @param {*} [options.params]\n     * @return {Array<Promise>} A tuple of [response, done]\n     *     promises that can be used to determine when the response resolves as\n     *     well as when the handler has completed all its work.\n     */\n    handleAll(options) {\n        // Allow for flexible options to be passed.\n        if (options instanceof FetchEvent) {\n            options = {\n                event: options,\n                request: options.request,\n            };\n        }\n        const event = options.event;\n        const request = typeof options.request === 'string' ?\n            new Request(options.request) :\n            options.request;\n        const params = 'params' in options ? options.params : undefined;\n        const handler = new StrategyHandler(this, { event, request, params });\n        const responseDone = this._getResponse(handler, request, event);\n        const handlerDone = this._awaitComplete(responseDone, handler, request, event);\n        // Return an array of promises, suitable for use with Promise.all().\n        return [responseDone, handlerDone];\n    }\n    async _getResponse(handler, request, event) {\n        await handler.runCallbacks('handlerWillStart', { event, request });\n        let response = undefined;\n        try {\n            response = await this._handle(request, handler);\n            // The \"official\" Strategy subclasses all throw this error automatically,\n            // but in case a third-party Strategy doesn't, ensure that we have a\n            // consistent failure when there's no response or an error response.\n            if (!response || response.type === 'error') {\n                throw new WorkboxError('no-response', { url: request.url });\n            }\n        }\n        catch (error) {\n            if (error instanceof Error) {\n                for (const callback of handler.iterateCallbacks('handlerDidError')) {\n                    response = await callback({ error, event, request });\n                    if (response) {\n                        break;\n                    }\n                }\n            }\n            if (!response) {\n                throw error;\n            }\n            else if (process.env.NODE_ENV !== 'production') {\n                logger.log(`While responding to '${getFriendlyURL(request.url)}', ` +\n                    `an ${error instanceof Error ? error.toString() : ''} error occurred. Using a fallback response provided by ` +\n                    `a handlerDidError plugin.`);\n            }\n        }\n        for (const callback of handler.iterateCallbacks('handlerWillRespond')) {\n            response = await callback({ event, request, response });\n        }\n        return response;\n    }\n    async _awaitComplete(responseDone, handler, request, event) {\n        let response;\n        let error;\n        try {\n            response = await responseDone;\n        }\n        catch (error) {\n            // Ignore errors, as response errors should be caught via the `response`\n            // promise above. The `done` promise will only throw for errors in\n            // promises passed to `handler.waitUntil()`.\n        }\n        try {\n            await handler.runCallbacks('handlerDidRespond', {\n                event,\n                request,\n                response,\n            });\n            await handler.doneWaiting();\n        }\n        catch (waitUntilError) {\n            if (waitUntilError instanceof Error) {\n                error = waitUntilError;\n            }\n        }\n        await handler.runCallbacks('handlerDidComplete', {\n            event,\n            request,\n            response,\n            error: error,\n        });\n        handler.destroy();\n        if (error) {\n            throw error;\n        }\n    }\n}\nexport { Strategy };\n/**\n * Classes extending the `Strategy` based class should implement this method,\n * and leverage the [`handler`]{@link module:workbox-strategies.StrategyHandler}\n * arg to perform all fetching and cache logic, which will ensure all relevant\n * cache, cache options, fetch options and plugins are used (per the current\n * strategy instance).\n *\n * @name _handle\n * @instance\n * @abstract\n * @function\n * @param {Request} request\n * @param {module:workbox-strategies.StrategyHandler} handler\n * @return {Promise<Response>}\n *\n * @memberof module:workbox-strategies.Strategy\n */\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { cacheNames } from 'workbox-core/_private/cacheNames.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { waitUntil } from 'workbox-core/_private/waitUntil.js';\nimport { createCacheKey } from './utils/createCacheKey.js';\nimport { PrecacheInstallReportPlugin } from './utils/PrecacheInstallReportPlugin.js';\nimport { PrecacheCacheKeyPlugin } from './utils/PrecacheCacheKeyPlugin.js';\nimport { printCleanupDetails } from './utils/printCleanupDetails.js';\nimport { printInstallDetails } from './utils/printInstallDetails.js';\nimport { PrecacheStrategy } from './PrecacheStrategy.js';\nimport './_version.js';\n/**\n * Performs efficient precaching of assets.\n *\n * @memberof module:workbox-precaching\n */\nclass PrecacheController {\n    /**\n     * Create a new PrecacheController.\n     *\n     * @param {Object} [options]\n     * @param {string} [options.cacheName] The cache to use for precaching.\n     * @param {string} [options.plugins] Plugins to use when precaching as well\n     * as responding to fetch events for precached assets.\n     * @param {boolean} [options.fallbackToNetwork=true] Whether to attempt to\n     * get the response from the network if there's a precache miss.\n     */\n    constructor({ cacheName, plugins = [], fallbackToNetwork = true } = {}) {\n        this._urlsToCacheKeys = new Map();\n        this._urlsToCacheModes = new Map();\n        this._cacheKeysToIntegrities = new Map();\n        this._strategy = new PrecacheStrategy({\n            cacheName: cacheNames.getPrecacheName(cacheName),\n            plugins: [\n                ...plugins,\n                new PrecacheCacheKeyPlugin({ precacheController: this }),\n            ],\n            fallbackToNetwork,\n        });\n        // Bind the install and activate methods to the instance.\n        this.install = this.install.bind(this);\n        this.activate = this.activate.bind(this);\n    }\n    /**\n     * @type {module:workbox-precaching.PrecacheStrategy} The strategy created by this controller and\n     * used to cache assets and respond to fetch events.\n     */\n    get strategy() {\n        return this._strategy;\n    }\n    /**\n     * Adds items to the precache list, removing any duplicates and\n     * stores the files in the\n     * [\"precache cache\"]{@link module:workbox-core.cacheNames} when the service\n     * worker installs.\n     *\n     * This method can be called multiple times.\n     *\n     * @param {Array<Object|string>} [entries=[]] Array of entries to precache.\n     */\n    precache(entries) {\n        this.addToCacheList(entries);\n        if (!this._installAndActiveListenersAdded) {\n            self.addEventListener('install', this.install);\n            self.addEventListener('activate', this.activate);\n            this._installAndActiveListenersAdded = true;\n        }\n    }\n    /**\n     * This method will add items to the precache list, removing duplicates\n     * and ensuring the information is valid.\n     *\n     * @param {Array<module:workbox-precaching.PrecacheController.PrecacheEntry|string>} entries\n     *     Array of entries to precache.\n     */\n    addToCacheList(entries) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isArray(entries, {\n                moduleName: 'workbox-precaching',\n                className: 'PrecacheController',\n                funcName: 'addToCacheList',\n                paramName: 'entries',\n            });\n        }\n        const urlsToWarnAbout = [];\n        for (const entry of entries) {\n            // See https://github.com/GoogleChrome/workbox/issues/2259\n            if (typeof entry === 'string') {\n                urlsToWarnAbout.push(entry);\n            }\n            else if (entry && entry.revision === undefined) {\n                urlsToWarnAbout.push(entry.url);\n            }\n            const { cacheKey, url } = createCacheKey(entry);\n            const cacheMode = (typeof entry !== 'string' && entry.revision) ?\n                'reload' : 'default';\n            if (this._urlsToCacheKeys.has(url) &&\n                this._urlsToCacheKeys.get(url) !== cacheKey) {\n                throw new WorkboxError('add-to-cache-list-conflicting-entries', {\n                    firstEntry: this._urlsToCacheKeys.get(url),\n                    secondEntry: cacheKey,\n                });\n            }\n            if (typeof entry !== 'string' && entry.integrity) {\n                if (this._cacheKeysToIntegrities.has(cacheKey) &&\n                    this._cacheKeysToIntegrities.get(cacheKey) !== entry.integrity) {\n                    throw new WorkboxError('add-to-cache-list-conflicting-integrities', {\n                        url,\n                    });\n                }\n                this._cacheKeysToIntegrities.set(cacheKey, entry.integrity);\n            }\n            this._urlsToCacheKeys.set(url, cacheKey);\n            this._urlsToCacheModes.set(url, cacheMode);\n            if (urlsToWarnAbout.length > 0) {\n                const warningMessage = `Workbox is precaching URLs without revision ` +\n                    `info: ${urlsToWarnAbout.join(', ')}\\nThis is generally NOT safe. ` +\n                    `Learn more at https://bit.ly/wb-precache`;\n                if (process.env.NODE_ENV === 'production') {\n                    // Use console directly to display this warning without bloating\n                    // bundle sizes by pulling in all of the logger codebase in prod.\n                    console.warn(warningMessage);\n                }\n                else {\n                    logger.warn(warningMessage);\n                }\n            }\n        }\n    }\n    /**\n     * Precaches new and updated assets. Call this method from the service worker\n     * install event.\n     *\n     * Note: this method calls `event.waitUntil()` for you, so you do not need\n     * to call it yourself in your event handlers.\n     *\n     * @param {ExtendableEvent} event\n     * @return {Promise<module:workbox-precaching.InstallResult>}\n     */\n    install(event) {\n        // waitUntil returns Promise<any>\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-return\n        return waitUntil(event, async () => {\n            const installReportPlugin = new PrecacheInstallReportPlugin();\n            this.strategy.plugins.push(installReportPlugin);\n            // Cache entries one at a time.\n            // See https://github.com/GoogleChrome/workbox/issues/2528\n            for (const [url, cacheKey] of this._urlsToCacheKeys) {\n                const integrity = this._cacheKeysToIntegrities.get(cacheKey);\n                const cacheMode = this._urlsToCacheModes.get(url);\n                const request = new Request(url, {\n                    integrity,\n                    cache: cacheMode,\n                    credentials: 'same-origin',\n                });\n                await Promise.all(this.strategy.handleAll({\n                    params: { cacheKey },\n                    request,\n                    event,\n                }));\n            }\n            const { updatedURLs, notUpdatedURLs } = installReportPlugin;\n            if (process.env.NODE_ENV !== 'production') {\n                printInstallDetails(updatedURLs, notUpdatedURLs);\n            }\n            return { updatedURLs, notUpdatedURLs };\n        });\n    }\n    /**\n     * Deletes assets that are no longer present in the current precache manifest.\n     * Call this method from the service worker activate event.\n     *\n     * Note: this method calls `event.waitUntil()` for you, so you do not need\n     * to call it yourself in your event handlers.\n     *\n     * @param {ExtendableEvent} event\n     * @return {Promise<module:workbox-precaching.CleanupResult>}\n     */\n    activate(event) {\n        // waitUntil returns Promise<any>\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-return\n        return waitUntil(event, async () => {\n            const cache = await self.caches.open(this.strategy.cacheName);\n            const currentlyCachedRequests = await cache.keys();\n            const expectedCacheKeys = new Set(this._urlsToCacheKeys.values());\n            const deletedURLs = [];\n            for (const request of currentlyCachedRequests) {\n                if (!expectedCacheKeys.has(request.url)) {\n                    await cache.delete(request);\n                    deletedURLs.push(request.url);\n                }\n            }\n            if (process.env.NODE_ENV !== 'production') {\n                printCleanupDetails(deletedURLs);\n            }\n            return { deletedURLs };\n        });\n    }\n    /**\n     * Returns a mapping of a precached URL to the corresponding cache key, taking\n     * into account the revision information for the URL.\n     *\n     * @return {Map<string, string>} A URL to cache key mapping.\n     */\n    getURLsToCacheKeys() {\n        return this._urlsToCacheKeys;\n    }\n    /**\n     * Returns a list of all the URLs that have been precached by the current\n     * service worker.\n     *\n     * @return {Array<string>} The precached URLs.\n     */\n    getCachedURLs() {\n        return [...this._urlsToCacheKeys.keys()];\n    }\n    /**\n     * Returns the cache key used for storing a given URL. If that URL is\n     * unversioned, like `/index.html', then the cache key will be the original\n     * URL with a search parameter appended to it.\n     *\n     * @param {string} url A URL whose cache key you want to look up.\n     * @return {string} The versioned URL that corresponds to a cache key\n     * for the original URL, or undefined if that URL isn't precached.\n     */\n    getCacheKeyForURL(url) {\n        const urlObject = new URL(url, location.href);\n        return this._urlsToCacheKeys.get(urlObject.href);\n    }\n    /**\n     * @param {string} url A cache key whose SRI you want to look up.\n     * @return {string} The subresource integrity associated with the cache key,\n     * or undefined if it's not set.\n     */\n    getIntegrityForCacheKey(cacheKey) {\n        return this._cacheKeysToIntegrities.get(cacheKey);\n    }\n    /**\n     * This acts as a drop-in replacement for\n     * [`cache.match()`](https://developer.mozilla.org/en-US/docs/Web/API/Cache/match)\n     * with the following differences:\n     *\n     * - It knows what the name of the precache is, and only checks in that cache.\n     * - It allows you to pass in an \"original\" URL without versioning parameters,\n     * and it will automatically look up the correct cache key for the currently\n     * active revision of that URL.\n     *\n     * E.g., `matchPrecache('index.html')` will find the correct precached\n     * response for the currently active service worker, even if the actual cache\n     * key is `'/index.html?__WB_REVISION__=1234abcd'`.\n     *\n     * @param {string|Request} request The key (without revisioning parameters)\n     * to look up in the precache.\n     * @return {Promise<Response|undefined>}\n     */\n    async matchPrecache(request) {\n        const url = request instanceof Request ? request.url : request;\n        const cacheKey = this.getCacheKeyForURL(url);\n        if (cacheKey) {\n            const cache = await self.caches.open(this.strategy.cacheName);\n            return cache.match(cacheKey);\n        }\n        return undefined;\n    }\n    /**\n     * Returns a function that looks up `url` in the precache (taking into\n     * account revision information), and returns the corresponding `Response`.\n     *\n     * @param {string} url The precached URL which will be used to lookup the\n     * `Response`.\n     * @return {module:workbox-routing~handlerCallback}\n     */\n    createHandlerBoundToURL(url) {\n        const cacheKey = this.getCacheKeyForURL(url);\n        if (!cacheKey) {\n            throw new WorkboxError('non-precached-url', { url });\n        }\n        return (options) => {\n            options.request = new Request(url);\n            options.params = Object.assign({ cacheKey }, options.params);\n            return this.strategy.handle(options);\n        };\n    }\n}\nexport { PrecacheController };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { PrecacheController } from '../PrecacheController.js';\nimport '../_version.js';\nlet precacheController;\n/**\n * @return {PrecacheController}\n * @private\n */\nexport const getOrCreatePrecacheController = () => {\n    if (!precacheController) {\n        precacheController = new PrecacheController();\n    }\n    return precacheController;\n};\n", "/*\n  Copyright 2020 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { getFriendlyURL } from 'workbox-core/_private/getFriendlyURL.js';\nimport { Route } from 'workbox-routing/Route.js';\nimport { generateURLVariations } from './utils/generateURLVariations.js';\nimport './_version.js';\n/**\n * A subclass of [Route]{@link module:workbox-routing.Route} that takes a\n * [PrecacheController]{@link module:workbox-precaching.PrecacheController}\n * instance and uses it to match incoming requests and handle fetching\n * responses from the precache.\n *\n * @memberof module:workbox-precaching\n * @extends module:workbox-routing.Route\n */\nclass PrecacheRoute extends Route {\n    /**\n     * @param {PrecacheController} precacheController A `PrecacheController`\n     * instance used to both match requests and respond to fetch events.\n     * @param {Object} [options] Options to control how requests are matched\n     * against the list of precached URLs.\n     * @param {string} [options.directoryIndex=index.html] The `directoryIndex` will\n     * check cache entries for a URLs ending with '/' to see if there is a hit when\n     * appending the `directoryIndex` value.\n     * @param {Array<RegExp>} [options.ignoreURLParametersMatching=[/^utm_/, /^fbclid$/]] An\n     * array of regex's to remove search params when looking for a cache match.\n     * @param {boolean} [options.cleanURLs=true] The `cleanURLs` option will\n     * check the cache for the URL with a `.html` added to the end of the end.\n     * @param {module:workbox-precaching~urlManipulation} [options.urlManipulation]\n     * This is a function that should take a URL and return an array of\n     * alternative URLs that should be checked for precache matches.\n     */\n    constructor(precacheController, options) {\n        const match = ({ request }) => {\n            const urlsToCacheKeys = precacheController.getURLsToCacheKeys();\n            for (const possibleURL of generateURLVariations(request.url, options)) {\n                const cacheKey = urlsToCacheKeys.get(possibleURL);\n                if (cacheKey) {\n                    const integrity = precacheController.getIntegrityForCacheKey(cacheKey);\n                    return { cacheKey, integrity };\n                }\n            }\n            if (process.env.NODE_ENV !== 'production') {\n                logger.debug(`Precaching did not find a match for ` +\n                    getFriendlyURL(request.url));\n            }\n            return;\n        };\n        super(match, precacheController.strategy);\n    }\n}\nexport { PrecacheRoute };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { removeIgnoredSearchParams } from './removeIgnoredSearchParams.js';\nimport '../_version.js';\n/**\n * Generator function that yields possible variations on the original URL to\n * check, one at a time.\n *\n * @param {string} url\n * @param {Object} options\n *\n * @private\n * @memberof module:workbox-precaching\n */\nexport function* generateURLVariations(url, { ignoreURLParametersMatching = [/^utm_/, /^fbclid$/], directoryIndex = 'index.html', cleanURLs = true, urlManipulation, } = {}) {\n    const urlObject = new URL(url, location.href);\n    urlObject.hash = '';\n    yield urlObject.href;\n    const urlWithoutIgnoredParams = removeIgnoredSearchParams(urlObject, ignoreURLParametersMatching);\n    yield urlWithoutIgnoredParams.href;\n    if (directoryIndex && urlWithoutIgnoredParams.pathname.endsWith('/')) {\n        const directoryURL = new URL(urlWithoutIgnoredParams.href);\n        directoryURL.pathname += directoryIndex;\n        yield directoryURL.href;\n    }\n    if (cleanURLs) {\n        const cleanURL = new URL(urlWithoutIgnoredParams.href);\n        cleanURL.pathname += '.html';\n        yield cleanURL.href;\n    }\n    if (urlManipulation) {\n        const additionalURLs = urlManipulation({ url: urlObject });\n        for (const urlToAttempt of additionalURLs) {\n            yield urlToAttempt.href;\n        }\n    }\n}\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * Removes any URL search parameters that should be ignored.\n *\n * @param {URL} urlObject The original URL.\n * @param {Array<RegExp>} ignoreURLParametersMatching RegExps to test against\n * each search parameter name. Matches mean that the search parameter should be\n * ignored.\n * @return {URL} The URL with any ignored search parameters removed.\n *\n * @private\n * @memberof module:workbox-precaching\n */\nexport function removeIgnoredSearchParams(urlObject, ignoreURLParametersMatching = []) {\n    // Convert the iterable into an array at the start of the loop to make sure\n    // deletion doesn't mess up iteration.\n    for (const paramName of [...urlObject.searchParams.keys()]) {\n        if (ignoreURLParametersMatching.some((regExp) => regExp.test(paramName))) {\n            urlObject.searchParams.delete(paramName);\n        }\n    }\n    return urlObject;\n}\n", "/*\n  Copyright 2019 Google LLC\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { registerRoute } from 'workbox-routing/registerRoute.js';\nimport { getOrCreatePrecacheController } from './utils/getOrCreatePrecacheController.js';\nimport { PrecacheRoute } from './PrecacheRoute.js';\nimport './_version.js';\n/**\n * Add a `fetch` listener to the service worker that will\n * respond to\n * [network requests]{@link https://developer.mozilla.org/en-US/docs/Web/API/Service_Worker_API/Using_Service_Workers#Custom_responses_to_requests}\n * with precached assets.\n *\n * Requests for assets that aren't precached, the `FetchEvent` will not be\n * responded to, allowing the event to fall through to other `fetch` event\n * listeners.\n *\n * @param {Object} [options] See\n * [PrecacheRoute options]{@link module:workbox-precaching.PrecacheRoute}.\n *\n * @memberof module:workbox-precaching\n */\nfunction addRoute(options) {\n    const precacheController = getOrCreatePrecacheController();\n    const precacheRoute = new PrecacheRoute(precacheController, options);\n    registerRoute(precacheRoute);\n}\nexport { addRoute };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { Route } from './Route.js';\nimport { RegExpRoute } from './RegExpRoute.js';\nimport { getOrCreateDefaultRouter } from './utils/getOrCreateDefaultRouter.js';\nimport './_version.js';\n/**\n * Easily register a RegExp, string, or function with a caching\n * strategy to a singleton Router instance.\n *\n * This method will generate a Route for you if needed and\n * call [registerRoute()]{@link module:workbox-routing.Router#registerRoute}.\n *\n * @param {RegExp|string|module:workbox-routing.Route~matchCallback|module:workbox-routing.Route} capture\n * If the capture param is a `Route`, all other arguments will be ignored.\n * @param {module:workbox-routing~handlerCallback} [handler] A callback\n * function that returns a Promise resulting in a Response. This parameter\n * is required if `capture` is not a `Route` object.\n * @param {string} [method='GET'] The HTTP method to match the Route\n * against.\n * @return {module:workbox-routing.Route} The generated `Route`(Useful for\n * unregistering).\n *\n * @memberof module:workbox-routing\n */\nfunction registerRoute(capture, handler, method) {\n    let route;\n    if (typeof capture === 'string') {\n        const captureUrl = new URL(capture, location.href);\n        if (process.env.NODE_ENV !== 'production') {\n            if (!(capture.startsWith('/') || capture.startsWith('http'))) {\n                throw new WorkboxError('invalid-string', {\n                    moduleName: 'workbox-routing',\n                    funcName: 'registerRoute',\n                    paramName: 'capture',\n                });\n            }\n            // We want to check if Express-style wildcards are in the pathname only.\n            // TODO: Remove this log message in v4.\n            const valueToCheck = capture.startsWith('http') ?\n                captureUrl.pathname : capture;\n            // See https://github.com/pillarjs/path-to-regexp#parameters\n            const wildcards = '[*:?+]';\n            if ((new RegExp(`${wildcards}`)).exec(valueToCheck)) {\n                logger.debug(`The '$capture' parameter contains an Express-style wildcard ` +\n                    `character (${wildcards}). Strings are now always interpreted as ` +\n                    `exact matches; use a RegExp for partial or wildcard matches.`);\n            }\n        }\n        const matchCallback = ({ url }) => {\n            if (process.env.NODE_ENV !== 'production') {\n                if ((url.pathname === captureUrl.pathname) &&\n                    (url.origin !== captureUrl.origin)) {\n                    logger.debug(`${capture} only partially matches the cross-origin URL ` +\n                        `${url.toString()}. This route will only handle cross-origin requests ` +\n                        `if they match the entire URL.`);\n                }\n            }\n            return url.href === captureUrl.href;\n        };\n        // If `capture` is a string then `handler` and `method` must be present.\n        route = new Route(matchCallback, handler, method);\n    }\n    else if (capture instanceof RegExp) {\n        // If `capture` is a `RegExp` then `handler` and `method` must be present.\n        route = new RegExpRoute(capture, handler, method);\n    }\n    else if (typeof capture === 'function') {\n        // If `capture` is a function then `handler` and `method` must be present.\n        route = new Route(capture, handler, method);\n    }\n    else if (capture instanceof Route) {\n        route = capture;\n    }\n    else {\n        throw new WorkboxError('unsupported-route-type', {\n            moduleName: 'workbox-routing',\n            funcName: 'registerRoute',\n            paramName: 'capture',\n        });\n    }\n    const defaultRouter = getOrCreateDefaultRouter();\n    defaultRouter.registerRoute(route);\n    return route;\n}\nexport { registerRoute };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { addRoute } from './addRoute.js';\nimport { precache } from './precache.js';\nimport './_version.js';\n/**\n * This method will add entries to the precache list and add a route to\n * respond to fetch events.\n *\n * This is a convenience method that will call\n * [precache()]{@link module:workbox-precaching.precache} and\n * [addRoute()]{@link module:workbox-precaching.addRoute} in a single call.\n *\n * @param {Array<Object|string>} entries Array of entries to precache.\n * @param {Object} [options] See\n * [PrecacheRoute options]{@link module:workbox-precaching.PrecacheRoute}.\n *\n * @memberof module:workbox-precaching\n */\nfunction precacheAndRoute(entries, options) {\n    precache(entries);\n    addRoute(options);\n}\nexport { precacheAndRoute };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { getOrCreatePrecacheController } from './utils/getOrCreatePrecacheController.js';\nimport './_version.js';\n/**\n * Adds items to the precache list, removing any duplicates and\n * stores the files in the\n * [\"precache cache\"]{@link module:workbox-core.cacheNames} when the service\n * worker installs.\n *\n * This method can be called multiple times.\n *\n * Please note: This method **will not** serve any of the cached files for you.\n * It only precaches files. To respond to a network request you call\n * [addRoute()]{@link module:workbox-precaching.addRoute}.\n *\n * If you have a single array of files to precache, you can just call\n * [precacheAndRoute()]{@link module:workbox-precaching.precacheAndRoute}.\n *\n * @param {Array<Object|string>} [entries=[]] Array of entries to precache.\n *\n * @memberof module:workbox-precaching\n */\nfunction precache(entries) {\n    const precacheController = getOrCreatePrecacheController();\n    precacheController.precache(entries);\n}\nexport { precache };\n"], "names": ["self", "_", "e", "messageGenerator", "code", "args", "msg", "length", "JSON", "stringify", "WorkboxError", "Error", "constructor", "errorCode", "details", "super", "this", "name", "normalize<PERSON><PERSON><PERSON>", "handler", "handle", "Route", "match", "method", "setCatchHandler", "<PERSON><PERSON><PERSON><PERSON>", "RegExpRoute", "regExp", "url", "result", "exec", "href", "origin", "location", "index", "slice", "Router", "_routes", "Map", "_defaultHandlerMap", "routes", "addFetchListener", "addEventListener", "event", "request", "responsePromise", "handleRequest", "respondWith", "addCacheListener", "data", "type", "payload", "requestPromises", "Promise", "all", "urlsToCache", "map", "entry", "Request", "waitUntil", "ports", "then", "postMessage", "URL", "protocol", "startsWith", "<PERSON><PERSON><PERSON><PERSON>", "params", "route", "findMatchingRoute", "has", "get", "err", "reject", "_catch<PERSON><PERSON>ler", "catch", "async", "catchErr", "matchResult", "Array", "isArray", "Object", "keys", "undefined", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "set", "registerRoute", "push", "unregisterRoute", "routeIndex", "indexOf", "splice", "defaultRouter", "_cacheNameDetails", "googleAnalytics", "precache", "prefix", "runtime", "suffix", "registration", "scope", "_createCacheName", "cacheName", "filter", "value", "join", "cacheNames", "userCacheName", "asyncFn", "returnPromise", "createCacheKey", "urlObject", "cache<PERSON>ey", "revision", "cacheKeyURL", "originalURL", "searchParams", "PrecacheInstallReportPlugin", "updatedURLs", "notUpdatedURLs", "handlerWillStart", "state", "originalRequest", "cachedResponseWillBeUsed", "cachedResponse", "PrecacheCacheKeyPlugin", "precacheController", "cacheKeyWillBeUsed", "_precacheController", "getCacheKeyForURL", "headers", "supportStatus", "copyResponse", "response", "modifier", "clonedResponse", "clone", "responseInit", "Headers", "status", "statusText", "modifiedResponseInit", "body", "testResponse", "Response", "error", "canConstructResponseFromBodyStream", "blob", "stripParams", "fullURL", "ignoreParams", "strippedURL", "param", "delete", "Deferred", "promise", "resolve", "quotaErrorCallbacks", "Set", "toRequest", "input", "StrategyHandler", "strategy", "options", "_cacheKeys", "assign", "_strategy", "_handler<PERSON><PERSON><PERSON><PERSON>", "_extendLifetimePromises", "_plugins", "plugins", "_pluginStateMap", "plugin", "mode", "FetchEvent", "preloadResponse", "possiblePreloadResponse", "<PERSON><PERSON><PERSON><PERSON>", "cb", "iterateCallbacks", "thrownErrorMessage", "message", "pluginFilteredRequest", "fetchResponse", "fetch", "fetchOptions", "callback", "runCallbacks", "responseClone", "cachePut", "key", "matchOptions", "effectiveRequest", "get<PERSON><PERSON><PERSON><PERSON>", "multiMatchOptions", "caches", "ms", "setTimeout", "String", "replace", "RegExp", "responseToCache", "_ensureResponseSafeToCache", "cache", "open", "hasCacheUpdateCallback", "oldResponse", "strippedRequestURL", "keysOptions", "ignoreSearch", "cacheKeys", "cacheMatchIgnoreParams", "put", "executeQuotaErrorCallbacks", "newResponse", "stateful<PERSON><PERSON><PERSON>", "statefulParam", "shift", "destroy", "pluginsUsed", "PrecacheStrategy", "responseDone", "handleAll", "_getResponse", "_awaitComplete", "_handle", "doneWaiting", "waitUntilError", "_fallbackToNetwork", "fallbackToNetwork", "copyRedirectedCacheableResponsesPlugin", "cacheMatch", "_handleInstall", "_handleFetch", "integrityInManifest", "integrity", "integrityInRequest", "noIntegrityConflict", "_useDefaultCacheabilityPluginIfNeeded", "defaultPluginIndex", "cacheWillUpdatePluginCount", "entries", "defaultPrecacheCacheabilityPlugin", "cacheWillUpdate", "redirected", "PrecacheController", "_urlsTo<PERSON><PERSON><PERSON><PERSON><PERSON>", "_urlsToCacheModes", "_cacheKeysToIntegrities", "install", "bind", "activate", "addToCacheList", "_installAndActiveListenersAdded", "urlsToWarnAbout", "cacheMode", "firstEntry", "secondEntry", "warningMessage", "console", "warn", "installReportPlugin", "credentials", "currentlyCachedRequests", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "values", "deletedURLs", "getURLsToCacheKeys", "getCachedURLs", "getIntegrityForCacheKey", "createHandlerBoundToURL", "getOrCreatePrecacheController", "PrecacheRoute", "urlsTo<PERSON>ache<PERSON><PERSON>s", "possibleURL", "ignoreURLParametersMatching", "directoryIndex", "cleanURLs", "urlManipulation", "hash", "urlWithoutIgnoredParams", "paramName", "some", "test", "removeIgnoredSearchParams", "pathname", "endsWith", "directoryURL", "cleanURL", "additionalURLs", "urlToAttempt", "generateURLVariations", "addRoute", "capture", "captureUrl", "moduleName", "funcName"], "mappings": "qEAEA,IACIA,KAAK,uBAAyBC,IAElC,MAAOC,ICEP,MCgBaC,EAdI,CAACC,KAASC,KACvB,IAAIC,EAAMF,EAIV,OAHIC,EAAKE,OAAS,IACdD,GAAQ,OAAME,KAAKC,UAAUJ,MAE1BC,GCIX,MAAMI,UAAqBC,MASvBC,YAAYC,EAAWC,GAEnBC,MADgBZ,EAAiBU,EAAWC,IAE5CE,KAAKC,KAAOJ,EACZG,KAAKF,QAAUA,GC7BvB,IACId,KAAK,0BAA4BC,IAErC,MAAOC,ICWA,MCAMgB,EAAoBC,GACzBA,GAA8B,iBAAZA,EASXA,EAWA,CAAEC,OAAQD,GCjBzB,MAAME,EAYFT,YAAYU,EAAOH,EAASI,EFhBH,OE8BrBP,KAAKG,QAAUD,EAAiBC,GAChCH,KAAKM,MAAQA,EACbN,KAAKO,OAASA,EAOlBC,gBAAgBL,GACZH,KAAKS,aAAeP,EAAiBC,IChC7C,MAAMO,UAAoBL,EActBT,YAAYe,EAAQR,EAASI,GAiCzBR,OAxBc,EAAGa,UACb,MAAMC,EAASF,EAAOG,KAAKF,EAAIG,MAE/B,GAAKF,IAOAD,EAAII,SAAWC,SAASD,QAA6B,IAAjBH,EAAOK,OAYhD,OAAOL,EAAOM,MAAM,KAEXhB,EAASI,ICxC9B,MAAMa,EAIFxB,cACII,KAAKqB,EAAU,IAAIC,IACnBtB,KAAKuB,EAAqB,IAAID,IAO9BE,aACA,OAAOxB,KAAKqB,EAMhBI,mBAEIzC,KAAK0C,iBAAiB,SAAWC,IAC7B,MAAMC,QAAEA,GAAYD,EACdE,EAAkB7B,KAAK8B,cAAc,CAAEF,UAASD,UAClDE,GACAF,EAAMI,YAAYF,MA0B9BG,mBAEIhD,KAAK0C,iBAAiB,WAAaC,IAE/B,GAAIA,EAAMM,MAA4B,eAApBN,EAAMM,KAAKC,KAAuB,CAChD,MAAMC,QAAEA,GAAYR,EAAMM,KAIpBG,EAAkBC,QAAQC,IAAIH,EAAQI,YAAYC,KAAKC,IACpC,iBAAVA,IACPA,EAAQ,CAACA,IAEb,MAAMb,EAAU,IAAIc,WAAWD,GAC/B,OAAOzC,KAAK8B,cAAc,CAAEF,UAASD,cAKzCA,EAAMgB,UAAUP,GAEZT,EAAMiB,OAASjB,EAAMiB,MAAM,IACtBR,EAAgBS,MAAK,IAAMlB,EAAMiB,MAAM,GAAGE,aAAY,SAiB3EhB,eAAcF,QAAEA,EAAFD,MAAWA,IASrB,MAAMf,EAAM,IAAImC,IAAInB,EAAQhB,IAAKK,SAASF,MAC1C,IAAKH,EAAIoC,SAASC,WAAW,QAIzB,OAEJ,MAAMC,EAAatC,EAAII,SAAWC,SAASD,QACrCmC,OAAEA,EAAFC,MAAUA,GAAUpD,KAAKqD,kBAAkB,CAC7C1B,QACAC,UACAsB,aACAtC,QAEJ,IAAIT,EAAUiD,GAASA,EAAMjD,QAgB7B,MAAMI,EAASqB,EAAQrB,OAQvB,IAPKJ,GAAWH,KAAKuB,EAAmB+B,IAAI/C,KAKxCJ,EAAUH,KAAKuB,EAAmBgC,IAAIhD,KAErCJ,EAMD,OAkBJ,IAAI0B,EACJ,IACIA,EAAkB1B,EAAQC,OAAO,CAAEQ,MAAKgB,UAASD,QAAOwB,WAE5D,MAAOK,GACH3B,EAAkBQ,QAAQoB,OAAOD,GAGrC,MAAM/C,EAAe2C,GAASA,EAAM3C,aAsCpC,OArCIoB,aAA2BQ,UAAYrC,KAAK0D,GAAiBjD,KAC7DoB,EAAkBA,EAAgB8B,OAAMC,UAEpC,GAAInD,EAUA,IACI,aAAaA,EAAaL,OAAO,CAAEQ,MAAKgB,UAASD,QAAOwB,WAE5D,MAAOU,GACCA,aAAoBlE,QACpB6D,EAAMK,GAIlB,GAAI7D,KAAK0D,EAUL,OAAO1D,KAAK0D,EAActD,OAAO,CAAEQ,MAAKgB,UAASD,UAErD,MAAM6B,MAGP3B,EAiBXwB,mBAAkBzC,IAAEA,EAAFsC,WAAOA,EAAPtB,QAAmBA,EAAnBD,MAA4BA,IAC1C,MAAMH,EAASxB,KAAKqB,EAAQkC,IAAI3B,EAAQrB,SAAW,GACnD,IAAK,MAAM6C,KAAS5B,EAAQ,CACxB,IAAI2B,EAGJ,MAAMW,EAAcV,EAAM9C,MAAM,CAAEM,MAAKsC,aAAYtB,UAASD,UAC5D,GAAImC,EA6BA,OAjBAX,EAASW,GACLC,MAAMC,QAAQb,IAA6B,IAAlBA,EAAO5D,QAI1BuE,EAAYlE,cAAgBqE,QACE,IAApCA,OAAOC,KAAKJ,GAAavE,QAIG,kBAAhBuE,KAPZX,OAASgB,GAcN,CAAEf,QAAOD,UAIxB,MAAO,GAgBXiB,kBAAkBjE,EAASI,EJxSF,OIySrBP,KAAKuB,EAAmB8C,IAAI9D,EAAQL,EAAiBC,IASzDK,gBAAgBL,GACZH,KAAK0D,EAAgBxD,EAAiBC,GAO1CmE,cAAclB,GAiCLpD,KAAKqB,EAAQiC,IAAIF,EAAM7C,SACxBP,KAAKqB,EAAQgD,IAAIjB,EAAM7C,OAAQ,IAInCP,KAAKqB,EAAQkC,IAAIH,EAAM7C,QAAQgE,KAAKnB,GAOxCoB,gBAAgBpB,GACZ,IAAKpD,KAAKqB,EAAQiC,IAAIF,EAAM7C,QACxB,MAAM,IAAIb,EAAa,6CAA8C,CACjEa,OAAQ6C,EAAM7C,SAGtB,MAAMkE,EAAazE,KAAKqB,EAAQkC,IAAIH,EAAM7C,QAAQmE,QAAQtB,GAC1D,KAAIqB,GAAc,GAId,MAAM,IAAI/E,EAAa,yCAHvBM,KAAKqB,EAAQkC,IAAIH,EAAM7C,QAAQoE,OAAOF,EAAY,ICtX9D,IAAIG,ECDJ,MAAMC,EAAoB,CACtBC,gBAAiB,kBACjBC,SAAU,cACVC,OAAQ,UACRC,QAAS,UACTC,OAAgC,oBAAjBC,aAA+BA,aAAaC,MAAQ,IAEjEC,EAAoBC,GACf,CAACT,EAAkBG,OAAQM,EAAWT,EAAkBK,QAC1DK,QAAQC,GAAUA,GAASA,EAAMjG,OAAS,IAC1CkG,KAAK,KAODC,EAWSC,GACPA,GAAiBN,EAAiBR,EAAkBE,UAZtDW,EAiBQC,GACNA,GAAiBN,EAAiBR,EAAkBI,SC3BnE,SAAStC,EAAUhB,EAAOiE,GACtB,MAAMC,EAAgBD,IAEtB,OADAjE,EAAMgB,UAAUkD,GACTA,ECjBX,IACI7G,KAAK,6BAA+BC,IAExC,MAAOC,ICeA,SAAS4G,EAAerD,GAC3B,IAAKA,EACD,MAAM,IAAI/C,EAAa,oCAAqC,CAAE+C,UAIlE,GAAqB,iBAAVA,EAAoB,CAC3B,MAAMsD,EAAY,IAAIhD,IAAIN,EAAOxB,SAASF,MAC1C,MAAO,CACHiF,SAAUD,EAAUhF,KACpBH,IAAKmF,EAAUhF,MAGvB,MAAMkF,SAAEA,EAAFrF,IAAYA,GAAQ6B,EAC1B,IAAK7B,EACD,MAAM,IAAIlB,EAAa,oCAAqC,CAAE+C,UAIlE,IAAKwD,EAAU,CACX,MAAMF,EAAY,IAAIhD,IAAInC,EAAKK,SAASF,MACxC,MAAO,CACHiF,SAAUD,EAAUhF,KACpBH,IAAKmF,EAAUhF,MAKvB,MAAMmF,EAAc,IAAInD,IAAInC,EAAKK,SAASF,MACpCoF,EAAc,IAAIpD,IAAInC,EAAKK,SAASF,MAE1C,OADAmF,EAAYE,aAAa/B,IAxCC,kBAwC0B4B,GAC7C,CACHD,SAAUE,EAAYnF,KACtBH,IAAKuF,EAAYpF,MCvCzB,MAAMsF,EACFzG,cACII,KAAKsG,YAAc,GACnBtG,KAAKuG,eAAiB,GACtBvG,KAAKwG,iBAAmB5C,OAAShC,UAAS6E,YAElCA,IACAA,EAAMC,gBAAkB9E,IAGhC5B,KAAK2G,yBAA2B/C,OAASjC,QAAO8E,QAAOG,qBACnD,GAAmB,YAAfjF,EAAMO,MACFuE,GAASA,EAAMC,iBACZD,EAAMC,2BAA2BhE,QAAS,CAE7C,MAAM9B,EAAM6F,EAAMC,gBAAgB9F,IAC9BgG,EACA5G,KAAKuG,eAAehC,KAAK3D,GAGzBZ,KAAKsG,YAAY/B,KAAK3D,GAIlC,OAAOgG,ICxBnB,MAAMC,EACFjH,aAAYkH,mBAAEA,IACV9G,KAAK+G,mBAAqBnD,OAAShC,UAASuB,aAGxC,MAAM6C,GAAY7C,aAAuC,EAASA,EAAO6C,WACrEhG,KAAKgH,EAAoBC,kBAAkBrF,EAAQhB,KAEvD,OAAOoF,EACD,IAAItD,QAAQsD,EAAU,CAAEkB,QAAStF,EAAQsF,UACzCtF,GAEV5B,KAAKgH,EAAsBF,GClBnC,IAAIK,ECqBJvD,eAAewD,EAAaC,EAAUC,GAClC,IAAItG,EAAS,KAEb,GAAIqG,EAASzG,IAAK,CAEdI,EADoB,IAAI+B,IAAIsE,EAASzG,KAChBI,OAEzB,GAAIA,IAAWhC,KAAKiC,SAASD,OACzB,MAAM,IAAItB,EAAa,6BAA8B,CAAEsB,WAE3D,MAAMuG,EAAiBF,EAASG,QAE1BC,EAAe,CACjBP,QAAS,IAAIQ,QAAQH,EAAeL,SACpCS,OAAQJ,EAAeI,OACvBC,WAAYL,EAAeK,YAGzBC,EAAuBP,EAAWA,EAASG,GAAgBA,EAI3DK,EDjCV,WACI,QAAsB3D,IAAlBgD,EAA6B,CAC7B,MAAMY,EAAe,IAAIC,SAAS,IAClC,GAAI,SAAUD,EACV,IACI,IAAIC,SAASD,EAAaD,MAC1BX,GAAgB,EAEpB,MAAOc,GACHd,GAAgB,EAGxBA,GAAgB,EAEpB,OAAOA,ECmBMe,GACTX,EAAeO,WAAaP,EAAeY,OAC/C,OAAO,IAAIH,SAASF,EAAMD,GC9C9B,SAASO,EAAYC,EAASC,GAC1B,MAAMC,EAAc,IAAIxF,IAAIsF,GAC5B,IAAK,MAAMG,KAASF,EAChBC,EAAYnC,aAAaqC,OAAOD,GAEpC,OAAOD,EAAYxH,KCIvB,MAAM2H,EAIF9I,cACII,KAAK2I,QAAU,IAAItG,SAAQ,CAACuG,EAASnF,KACjCzD,KAAK4I,QAAUA,EACf5I,KAAKyD,OAASA,MCZ1B,MAAMoF,EAAsB,IAAIC,ICThC,IACI9J,KAAK,6BAA+BC,IAExC,MAAOC,ICWP,SAAS6J,EAAUC,GACf,MAAyB,iBAAVA,EAAsB,IAAItG,QAAQsG,GAASA,EAW9D,MAAMC,EAkBFrJ,YAAYsJ,EAAUC,GAClBnJ,KAAKoJ,EAAa,GA8ClBnF,OAAOoF,OAAOrJ,KAAMmJ,GACpBnJ,KAAK2B,MAAQwH,EAAQxH,MACrB3B,KAAKsJ,EAAYJ,EACjBlJ,KAAKuJ,EAAmB,IAAIb,EAC5B1I,KAAKwJ,EAA0B,GAG/BxJ,KAAKyJ,EAAW,IAAIP,EAASQ,SAC7B1J,KAAK2J,EAAkB,IAAIrI,IAC3B,IAAK,MAAMsI,KAAU5J,KAAKyJ,EACtBzJ,KAAK2J,EAAgBtF,IAAIuF,EAAQ,IAErC5J,KAAK2B,MAAMgB,UAAU3C,KAAKuJ,EAAiBZ,SAepC/E,YAACoF,GACR,MAAMrH,MAAEA,GAAU3B,KAClB,IAAI4B,EAAUmH,EAAUC,GACxB,GAAqB,aAAjBpH,EAAQiI,MACRlI,aAAiBmI,YACjBnI,EAAMoI,gBAAiB,CACvB,MAAMC,QAAgCrI,EAAMoI,gBAC5C,GAAIC,EAKA,OAAOA,EAMf,MAAMtD,EAAkB1G,KAAKiK,YAAY,gBACrCrI,EAAQ4F,QAAU,KACtB,IACI,IAAK,MAAM0C,KAAMlK,KAAKmK,iBAAiB,oBACnCvI,QAAgBsI,EAAG,CAAEtI,QAASA,EAAQ4F,QAAS7F,UAGvD,MAAO6B,GACH,GAAIA,aAAe7D,MACf,MAAM,IAAID,EAAa,kCAAmC,CAAE0K,mBAAoB5G,EAAI6G,UAM5F,MAAMC,EAAwB1I,EAAQ4F,QACtC,IACI,IAAI+C,EAEJA,QAAsBC,MAAM5I,EAA0B,aAAjBA,EAAQiI,UACzC1F,EAAYnE,KAAKsJ,EAAUmB,cAM/B,IAAK,MAAMC,KAAY1K,KAAKmK,iBAAiB,mBACzCI,QAAsBG,EAAS,CAC3B/I,QACAC,QAAS0I,EACTjD,SAAUkD,IAGlB,OAAOA,EAEX,MAAOtC,GAeH,MARIvB,SACM1G,KAAK2K,aAAa,eAAgB,CACpC1C,MAAOA,EACPtG,QACA+E,gBAAiBA,EAAgBc,QACjC5F,QAAS0I,EAAsB9C,UAGjCS,GAaQrE,uBAACoF,GACnB,MAAM3B,QAAiBrH,KAAKwK,MAAMxB,GAC5B4B,EAAgBvD,EAASG,QAE/B,OADKxH,KAAK2C,UAAU3C,KAAK6K,SAAS7B,EAAO4B,IAClCvD,EAcKzD,iBAACkH,GACb,MAAMlJ,EAAUmH,EAAU+B,GAC1B,IAAIlE,EACJ,MAAMtB,UAAEA,EAAFyF,aAAaA,GAAiB/K,KAAKsJ,EACnC0B,QAAyBhL,KAAKiL,YAAYrJ,EAAS,QACnDsJ,EAAoBjH,OAAOoF,OAAOpF,OAAOoF,OAAO,GAAI0B,GAAe,CAAEzF,cAC3EsB,QAAuBuE,OAAO7K,MAAM0K,EAAkBE,GAStD,IAAK,MAAMR,KAAY1K,KAAKmK,iBAAiB,4BACzCvD,QAAwB8D,EAAS,CAC7BpF,YACAyF,eACAnE,iBACAhF,QAASoJ,EACTrJ,MAAO3B,KAAK2B,cACTwC,EAEX,OAAOyC,EAiBGhD,eAACkH,EAAKzD,GAChB,MAAMzF,EAAUmH,EAAU+B,GCtP3B,IAAiBM,UDyPF,ECxPX,IAAI/I,SAASuG,GAAYyC,WAAWzC,EAASwC,MDyPhD,MAAMJ,QAAyBhL,KAAKiL,YAAYrJ,EAAS,SAiBzD,IAAKyF,EAKD,MAAM,IAAI3H,EAAa,6BAA8B,CACjDkB,KExRQA,EFwRYoK,EAAiBpK,IEvRlC,IAAImC,IAAIuI,OAAO1K,GAAMK,SAASF,MAG/BA,KAAKwK,QAAQ,IAAIC,OAAQ,IAAGvK,SAASD,UAAW,OAJ1CJ,MF2RhB,MAAM6K,QAAwBzL,KAAK0L,EAA2BrE,GAC9D,IAAKoE,EAKD,OAAO,EAEX,MAAMnG,UAAEA,EAAFyF,aAAaA,GAAiB/K,KAAKsJ,EACnCqC,QAAc3M,KAAKmM,OAAOS,KAAKtG,GAC/BuG,EAAyB7L,KAAKiK,YAAY,kBAC1C6B,EAAcD,QJpR5BjI,eAAsC+H,EAAO/J,EAAS0G,EAAcyC,GAChE,MAAMgB,EAAqB3D,EAAYxG,EAAQhB,IAAK0H,GAEpD,GAAI1G,EAAQhB,MAAQmL,EAChB,OAAOJ,EAAMrL,MAAMsB,EAASmJ,GAGhC,MAAMiB,EAAc/H,OAAOoF,OAAOpF,OAAOoF,OAAO,GAAI0B,GAAe,CAAEkB,cAAc,IAC7EC,QAAkBP,EAAMzH,KAAKtC,EAASoK,GAC5C,IAAK,MAAMhG,KAAYkG,EAEnB,GAAIH,IADwB3D,EAAYpC,EAASpF,IAAK0H,GAElD,OAAOqD,EAAMrL,MAAM0F,EAAU+E,GIwQkBoB,CAInDR,EAAOX,EAAiBxD,QAAS,CAAC,mBAAoBuD,GAClD,KAKJ,UACUY,EAAMS,IAAIpB,EAAkBa,EAC9BJ,EAAgBjE,QAAUiE,GAElC,MAAOxD,GACH,GAAIA,aAAiBtI,MAKjB,KAHmB,uBAAfsI,EAAMhI,YG9S1B2D,iBAKI,IAAK,MAAM8G,KAAY7B,QACb6B,IHySY2B,GAEJpE,EAGd,IAAK,MAAMyC,KAAY1K,KAAKmK,iBAAiB,wBACnCO,EAAS,CACXpF,YACAwG,cACAQ,YAAab,EAAgBjE,QAC7B5F,QAASoJ,EACTrJ,MAAO3B,KAAK2B,QAGpB,OAAO,EAaMiC,kBAAChC,EAASiI,GACvB,IAAK7J,KAAKoJ,EAAWS,GAAO,CACxB,IAAImB,EAAmBpJ,EACvB,IAAK,MAAM8I,KAAY1K,KAAKmK,iBAAiB,sBACzCa,EAAmBjC,QAAgB2B,EAAS,CACxCb,OACAjI,QAASoJ,EACTrJ,MAAO3B,KAAK2B,MAEZwB,OAAQnD,KAAKmD,UAGrBnD,KAAKoJ,EAAWS,GAAQmB,EAE5B,OAAOhL,KAAKoJ,EAAWS,GAS3BI,YAAYhK,GACR,IAAK,MAAM2J,KAAU5J,KAAKsJ,EAAUI,QAChC,GAAIzJ,KAAQ2J,EACR,OAAO,EAGf,OAAO,EAkBOhG,mBAAC3D,EAAMuI,GACrB,IAAK,MAAMkC,KAAY1K,KAAKmK,iBAAiBlK,SAGnCyK,EAASlC,GAYN2B,kBAAClK,GACd,IAAK,MAAM2J,KAAU5J,KAAKsJ,EAAUI,QAChC,GAA4B,mBAAjBE,EAAO3J,GAAsB,CACpC,MAAMwG,EAAQzG,KAAK2J,EAAgBpG,IAAIqG,GACjC2C,EAAoB/D,IACtB,MAAMgE,EAAgBvI,OAAOoF,OAAOpF,OAAOoF,OAAO,GAAIb,GAAQ,CAAE/B,UAGhE,OAAOmD,EAAO3J,GAAMuM,UAElBD,GAiBlB5J,UAAUgG,GAEN,OADA3I,KAAKwJ,EAAwBjF,KAAKoE,GAC3BA,EAYM/E,oBACb,IAAI+E,EACJ,KAAOA,EAAU3I,KAAKwJ,EAAwBiD,eACpC9D,EAOd+D,UACI1M,KAAKuJ,EAAiBX,QAAQ,MAYFhF,QAACyD,GAC7B,IAAIoE,EAAkBpE,EAClBsF,GAAc,EAClB,IAAK,MAAMjC,KAAY1K,KAAKmK,iBAAiB,mBAOzC,GANAsB,QAAyBf,EAAS,CAC9B9I,QAAS5B,KAAK4B,QACdyF,SAAUoE,EACV9J,MAAO3B,KAAK2B,cACTwC,EACPwI,GAAc,GACTlB,EACD,MAwBR,OArBKkB,GACGlB,GAA8C,MAA3BA,EAAgB9D,SACnC8D,OAAkBtH,GAmBnBsH,GInef,MAAMmB,UCRN,MAuBIhN,YAAYuJ,EAAU,IAQlBnJ,KAAKsF,UAAYI,EAA0ByD,EAAQ7D,WAQnDtF,KAAK0J,QAAUP,EAAQO,SAAW,GAQlC1J,KAAKyK,aAAetB,EAAQsB,aAQ5BzK,KAAK+K,aAAe5B,EAAQ4B,aAqBhC3K,OAAO+I,GACH,MAAO0D,GAAgB7M,KAAK8M,UAAU3D,GACtC,OAAO0D,EAwBXC,UAAU3D,GAEFA,aAAmBW,aACnBX,EAAU,CACNxH,MAAOwH,EACPvH,QAASuH,EAAQvH,UAGzB,MAAMD,EAAQwH,EAAQxH,MAChBC,EAAqC,iBAApBuH,EAAQvH,QAC3B,IAAIc,QAAQyG,EAAQvH,SACpBuH,EAAQvH,QACNuB,EAAS,WAAYgG,EAAUA,EAAQhG,YAASgB,EAChDhE,EAAU,IAAI8I,EAAgBjJ,KAAM,CAAE2B,QAAOC,UAASuB,WACtD0J,EAAe7M,KAAK+M,EAAa5M,EAASyB,EAASD,GAGzD,MAAO,CAACkL,EAFY7M,KAAKgN,EAAeH,EAAc1M,EAASyB,EAASD,IAI1DiC,QAACzD,EAASyB,EAASD,GAEjC,IAAI0F,QADElH,EAAQwK,aAAa,mBAAoB,CAAEhJ,QAAOC,YAExD,IAKI,GAJAyF,QAAiBrH,KAAKiN,EAAQrL,EAASzB,IAIlCkH,GAA8B,UAAlBA,EAASnF,KACtB,MAAM,IAAIxC,EAAa,cAAe,CAAEkB,IAAKgB,EAAQhB,MAG7D,MAAOqH,GACH,GAAIA,aAAiBtI,MACjB,IAAK,MAAM+K,KAAYvK,EAAQgK,iBAAiB,mBAE5C,GADA9C,QAAiBqD,EAAS,CAAEzC,QAAOtG,QAAOC,YACtCyF,EACA,MAIZ,IAAKA,EACD,MAAMY,EAQd,IAAK,MAAMyC,KAAYvK,EAAQgK,iBAAiB,sBAC5C9C,QAAiBqD,EAAS,CAAE/I,QAAOC,UAASyF,aAEhD,OAAOA,EAESzD,QAACiJ,EAAc1M,EAASyB,EAASD,GACjD,IAAI0F,EACAY,EACJ,IACIZ,QAAiBwF,EAErB,MAAO5E,IAKP,UACU9H,EAAQwK,aAAa,oBAAqB,CAC5ChJ,QACAC,UACAyF,mBAEElH,EAAQ+M,cAElB,MAAOC,GACCA,aAA0BxN,QAC1BsI,EAAQkF,GAUhB,SAPMhN,EAAQwK,aAAa,qBAAsB,CAC7ChJ,QACAC,UACAyF,WACAY,MAAOA,IAEX9H,EAAQuM,UACJzE,EACA,MAAMA,IDlKdrI,YAAYuJ,EAAU,IAClBA,EAAQ7D,UAAYI,EAA2ByD,EAAQ7D,WACvDvF,MAAMoJ,GACNnJ,KAAKoN,GAC6B,IAA9BjE,EAAQkE,kBAKZrN,KAAK0J,QAAQnF,KAAKqI,EAAiBU,wCAS1B1J,QAAChC,EAASzB,GACnB,MAAMkH,QAAiBlH,EAAQoN,WAAW3L,GAC1C,OAAIyF,IAKAlH,EAAQwB,OAAgC,YAAvBxB,EAAQwB,MAAMO,WAClBlC,KAAKwN,EAAe5L,EAASzB,SAIjCH,KAAKyN,EAAa7L,EAASzB,IAE1ByD,QAAChC,EAASzB,GACxB,IAAIkH,EACJ,MAAMlE,EAAUhD,EAAQgD,QAAU,GAElC,IAAInD,KAAKoN,EA+BL,MAAM,IAAI1N,EAAa,yBAA0B,CAC7C4F,UAAWtF,KAAKsF,UAChB1E,IAAKgB,EAAQhB,MAjCQ,CAMzB,MAAM8M,EAAsBvK,EAAOwK,UAC7BC,EAAqBhM,EAAQ+L,UAC7BE,GAAuBD,GAAsBA,IAAuBF,EAC1ErG,QAAiBlH,EAAQqK,MAAM,IAAI9H,QAAQd,EAAS,CAChD+L,UAAWC,GAAsBF,KAOjCA,GAAuBG,IACvB7N,KAAK8N,UACmB3N,EAAQ0K,SAASjJ,EAASyF,EAASG,UA+BnE,OAAOH,EAESzD,QAAChC,EAASzB,GAC1BH,KAAK8N,IACL,MAAMzG,QAAiBlH,EAAQqK,MAAM5I,GAIrC,UADwBzB,EAAQ0K,SAASjJ,EAASyF,EAASG,SAIvD,MAAM,IAAI9H,EAAa,0BAA2B,CAC9CkB,IAAKgB,EAAQhB,IACb+G,OAAQN,EAASM,SAGzB,OAAON,EA6BXyG,IACI,IAAIC,EAAqB,KACrBC,EAA6B,EACjC,IAAK,MAAO9M,EAAO0I,KAAW5J,KAAK0J,QAAQuE,UAEnCrE,IAAWgD,EAAiBU,yCAI5B1D,IAAWgD,EAAiBsB,oCAC5BH,EAAqB7M,GAErB0I,EAAOuE,iBACPH,KAG2B,IAA/BA,EACAhO,KAAK0J,QAAQnF,KAAKqI,EAAiBsB,mCAE9BF,EAA6B,GAA4B,OAAvBD,GAEvC/N,KAAK0J,QAAQ/E,OAAOoJ,EAAoB,IAKpDnB,EAAiBsB,kCAAoC,CACjDtK,gBAAA,OAAsByD,SAAEA,MACfA,GAAYA,EAASM,QAAU,IACzB,KAEJN,GAGfuF,EAAiBU,uCAAyC,CACtD1J,gBAAA,OAAsByD,SAAEA,KACbA,EAAS+G,iBAAmBhH,EAAaC,GAAYA,GE3LpE,MAAMgH,EAWFzO,aAAY0F,UAAEA,EAAFoE,QAAaA,EAAU,GAAvB2D,kBAA2BA,GAAoB,GAAS,IAChErN,KAAKsO,EAAmB,IAAIhN,IAC5BtB,KAAKuO,EAAoB,IAAIjN,IAC7BtB,KAAKwO,EAA0B,IAAIlN,IACnCtB,KAAKsJ,EAAY,IAAIsD,EAAiB,CAClCtH,UAAWI,EAA2BJ,GACtCoE,QAAS,IACFA,EACH,IAAI7C,EAAuB,CAAEC,mBAAoB9G,QAErDqN,sBAGJrN,KAAKyO,QAAUzO,KAAKyO,QAAQC,KAAK1O,MACjCA,KAAK2O,SAAW3O,KAAK2O,SAASD,KAAK1O,MAMnCkJ,eACA,OAAOlJ,KAAKsJ,EAYhBvE,SAASkJ,GACLjO,KAAK4O,eAAeX,GACfjO,KAAK6O,IACN7P,KAAK0C,iBAAiB,UAAW1B,KAAKyO,SACtCzP,KAAK0C,iBAAiB,WAAY1B,KAAK2O,UACvC3O,KAAK6O,GAAkC,GAU/CD,eAAeX,GASX,MAAMa,EAAkB,GACxB,IAAK,MAAMrM,KAASwL,EAAS,CAEJ,iBAAVxL,EACPqM,EAAgBvK,KAAK9B,GAEhBA,QAA4B0B,IAAnB1B,EAAMwD,UACpB6I,EAAgBvK,KAAK9B,EAAM7B,KAE/B,MAAMoF,SAAEA,EAAFpF,IAAYA,GAAQkF,EAAerD,GACnCsM,EAA8B,iBAAVtM,GAAsBA,EAAMwD,SAClD,SAAW,UACf,GAAIjG,KAAKsO,EAAiBhL,IAAI1C,IAC1BZ,KAAKsO,EAAiB/K,IAAI3C,KAASoF,EACnC,MAAM,IAAItG,EAAa,wCAAyC,CAC5DsP,WAAYhP,KAAKsO,EAAiB/K,IAAI3C,GACtCqO,YAAajJ,IAGrB,GAAqB,iBAAVvD,GAAsBA,EAAMkL,UAAW,CAC9C,GAAI3N,KAAKwO,EAAwBlL,IAAI0C,IACjChG,KAAKwO,EAAwBjL,IAAIyC,KAAcvD,EAAMkL,UACrD,MAAM,IAAIjO,EAAa,4CAA6C,CAChEkB,QAGRZ,KAAKwO,EAAwBnK,IAAI2B,EAAUvD,EAAMkL,WAIrD,GAFA3N,KAAKsO,EAAiBjK,IAAIzD,EAAKoF,GAC/BhG,KAAKuO,EAAkBlK,IAAIzD,EAAKmO,GAC5BD,EAAgBvP,OAAS,EAAG,CAC5B,MAAM2P,EACD,qDAAQJ,EAAgBrJ,KAAK,8EAK9B0J,QAAQC,KAAKF,KAkB7BT,QAAQ9M,GAGJ,OAAOgB,EAAUhB,GAAOiC,UACpB,MAAMyL,EAAsB,IAAIhJ,EAChCrG,KAAKkJ,SAASQ,QAAQnF,KAAK8K,GAG3B,IAAK,MAAOzO,EAAKoF,KAAahG,KAAKsO,EAAkB,CACjD,MAAMX,EAAY3N,KAAKwO,EAAwBjL,IAAIyC,GAC7C+I,EAAY/O,KAAKuO,EAAkBhL,IAAI3C,GACvCgB,EAAU,IAAIc,QAAQ9B,EAAK,CAC7B+M,YACAhC,MAAOoD,EACPO,YAAa,sBAEXjN,QAAQC,IAAItC,KAAKkJ,SAAS4D,UAAU,CACtC3J,OAAQ,CAAE6C,YACVpE,UACAD,WAGR,MAAM2E,YAAEA,EAAFC,eAAeA,GAAmB8I,EAIxC,MAAO,CAAE/I,cAAaC,qBAa9BoI,SAAShN,GAGL,OAAOgB,EAAUhB,GAAOiC,UACpB,MAAM+H,QAAc3M,KAAKmM,OAAOS,KAAK5L,KAAKkJ,SAAS5D,WAC7CiK,QAAgC5D,EAAMzH,OACtCsL,EAAoB,IAAI1G,IAAI9I,KAAKsO,EAAiBmB,UAClDC,EAAc,GACpB,IAAK,MAAM9N,KAAW2N,EACbC,EAAkBlM,IAAI1B,EAAQhB,aACzB+K,EAAMlD,OAAO7G,GACnB8N,EAAYnL,KAAK3C,EAAQhB,MAMjC,MAAO,CAAE8O,kBASjBC,qBACI,OAAO3P,KAAKsO,EAQhBsB,gBACI,MAAO,IAAI5P,KAAKsO,EAAiBpK,QAWrC+C,kBAAkBrG,GACd,MAAMmF,EAAY,IAAIhD,IAAInC,EAAKK,SAASF,MACxC,OAAOf,KAAKsO,EAAiB/K,IAAIwC,EAAUhF,MAO/C8O,wBAAwB7J,GACpB,OAAOhG,KAAKwO,EAAwBjL,IAAIyC,GAoBzBpC,oBAAChC,GAChB,MAAMhB,EAAMgB,aAAmBc,QAAUd,EAAQhB,IAAMgB,EACjDoE,EAAWhG,KAAKiH,kBAAkBrG,GACxC,GAAIoF,EAAU,CAEV,aADoBhH,KAAKmM,OAAOS,KAAK5L,KAAKkJ,SAAS5D,YACtChF,MAAM0F,IAY3B8J,wBAAwBlP,GACpB,MAAMoF,EAAWhG,KAAKiH,kBAAkBrG,GACxC,IAAKoF,EACD,MAAM,IAAItG,EAAa,oBAAqB,CAAEkB,QAElD,OAAQuI,IACJA,EAAQvH,QAAU,IAAIc,QAAQ9B,GAC9BuI,EAAQhG,OAASc,OAAOoF,OAAO,CAAErD,YAAYmD,EAAQhG,QAC9CnD,KAAKkJ,SAAS9I,OAAO+I,KCvRxC,IAAIrC,EAKG,MAAMiJ,EAAgC,KACpCjJ,IACDA,EAAqB,IAAIuH,GAEtBvH,GCGX,MAAMkJ,UAAsB3P,EAiBxBT,YAAYkH,EAAoBqC,GAgB5BpJ,OAfc,EAAG6B,cACb,MAAMqO,EAAkBnJ,EAAmB6I,qBAC3C,IAAK,MAAMO,KCtBhB,UAAgCtP,GAAKuP,4BAAEA,EAA8B,CAAC,QAAS,YAA1CC,eAAuDA,EAAiB,aAAxEC,UAAsFA,GAAY,EAAlGC,gBAAwGA,GAAqB,IACrK,MAAMvK,EAAY,IAAIhD,IAAInC,EAAKK,SAASF,MACxCgF,EAAUwK,KAAO,SACXxK,EAAUhF,KAChB,MAAMyP,ECHH,SAAmCzK,EAAWoK,EAA8B,IAG/E,IAAK,MAAMM,IAAa,IAAI1K,EAAUK,aAAalC,QAC3CiM,EAA4BO,MAAM/P,GAAWA,EAAOgQ,KAAKF,MACzD1K,EAAUK,aAAaqC,OAAOgI,GAGtC,OAAO1K,EDLyB6K,CAA0B7K,EAAWoK,GAErE,SADMK,EAAwBzP,KAC1BqP,GAAkBI,EAAwBK,SAASC,SAAS,KAAM,CAClE,MAAMC,EAAe,IAAIhO,IAAIyN,EAAwBzP,MACrDgQ,EAAaF,UAAYT,QACnBW,EAAahQ,KAEvB,GAAIsP,EAAW,CACX,MAAMW,EAAW,IAAIjO,IAAIyN,EAAwBzP,MACjDiQ,EAASH,UAAY,cACfG,EAASjQ,KAEnB,GAAIuP,EAAiB,CACjB,MAAMW,EAAiBX,EAAgB,CAAE1P,IAAKmF,IAC9C,IAAK,MAAMmL,KAAgBD,QACjBC,EAAanQ,MDGOoQ,CAAsBvP,EAAQhB,IAAKuI,GAAU,CACnE,MAAMnD,EAAWiK,EAAgB1M,IAAI2M,GACrC,GAAIlK,EAAU,CAEV,MAAO,CAAEA,WAAU2H,UADD7G,EAAmB+I,wBAAwB7J,QAU5Dc,EAAmBoC,WG7BxC,SAASkI,EAASjI,GACd,MAAMrC,EAAqBiJ,KCM/B,SAAuBsB,EAASlR,EAASI,GACrC,IAAI6C,EACJ,GAAuB,iBAAZiO,EAAsB,CAC7B,MAAMC,EAAa,IAAIvO,IAAIsO,EAASpQ,SAASF,MAiC7CqC,EAAQ,IAAI/C,GAZU,EAAGO,SASdA,EAAIG,OAASuQ,EAAWvQ,MAGFZ,EAASI,QAEzC,GAAI8Q,aAAmB7F,OAExBpI,EAAQ,IAAI1C,EAAY2Q,EAASlR,EAASI,QAEzC,GAAuB,mBAAZ8Q,EAEZjO,EAAQ,IAAI/C,EAAMgR,EAASlR,EAASI,OAEnC,MAAI8Q,aAAmBhR,GAIxB,MAAM,IAAIX,EAAa,yBAA0B,CAC7C6R,WAAY,kBACZC,SAAU,gBACVf,UAAW,YANfrN,EAAQiO,GzB7DPzM,IACDA,EAAgB,IAAIxD,EAEpBwD,EAAcnD,mBACdmD,EAAc5C,oBAEX4C,GyBiEON,cAAclB,GD7D5BkB,CADsB,IAAI0L,EAAclJ,EAAoBqC,uBEHhE,SAA0B8E,EAAS9E,ICInC,SAAkB8E,GACa8B,IACRhL,SAASkJ,GDL5BlJ,CAASkJ,GACTmD,EAASjI"}