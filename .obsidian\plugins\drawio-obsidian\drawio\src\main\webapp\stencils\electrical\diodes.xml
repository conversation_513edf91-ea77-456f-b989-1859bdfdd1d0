<shapes name="mxgraph.electrical.diodes">
	<shape aspect="variable" h="60" name="Diode" strokewidth="inherit" w="100">
		<connections>
			<constraint name="in" perimeter="0" x="0" y="0.5"/>
			<constraint name="out" perimeter="0" x="1" y="0.5"/>
		</connections>
		<background>
			<path>
				<move x="30" y="0"/>
				<line x="70" y="30"/>
				<line x="30" y="60"/>
				<close/>
				<move x="0" y="30"/>
				<line x="30" y="30"/>
				<move x="70" y="0"/>
				<line x="70" y="60"/>
				<move x="70" y="30"/>
				<line x="100" y="30"/>
			</path>
		</background>
		<foreground>
			<fillstroke/>
		</foreground>
	</shape>
	<shape aspect="variable" h="60" name="Field Effect Diode" strokewidth="inherit" w="100">
		<connections>
			<constraint name="in" perimeter="0" x="0" y="0.5"/>
			<constraint name="out" perimeter="0" x="1" y="0.5"/>
		</connections>
		<background>
			<path>
				<move x="30" y="0"/>
				<line x="70" y="30"/>
				<line x="30" y="60"/>
				<close/>
				<move x="0" y="30"/>
				<line x="30" y="30"/>
				<move x="70" y="0"/>
				<line x="70" y="60"/>
				<move x="70" y="30"/>
				<line x="100" y="30"/>
				<move x="65" y="0"/>
				<line x="75" y="0"/>
				<move x="65" y="60"/>
				<line x="75" y="60"/>
			</path>
		</background>
		<foreground>
			<fillstroke/>
		</foreground>
	</shape>
	<shape aspect="variable" h="80" name="Four Layer Diode" strokewidth="inherit" w="100">
		<connections>
			<constraint name="in" perimeter="0" x="0" y="0.5"/>
			<constraint name="out" perimeter="0" x="1" y="0.5"/>
		</connections>
		<background>
			<path>
				<move x="30" y="10"/>
				<line x="70" y="40"/>
				<line x="30" y="40"/>
				<close/>
				<move x="0" y="40"/>
				<line x="30" y="40"/>
				<move x="30" y="40"/>
				<line x="30" y="70"/>
				<move x="70" y="40"/>
				<line x="100" y="40"/>
			</path>
		</background>
		<foreground>
			<fillstroke/>
			<ellipse h="80" w="80" x="10" y="0"/>
			<stroke/>
		</foreground>
	</shape>
	<shape aspect="variable" h="60" name="Gunn Diode" strokewidth="inherit" w="100">
		<connections>
			<constraint name="in" perimeter="0" x="0" y="0.5"/>
			<constraint name="out" perimeter="0" x="1" y="0.5"/>
		</connections>
		<background>
			<path>
				<move x="10" y="0"/>
				<line x="90" y="60"/>
				<line x="90" y="0"/>
				<line x="10" y="60"/>
				<close/>
				<move x="0" y="30"/>
				<line x="10" y="30"/>
				<move x="90" y="30"/>
				<line x="100" y="30"/>
			</path>
		</background>
		<foreground>
			<fillstroke/>
		</foreground>
	</shape>
	<shape aspect="variable" h="60" name="Schottky Diode" strokewidth="inherit" w="100">
		<connections>
			<constraint name="in" perimeter="0" x="0" y="0.5"/>
			<constraint name="out" perimeter="0" x="1" y="0.5"/>
		</connections>
		<background>
			<path>
				<move x="30" y="0"/>
				<line x="70" y="30"/>
				<line x="30" y="60"/>
				<close/>
			</path>
		</background>
		<foreground>
			<fillstroke/>
			<path>
				<move x="0" y="30"/>
				<line x="30" y="30"/>
				<move x="60" y="5"/>
				<line x="60" y="0"/>
				<line x="70" y="0"/>
				<line x="70" y="60"/>
				<line x="80" y="60"/>
				<line x="80" y="55"/>
				<move x="70" y="30"/>
				<line x="100" y="30"/>
			</path>
			<stroke/>
		</foreground>
	</shape>
	<shape aspect="variable" h="60" name="Transorb 1" strokewidth="inherit" w="100">
		<connections>
			<constraint name="in" perimeter="0" x="0" y="0.5"/>
			<constraint name="out" perimeter="0" x="1" y="0.5"/>
		</connections>
		<background>
			<path>
				<move x="10" y="0"/>
				<line x="90" y="60"/>
				<line x="90" y="0"/>
				<line x="10" y="60"/>
				<close/>
			</path>
		</background>
		<foreground>
			<fillstroke/>
			<path>
				<move x="50" y="0"/>
				<line x="50" y="60"/>
				<move x="0" y="30"/>
				<line x="10" y="30"/>
				<move x="90" y="30"/>
				<line x="100" y="30"/>
			</path>
			<stroke/>
		</foreground>
	</shape>
	<shape aspect="variable" h="60" name="Transorb 2" strokewidth="inherit" w="100">
		<connections>
			<constraint name="in" perimeter="0" x="0" y="0.5"/>
			<constraint name="out" perimeter="0" x="1" y="0.5"/>
		</connections>
		<background>
			<path>
				<move x="10" y="0"/>
				<line x="90" y="60"/>
				<line x="90" y="0"/>
				<line x="10" y="60"/>
				<close/>
			</path>
		</background>
		<foreground>
			<fillstroke/>
			<path>
				<move x="40" y="0"/>
				<line x="50" y="10"/>
				<line x="50" y="50"/>
				<line x="60" y="60"/>
				<move x="0" y="30"/>
				<line x="10" y="30"/>
				<move x="90" y="30"/>
				<line x="100" y="30"/>
			</path>
			<stroke/>
		</foreground>
	</shape>
	<shape aspect="variable" h="60" name="Tunnel Diode" strokewidth="inherit" w="100">
		<connections>
			<constraint name="in" perimeter="0" x="0" y="0.5"/>
			<constraint name="out" perimeter="0" x="1" y="0.5"/>
		</connections>
		<background>
			<path>
				<move x="30" y="0"/>
				<line x="70" y="30"/>
				<line x="30" y="60"/>
				<close/>
			</path>
		</background>
		<foreground>
			<fillstroke/>
			<path>
				<move x="60" y="0"/>
				<line x="70" y="0"/>
				<line x="70" y="60"/>
				<line x="60" y="60"/>
				<move x="70" y="30"/>
				<line x="100" y="30"/>
				<move x="0" y="30"/>
				<line x="30" y="30"/>
			</path>
			<stroke/>
		</foreground>
	</shape>
	<shape aspect="variable" h="80" name="Tunnel Diode 2" strokewidth="inherit" w="100">
		<connections>
			<constraint name="in" perimeter="0" x="0" y="0.5"/>
			<constraint name="out" perimeter="0" x="1" y="0.5"/>
		</connections>
		<background>
			<path>
				<move x="30" y="10"/>
				<line x="70" y="40"/>
				<line x="30" y="70"/>
				<close/>
			</path>
		</background>
		<foreground>
			<fillstroke/>
			<path>
				<move x="0" y="40"/>
				<line x="30" y="40"/>
				<move x="70" y="10"/>
				<line x="70" y="70"/>
				<move x="70" y="40"/>
				<line x="100" y="40"/>
			</path>
			<fillstroke/>
			<ellipse h="80" w="80" x="10" y="0"/>
			<stroke/>
		</foreground>
	</shape>
	<shape aspect="variable" h="60" name="Varactor - Varicap" strokewidth="inherit" w="100">
		<connections>
			<constraint name="in" perimeter="0" x="0" y="0.5"/>
			<constraint name="out" perimeter="0" x="1" y="0.5"/>
		</connections>
		<background>
			<path>
				<move x="30" y="0"/>
				<line x="70" y="30"/>
				<line x="30" y="60"/>
				<close/>
			</path>
		</background>
		<foreground>
			<fillstroke/>
			<path>
				<move x="75" y="0"/>
				<line x="75" y="60"/>
				<move x="70" y="60"/>
				<line x="70" y="0"/>
				<move x="70" y="30"/>
				<line x="100" y="30"/>
				<move x="0" y="30"/>
				<line x="30" y="30"/>
			</path>
			<stroke/>
		</foreground>
	</shape>
	<shape aspect="variable" h="50" name="Zener Diode 1" strokewidth="inherit" w="100">
		<connections>
			<constraint name="in" perimeter="0" x="0" y="0.5"/>
			<constraint name="out" perimeter="0" x="1" y="0.5"/>
		</connections>
		<background>
			<path>
				<move x="25" y="0"/>
				<line x="75" y="25"/>
				<line x="25" y="50"/>
				<close/>
			</path>
		</background>
		<foreground>
			<fillstroke/>
			<path>
				<move x="65" y="0"/>
				<line x="75" y="0"/>
				<line x="75" y="50"/>
				<line x="85" y="50"/>
				<move x="0" y="25"/>
				<line x="25" y="25"/>
				<move x="75" y="25"/>
				<line x="100" y="25"/>
			</path>
			<stroke/>
		</foreground>
	</shape>
	<shape aspect="variable" h="50" name="Zener Diode 2" strokewidth="inherit" w="100">
		<connections>
			<constraint name="in" perimeter="0" x="0" y="0.5"/>
			<constraint name="out" perimeter="0" x="1" y="0.5"/>
		</connections>
		<background>
			<path>
				<move x="25" y="0"/>
				<line x="75" y="25"/>
				<line x="25" y="50"/>
				<close/>
			</path>
		</background>
		<foreground>
			<fillstroke/>
			<path>
				<move x="65" y="0"/>
				<line x="75" y="0"/>
				<line x="75" y="50"/>
				<move x="0" y="25"/>
				<line x="25" y="25"/>
				<move x="75" y="25"/>
				<line x="100" y="25"/>
			</path>
			<stroke/>
		</foreground>
	</shape>
	<shape aspect="variable" h="50" name="Zener Diode 3" strokewidth="inherit" w="100">
		<connections>
			<constraint name="in" perimeter="0" x="0" y="0.5"/>
			<constraint name="out" perimeter="0" x="1" y="0.5"/>
		</connections>
		<background>
			<path>
				<move x="25" y="0"/>
				<line x="75" y="25"/>
				<line x="25" y="50"/>
				<close/>
			</path>
		</background>
		<foreground>
			<fillstroke/>
			<path>
				<move x="65" y="0"/>
				<line x="75" y="10"/>
				<line x="75" y="40"/>
				<line x="85" y="50"/>
				<move x="0" y="25"/>
				<line x="25" y="25"/>
				<move x="75" y="25"/>
				<line x="100" y="25"/>
			</path>
			<stroke/>
		</foreground>
	</shape>
</shapes>