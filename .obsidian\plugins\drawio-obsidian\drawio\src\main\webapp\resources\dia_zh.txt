# *DO NOT DIRECTLY EDIT THIS FILE, IT IS AUTOMATICALLY GENERATED AND IT IS BASED ON:*
# https://docs.google.com/spreadsheet/ccc?key=0AmQEO36liL4FdDJLWVNMaVV2UmRKSnpXU09MYkdGbEE
about=关于
aboutDrawio=关于 draw.io
accessDenied=没有权限访问
accounts=账号
action=操作
actualSize=实际尺寸
add=添加
addAccount=添加账户
addedFile=已添加 {1}
addImages=添加图片
addImageUrl=添加图片地址
addLayer=添加图层
addProperty=添加属性
address=地址
addToExistingDrawing=添加至当前绘图
addToScratchpad=添加到便签本
addWaypoint=添加航点
adjustTo=调整到
advanced=高级
smartTemplate=Smart Template
align=对齐
alignment=对齐
allChangesLost=所有修改均将会丢失！
allPages=所有页面
allProjects=所有方案
allSpaces=所有部分
allTags=所有标签
anchor=锚
android=Android
angle=角度
arc=圆弧
areYouSure=是否确定？
ensureDataSaved=关闭前请确保您的数据已保存。
allChangesSaved=所有更改均已保存
allChangesSavedInDrive=所有更改均保存至 Google Drive 中
allowPopups=允许弹出式窗口以阻止此对话框
allowRelativeUrl=允许相对地址
alreadyConnected=节点已连接
appearance=外观
apply=应用
archiMate21=ArchiMate 2.1
arrange=调整图形
arrow=箭头
arrows=箭头
asNew=作为新绘图
atlas=Atlas
author=作者
authorizationRequired=需要授权
authorizeThisAppIn=在 {1} 里授权此应用：
authorize=授权
authorizing=正在授权
automatic=自动
autosave=自动保存
autosize=自动调整
attachments=附件
aws=AWS
aws3d=AWS 3D
azure=Azure
back=后退
background=背景
backgroundColor=背景色
backgroundImage=背景图片
basic=基本
beta=beta
blankDrawing=空白绘图
blankDiagram=空白框图
block=区块
blockquote=区块引言
blog=博客
bold=粗体
bootstrap=Bootstrap
border=边框
borderColor=边框颜色
borderWidth=边框宽度
bottom=下
bottomAlign=向下对齐
bottomLeft=左下
bottomRight=右下
bpmn=BPMN
bringForward=上移一层
browser=浏览器
bulletedList=项目符号列表
business=商务
busy=处理中
cabinets=机箱
cancel=取消
center=水平居中
cannotLoad=载入失败。请稍后重试。
cannotLogin=登录失败。请稍后重试。
cannotOpenFile=无法打开文件
change=更改
changeOrientation=改变方向
changeUser=更改用户
changeStorage=修改存储方式
changesNotSaved=更改尚未保存
classDiagram=类图
userJoined={1} 已加入
userLeft={1} 已离开
chatWindowTitle=聊天
chooseAnOption=请选择一项
chromeApp=Chrome 应用
collaborativeEditingNotice=协同编辑的重要通知
compare=比较
compressed=已压缩
commitMessage=提交信息
configLinkWarn=该链接将配置 draw.io. 仅当您信任提供给你链接的人时点击确定.
configLinkConfirm=点击确定配置并重启 draw.io
container=容器
csv=CSV
dark=深色
diagramLanguage=绘图语言
diagramType=绘图类型
diagramXmlDesc=XML 文件
diagramHtmlDesc=HTML 文件
diagramPngDesc=可编辑位图文件
diagramSvgDesc=可编辑矢量图文件
didYouMeanToExportToPdf=是否要导出到 PDF？
disabled=Disabled
draftFound={1} 的草稿已经找到。将其加载到编辑器或将其丢弃以继续。
draftRevisionMismatch=此页面上的共享草稿有一个不同的版本。请从草稿编辑以确保您正在使用最新版本。
selectDraft=选择一个草稿继续编辑:
dragAndDropNotSupported=暂不支持图片拖放功能。是否要使用导入？
dropboxCharsNotAllowed=系统不允许使用下列字符：\ / : ? * " |
check=核查
checksum=校验码
circle=圆形
cisco=Cisco
classic=经典
clearDefaultStyle=清除默认风格
clearWaypoints=清除航点
clipart=剪贴画
close=关闭
closingFile=正在关闭文件
realtimeCollaboration=实时协作
collaborate=合作
collaborator=合作者
collaborators=合作者
collapse=折叠
collapseExpand=折叠 / 展开
collapse-expand=点击折叠 / 展开\nShift-点击以移动周边图形 \nAlt-点击以保护组别尺寸
collapsible=可折叠
comic=手绘
comment=评论
commentsNotes=评论 / 备注
compress=压缩
configuration=配置
connect=连接
connecting=正在连接
connectWithDrive=连接 Google Drive
connection=连接
connectionArrows=连接箭头
connectionPoints=连接点
constrainProportions=限制比例
containsValidationErrors=包含验证错误
copiedToClipboard=已复制到剪贴板
copy=复制
copyConnect=连接时复制
copyCreated=已经创建文件的副本。
copyData=复制数据
copyOf={1} 的副本
copyOfDrawing=绘图副本
copySize=复制大小
copyStyle=复制样式
create=创建
createBlankDiagram=创建空白绘图
createNewDiagram=创建新绘图
createRevision=创建修订版本
createShape=创建图形
crop=导出单页
curved=曲线
custom=自定义
current=当前
currentPage=当前页
cut=剪切
dashed=虚线
decideLater=稍后再决定
default=默认值
delete=删除
deleteColumn=删除列
deleteLibrary401=没有权限删除此图库
deleteLibrary404=未找到所选图库
deleteLibrary500=删除图库时出错
deleteLibraryConfirm=您即将永久删除此图库。您确定要这样操作吗？
deleteRow=删除行
description=描述
describeYourDiagram=Describe your diagram
device=设备
diagram=绘图
diagramContent=绘图内容
diagramLocked=绘图已经锁定以避免进一步的数据丢失。
diagramLockedBySince=绘图已经在 {2} 之前被 {1} 锁定
diagramName=绘图名称
diagramIsPublic=绘图为公开状态
diagramIsNotPublic=绘图为未公开状态
diamond=方块
diamondThin=方块（细）
didYouKnow=您知道吗......
direction=方向
discard=丢弃
discardChangesAndReconnect=取消更改并重新连接
googleDriveMissingClickHere=找不到 Google Drive？请点击这里！
discardChanges=放弃更改
disconnected=未连接
distribute=等距分布
done=完成
doNotShowAgain=不再显示
dotted=点线
doubleClickOrientation=双击以改变方向
doubleClickTooltip=双击以插入文字
doubleClickChangeProperty=双击以更改属性名
download=下载
downloadDesktop=获取桌面版
downloadAs=下载为
clickHereToSave=点击此处保存。
dpi=DPI
draftDiscarded=草稿已丢弃
draftSaved=草稿已保存
dragElementsHere=把元素拖至此处
dragImagesHere=把图像或网络链接拖至此处
dragUrlsHere=将 URL 地址拖至此处
draw.io=draw.io
drawing=绘图 {1}
drawingEmpty=绘图空白
drawingTooLarge=绘图过大
drawioForWork=Draw.io for GSuite
dropbox=Dropbox
duplicate=创建副本
duplicateIt=复制 {1}
divider=分隔线
dx=Dx
dy=Dy
east=向右
edit=编辑
editData=编辑数据
editDiagram=编辑绘图
editGeometry=编辑几何图形
editImage=编辑图片
editImageUrl=编辑图片 URL 地址
editLink=编辑链接
editShape=编辑图形
editStyle=编辑样式
editText=编辑文字
editTooltip=编辑提示
glass=玻璃
googleImages=Google 图片
imageSearch=图片搜索
eip=EIP
embed=嵌入
embedFonts=嵌入字体
embedImages=嵌入图片
mainEmbedNotice=将此粘贴至页面上
electrical=电路
ellipse=椭圆形
embedNotice=将此一次性粘贴至本页页尾
enterGroup=进入组进行编辑
enterName=输入名称
enterPropertyName=输入属性名
enterValue=输入值
entityRelation=实体关系
entityRelationshipDiagram=E-R图
error=出错
errorDeletingFile=删除文件出错
errorLoadingFile=加载文件出错
errorRenamingFile=文件修改名称出错
errorRenamingFileNotFound=文件修改名称出错。找不到文件。
errorRenamingFileForbidden=文件修改名称出错。没有足够的访问权限。
errorSavingDraft=保存草稿出错
errorSavingFile=保存文件出错
errorSavingFileUnknown=Google 服务器授权出错。请刷新页面，然后重试。
errorSavingFileForbidden=保存文件时出错。没有足够的访问权限。
errorSavingFileNameConflict=无法保存此绘图。当前页面已经包含名为 '{1}' 的文件。
errorSavingFileNotFound=保存文件时出错，文件未找到。
errorSavingFileReadOnlyMode=只读模式开启时无法保存绘图。
errorSavingFileSessionTimeout=您的会话已经结束。请 <a target='_blank' href='{1}'>{2}</a>，然后返回此标签以尝试再次保存。
errorSendingFeedback=发送反馈出错。
errorUpdatingPreview=更新预览出错。
exit=退出
exitGroup=退出编辑组
expand=展开
export=导出
exporting=正在导出
exportAs=导出为
exportOptionsDisabled=已禁止导出
exportOptionsDisabledDetails=所有者已禁止评论者及浏览者下载、打印或复制该文件。
externalChanges=外部修改
extras=其它
facebook=Facebook
failedToSaveTryReconnect=保存失败，正在尝试重新连接
featureRequest=增加功能请求
feedback=反馈
feedbackSent=反馈发送成功
floorplans=平面图
file=文件
fileChangedOverwriteDialog=文件已经被修改。是否保存文件并覆盖这些更改？
fileChangedSyncDialog=文件已经被修改。是否要同步这些修改？
fileChangedSync=文件已经被修改。点击这里进行同步。
overwrite=覆盖
synchronize=同步
filename=文件名
fileExists=文件已存在
fileMovedToTrash=文件已经移到垃圾箱
fileNearlyFullSeeFaq=文件即将达到上限，请参阅常见问题
fileNotFound=未找到文件
repositoryNotFound=未找到资源库
fileNotFoundOrDenied=文件未找到。其不存在或您没有查阅权限。
fileNotLoaded=文件无法加载
fileNotSaved=文件无法保存
fileOpenLocation=你想如何打开这些文件？
filetypeHtml=.html 会保存文件为 HTML 格式并重定向到云端 URL
filetypePng=.png 会保存文件为 PNG 格式并包含嵌入数据
filetypeSvg=.svg 会保存文件为 SVG 格式并包含嵌入数据
fileWillBeSavedInAppFolder={1} 将保存至应用软件文件夹
fill=填充
fillColor=填充色
filterCards=卡片筛选
find=查找
fit=适应大小
fitContainer=调整容器尺寸
fitIntoContainer=适应容器尺寸
fitPage=整页显示
fitPageWidth=适应页面宽度
fitTo=适应
fitToSheetsAcross=横向页数
fitToBy=按
fitToSheetsDown=纵向页数
fitTwoPages=双页
fitWindow=适应窗口大小
flip=翻转
flipH=水平翻转
flipV=垂直翻转
flowchart=流程图
folder=文件夹
font=字体
fontColor=字体颜色
fontFamily=字体
fontSize=字体大小
forbidden=您没有该文件的访问权限
format=格式
formatPanel=格式面板
formatted=格式化
formattedText=格式化文本
formatPng=PNG
formatGif=GIF
formatJpg=JPEG
formatPdf=PDF
formatSql=SQL
formatSvg=SVG
formatHtmlEmbedded=HTML
formatSvgEmbedded=SVG（XML）
formatVsdx=VSDX
formatVssx=VSSX
formatWebp=WebP
formatXmlPlain=XML（文本文件）
formatXml=XML
forum=讨论组 / 帮助论坛
freehand=自由绘图
fromTemplate=从模板
fromTemplateUrl=从模板 URL 地址
fromText=从文本
fromUrl=从 URL 地址
fromThisPage=从当前页
fullscreen=全屏
gap=间隙
gcp=GCP
general=通用
getNotionChromeExtension=获取 Notion Chrome 扩展程序
github=GitHub
gitlab=GitLab
gliffy=Gliffy
global=全局
googleDocs=Google 文档
googleDrive=Google Drive
googleGadget=Google Gadget
googleSharingNotAvailable=分享只有使用 Google Drive 时可用. 请点击下方的打开, 从更多操作菜单中分享.
googleSlides=Google 幻灯片
googleSites=Google 协作平台
googleSheets=Google 表格
gradient=渐变
gradientColor=颜色
grid=网格
gridColor=网格线颜色
gridSize=网格大小
group=组合
guides=参考线
hateApp=我讨厌 draw.io
heading=标题
height=高
help=帮助
helpTranslate=帮助我们翻译此应用
hide=隐藏
hideIt=隐藏 {1}
hidden=已隐藏
home=首页
horizontal=水平
horizontalFlow=水平流
horizontalTree=水平树
howTranslate=你所选语言的翻译质量如何？
html=HTML
htmlText=HTML 文本
id=ID
iframe=IFrame
ignore=忽略
image=图片
imageUrl=图片 URL 地址
images=图片
imagePreviewError=无法预览图片。请检查 URL 地址。
imageTooBig=图片太大
imgur=Imgur
import=导入
importFrom=从...导入
improveContrast=Improve Contrast
includeCopyOfMyDiagram=包含绘图副本
increaseIndent=增加缩进
decreaseIndent=减少缩进
insert=插入
insertColumnBefore=左边插入列
insertColumnAfter=右边插入列
insertEllipse=插入椭圆
insertImage=插入图片
insertHorizontalRule=插入水平标尺
insertLink=插入链接
insertPage=插入页面
insertRectangle=插入矩形
insertRhombus=插入菱形
insertRowBefore=上方插入行
insertRowAfter=下方插入行
insertText=插入文本
inserting=正在插入
installApp=安装应用
invalidFilename=绘图名称不能包含以下特殊字符： \ / | : ; { } < > & + ? = "
invalidLicenseSeeThisPage=您的许可无效，请参阅此<a target="_blank" href="https://www.drawio.com/doc/faq/license-drawio-confluence-jira-cloud">页面</a>。
invalidInput=无效输入
invalidName=无效名称
invalidOrMissingFile=无效或丢失的文件
invalidPublicUrl=无效的公开 URL 地址
isometric=等尺寸
ios=iOS
italic=斜体
kennedy=Kennedy
keyboardShortcuts=快捷键
labels=标签
layers=图层
landscape=横向
language=语言
leanMapping=价值流图
lastChange={1}以前最新更改
lessThanAMinute=一分钟以内
licensingError=授权出错
licenseHasExpired={1} 的许可证已于 {2} 过期。请点击此处。
licenseRequired=该功能需要 draw.io 被授权
licenseWillExpire={1} 的许可证将于 {2} 过期。请点击此处。
light=浅色
lineJumps=跨线
linkAccountRequired=如果绘图未公开，则需要提供谷歌账户才能查看该链接。
linkText=链接文本
list=列表
minute=分钟
minutes=分钟
hours=小时
days=天
months=月
years=年
restartForChangeRequired=更改将在页面刷新后生效。
laneColor=泳道颜色
languageCode=Language Code
lastModified=最近修改
layout=布局
left=左
leftAlign=左对齐
leftToRight=右对齐
libraryTooltip=将图形拖放至此或单击+以插入。双击进行编辑。
lightbox=灯箱特效
line=线条
lineend=线末端
lineheight=行高
linestart=线始端
linewidth=线宽
link=连接
links=连接
loading=加载中
lockUnlock=锁定 / 解锁
loggedOut=注销
logIn=登录
loveIt=我爱 {1}
lucidchart=Lucidchart
maps=地图
mathematicalTypesetting=数学排版
makeCopy=创建副本
manual=手册
merge=合并
mermaid=Mermaid
microsoftOffice=Microsoft Office
microsoftExcel=Microsoft Excel
microsoftPowerPoint=Microsoft PowerPoint
microsoftWord=Microsoft Word
middle=垂直居中
minimal=Minimal
misc=杂项
mockups=实体模型
modern=Modern
modificationDate=修改日期
modifiedBy=修改者
more=更多
moreResults=更多结果
moreShapes=更多图形
move=移动
moveToFolder=移动至文件夹
moving=移动中
moveSelectionTo=将所选移至 {1}
myDrive=My Drive
myFiles=My Files
name=名称
navigation=导航
network=网络
networking=网络
new=新建
newLibrary=新增图库
nextPage=下一页
no=否
noPickFolder=不，选择文件夹
noAttachments=未找到附件
noColor=无颜色
noFiles=无文件
noFileSelected=未选择文件
noLibraries=未找到图库
noMoreResults=无其他结果
none=无
noOtherViewers=无其他查阅者
noPlugins=无插件
noPreview=无预览
noResponse=服务器无响应
noResultsFor=未找到 '{1}' 的相关结果
noRevisions=无修订
noSearchResults=查询无结果
noPageContentOrNotSaved=此页面上找不到锚点，或尚未保存
normal=正常
north=向上
notADiagramFile=非绘图文件
notALibraryFile=非图库文件
notAvailable=不可用
notAUtf8File=非 UTF-8 格式文件
notConnected=未连接
note=备注
notion=Notion
notSatisfiedWithImport=对导入不满意?
notUsingService=未使用 {1}?
numberedList=编号列表
offline=离线
ok=确定
oneDrive=OneDrive
online=线上
opacity=不透明度
open=打开
openArrow=开放的箭头
openExistingDiagram=打开现有绘图
openFile=打开文件
openFrom=从...打开
openLibrary=打开图库
openLibraryFrom=从...打开图库
openLink=打开链接
openInNewWindow=在新窗口打开
openInThisWindow=在当前窗口打开
openIt=打开{1}
openRecent=打开最近使用的文件
openSupported=支持的格式有本软件保存的文件 (.xml), .vsdx 及 .gliffy
options=选项
organic=力导向图
orgChart=组织结构图
orthogonal=正交
otherViewer=其他查阅者
otherViewers=其他查阅者
outline=缩略图
oval=椭圆形
page=页面
pageContent=页面内容
pageNotFound=未找到页面
pageWithNumber=第 {1} 页
pages=页面
pageTabs=页面标签
pageView=页面视图
pageSetup=页面设置
pageScale=页面比例
pan=移动画布
panTooltip=按住空格键并拖拽以移动画布
paperSize=页面尺寸
pattern=样式
parallels=Parallels
paste=粘贴
pasteData=粘贴数据
pasteHere=在这粘贴
pasteSize=粘贴大小
pasteStyle=粘贴样式
perimeter=周长
permissionAnyone=任何人均可编辑
permissionAuthor=只有本人可编辑
pickFolder=选择文件夹
pickLibraryDialogTitle=选择图库
publicDiagramUrl=绘图的公共 URL 地址
placeholders=占位符
plantUml=PlantUML
plugins=插件
pluginUrl=插件 URL 地址
pluginWarning=本页面已要求载入以下插件：\n \n {1}\n \n 是否现在载入这些插件？\n \n 备注：确保在完全理解与此相关的安全问题的情况下再允许这些插件运行。\n
plusTooltip=单击进行连接与复制（ctrl+单击进行复制，shift+单击进行连接）。拖拽进行连接（ctrl+拖拽进行复制）。
portrait=竖向
position=位置
posterPrint=海报样式
preferences=首选项
preview=预览
previousPage=上一页
presentationMode=Presentation Mode
print=打印
printAllPages=打印所有页
procEng=工艺流程
project=方案
priority=优先级
processForHiringNewEmployee=Process for hiring a new employee
properties=属性
publish=发布
quickStart=快速入门视频
rack=机架
radial=Radial
radialTree=径向树
readOnly=只读
reconnecting=重新连接
recentlyUpdated=最近更新
recentlyViewed=最近阅览
rectangle=矩形
redirectToNewApp=该文件是在此应用软件的新版本中所创建或修改的。正在重新定向。
realtimeTimeout=似乎您在离线状态下做过更改。对不起，这些更改不予保存。
redo=重做
refresh=刷新
regularExpression=正则表达式
relative=相对
relativeUrlNotAllowed=不允许使用相对 URL 地址
rememberMe=记住我
rememberThisSetting=记住此设置
removeFormat=清除格式
removeFromGroup=移出组合
removeIt=删除 {1}
removeWaypoint=删除航点
rename=重命名
renamed=已重命名
renameIt=重命名 {1}
renaming=正在重命名
replace=替换
replaceIt={1} 已经存在了。确定要替换它吗？
replaceExistingDrawing=替换当前绘图
required=必填
requirementDiagram=Requirement Diagram
reset=重置
resetView=重置视图
resize=调整大小
resizeLargeImages=您是否想调整大图片的大小使应用运行更快?
retina=Retina
responsive=响应式
restore=恢复
restoring=正在恢复
retryingIn={1} 秒后重试
retryingLoad=载入失败，正在重试...
retryingLogin=登录超时，正在重试...
reverse=翻转
revision=修订
revisionHistory=修订历史
rhombus=菱形
right=右
rightAlign=右对齐
rightToLeft=由右至左
rotate=旋转
rotateTooltip=点选拖拽旋转，或点击选择90度
rotation=旋转
rounded=圆角
save=保存
saveAndExit=保存并退出
saveAs=另存为
saveAsXmlFile=另存为XML文件？
saved=已保存
saveDiagramFirst=请先保存绘图
saveDiagramsTo=保存绘图到
saveLibrary403=没有足够的权限编辑此图库
saveLibrary500=保存图库时出错
saveLibraryReadOnly=只读模式开启时无法保存图库
saving=正在保存
scratchpad=便笺本
scrollbars=滚动条
search=搜索
searchShapes=搜索图形
selectAll=全选
selectionOnly=仅所选内容
selectCard=选择卡片
selectEdges=选择边线
selectFile=选择文件
selectFolder=选择文件夹
selectFont=选择字体
selectNone=全不选
selectTemplate=选择模板
selectVertices=选择顶点
sendBackward=下移一层
sendMessage=发送
sendYourFeedback=发送您的反馈
sequenceDiagram=Sequence Diagram
serviceUnavailableOrBlocked=服务无法使用或已被屏蔽
sessionExpired=会话已过期，请刷新浏览器窗口。
sessionTimeoutOnSave=会话已超时，您的 Google Drive 连接已断开。按确定键登录并保存。
setAsDefaultStyle=设置为默认样式
settings=设置
shadow=阴影
shape=形状
shapes=形状
share=共享
shareCursor=Share Mouse Cursor
shareLink=共享编辑的链接
sharingAvailable=共享可用于 Google Drive 和 OneDrive 文件.
saveItToGoogleDriveToCollaborate=You'll need to save "{1}" to Google Drive before you can collaborate.
saveToGoogleDrive=Save to Google Drive
sharp=锐利
show=显示
showRemoteCursors=Show Remote Mouse Cursors
showStartScreen=显示开始画面
sidebarTooltip=单击以展开。将图形拖拽至绘图中。Shift+单击以改变所选内容。Alt+单击以插入及连接。
signs=标识
signOut=注销
simple=简单
simpleArrow=简单箭头
simpleViewer=Simple Viewer
size=大小
sketch=草图
snapToGrid=对齐到网格
solid=实线
sourceSpacing=源距
south=向下
software=软件
space=空间
spacing=间距
specialLink=特殊链接
stateDiagram=State Diagram
standard=标准
startDrawing=开始绘图
stopDrawing=结束绘图
starting=开启中
straight=直线
strikethrough=删除线
strokeColor=线条颜色
style=样式
subscript=下标
summary=概要
superscript=上标
support=支持
swap=Swap
swimlaneDiagram=泳道图
sysml=SysML
tags=标签
table=表格
tables=表格
takeOver=接管
targetSpacing=目标间距
template=模板
templates=模板
text=文本
textAlignment=文本对齐
textOpacity=字体不透明度
theme=主题
timeout=超时
title=标题
to=至
toBack=移至最后
toFront=移至最前
tooLargeUseDownload=文件太大，请使用下载。
toolbar=工具栏
tooltips=提示
top=上
topAlign=向上对齐
topLeft=左上
topRight=右上
transparent=透明
transparentBackground=透明背景
trello=Trello
tryAgain=重试
tryOpeningViaThisPage=尝试通过此页面开启
turn=旋转90°
type=类型
twitter=Twitter
uml=UML
unassigned=Unassigned
underline=下划线
undo=撤销
ungroup=取消组合
unmerge=取消合并
unsavedChanges=未保存的更改
unsavedChangesClickHereToSave=修改未保存。点击此处保存。
untitled=未命名
untitledDiagram=未命名绘图
untitledLayer=未命名图层
untitledLibrary=未命名图库
unknownError=未知错误
updateFile=更新{1}
updatingDocument=文件更新中。请稍候...
updatingPreview=预览更新中。请稍候...
updatingSelection=选择更新中。请稍候...
upload=上传
url=URL
useOffline=离线使用
useRootFolder=要使用根目录吗？
userManual=用户手册
vertical=垂直
verticalFlow=垂直流
verticalTree=垂直树
view=查看
viewerSettings=查看器设置
viewUrl=用于查看的链接：{1}
voiceAssistant=语音助手（测试版）
warning=警告
waypoints=航点
west=向左
where=Where
width=宽
wiki=Wiki
wordWrap=自动换行
writingDirection=书写方向
yes=是
yourEmailAddress=您的电子邮件地址
zoom=缩放
zoomIn=放大
zoomOut=缩小
basic=基本
businessprocess=业务流程图
charts=图表
engineering=工程
flowcharts=流程图
gmdl=材料设计
mindmaps=思维导图
mockups=模型图
networkdiagrams=网络结构图
nothingIsSelected=未选择
other=其他
softwaredesign=软件设计图
venndiagrams=维恩图
webEmailOrOther=网站、电子邮件或其他网络地址
webLink=Web链接
wireframes=线框图
property=属性
value=值
showMore=显示更多
showLess=显示更少
myDiagrams=我的绘图
allDiagrams=全部绘图
recentlyUsed=最近使用
listView=列表视图
gridView=网格视图
resultsFor={1}' 的结果
oneDriveCharsNotAllowed=不允许下列字符: ~ " # %  * : < > ? / \ { | }
oneDriveInvalidDeviceName=指定的设备名称无效
officeNotLoggedOD=您没有登录到 OneDrive. 请先打开 draw.io 任务栏并登录.
officeSelectSingleDiag=请选择一个没有其他内容的 draw.io 绘图
officeSelectDiag=请选择 draw.io 绘图
officeCannotFindDiagram=在选择中无法找到 draw.io 绘图
noDiagrams=没有找到绘图
authFailed=认证失败
officeFailedAuthMsg=无法成功认证用户或认证应用
convertingDiagramFailed=转换绘图失败
officeCopyImgErrMsg=由于主机应用的一些限制, 图片无法被插入. 请手动赋值图片然后粘贴到文档中.
insertingImageFailed=插入图片失败
officeCopyImgInst=说明: 右键点击下方的图片. 在上下文菜单中选择 "复制图片". 然后在文档中, 右键点击并在上下文菜单中选择 "粘贴"
folderEmpty=文件夹为空
recent=最近
sharedWithMe=与我分享
sharepointSites=Sharepoint 站点
errorFetchingFolder=获取文件夹项目出错
errorAuthOD=OneDrive 认证出错
officeMainHeader=添加 draw.io 绘图到您的文档
officeStepsHeader=该加载项执行以下步骤：
officeStep1=连接到 Microsoft OneDrive, Google Drive 或您的设备
officeStep2=选择 draw.io 绘图
officeStep3=插入绘图到文档
officeAuthPopupInfo=请在弹出的窗口中完成认证
officeSelDiag=选择 draw.io 绘图
files=文件
shared=已分享
sharepoint=Sharepoint
officeManualUpdateInst=说明: 从文档中复制 draw.io 绘图. 然后在下方的框中右键点击然后在上下文菜单中选择 "粘贴".
officeClickToEdit=点击图标开始编辑
pasteDiagram=在这里粘贴 draw.io 绘图
connectOD=连接到 OneDrive
selectChildren=选择子元素
selectSiblings=选择同级元素
selectParent=选择父元素
selectDescendants=选择后续元素
lastSaved=上次保存 {1} 之前
resolve=解决
reopen=重新打开
showResolved=显示已解决
reply=回复
objectNotFound=找不到对象
reOpened=重新打开的
markedAsResolved=标记为已解决
noCommentsFound=找不到评论
comments=评论
timeAgo={1} 之前
confluenceCloud=Confluence 云
libraries=库
confAnchor=Confluence 页面锚点
confTimeout=连接超时
confSrvTakeTooLong={1} 服务器响应时间过长
confCannotInsertNew=无法在新的 Confluence 页中插入 draw.io 绘图
confSaveTry=请保存页面并重试
confCannotGetID=无法确定页面ID
confContactAdmin=请联系您的 Confluence 管理员
readErr=读取错误
editingErr=编辑错误
confExtEditNotPossible=绘图无法在外部编辑. 请尝试在编辑页面时编辑它.
confEditedExt=绘图/页面 已在外部编辑
diagNotFound=绘图找不到
confEditedExtRefresh=绘图/页面 已在外部编辑. 请刷新页面.
confCannotEditDraftDelOrExt=无法在草稿页面中进行编辑, 该绘图已经在其他页面删除或外部编辑. 请检查页面.
retBack=Return back
confDiagNotPublished=改绘图不属于已发布的页面
createdByDraw=使用 draw.io 创建
filenameShort=文件名太短
invalidChars=无效的字符
alreadyExst={1} 已经存在
draftReadErr=草稿读取错误
diagCantLoad=无法加载绘图
draftWriteErr=草稿写入错误
draftCantCreate=无法创建草稿
confDuplName=检测到重复的绘图名. 请重新选择.
confSessionExpired=似乎登陆已经过期. 重新登录以避免中断.
login=登录
drawPrev=draw.io 预览
drawDiag=draw.io 绘图
invalidCallFnNotFound=无效调用: 无法找到 {1}
invalidCallErrOccured=无效调用: 检测到错误, {1}
anonymous=匿名
confGotoPage=Go to containing page
showComments=显示评论
confError=错误：{1}
gliffyImport=导入 Gliffy
gliffyImportInst1=点击 "开始导入" 按钮导入所有 Gliffy 绘图到 draw.io。
gliffyImportInst2=请注意，导入过程需要一些时间，导入完成前浏览器窗口必须保持打开状态。
startImport=开始导入
drawConfig=draw.io 配置
customLib=自定义库
customTemp=自定义模板
pageIdsExp=导出页面 ID
drawReindex=draw.io 正在重新索引 (测试)
working=Working
drawConfigNotFoundInst=draw.io 配置空间 (DRAWIOCONFIG) 不存在. 存储 draw.io 配置文件和自定义库/模板时需要该空间.
createConfSp=创建配置空间
unexpErrRefresh=发生意外错误, 请刷新页面, 然后重试.
configJSONInst=在下方编辑器填写 draw.io JSON 配置内容然后点击保存. 如果需要帮助, 请参考
thisPage=this page
curCustLib=Current Custom Libraries
libName=Library Name
action=操作
drawConfID=draw.io 配置 ID
addLibInst=Click the "Add Library" button to upload a new library.
addLib=Add Library
customTempInst1=Custom templates are draw.io diagrams saved in children pages of
customTempInst2=如需更多信息，请参考
tempsPage=模板页面
pageIdsExpInst1=选择导出目标，然后点击"开始导出"按钮以导出所有的页面 ID。
pageIdsExpInst2=Please note that the export procedure will take some time and the browser window must remain open until the export is completed.
startExp=开始导出
refreshDrawIndex=刷新 draw.io 绘图索引
reindexInst1=点击"开始索引"按钮来刷新 draw.io 绘图索引。
reindexInst2=请注意，编制索引需要一些时间，在索引编制完成之前，浏览器窗口必须保持打开状态。
startIndexing=开始索引
confAPageFoundFetch=找到页面 "{1}"，获取中
confAAllDiagDone=All {1} diagrams processed. Process finished.
confAStartedProcessing=开始处理页面 "{1}"
confAAllDiagInPageDone=All {1} diagrams in page "{2}" processed successfully.
confAPartialDiagDone={1} out of {2} {3} diagrams in page "{4}" processed successfully.
confAUpdatePageFailed=更新页面 "{1}" 失败。
confANoDiagFoundInPage=No {1} diagrams found in page "{2}".
confAFetchPageFailed=获取页面失败。
confANoDiagFound=No {1} diagrams found. Process finished.
confASearchFailed=Searching for {1} diagrams failed. Please try again later.
confAGliffyDiagFound={2} diagram "{1}" found. Importing
confAGliffyDiagImported={2} diagram "{1}" imported successfully.
confASavingImpGliffyFailed=Saving imported {2} diagram "{1}" failed.
confAImportedFromByDraw=Imported from "{1}" by draw.io
confAImportGliffyFailed=Importing {2} diagram "{1}" failed.
confAFetchGliffyFailed=Fetching {2} diagram "{1}" failed.
confACheckBrokenDiagLnk=Checking for broken diagrams links.
confADelDiagLinkOf=Deleting diagram link of "{1}"
confADupLnk=(duplicate link)
confADelDiagLnkFailed=Deleting diagram link of "{1}" failed.
confAUnexpErrProcessPage=Unexpected error during processing the page with id: {1}
confADiagFoundIndex=Diagram "{1}" found. Indexing
confADiagIndexSucc=Diagram "{1}" indexed successfully.
confAIndexDiagFailed=索引绘图 "{1}" 失败。
confASkipDiagOtherPage=Skipped "{1}" as it belongs to another page!
confADiagUptoDate=绘图 "{1}" 已是最新。
confACheckPagesWDraw=正在检查包含 draw.io 绘图的页面。
confAErrOccured=发生错误！
savedSucc=保存成功
confASaveFailedErr=保存失败（意外错误）
character=字符
confAConfPageDesc=This page contains draw.io configuration file (configuration.json) as attachment
confALibPageDesc=This page contains draw.io custom libraries as attachments
confATempPageDesc=This page contains draw.io custom templates as attachments
working=Working
confAConfSpaceDesc=This space is used to store draw.io configuration files and custom libraries/templates
confANoCustLib=No Custom Libraries
delFailed=删除失败!
showID=显示 ID
confAIncorrectLibFileType=无效的文件类型。库应该是 XML 文件。
uploading=正在上传
confALibExist=库已经存在
confAUploadSucc=上传成功
confAUploadFailErr=上传失败（意外错误）
hiResPreview=高分辨率预览
officeNotLoggedGD=您没有登录到 Google Drive。请先打开 draw.io 任务面板并登录。
officePopupInfo=请在弹出的窗口中完成流程。
pickODFile=选择 OneDrive 文件
createODFile=创建 OneDrive 文件
pickGDriveFile=选择 Google Drive 文件
createGDriveFile=创建 Goole Drive 文件
pickDeviceFile=选择设备文件
vsdNoConfig="vsdurl" 没有配置
ruler=标尺
units=单位
points=点
inches=英寸
millimeters=毫米
confEditDraftDelOrExt=This diagram is in a draft page, is deleted from the page, or is edited externally. It will be saved as a new attachment version and may not be reflected in the page.
confDiagEditedExt=Diagram is edited in another session. It will be saved as a new attachment version but the page will show other session's modifications.
macroNotFound=找不到宏
confAInvalidPageIdsFormat=Incorrect Page IDs file format
confACollectingCurPages=Collecting current pages
confABuildingPagesMap=Building pages mapping
confAProcessDrawDiag=开始处理导入的 draw.io 绘图
confAProcessDrawDiagDone=处理导入的 draw.io 绘图已完成
confAProcessImpPages=开始处理导入的页面
confAErrPrcsDiagInPage=Error processing draw.io diagrams in page "{1}"
confAPrcsDiagInPage=Processing draw.io diagrams in page "{1}"
confAImpDiagram=正在导入绘图 "{1}"
confAImpDiagramFailed=Importing diagram "{1}" failed. Cannot find its new page ID. Maybe it points to a page that is not imported.
confAImpDiagramError=Error importing diagram "{1}". Cannot fetch or save the diagram. Cannot fix this diagram links.
confAUpdateDgrmCCFailed=Updating link to diagram "{1}" failed.
confImpDiagramSuccess=Updating diagram "{1}" done successfully.
confANoLnksInDrgm=No links to update in: {1}
confAUpdateLnkToPg=Updated link to page: "{1}" in diagram: "{2}"
confAUpdateLBLnkToPg=Updated lightbox link to page: "{1}" in diagram: "{2}"
confAUpdateLnkBase=Updated base URL from: "{1}" to: "{2}" in diagram: "{3}"
confAPageIdsImpDone=已导入页面 ID
confAPrcsMacrosInPage=正在处理页面 "{1}" 的宏
confAErrFetchPage=Error fetching page "{1}"
confAFixingMacro=Fixing macro of diagram "{1}"
confAErrReadingExpFile=读取导出的文件时发生错误
confAPrcsDiagInPageDone=Processing draw.io diagrams in page "{1}" finished
confAFixingMacroSkipped=无法修复图表 "{1}" 的宏。无法找到其新的页面 ID。也许它指向的页面没有被导入。
pageIdsExpTrg=导出目标
confALucidDiagImgImported={2} diagram "{1}" image extracted successfully
confASavingLucidDiagImgFailed=Extracting {2} diagram "{1}" image failed
confGetInfoFailed=Fetching file info from {1} failed.
confCheckCacheFailed=Cannot get cached file info.
confReadFileErr=Cannot read "{1}" file from {2}.
confSaveCacheFailed=意外错误。无法保存缓存文件。
orgChartType=组织结构图类型
linear=线性
hanger2=Hanger 2
hanger4=Hanger 4
fishbone1=Fishbone 1
fishbone2=Fishbone 2
1ColumnLeft=单列居左
1ColumnRight=单列居右
smart=智能
parentChildSpacing=父子间距
siblingSpacing=兄弟间距
confNoPermErr=抱歉，您没有足够的权限查看页面 {1} 中嵌入的绘图
copyAsImage=复制为图像
lucidImport=导入 Lucidchart
lucidImportInst1=点击 "开始导入" 按钮导入所有 Lucidchart 图表
installFirst=请先安装 {1}
drawioChromeExt=draw.io Chrome 扩展
loginFirstThen=请先登录 {1}，然后 {2}
errFetchDocList=错误：无法获取文档列表
builtinPlugins=内置插件
extPlugins=扩展插件
backupFound=找到备份文件
chromeOnly=该功能只适用于 Google Chrome
msgDeleted=消息已经删除
confAErrFetchDrawList=无法获取绘图列表。部分绘图已跳过。
confAErrCheckDrawDiag=无法检查绘图 {1}
confAErrFetchPageList=无法获取页面列表
confADiagImportIncom={1} diagram "{2}" is imported partially and may have missing shapes
invalidSel=无效选择
diagNameEmptyErr=绘图名称不能为空
openDiagram=打开绘图
newDiagram=新绘图
editable=可编辑
confAReimportStarted=开始 {1} 绘图的重新导入...
spaceFilter=Filter by spaces
curViewState=查看器当前状态
pageLayers=页面和图层
customize=自定义
firstPage=第一页 (所有图层)
curEditorState=编辑器当前状态
noAnchorsFound=未找到锚点
attachment=附件
curDiagram=当前绘图
recentDiags=最近绘图
csvImport=导入 CSV
chooseFile=选择文件...
choose=选择
gdriveFname=Google Drive 文件名
widthOfViewer=查看器宽度 (px)
heightOfViewer=查看器高度 (px)
autoSetViewerSize=自动设置查看器的大小
thumbnail=缩略图
prevInDraw=在 draw.io 中预览
onedriveFname=OneDrive 文件名
diagFname=绘图文件名
diagUrl=绘图地址
showDiag=显示绘图
diagPreview=绘图预览
csvFileUrl=CSV 文件 URL
generate=生成
selectDiag2Insert=请选择绘图并插入。
errShowingDiag=意外的错误。无法显示绘图。
noRecentDiags=没有最近的绘图
fetchingRecentFailed=无法获取最近的绘图
useSrch2FindDiags=使用搜索框查找 draw.io 绘图
cantReadChckPerms=无法读取绘图。请检查您具备此文件的读取权限。
cantFetchChckPerms=无法获取绘图信息。请检查您具备此文件的读取权限。
searchFailed=搜索失败。请稍后再试。
plsTypeStr=请输入文本以搜索。
unsupportedFileChckUrl=不支持的文件。请检查指定URL
diagNotFoundChckUrl=绘图不存在或无法访问。请检查指定URL
csvNotFoundChckUrl=CSV 文件不存在或无法访问。请检查指定URL
cantReadUpload=无法读取上传的绘图
select=选择
errCantGetIdType=Unexpected Error: Cannot get content id or type.
errGAuthWinBlocked=错误：Google Authentication 窗口被阻止
authDrawAccess=授权 draw.io 访问 {1} 数据
connTimeout=连接超时
errAuthSrvc=无法与 {1} 认证
plsSelectFile=请选择文件
mustBgtZ={1} 应大于或等于0
cantLoadPrev=无法加载文件预览
errAccessFile=错误：访问被拒绝。请没有权限访问 "{1}"。
noPrevAvail=预览不可用。
personalAccNotSup=个人设置不支持。
errSavingTryLater=保存时发生错误，请稍后重试。
plsEnterFld=请输入 {1}
invalidDiagUrl=无效的绘图地址
unsupportedVsdx=不支持的 vsdx 文件
unsupportedImg=不支持的图片文件
unsupportedFormat=不支持的文件格式
plsSelectSingleFile=请仅选择单个文件
attCorrupt=附件文件 "{1}" 损坏
loadAttFailed=无法加载附件 "{1}"
embedDrawDiag=嵌入 draw.io 绘图
addDiagram=添加绘图
embedDiagram=嵌入绘图
editOwningPg=编辑拥有的页面
deepIndexing=Deep Indexing (Index diagrams that aren't used in any page also)
confADeepIndexStarted=已启动深度索引
confADeepIndexDone=深度索引已完成
officeNoDiagramsSelected=No diagrams found in the selection
officeNoDiagramsInDoc=No diagrams found in the document
officeNotSupported=This feature is not supported in this host application
someImagesFailed={1} out of {2} failed due to the following errors
importingNoUsedDiagrams=Importing {1} Diagrams not used in pages
importingDrafts=Importing {1} Diagrams in drafts
processingDrafts=处理草稿
updatingDrafts=正在更新草稿
updateDrafts=更新草稿
notifications=通知
drawioImp=draw.io 导入
confALibsImp=导入 draw.io 库
confALibsImpFailed=导入 {1} 库失败
contributors=贡献者
drawDiagrams=draw.io 绘图
errFileNotFoundOrNoPer=错误：访问被拒绝。文件不存在或您没有在 {2} 访问 {1} 的权限。
confACheckPagesWEmbed=正在检查嵌入了 draw.io 绘图的页面。
confADelBrokenEmbedDiagLnk=正在移除失效的嵌入绘图链接
replaceWith=替换为
replaceAll=全部替换
confASkipDiagModified=Skipped "{1}" as it was modified after initial import
replFind=替换 / 查找
matchesRepl={1} 个匹配已替换
draftErrDataLoss=An error occurred while reading the draft file. The diagram cannot be edited now to prevent any possible data loss. Please try again later or contact support.
ibm=IBM
linkToDiagramHint=Add a link to this diagram. The diagram can only be edited from the page that owns it.
linkToDiagram=Link to Diagram
changedBy=Changed By
lastModifiedOn=最后修改于
searchResults=搜索结果
showAllTemps=显示所有模板
notionToken=Notion 令牌
selectDB=选择数据库
noDBs=没有数据库
diagramEdited={1} diagram "{2}" edited
confDraftPermissionErr=无法写入草稿。您是否有写入/读取该页面附件的权限？
confFileTooBigErr=草稿文件过大。请检查 Confluence 配置中 "附件设置" 里的 "附件最大大小"
owner=所有者
repository=仓库
branch=分支
meters=米
teamsNoEditingMsg=编辑器功能仅在桌面环境中可用（在微软 Teams 应用或浏览器中）
contactOwner=联系所有者
viewerOnlyMsg=您不能在移动平台编辑绘图，请使用桌面客户端或网页浏览器。
website=网站
check4Updates=检查更新
attWriteFailedRetry={1}: 附件写入失败，请在 {2} 秒后重试...
confPartialPageList=由于 Confluence 的问题，我们无法获取所有的页面。仅使用 {1} 页面继续。
spellCheck=拼写检查
noChange=无变化
lblToSvg=转换标签为 SVG
txtSettings=文本设置
LinksLost=连接将会丢失
arcSize=弧度
editConnectionPoints=编辑连接点
notInOffline=离线时不支持
notInDesktop=桌面应用中不支持
confConfigSpaceArchived=draw.io 配置空间 (DRAWIOCONFIG) 已存档。请先恢复。
confACleanOldVerStarted=开始清理旧的绘图草稿版本
confACleanOldVerDone=清理旧的绘图草稿版本完成
confACleaningFile=清理绘图草稿 "{1}" 旧的版本
confAFileCleaned=清理绘图草稿 "{1}" 完成
confAFileCleanFailed=清理绘图草稿 "{1}" 失败
confACleanOnly=仅清理绘图草稿
brush=笔刷
openDevTools=打开开发者工具
autoBkp=自动备份
confAIgnoreCollectErr=忽略收集当前页面的错误
drafts=草稿
draftSaveInt=草稿保存间隔 [秒] (0为禁用)
pluginsDisabled=外部插件已禁用
extExpNotConfigured=没有配置外部图片服务
pathFilename=路径/文件名
confAHugeInstances=Very Large Instances
confAHugeInstancesDesc=If this instance includes 100,000+ pages, it is faster to request the current instance pages list from Atlassian. Please contact our support for more details.
choosePageIDsFile=Choose current page IDs csv file
chooseDrawioPsgesFile=Choose pages with draw.io diagrams csv file
private=Private
diagramTooLarge=绘图过大，请减小其尺寸后再试一次。
selectAdminUsers=选择管理用户
xyzTeam={1} Team
addTeamTitle=新建一个 draw.io 团队
addTeamInst1=To create a new draw.io Team, you need to create a new Atlassian group with "drawio-" prefix (e.g, a group named "drawio-marketing").
addTeamInst2=Then, configure which team member can edit/add configuration, templates, and libraries from this page.
drawioTeams=draw.io 团队
members=队员
adminEditors=管理员/编辑人员
allowAll=Allow all
noTeams=找不到团队
errorLoadingTeams=加载团队错误
noTeamMembers=找不到队员
errLoadTMembers=加载队员错误
errCreateTeamPage=Error creating team "{1}" page in "draw.io Configuration" space, please check you have the required permissions.
gotoConfigPage=Please create the space from draw.io "Configuration" page.
noAdminsSelected=No admins/editors selected
errCreateConfigFile=Error creating "configuration.json" file, please check you have the required permissions.
errSetPageRestr=Error setting page restrictions
notAdmin4Team=你不是此团队的管理员
configUpdated=Configuration updated, restart the editor if you want to work with last configuration.
outOfDateRevisionAlert=You are editing a historical revision of the diagram, please review the revision and open it to replace the latest version. Or close and overwrite/merge later.
confAErrFaqs=There are {1} error(s), the following instructions may help fixing most of the cases. (Please download the log for future references)
confA403ErrFaq=There are ({1}) 403 error(s). The current users must have add (write) permissions on all pages and attachments. Even admins sometimes are not allowed to write to some pages via page restrictions
confA404ErrFaq=There are ({1}) 404 error(s). The attachment/page is not found. This is due to improper migration or the diagram file (an attachment of the page) is deleted.
confA500ErrFaq=There are ({1}) 500 error(s). An internal server error in Confluence Cloud. Such errors are due to overloading the server and usually fixed by retrying the process.
confAOtherErrFaq=There are ({1}) other error(s). Please check the error description. If the description is not clear, please contact our support.
confAReplaceBaseUrl=Replace Base URL in diagram links when no page ID mapping is found
drawSvgPrev=draw.io SVG preview
googleFonts=Google Fonts
diagDupl=Duplicate Diagram Detected
diagDuplMsg=This diagram is used in multiple places, which can result in unexpected results when edited. We've created an independent copy. Please open the editor again.
diagDuplNoEditMsg=This diagram is used in multiple places. Please edit it within its own page.
confCloudMigConfirm=Warning: This process will edit many pages and diagrams, so it is recommended to stop the Synchrony service during the process. Do you want to proceed?
confCloudMigNotice=In the Cloud instance, please add linkAdjustments to draw.io configuration as follows {1}. Without this configuation, links in diagrams pointing to Confluence pages will not work.
