:root {
	--panel-color: #f1f3f4;
	--border-color: #dadce0;
	--text-color: #707070;
}
.geDarkMode {
	filter: invert(93%) hue-rotate(180deg);
	background:#151515;
}
.geDarkMode image, .geDarkMode img:not(.geAdaptiveAsset), .geDarkMode iframe {
	filter: invert(100%) hue-rotate(180deg) saturate(1.25);
}
.geDarkMode div.mxWindow {
	border-color: darkgray;
}
.geDarkMode div.mxRubberband {
	border:1px dashed #202020 !important;
	background:#a0a0a0 !important;
}
.geDarkMode .geAdaptiveAsset {
	filter:none !important
}
.geEditor {
	position:absolute;
	width:100%;
	height:100%;
}
.geEditor *, div.mxWindow, .mxWindowTitle,
.geEditor .geToolbarContainer .geColorButton {
	border-color:var(--border-color);
}
html div.mxWindow, .geDialog, .geSketch .geToolbarContainer {
	border-radius: 5px;
	box-shadow: 0px 0px 2px #C0C0C0;
}
div td.mxWindowTitle {
	border-bottom-style:solid;
	border-bottom-width:1px;
	font-size: 13px;
	height: 22px;
}
.mxWindowTitle > div > img {
	padding: 4px;
}
.geEditor {
	font-family:-apple-system, BlinkMacSystemFont, "Segoe UI Variable", "Segoe UI", system-ui, ui-sans-serif, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji";
	font-size:14px;
	border:none;
	margin:0px;
}
.geBackground {
	background:white;
}
.geEditor input[type=text]::-ms-clear {
	display: none;
}
.geEditor input, .geEditor select,
.geEditor textarea, .geEditor button {
    font-size: inherit;
}
.geMenubarContainer, .geToolbarContainer, .geHsplit,
.geVsplit, .mxWindowTitle, .geSidebarContainer,
.geEditor .geTabItem {
	background:var(--panel-color);
}
.geButtonContainer {
	display: inline-flex;
	align-items: center;
	padding: 0 32px 0 0;
	margin-left: auto;
}
.geEditor .geTabItem {
	border-width:1px;
	border-top-style:solid;
}
.geEditor .geTabItem {
	cursor:default;
	user-select:none;
}
.geEditor div.mxTooltip {
	background: var(--panel-color);
	font-size: 11px;
	color: black;
	padding:6px;
}
.geDragPreview {
	border: 1px dashed black;
}
html > body > div > div.geToolbarContainer.geSimpleMainMenu,
html > body > div > div.geToolbarContainer.geSimpleMainMenu .geToolbarContainer{
	border-top: none !important;
	border-left: none !important;
	border-right: none !important;
}
html > body > div > div.geToolbarContainer.geSimpleMainMenu .geToolbarContainer{
	border:none !important;
}
.geMenubarContainer {
	display:inline-flex;
	align-items:center;
}
.geMenubarContainer .geItem, .geToolbar .geButton, .geToolbar .geLabel {
	cursor:pointer !important;
}
.geSidebarContainer .geTitle {
	cursor:default !important;
}
.geBackgroundPage {
  	box-shadow:0px 0px 2px 1px #d1d1d1;
}
.geSidebarContainer a, .geMenubarContainer a, .geToolbar a {
	color:#000000;
	text-decoration:none;
}
.geMenubarContainer, .geToolbarContainer, .geDiagramContainer, .geSidebarContainer, .geFooterContainer, .geHsplit, .geVsplit {
	overflow:hidden;
	position:absolute;
	cursor:default;
}
.geFormatContainer {
	overflow-x:hidden !important;
	overflow-y:auto !important;
	font-size:12px;
	border-left:1px solid #dadce0;
	transition:width 0.3s;
}
.geFormatContainer button:not(.geColorBtn), .geFormatContainer select {
	border-radius:4px;
	padding:2px;
}
.geSidebarFooter {
	border-top:1px solid #dadce0;
}
.geFormatSection {
	border-bottom:1px solid #dadce0;
	border-color:#dadce0;
}
.geDiagramContainer {
	background-color:#ffffff;
	font-size:0px;
	outline:none;
}
.geMenubar, .geToolbar {
	white-space:nowrap;
	display:block;
	width:100%;
}
.geMenubarContainer .geItem, .geToolbar .geButton, .geToolbar .geLabel,
.geSidebar, .geSidebar .geItem, .mxPopupMenuItem {
	-webkit-transition: all 0.1s ease-in-out;
	-moz-transition: all 0.1s ease-in-out;
	-o-transition: all 0.1s ease-in-out;
	-ms-transition: all 0.1s ease-in-out;
	transition: all 0.1s ease-in-out;
}
.geHint {
	background-color: #ffffff;
	border: 1px solid gray;
	padding: 4px 16px 4px 16px;
	border-radius:3px;
	-webkit-box-shadow: 1px 1px 2px 0px #ddd;
	-moz-box-shadow: 1px 1px 2px 0px #ddd;
	box-shadow: 1px 1px 2px 0px #ddd;
	opacity:0.8;
	font-size:9pt;
}
.geHint img {
	opacity: 0.7;
}
.geHint img:hover
{
	opacity: 1;
}
.geUser {
	color:var(--text-color);
	display:inline-block;
	cursor:pointer;
	font-size:10px;
}
.geStatus > * {
	overflow:hidden;
	white-space:nowrap;
	vertical-align:middle;
	display:inline-block;
	font-size:12px;
	color:var(--text-color);
}
a.geStatus {
	display:inline-flex;
	align-items:center;
	white-space:nowrap;
	min-width:0;
	height:100%;
}
.geStatus *[data-action] {
	cursor:pointer;
}
.geStatus img {
	max-width:16px;
	vertical-align:bottom;
}
a.geStatus div + div {
	margin-left:8px;
}
a.geStatus .geStatusBox {
	border-style: solid;
	border-width: 1px;
	border-radius: 3px;
	font-size: 10px;
	padding: 3px;
}
a.geStatus .geStatusAlert {
	padding:4px 8px;
	background-color:#eacccc;
	border:1px solid #dca4ad;
	color:#b62623 !important;
	border-radius:3px;
}
a.geStatus .geStatusAlertOrange {
	padding:4px 8px;
	background-color:rgb(242, 147, 30);
	border:rgb(240, 135, 5);
	color:#000000 !important;
	border-radius:3px;
}
html body div.geSmallBanner {
	background-color: #F2931E;
	background-image: linear-gradient(#F2931E 0px,#F08707 100%);
	border: 1px solid #F08707;
	color: #000;
}
html body div.geSmallBanner:hover:not([disabled]) {
	background-color: #ffb75e;
	background-image: linear-gradient(#ffb75e 0px,#F2931E 100%);
	border: 1px solid #F08707;
	color: #000;
}
a.geStatus .geStatusMessage {
	padding:4px 6px 4px 6px;
	font-size:12px;
	background: -webkit-linear-gradient(top,#dff0d8 0,#c8e5bc 100%);
    background: -o-linear-gradient(top,#dff0d8 0,#c8e5bc 100%);
    background: -webkit-gradient(linear,left top,left bottom,from(#dff0d8),to(#c8e5bc));
    background: linear-gradient(to bottom,#dff0d8 0,#c8e5bc 100%);
    background-repeat: repeat-x;
    border:1px solid #b2dba1;
	border-radius:3px;
	color:#3c763d !important;
}
.geAlert {
	position:absolute;
	white-space:nowrap;
	padding:14px;
	background-color:#f2dede;
	border:1px solid #ebccd1;
	color:#a94442;
	border-radius:3px;
	-webkit-box-shadow: 2px 2px 3px 0px #ddd;
	-moz-box-shadow: 2px 2px 3px 0px #ddd;
	box-shadow: 2px 2px 3px 0px #ddd;
}
.geEditor input, .geEditor button, .geEditor select, .geColorBtn {
	border: 1px solid #d8d8d8;
	border-radius: 4px;
}
.geEditor button, .geEditor select, .geColorBtn {
	background:#eee;
}
.geEditor button:hover:not([disabled], .geBigButton, .geShareBtn),
.geEditor select:hover:not([disabled]), .geColorBtn:hover:not([disabled]) {
	background:#e5e5e5;
}
.geColorDropper {
	cursor:pointer;
	opacity:0.7;
}
.geColorDropper:hover {
	opacity:1;
}
.geBtn, .mxWindow .geBtn {
	font-size: 13px;
	font-weight: 500;
	border-radius: 4px;
	height: 30px;
	margin: 0 0 0 8px;
	min-width: 72px;
	outline: 0;
	padding: 0 8px;
}
.geBtn:hover:not([disabled]), .geBtn:focus {
	-webkit-box-shadow: 0px 1px 1px rgba(0,0,0,0.1);
	-moz-box-shadow: 0px 1px 1px rgba(0,0,0,0.1);
	box-shadow: 0px 1px 1px rgba(0,0,0,0.1);
	color: #111;
}
.geToolbarContainer {
	border-width:1px;
	border-color:lightgray;
	border-style:none none solid none;
	box-shadow:none;
	z-index:1;
}
.geShapePicker {
	position:absolute;
	border-radius:10px;
	border-style:solid;
	border-width:1px;
	padding:6px 0 8px 0;
	text-align:center;
	box-shadow:0px 0px 3px 1px #d1d1d1;
}
.geBtnStepper {
	border-radius:3px;
	border-style:solid;
	border-width:1px;
}
.geBtnUp {
	background-image: url(data:image/gif;base64,R0lGODlhCgAGAJECAGZmZtXV1f///wAAACH/C1hNUCBEYXRhWE1QPD94cGFja2V0IGJlZ2luPSLvu78iIGlkPSJXNU0wTXBDZWhpSHpyZVN6TlRjemtjOWQiPz4gPHg6eG1wbWV0YSB4bWxuczp4PSJhZG9iZTpuczptZXRhLyIgeDp4bXB0az0iQWRvYmUgWE1QIENvcmUgNS4wLWMwNjAgNjEuMTM0Nzc3LCAyMDEwLzAyLzEyLTE3OjMyOjAwICAgICAgICAiPiA8cmRmOlJERiB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmRmLXN5bnRheC1ucyMiPiA8cmRmOkRlc2NyaXB0aW9uIHJkZjphYm91dD0iIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8iIHhtbG5zOnhtcE1NPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvbW0vIiB4bWxuczpzdFJlZj0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL3NUeXBlL1Jlc291cmNlUmVmIyIgeG1wOkNyZWF0b3JUb29sPSJBZG9iZSBQaG90b3Nob3AgQ1M1IE1hY2ludG9zaCIgeG1wTU06SW5zdGFuY2VJRD0ieG1wLmlpZDo0QzM3ODJERjg4NUQxMUU0OTFEQ0E2MzRGQzcwNUY3NCIgeG1wTU06RG9jdW1lbnRJRD0ieG1wLmRpZDo0QzM3ODJFMDg4NUQxMUU0OTFEQ0E2MzRGQzcwNUY3NCI+IDx4bXBNTTpEZXJpdmVkRnJvbSBzdFJlZjppbnN0YW5jZUlEPSJ4bXAuaWlkOjRDMzc4MkREODg1RDExRTQ5MURDQTYzNEZDNzA1Rjc0IiBzdFJlZjpkb2N1bWVudElEPSJ4bXAuZGlkOjRDMzc4MkRFODg1RDExRTQ5MURDQTYzNEZDNzA1Rjc0Ii8+IDwvcmRmOkRlc2NyaXB0aW9uPiA8L3JkZjpSREY+IDwveDp4bXBtZXRhPiA8P3hwYWNrZXQgZW5kPSJyIj8+Af/+/fz7+vn49/b19PPy8fDv7u3s6+rp6Ofm5eTj4uHg397d3Nva2djX1tXU09LR0M/OzczLysnIx8bFxMPCwcC/vr28u7q5uLe2tbSzsrGwr66trKuqqainpqWko6KhoJ+enZybmpmYl5aVlJOSkZCPjo2Mi4qJiIeGhYSDgoGAf359fHt6eXh3dnV0c3JxcG9ubWxramloZ2ZlZGNiYWBfXl1cW1pZWFdWVVRTUlFQT05NTEtKSUhHRkVEQ0JBQD8+PTw7Ojk4NzY1NDMyMTAvLi0sKyopKCcmJSQjIiEgHx4dHBsaGRgXFhUUExIREA8ODQwLCgkIBwYFBAMCAQAAIfkEAQAAAgAsAAAAAAoABgAAAg6UjwiQBhGYglCKhXFLBQA7);
	background-position: center center;
	background-repeat: no-repeat;
	border-bottom-style:solid;
	border-width:1px;
}
.geBtnUp:active {
	background-color: #4d90fe;
	background-image: linear-gradient(#4d90fe 0px,#357ae8 100%);
}
.geBtnDown {
	background-image: url(data:image/gif;base64,R0lGODlhCgAGAJECANXV1WZmZv///wAAACH/C1hNUCBEYXRhWE1QPD94cGFja2V0IGJlZ2luPSLvu78iIGlkPSJXNU0wTXBDZWhpSHpyZVN6TlRjemtjOWQiPz4gPHg6eG1wbWV0YSB4bWxuczp4PSJhZG9iZTpuczptZXRhLyIgeDp4bXB0az0iQWRvYmUgWE1QIENvcmUgNS4wLWMwNjAgNjEuMTM0Nzc3LCAyMDEwLzAyLzEyLTE3OjMyOjAwICAgICAgICAiPiA8cmRmOlJERiB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmRmLXN5bnRheC1ucyMiPiA8cmRmOkRlc2NyaXB0aW9uIHJkZjphYm91dD0iIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8iIHhtbG5zOnhtcE1NPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvbW0vIiB4bWxuczpzdFJlZj0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL3NUeXBlL1Jlc291cmNlUmVmIyIgeG1wOkNyZWF0b3JUb29sPSJBZG9iZSBQaG90b3Nob3AgQ1M1IE1hY2ludG9zaCIgeG1wTU06SW5zdGFuY2VJRD0ieG1wLmlpZDo0QzM3ODJEQjg4NUQxMUU0OTFEQ0E2MzRGQzcwNUY3NCIgeG1wTU06RG9jdW1lbnRJRD0ieG1wLmRpZDo0QzM3ODJEQzg4NUQxMUU0OTFEQ0E2MzRGQzcwNUY3NCI+IDx4bXBNTTpEZXJpdmVkRnJvbSBzdFJlZjppbnN0YW5jZUlEPSJ4bXAuaWlkOjRDMzc4MkQ5ODg1RDExRTQ5MURDQTYzNEZDNzA1Rjc0IiBzdFJlZjpkb2N1bWVudElEPSJ4bXAuZGlkOjRDMzc4MkRBODg1RDExRTQ5MURDQTYzNEZDNzA1Rjc0Ii8+IDwvcmRmOkRlc2NyaXB0aW9uPiA8L3JkZjpSREY+IDwveDp4bXBtZXRhPiA8P3hwYWNrZXQgZW5kPSJyIj8+Af/+/fz7+vn49/b19PPy8fDv7u3s6+rp6Ofm5eTj4uHg397d3Nva2djX1tXU09LR0M/OzczLysnIx8bFxMPCwcC/vr28u7q5uLe2tbSzsrGwr66trKuqqainpqWko6KhoJ+enZybmpmYl5aVlJOSkZCPjo2Mi4qJiIeGhYSDgoGAf359fHt6eXh3dnV0c3JxcG9ubWxramloZ2ZlZGNiYWBfXl1cW1pZWFdWVVRTUlFQT05NTEtKSUhHRkVEQ0JBQD8+PTw7Ojk4NzY1NDMyMTAvLi0sKyopKCcmJSQjIiEgHx4dHBsaGRgXFhUUExIREA8ODQwLCgkIBwYFBAMCAQAAIfkEAQAAAgAsAAAAAAoABgAAAg6UjxLLewEiCAnOZBzeBQA7);
	background-position: center center;
	background-repeat: no-repeat;
	border-style:solid;
	border-width:1px;
}
.geBtnDown:active {
	background-color: #4d90fe;
	background-image: linear-gradient(#4d90fe 0px,#357ae8 100%);
}
html .geColorBtn {
	padding: 0px;
}
html .geColorBtn:disabled {
	opacity: 0.5;
}
html .gePrimaryBtn {
	background-color: #4d90fe;
	background-image: linear-gradient(#4d90fe 0px,#4787ed 100%);
	border: 1px solid #3079ed;
	color: #fff;
}
html .gePrimaryBtn:hover:not([disabled]), html .gePrimaryBtn:focus {
	background-color: #357ae8;
	background-image: linear-gradient(#4d90fe 0px,#357ae8 100%);
	border: 1px solid #2f5bb7;
	color: #fff;
}
.gePrimaryBtn:disabled {
	opacity: .5;
}
button.geShareBtn {
	background-color: #F2931E;
	border-color: #F08705;
	color:#000000;
}
.geAlertLink {
	color:#843534;
	font-weight:700;
	text-decoration:none;
}
.geMenubar {
	padding:0px 2px 0px 2px;
	display:inline-flex;
	align-items:center;
}
.geMenubarContainer .geItem, .geToolbar .geItem {
	padding:6px 6px 6px 9px;
	cursor:default;
}
.geItem:hover:not([disabled]), .geToolbarButton:hover:not([disabled]),
.geBtn:hover:not([disabled]) {
	opacity: 1 !important;
}
.geItem:active:not([disabled]):not(.geStatus), .geBtn:active:not([disabled]),
.geStatus div[data-action]:active:not([disabled]),
html .geToolbarButton:active:not([disabled]) {
	opacity: 0.7 !important;
}
.geBtn:disabled {
	opacity: 0.5;
}
.geMenubarContainer .geItem:active {
	background: #F8C382;
}
.geToolbarContainer .geButton:hover {
	opacity:1;
	background: #eee;
	border-radius:2px;
}
.geToolbarContainer .geButton:active, .geToolbarContainer .geLabel:active {
	background: #F8C382;
}
.geToolbarContainer .geLabel:hover {
	background: #eee;
	border-radius:2px;
}
.geActiveButton:hover {
	opacity: 0.7;
}
.geActiveButton:active {
	opacity: 0.3;
}
.geToolbarButton {
	opacity: 0.6;
}
.geToolbarButton:active {
	opacity: 0.2 !important;
}
.mxDisabled:hover {
	background:inherit !important;
}
.geMenubar a.geStatus {
	color:#888888;
	padding:0px 0px 0px 10px;
	display:inline-flex;
	align-items:center;
	cursor:default !important;
}
.geMenubar a.geStatus:hover {
	background:transparent;
}
.geSidebarContainer .geToolbarContainer {
	background:transparent;
	border-bottom:none;
}
.geSidebarContainer button {
	text-overflow:ellipsis;
	overflow:hidden;
}
.geToolbar {
	padding:5px 0px 0px 6px;
	border-top:1px solid #dadce0;
	-webkit-box-shadow: inset 0 1px 0 0 #fff;
	-moz-box-shadow: inset 0 1px 0 0 #fff;
	box-shadow: inset 0 1px 0 0 #fff;
}
.geToolbarContainer .geSeparator {
	float:left;
	width:1px;
	height:20px;
	background:#e5e5e5;
	margin-left:6px;
	margin-right:6px;
	margin-top:4px;
}
.geToolbarContainer .geButton {
	float:left;
	width:20px;
	height:20px;
	padding:0px 2px 4px 2px;
	margin:2px;
	border:1px solid transparent;
	cursor:pointer;
	opacity:0.6;
}
div.mxWindow .geButton {
	margin: -1px 2px 2px 2px;
	padding: 1px 2px 2px 1px;
}
.geToolbarContainer .geLabel {
	float:left;
	margin:2px;
	cursor:pointer;
	padding:3px 5px 3px 5px;
	border:1px solid transparent;
}
.geToolbarContainer .mxDisabled:hover {
	border:1px solid transparent !important;
	opacity:0.2 !important;
}
.geDiagramBackdrop {
	background-color: #fbfbfb;
}
.geSidebarContainer {
	position:absolute;
	overflow-x:hidden;
	overflow-y:auto;
}
.mxWindowPane .geSidebarContainer {
	width:100%;
	height:100%;
}
.geEditor > div > .geMenubarContainer {
	border-bottom-style:solid;
	border-bottom-width:1px;
}
.geTabContainer {
	border-width:1px;
	border-top-style:solid;
	border-left-style:solid;
	border-right-style:solid;
	display:flex;
	white-space:nowrap;
	overflow:hidden;
	position:absolute;
	z-index:1;
}
.geTabScroller {
	display:inline-block;
	position:relative;
	max-width:calc(100% - 90px);
	white-space:nowrap;
	overflow:hidden;
	overflow-x:auto;
	-ms-overflow-style: none;
	scrollbar-width: none;
	left:0px;
}
.geTabScroller::-webkit-scrollbar {
	display: none;
}
.geToggleItem {
	padding:4px;
	border-radius:8px;
}
.geActiveItem {
	background-color:var(--border-color);
}
html body div.geActivePage, .geRuler {
	background:#fff;
}
.geInactivePage:hover, .geControlTab:hover {
	opacity:0.5;
}
.geTabMenuButton {
	width:14px;
	height:14px;
	margin-left:4px;
	margin-right:-6px;
	cursor:pointer;
}
.geInactivePage .geTabMenuButton {
	display:none;
}
.geTabMenuButton {
	display:inline-block;
	opacity:1;
}
.geTabMenuButton:hover {
	opacity:0.7;
}
.geTabContainer > :first-child {
	border-left-style:none;
}
.geTabContainer > :first-child > :first-child {
	border-left-style:none;
}
.geTab {
	height: 100%;
	border-left-width:1px;
	border-left-style:solid;
	text-overflow:ellipsis;
	border-color:#dadce0;
	color:rgb(112, 112, 112);
	font-size:12px;
	font-weight: 600;
	display: inline-flex;
	align-items: center;
}
.gePageTab {
	padding: 0px 12px 0px 12px;
}
.geSidebar {
	border-bottom:1px solid #e5e5e5;
	padding:6px;
	padding-left:10px;
	padding-bottom:6px;
	overflow:hidden;
}
.geSidebarContainer .geTitle {
	display:block;
	font-size:13px;
	border-color: #e5e5e5;
	border-bottom:1px solid #e5e5e5;
	font-weight:500;
	padding:8px 0px 8px 20px;
	margin:0px;
	cursor:default;
	white-space:nowrap;
	overflow:hidden;
	text-overflow:ellipsis;
	line-height:1.4em;
}
.geSidebarContainer .geTitle:hover {
	background: #eee;
	border-radius:2px;
}
.geSidebarContainer .geTitle:active {
	background-color:#F8C382;
}
.geSidebarContainer .geDropTarget {
	border-radius:10px;
	border:2px dotted #b0b0b0;
	text-align:center;
	padding:6px;
	margin:6px;
	color:#a0a0a0;
	font-size:13px;
}
.geTitle img {
	opacity:0.5;
}
.geTitle img:hover {
	opacity:1;
}
.geTitle .geButton {
	border:1px solid transparent;
	padding:3px;
	border-radius:2px;
}
.geTitle .geButton:hover {
	border:1px solid gray;
}
.geSidebar .geItem {
	display:inline-block;
	background-repeat:no-repeat;
	background-position:50% 50%;
	border-radius: 8px;
}
.geSidebar .geItem, .geShapePicker .geItem {
	transition: transform 100ms ease-out;
}
.geSidebar .geItem:hover {
	background-color:#e0e0e0;
}
.geSidebar .geItem:active, .geShapePicker .geItem:active {
	transform: scale(0.8,0.8);
}
.geItem {
	vertical-align: top;
	display: inline-block;
}
.geSidebarTooltip {
	position:absolute;
	background:#fbfbfb;
	overflow:hidden;
	box-shadow:0 2px 6px 2px rgba(60,64,67,.15);
	border-radius:6px;
}
.geFooterContainer {
	background:#e5e5e5;
	border-top:1px solid #c0c0c0;
}
.geFooterContainer a {
	display:inline-block;
	box-sizing:border-box;
	width:100%;
	white-space:nowrap;
	font-size:14px;
	color:#235695;
	font-weight:bold;
	text-decoration:none;
}
.geFooterContainer table {
	border-collapse:collapse;
	margin:0 auto;
}
.geFooterContainer td {
	border-left:1px solid #c0c0c0;
	border-right:1px solid #c0c0c0;
}
.geToolbarButton {
	border-color:#333333;
}
.geFooterContainer td:hover {
	background-color: #b3b3b3;
}
.geHsplit {
	cursor:col-resize;
}
.geVsplit {
	font-size:1pt;
	cursor:row-resize;
}
.geHsplit {
	border-left:1px solid var(--border-color);
	background-color:transparent;
}
.geVSplit {
	border-top:1px solid var(--border-color);
	border-bottom:1px solid var(--border-color);
}
.geHsplit:hover, .geVsplit:hover {
	background-color:var(--border-color);
	opacity:0.7;
}
.mxWindow {
	background:var(--panel-color);
}
.geDialog {
	position:absolute;
	background:white;
	line-height:1em;
	overflow:hidden;
	padding:30px;
	border:1px solid #acacac;
	-webkit-box-shadow:0px 0px 2px 2px #d5d5d5;
	-moz-box-shadow:0px 0px 2px 2px #d5d5d5;
	box-shadow:0px 0px 2px 2px #d5d5d5;
	z-index: 2;
}
.geTransDialog {
	position:absolute;
	overflow:hidden;
	z-index: 2;
}
.geDialogClose {
	position:absolute;
	width:9px;
	height:9px;
	opacity:0.5;
	cursor:pointer;
}
.geDialogClose:hover {
	opacity:1;
}
.geDialogTitle {
	box-sizing:border-box;
	white-space:nowrap;
	background:rgb(229, 229, 229);
	border-bottom:1px solid rgb(192, 192, 192);
	font-size:15px;
	font-weight:bold;
	text-align:center;
	color:rgb(35, 86, 149);
}
.geDialogFooter {
	background:whiteSmoke;
	white-space:nowrap;
	text-align:right;
	box-sizing:border-box;
	border-top:1px solid #e5e5e5;
	color:darkGray;
}
.geSprite {
	background:url('data:image/png;base64,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') no-repeat;
	width:21px;
	height:21px;
}
.geEditor .geBaseButton {
	padding:10px;
	border-radius:6px;
	border:1px solid #c0c0c0;
	cursor:pointer;
	background-color:#ececec;
	background-image:linear-gradient(#ececec 0%, #fcfcfc 100%);
}
.geEditor .geBaseButton:hover {
	background:#ececec;
}
.geEditor .geBigButton {
	color:#ffffff;
	border: none;
	padding:4px 10px;
	font-size:14px;
	white-space: nowrap;
	border-radius:3px;
	background-color:#0052cc;
	cursor:pointer;
	transition: background-color 0.1s ease-out;
	overflow:hidden;
	text-overflow: ellipsis;
}
.geEditor .geBigButton:hover {
	background-color:#0065ff;
}
.geEditor .geBigButton:active {
	background-color:#0747a6;
	opacity:1;
}
html body .geBigStandardButton {
	color: #344563;
	background-color: rgba(9, 30, 66, 0.08);
}
html body .geBigStandardButton:hover {
	background-color: rgba(9, 30, 66, 0.13);
}
html body .geBigStandardButton:active {
	background-color: #F8C382;
	color: #600000;
}
@media print {
	div.geNoPrint { display: none !important; }
}
.geSprite-actualsize { background-position: 0 0; }
.geSprite-bold { background-position: 0 -46px; }
.geSprite-bottom { background-position: 0 -92px; }
.geSprite-center { background-position: 0 -138px; }
.geSprite-delete { background-position: 0 -184px; }
.geSprite-fillcolor { background-position: 0 -229px; }
.geSprite-fit { background-position: 0 -277px; }
.geSprite-fontcolor { background-position: 0 -322px; }
.geSprite-gradientcolor { background-position: 0 -368px; }
.geSprite-image { background-position: 0 -414px; }
.geSprite-italic { background-position: 0 -460px; }
.geSprite-left { background-position: 0 -505px; }
.geSprite-middle { background-position: 0 -552px; }
.geSprite-print { background-position: 0 -598px; }
.geSprite-redo { background-position: 0 -644px; }
.geSprite-right { background-position: 0 -689px; }
.geSprite-shadow { background-position: 0 -735px; }
.geSprite-strokecolor { background-position: 0 -782px; }
.geSprite-top { background-position: 0 -828px; }
.geSprite-underline { background-position: 0 -874px; }
.geSprite-undo { background-position: 0 -920px; }
.geSprite-zoomin { background-position: 0 -966px; }
.geSprite-zoomout { background-position: 0 -1012px; }
.geSprite-arrow { background-position: 0 -1059px; }
.geSprite-linkedge { background-position: 0 -1105px; }
.geSprite-straight { background-position: 0 -1150px; }
.geSprite-entity { background-position: 0 -1196px; }
.geSprite-orthogonal { background-position: 0 -1242px; }
.geSprite-curved { background-position: 0 -1288px; }
.geSprite-noarrow { background-position: 0 -1334px; }
.geSprite-endclassic { background-position: 0 -1380px; }
.geSprite-endopen { background-position: 0 -1426px; }
.geSprite-endblock { background-position: 0 -1472px; }
.geSprite-endoval { background-position: 0 -1518px; }
.geSprite-enddiamond { background-position: 0 -1564px; }
.geSprite-endthindiamond { background-position: 0 -1610px; }
.geSprite-endclassictrans { background-position: 0 -1656px; }
.geSprite-endblocktrans { background-position: 0 -1702px; }
.geSprite-endovaltrans { background-position: 0 -1748px; }
.geSprite-enddiamondtrans { background-position: 0 -1794px; }
.geSprite-endthindiamondtrans { background-position: 0 -1840px; }
.geSprite-startclassic { background-position: 0 -1886px; }
.geSprite-startopen { background-position: 0 -1932px; }
.geSprite-startblock { background-position: 0 -1978px; }
.geSprite-startoval { background-position: 0 -2024px; }
.geSprite-startdiamond { background-position: 0 -2070px; }
.geSprite-startthindiamond { background-position: 0 -2116px; }
.geSprite-startclassictrans { background-position: 0 -2162px; }
.geSprite-startblocktrans { background-position: 0 -2208px; }
.geSprite-startovaltrans { background-position: 0 -2254px; }
.geSprite-startdiamondtrans { background-position: 0 -2300px; }
.geSprite-startthindiamondtrans { background-position: 0 -2346px; }
.geSprite-globe { background-position: 0 -2392px; }
.geSprite-orderedlist { background-position: 0 -2438px; }
.geSprite-unorderedlist { background-position: 0 -2484px; }
.geSprite-horizontalrule { background-position: 0 -2530px; }
.geSprite-link { background-position: 0 -2576px; }
.geSprite-indent { background-position: 0 -2622px; }
.geSprite-outdent { background-position: 0 -2668px; }
.geSprite-code { background-position: 0 -2714px; }
.geSprite-fontbackground { background-position: 0 -2760px; }
.geSprite-removeformat { background-position: 0 -2806px; }
.geSprite-superscript { background-position: 0 -2852px; }
.geSprite-subscript { background-position: 0 -2898px; }
.geSprite-table { background-position: 0 -2944px; }
.geSprite-deletecolumn { background-position: 0 -2990px; }
.geSprite-deleterow { background-position: 0 -3036px; }
.geSprite-insertcolumnafter { background-position: 0 -3082px; }
.geSprite-insertcolumnbefore { background-position: 0 -3128px; }
.geSprite-insertrowafter { background-position: 0 -3174px; }
.geSprite-insertrowbefore { background-position: 0 -3220px; }
.geSprite-grid { background-position: 0 -3272px; }
.geSprite-guides { background-position: 0 -3324px; }
.geSprite-dots { background-position: 0 -3370px; }
.geSprite-alignleft { background-position: 0 -3416px; }
.geSprite-alignright { background-position: 0 -3462px; }
.geSprite-aligncenter { background-position: 0 -3508px; }
.geSprite-aligntop { background-position: 0 -3554px; }
.geSprite-alignbottom { background-position: 0 -3600px; }
.geSprite-alignmiddle { background-position: 0 -3646px; }
.geSprite-justifyfull { background-position: 0 -3692px; }
.geSprite-formatpanel { background-position: 0 -3738px; }
.geSprite-connection { background-position: 0 -3784px; }
.geSprite-vertical { background-position: 0 -3830px; }
.geSprite-simplearrow { background-position: 0 -3876px; }
.geSprite-plus { background-position: 0 -3922px; }
.geSprite-rounded { background-position: 0 -3968px; }
.geSprite-toback { background-position: 0 -4014px; }
.geSprite-tofront { background-position: 0 -4060px; }
.geSprite-duplicate { background-position: 0 -4106px; }
.geSprite-insert { background-position: 0 -4152px; }
.geSprite-endblockthin { background-position: 0 -4201px; }
.geSprite-endblockthintrans { background-position: 0 -4247px; }
.geSprite-enderone { background-position: 0 -4293px; }
.geSprite-enderonetoone { background-position: 0 -4339px; }
.geSprite-enderonetomany { background-position: 0 -4385px; }
.geSprite-endermany { background-position: 0 -4431px; }
.geSprite-enderoneopt { background-position: 0 -4477px; }
.geSprite-endermanyopt { background-position: 0 -4523px; }
.geSprite-endclassicthin { background-position: 0 -4938px; }
.geSprite-endclassicthintrans { background-position: 0 -4984px; }
.geSprite-enddash { background-position: 0 -5029px; }
.geSprite-endcircleplus { background-position: 0 -5075px; }
.geSprite-endcircle { background-position: 0 -5121px; }
.geSprite-endasync { background-position: 0 -5167px; }
.geSprite-endasynctrans { background-position: 0 -5213px; }
.geSprite-startblockthin { background-position: 0 -4569px; }
.geSprite-startblockthintrans { background-position: 0 -4615px; }
.geSprite-starterone { background-position: 0 -4661px; }
.geSprite-starteronetoone { background-position: 0 -4707px; }
.geSprite-starteronetomany { background-position: 0 -4753px; }
.geSprite-startermany { background-position: 0 -4799px; }
.geSprite-starteroneopt { background-position: 0 -4845px; }
.geSprite-startermanyopt { background-position: 0 -4891px; }
.geSprite-startclassicthin { background-position: 0 -5259px; }
.geSprite-startclassicthintrans { background-position: 0 -5305px; }
.geSprite-startdash { background-position: 0 -5351px; }
.geSprite-startcircleplus { background-position: 0 -5397px; }
.geSprite-startcircle { background-position: 0 -5443px; }
.geSprite-startasync { background-position: 0 -5489px; }
.geSprite-startasynctrans { background-position: 0 -5535px; }
.geSprite-startcross { background-position: 0 -5581px; }
.geSprite-startopenthin { background-position: 0 -5627px; }
.geSprite-startopenasync { background-position: 0 -5673px; }
.geSprite-endcross { background-position: 0 -5719px; }
.geSprite-endopenthin { background-position: 0 -5765px; }
.geSprite-endopenasync { background-position: 0 -5811px; }
.geSprite-verticalelbow { background-position: 0 -5857px; }
.geSprite-horizontalelbow { background-position: 0 -5903px; }
.geSprite-horizontalisometric { background-position: 0 -5949px; }
.geSprite-verticalisometric { background-position: 0 -5995px; }
.geSvgSprite {
	background-position: center center;
}
.geFlipSprite {
	transform:scaleX(-1);
}
.geSprite-box {
	background-image: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='20' height='10' transform='translate(0.5,0.5)'><rect stroke='black' fill='none' x='2' y='2' width='6' height='6'/><path stroke='black' d='M8 5 L 18 5'/></svg>");
}
.geSprite-halfCircle {
	background-image: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='20' height='10' transform='translate(0.5,0.5)'><path stroke='black' fill='none' d='M 2 2 Q 6 2 6 5 Q 6 8 2 8 M 6 5 L 18 5'/></svg>");
}
html div.mxRubberband {
	border-color:#0000DD;
	background:#99ccff;
}
td.mxPopupMenuIcon div {
	width:16px;
	height:16px;
}
.geEditor div.mxPopupMenu {
	box-shadow: 0px 0px 2px #C0C0C0;
	background:var(--panel-color);
	border-radius:4px;
	border-style:solid;
	border-width:1px;
	border-color:lightgray;
	padding:3px;
}
.geSearchSidebar {
	padding: 14px 8px 0px 8px;
	box-sizing: border-box;
	min-height: 60px;
	overflow: hidden;
	width: 100%;
}
.geSearchSidebar input {
	font-size: 12px;
	box-sizing: border-box;
	border: 1px solid #d5d5d5;
	border-radius: 4px;
	width: 100%;
	outline: none;
	padding: 6px 20px 6px 6px
}
html table.mxPopupMenu {
	border-collapse:collapse;
	margin:0px;
}
html td.mxPopupMenuItem {
	padding:7px 30px 7px 30px;
	font-family:-apple-system, BlinkMacSystemFont, "Segoe UI Variable", "Segoe UI", system-ui, ui-sans-serif, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji";
	font-size:10pt;
}
html td.mxPopupMenuIcon {
	background-color:transparent;
	padding:0px;
}
td.mxPopupMenuIcon .geIcon {
	padding:2px;
	padding-bottom:4px;
	margin:2px;
	border:1px solid transparent;
	opacity:0.5;
}
html tr.mxPopupMenuItemHover {
	background-color: #e0e0e0;
	color: black;
}
table.mxPopupMenu hr {
	color:#cccccc;
	background-color:#cccccc;
	border:none;
	height:1px;
}
table.mxPopupMenu tr {
	font-size:4pt;
}
html td.mxWindowTitle {
 	color:rgb(112, 112, 112);
	background:#f1f3f4;
 	padding:4px;
}
table.geProperties {
	table-layout:fixed;
}
table.geProperties tr td {
	height:21px;
}
.gePropHeader, .gePropRow {
	border: 1px solid #e9e9e9;	
}
.gePropRowDark {
	border: 1px solid #4472C4;
}
.gePropHeader>.gePropHeaderCell {
    border-top: 0;
    border-bottom: 0;
    text-align: left;
	width: 50%;
}
.gePropHeader>.gePropHeaderCell:first-child {
    border-left: none;
}
.gePropHeader>.gePropHeaderCell:last-child {
    border-right: none;
}
.gePropHeader {
    background: #e5e5e5;
    color: black;
}
.gePropRowCell {
    border-left: 1px solid #f3f3f3;
	white-space:nowrap;
	text-overflow:ellipsis;
	overflow:hidden;
    max-width: 50%;
}
.gePropRow>.gePropRowCell {
    background: #fff;
}
.gePropRowAlt>.gePropRowCell {
    background: #fcfcfc;
}
.gePropRowDark>.gePropRowCell {
    background: #fff;
    color: #305496;
    font-weight: bold;
}
.gePropRowDarkAlt>.gePropRowCell {
    background: #D9E1F2;
    color: #305496;
    font-weight: bold;
}
.gePropEditor input:invalid {
  border: 1px solid red;
}
/* Templates dialog css */
.geTemplateDlg {
	width: 100%;
	height: 100%;
}
.geTemplateDlg ::-webkit-scrollbar {
    width:12px;
    height:12px;
}
.geTemplateDlg ::-webkit-scrollbar-track {
	background:whiteSmoke;
	box-shadow:inset 0 0 4px rgba(0,0,0,0.1);
}
.geTemplateDlg ::-webkit-scrollbar-thumb {
	background:#c5c5c5;
    border-radius:10px;
	border:whiteSmoke solid 3px;
}
.geTemplateDlg ::-webkit-scrollbar-thumb:hover {
	background:#b5b5b5;
}

.geTempDlgHeader {
	box-sizing: border-box;
	height: 62px;
	width: 100%;
	border: 1px solid #CCCCCC;
	border-radius: 5px 5px 0 0;
	background-color: #F5F5F5;
}
.geTempDlgHeaderLogo {
	height: 34px;
	margin: 14px 14px 14px 20px;
}
.geTempDlgSearchBox {
    color:#888888;
    background:url("/images/icon-search.svg") no-repeat;
	background-color: #FFFFFF;
	background-position: 15px;
	height: 40px;
	width: 40%;
	max-width: 400px;
	border: 1px solid #CCCCCC;
	border-radius: 3px;
    float:right;
    font-family:Arial,Helvetica,sans-serif;
    font-size:15px;
    line-height:36px;
    margin: 11px 36px 0 0;
    outline:medium none;
    padding:0 0 0 36px;
    text-shadow:1px 1px 0 white;
}
.geTemplatesList {
	box-sizing: border-box;
	float: left;
	height: calc(100% - 118px);
	width: 20%;
	border: 1px solid #CCCCCC;
	background-color: #FFFFFF;
	display: inline-block;
	overflow-x: hidden;
	overflow-y: auto;
}
.geTempDlgContent {
	box-sizing: border-box;
	float: right;
	height: calc(100% - 118px);
	width: 80%;
	border: 1px solid #CCCCCC;
	background-color: #FFFFFF;
	display: inline-block;
	overflow-x: hidden;
	overflow-y: auto;
	position: relative;
}
.geTempDlgFooter {
	box-sizing: border-box;
	height: 52px;
	width: 100%;
	border: 1px solid #CCCCCC;
	border-radius: 0 0 5px 5px;
	background-color: #F5F5F5;
	text-align: right;
	font-family: Helvetica;
	font-size: 14px;
	line-height: 17px;
	padding-top: 11px;
}
.geTempDlgCreateBtn, .geTempDlgOpenBtn {
	display: inline-block;
	width: 67px;
    border-radius: 3px;
    background-color: #3D72AD;
    padding: 6px;
    text-align: center;
    color: #fff;
    cursor: pointer;
    margin-left: 5px;
}
.geTempDlgCancelBtn {
	display: inline-block;
	width: 67px;
    padding: 6px;
    text-align: center;
    color: #3D72AD;
    cursor: pointer;
}
.geTempDlgCancelBtn:active, .geTempDlgCreateBtn:active, 
.geTempDlgOpenBtn:active, .geTempDlgShowAllBtn:active {
	transform: translateY(2px);
}
.geTempDlgBtnDisabled {
    background-color: #9fbddd;
}
.geTempDlgBtnDisabled:active {
    transform: translateY(0px);
}

.geTempDlgBtnBusy {
	background-image: url(/images/aui-wait.gif);
    background-repeat: no-repeat;
    background-position: 62px 7px;
}

.geTempDlgBack {
	height: 17px;
	color: #333333;
	font-family: Helvetica;
	font-size: 14px;
	font-weight: bold;
	line-height: 17px;
	padding: 25px 0 0 20px;
	cursor: pointer;
}
.geTempDlgHLine {
	height: 1px;
	width: calc(100% - 22px);
	background-color: #CCCCCC;
	margin: 20px 0 0 11px;
}
.geTemplatesLbl {
	height: 17px;
	color: #6D6D6D;
	font-family: Helvetica;
	font-size: 14px;
	font-weight: bold;
	line-height: 17px;
	text-transform: uppercase;
	margin: 20px 0 3px 20px;
}
.geTemplateCatLink {
	height: 17px;
	color: #3D72AD;
	font-family: Helvetica;
	font-size: 14px;
	line-height: 17px;
	margin: 12px 0 0 20px;
	cursor: pointer;
}
.geTempDlgNewDiagramCat {
	height: 280px;
	width: 100%;
	background-color: #555555;
}
.geTempDlgNewDiagramCatLbl {
	height: 17px;
	color: #FFFFFF;
	font-family: Helvetica;
	font-size: 14px;
	font-weight: bold;
	line-height: 17px;
	padding: 25px 0 0 20px;
	text-transform: uppercase;
}
.geTempDlgNewDiagramCatList {
	width: 100%;
	height: 190px;
	padding-left: 9px;
	box-sizing: border-box;
	overflow-y: auto;
	overflow-x: hidden; 
}
.geTempDlgNewDiagramCatFooter {
	width: 100%;
}
.geTempDlgShowAllBtn {
	width: 78px;
	border: 1px solid #777777;
	border-radius: 3px;
	cursor: pointer;
	text-align: center;
	color: #DDDDDD;
	font-family: Helvetica;
	font-size: 14px;
	line-height: 17px;
	padding: 4px;
	float: right;
	margin-right: 30px;
}
.geTempDlgNewDiagramCatItem {
	height: 155px;
	width: 134px;
	padding: 18px 6px 0 9px;
	display: inline-block;
}

.geTempDlgNewDiagramCatItemImg {
	box-sizing: border-box;
	height: 134px;
	width: 134px;
	border: 1px solid #CCCCCC;
	border-radius: 3px;
	background-color: #FFFFFF;
	display:table-cell;
	vertical-align:middle;
	text-align:center;
	cursor: pointer;
}

.geTempDlgNewDiagramCatItemActive > .geTempDlgNewDiagramCatItemImg {
	border: 4px solid #3D72AD;
}

.geTempDlgNewDiagramCatItemLbl {
	height: 17px;
	width: 100%;
	color: #FFFFFF;
	font-family: Helvetica;
	font-size: 14px;
	line-height: 17px;
	text-align: center;
	padding-top: 4px;
	cursor: pointer;
}

.geTempDlgDiagramsList {
	box-sizing: border-box;
	width: 100%;
	min-height: calc(100% - 280px);
	padding-left: 9px;
	box-sizing: border-box;
	background-color: #E5E5E5;
}

.geTempDlgDiagramsListHeader {
	width: 100%;
    height: 45px;
	padding: 18px 20px 0 11px;
	box-sizing: border-box;
}
.geTempDlgDiagramsListTitle {
	box-sizing: border-box;
	height: 17px;
	color: #666666;
	font-family: Helvetica;
	font-size: 14px;
	font-weight: bold;
	line-height: 17px;
	text-transform: uppercase;
	padding-top: 5px;
	display: inline-block;
}
.geTempDlgDiagramsListBtns {
	float: right;
	margin-top: -9px;
}		
.geTempDlgRadioBtn {
	box-sizing: border-box;
	border: 1px solid #CCCCCC;
	border-radius: 3px;
	background-color: #FFFFFF;
	color: #333333;
	display: inline-block;
	font-family: Helvetica;
	font-size: 14px;
	line-height: 17px;
	text-align: center;
	padding: 4px;
	cursor: pointer;
}
.geTempDlgRadioBtnActive {
	background-color: #555555;
	color: #FFFFFF;
}
.geTempDlgRadioBtnLarge {
	height: 27px;
	width: 120px;
}
/* TODO is there a better way for these buttons */
.geTempDlgRadioBtnSmall {
	position: relative;
	top: 9px;
	height: 27px;
	width: 27px;
}
.geTempDlgRadioBtnSmall img {
	position: absolute;
	top: 6px;
	left: 6px;
	height: 13px;
	width: 13px;
}
.geTempDlgSpacer {
    display: inline-block;
	width: 10px;
}

.geTempDlgDiagramsListGrid {
	width: 100%;
	white-space: nowrap;
	font-size: 13px;
	padding: 0px 20px 20px 10px;
    box-sizing: border-box;
    border-spacing: 0;
}

.geTempDlgDiagramsListGrid tr {
	height: 40px;
}

.geTempDlgDiagramsListGrid th {
	background-color: #E5E5E5;
	color: #8E8E8E;
	font-weight: bold;
	text-align: left;
	padding: 5px;
	border-bottom: 1px solid #CCCCCC;
	font-size: 14px;
}

.geTempDlgDiagramsListGrid td {
	background-color: #FFFFFF;
	color: #888888;
	padding: 5px;
	border-bottom: 1px solid #CCCCCC;
	overflow: hidden;
}

.geTempDlgDiagramsListGridActive td {
	border-bottom: 2px solid #3D72AD;
	border-top: 2px solid #3D72AD;
}

.geTempDlgDiagramsListGridActive td:first-child  {
	border-left: 2px solid #3D72AD;
}

.geTempDlgDiagramsListGridActive td:last-child  {
	border-right: 2px solid #3D72AD;
}

.geTempDlgDiagramTitle {
	font-weight: bold;
	color: #666666 !important;
}

.geTempDlgDiagramsTiles {
	position: relative;
	min-height: 100px;
}

.geTempDlgDiagramTile {
	height: 152px;
	width: 130px;
	padding: 20px 7px 0 10px;
	display: inline-block;
	position: relative;
}

.geTempDlgDiagramTileImg {
	box-sizing: border-box;
	height: 130px;
	width: 130px;
	border: 1px solid #CCCCCC;
	border-radius: 3px;
	background-color: #FFFFFF;
	display:table-cell;
	vertical-align:middle;
	text-align:center;
}

.geTempDlgDiagramTileImgLoading {
	background-image: url(/images/aui-wait.gif);
    background-repeat: no-repeat;
    background-position: center;
}

.geTempDlgDiagramTileImgError {
	background-image: url(/images/broken.png);
    background-repeat: no-repeat;
    background-position: center;
    background-color: #be3730;
}

.geTempDlgDiagramTileImg img{
	max-width: 117px;
    max-height: 117px;
    cursor: pointer;
}

.geTempDlgDiagramTileActive > .geTempDlgDiagramTileImg{
	border: 4px solid #3D72AD;
}

.geTempDlgDiagramTileLbl {
	height: 17px;
	width: 100%;
	color: #333333;
	font-family: Helvetica;
	font-size: 14px;
	line-height: 17px;
	text-align: center;
	padding-top: 5px;
	cursor: pointer;
}

.geTempDlgDiagramPreviewBtn {
	position: absolute;
	top: 28px;
	right: 15px;
	cursor: pointer;
}

.geTempDlgDiagramListPreviewBtn {
	cursor: pointer;
	padding-left: 5px;
	padding-right: 15px;
}

.geTempDlgDiagramPreviewBox {
	position: absolute;
    top: 3%;
    left: 10%;
    width: 80%;
    height: 94%;
    background: white;
    border: 4px solid #3D72AD;
    border-radius: 6px;
    box-sizing: border-box;
	display:table-cell;
	vertical-align:middle;
	text-align:center;
    z-index: 2;
}

.geTempDlgDialogMask {
	position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.geTempDlgDiagramPreviewBox img {
	max-width: 95%;
    max-height: 95%;
    vertical-align: middle;
}

.geTempDlgPreviewCloseBtn {
	position: absolute;
	top: 5px;
	right: 5px;
	cursor: pointer;
}

.geTempDlgLinkToDiagramHint {
	color: #555;
}

.geTempDlgLinkToDiagramBtn {
	color: #555;
    margin: 0 10px 0 10px;
    height: 27px;
    font-size: 14px;
}

.geTempDlgErrMsg {
	display: none;
	color: red;
    position: absolute;
    width: 100%;
    text-align: center;
}

.geTempDlgImportCat {
	font-weight: bold;
    background: #f9f9f9;
    padding: 5px 0px;
    padding: 10px;
    margin: 10px 10px 0px 0px;
}
/* Comments CSS */
.geCommentsWin {
	user-select: none;
	border: 1px solid whiteSmoke;
	height: 100%;
	margin-bottom: 10px;
	overflow: auto;	
}

.geCommentsToolbar {
	position: absolute;
	bottom: 0px;
	left: 0px;
	right: 0px;
	overflow: hidden;
	border-width: 1px 0px 0px 0px;
	border-color: #c3c3c3;
	border-style: solid;
	display: block;
	white-space: nowrap;
}

.geCommentsList {
	position: absolute;
	overflow: auto;
	left: 0px;
	right: 0px;
	top: 0px;	
}

.geCommentContainer {
	position: relative;
	padding: 12px;
	margin: 5px;
	min-height: 50px;
	display: block;
	background-color: white;
	border-width: 0px 0px 1px 0px;
	border-color: #c3c3c3;
	border-style: solid;
	border-radius: 10px;
	white-space: nowrap;
	box-shadow: 2px 2px 6px rgba(60,64,67,.15);
	color: #3C4043;
}

.geCommentHeader {
	width: 100%;
	height: 32px;
}

.geCommentUserImg {
	width: 32px;
	height: 32px;
	border-radius: 50%;
	float: left;
	background-color: whitesmoke;
}

.geCommentHeaderTxt {
	overflow: hidden;
	height: 32px;
	padding-left: 5px;
}

.geCommentUsername {
	overflow: hidden;
	height: 18px;
	font-size: 15px;
	font-weight: bold;
	text-overflow: ellipsis;
}

.geCommentDate {
	color: #707070;
	overflow: hidden;
	height: 14px;
	font-size: 11px;
	text-overflow: ellipsis;
}

.geCommentDate::first-letter {
    text-transform: uppercase;
}

.geCommentTxt {
	font-size: 14px;
    padding-top: 5px;
    white-space: normal;
    min-height: 12px;
}

.geCommentEditTxtArea {
    margin-top: 5px;
    font-size: 14px !important;
    min-height: 12px;
    max-width: 100%;
    min-width: 100%;
	width: 100%;
    box-sizing: border-box;
}

.geCommentEditBtns {
	width: 100%;
    box-sizing: border-box;
    padding-top: 5px;
    height: 20px;
}

.geCommentEditBtn {
	padding: 3px 8px 3px 8px !important;
    float: right !important;
    margin-left: 5px;
}

.geCommentActions {
	color: #707070;
	font-size: 12px;
}

.geCommentActionsList {
	list-style-type: disc;
	margin: 0px;
	padding: 10px 0 0 0;
}

.geCommentAction {
	display: inline-block;
    padding: 0;
}

.geCommentAction:before {
	content: "\2022";
	padding: 5px;
} 

.geCommentAction:first-child:before {
	content: "";
	padding: 0;
} 

.geCommentActionLnk {
	cursor: pointer;
	color: #707070;
	text-decoration: none;
}

.geCommentActionLnk:hover {
	text-decoration: underline;
}

.geCheckedBtn {
	background-color: #ccc;
    border-top: 1px solid black !important;
    border-left: 1px solid black !important;
}

.geCommentBusyImg {
	position: absolute;
	top: 5px;
	right: 5px;
}

.geAspectDlgListItem 
{
	width : 120px;
	height : 120px;
	display : inline-block;
	border: 3px solid #F0F0F0;
	border-radius: 5px;
	padding: 5px;
	margin : 2px 2px 20px 2px;
}

.geAspectDlgListItem:hover
{
	border: 3px solid #c5c5c5;
}

.geAspectDlgListItemSelected 
{
	border: 3px solid #3b73af;
}

.geAspectDlgListItemSelected:hover
{
	border: 3px solid #405a86;
}

.geAspectDlgListItemText
{
	text-overflow: ellipsis;
	max-width: 100%;
	min-height : 2em;
	overflow : hidden;
	text-align : center;
	margin-top : 10px;
}

.geAspectDlgList
{
	min-height: 184px;
	white-space: nowrap;
}

.geStripedTable
{
	border-collapse: collapse;
 	width: 100%;
 	table-layout: fixed;
}

.geStripedTable td, .geStripedTable th
{
	border: 1px solid #ddd;
	text-align: left;
	padding: 2px;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.geStripedTable tr:nth-child(odd){background-color: #f2f2f2;}

.geStripedTable tr:hover {background-color: #ddd;}

.geStripedTable th {
  padding-top: 4px;
  padding-bottom: 4px;
  background-color: #bbb;
}

.geNotification-box {
	display:flex;
	text-align: center;
	position: relative;
	cursor: pointer;
	width: 20px;
}
.geNotification-bell {
  animation: geBellAnim 1s 1s both;
}
.geNotification-bell * {
  display: block;
  margin: 0 auto;
  background-color: #656565;
}

.geBell-top {
  width: 2px;
  height: 2px;
  border-radius: 1px 1px 0 0;
}
.geBell-middle {
  width: 12px;
  height: 12px;
  margin-top: -1px;
  border-radius: 7px 7px 0 0;
}
.geBell-bottom {
  position: relative;
  z-index: 0;
  width: 16px;
  height: 1px;
}
.geBell-bottom::before,
.geBell-bottom::after {
  content: '';
  position: absolute;
  top: -4px;
}
.geBell-bottom::before {
  left: 1px;
  border-bottom-width: 4px;
  border-right: 0 solid transparent;
  border-left: 4px solid transparent;
}
.geBell-bottom::after {
  right: 1px;
  border-bottom-width: 4px;
  border-right: 4px solid transparent;
  border-left: 0 solid transparent;
}
.geBell-rad {
  width: 3px;
  height: 2px;
  margin-top: 0.5px;
  border-radius: 0 0 2px 2px;
  animation: geRadAnim 1s 2s both;
}
.geNotification-count {
  position: absolute;
  z-index: 1;
  top: -5px;
  right: -4px;
  width: 13px;
  height: 13px;
  line-height: 13px;
  font-size: 8px;
  border-radius: 50%;
  background-color: #ff4927;
  color: #FFF;
  animation: geZoomAnim 1s 1s both;
}
@keyframes geBellAnim {
  0% { transform: rotate(0); }
  10% { transform: rotate(30deg); }
  20% { transform: rotate(0); }
  80% { transform: rotate(0); }
  90% { transform: rotate(-30deg); }
  100% { transform: rotate(0); }
}
@keyframes geRadAnim {
  0% { transform: translateX(0); }
  10% { transform: translateX(5px); }
  20% { transform: translateX(0); }
  80% { transform: translateX(0); }
  90% { transform: translateX(-5px); }
  100% { transform: translateX(0); }
}
@keyframes geZoomAnim {
  0% { opacity: 0; transform: scale(0); }
  50% { opacity: 1; transform: scale(1); }
  100% { opacity: 1; }
}

.geNotifPanel {
  height: 300px;
  width: 300px;
  background: #fff;
  border-radius: 3px;
  overflow: hidden;
  -webkit-box-shadow: 10px 10px 15px 0 rgba(0, 0, 0, 0.3);
          box-shadow: 10px 10px 15px 0 rgba(0, 0, 0, 0.3);
  -webkit-transition: all .5s ease-in-out;
  transition: all .5s ease-in-out;
  position: absolute;
  right: 100px;
  top: 42px;
  z-index: 150;
}
.geNotifPanel .header {
  background: #cecece;
  color: #707070;
  font-size: 15px;
}
.geNotifPanel .header .title {
  display: block;
  text-align: center;
  line-height: 30px;
  font-weight: 600;
}
.geNotifPanel .header .closeBtn {
  position: absolute;
  line-height: 30px;
  cursor: pointer;
  right: 15px;
  top: 0;
}
.geNotifPanel .notifications {
  position: relative;
  height: 270px;
  overflow-x: hidden;
  overflow-y: auto;
}
.geNotifPanel .notifications .line {
  position: absolute;
  top: 0;
  left: 27px;
  height: 100%;
  width: 3px;
  background: #EBEBEB;
}
.geNotifPanel .notifications .notification {
  position: relative;
  z-index: 2;
  margin: 25px 20px 25px 43px;
}
.geNotifPanel .notifications .notification:nth-child(n+1) {
  animation: geHere-am-i 0.5s ease-out 0.4s;
  animation-fill-mode: both;
}
.geNotifPanel .notifications .notification:hover {
  color: #1B95E0;
  cursor: pointer;
}
.geNotifPanel .notifications .notification .circle {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  position: absolute;
  height: 11px;
  width: 11px;
  background: #fff;
  border: 2px solid #1B95E0;
  -webkit-box-shadow: 0 0 0 3px #fff;
          box-shadow: 0 0 0 3px #fff;
  border-radius: 6px;
  top: 0;
  left: -20px;
}

.geNotifPanel .notifications .notification .circle.active {
  background: #1B95E0;
}

.geNotifPanel .notifications .notification .time {
  display: block;
  font-size: 11px;
  line-height: 11px;
  margin-bottom: 2px;
}
.geNotifPanel .notifications .notification p {
  font-size: 15px;
  line-height: 20px;
  margin: 0;
}
.geNotifPanel .notifications .notification p b {
  font-weight: 600;
}
@-webkit-keyframes geHere-am-i {
  from {
    -webkit-transform: translate3d(0, 50px, 0);
            transform: translate3d(0, 50px, 0);
    opacity: 0;
  }
  to {
    -webkit-transform: translate3d(0, 0, 0);
            transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}

@keyframes geHere-am-i {
  from {
    -webkit-transform: translate3d(0, 50px, 0);
            transform: translate3d(0, 50px, 0);
    opacity: 0;
  }
  to {
    -webkit-transform: translate3d(0, 0, 0);
            transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}

.geTempTree {
  margin: 0;
  padding: 0;
}

.geTempTree, .geTempTreeActive, .geTempTreeNested {
  list-style-type: none;
  transition: all 0.5s;
}

.geTempTreeCaret {
  box-sizing: border-box;
  cursor: pointer;
  user-select: none;
  padding: 6px;
  width: 100%;
  transition: all 0.5s;
}

.geTempTreeCaret::before {
  content: "\25B6";
  display: inline-block;
  font-size: 10px;
  margin-right: 6px;
}

.geTempTreeCaret-down::before {
  transform: rotate(90deg);  
}

.geTempTreeNested {
  height: 0;
  opacity: 0;
}

.geTempTreeActive {
  height: 100%;
  opacity: 1;
}

.geTempTreeActive, .geTempTreeNested {
  padding-left: 15px;
}

.geTempTreeActive > li, .geTempTreeNested > li {
  box-sizing: border-box;
  padding: 3px;
  width: 100%;
  cursor: pointer;
  user-select: none;
  transition: all 0.5s;
}

/*Electron Window Controls*/
#geWindow-controls {
  display: grid;
  grid-template-columns: repeat(3, 30px);
  position: absolute;
  top: 2px;
  right: 3px;
  height: 22px;
  -webkit-app-region: no-drag;
}

#geWindow-controls .button {
  grid-row: 1 / span 1;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  user-select: none;
}
#min-button {
  grid-column: 1;
}
#max-button, #restore-button {
  grid-column: 2;
}
#close-button {
  grid-column: 3;
}
#geWindow-controls .button.dark:hover {
  background: rgba(255,255,255,0.1);
}
#geWindow-controls .button.dark:active {
  background: rgba(255,255,255,0.2);
}

#geWindow-controls .button.white:hover {
  background: rgba(0,0,0,0.1);
}
#geWindow-controls .button.white:active {
  background: rgba(0,0,0,0.2);
}

#close-button:hover {
  background: #E81123 !important;
}
#close-button:active {
  background: #F1707A !important;
}

#restore-button {
  display: none !important;
}
/*
.geMaximized #titlebar {
  width: 100%;
  padding: 0;
}
*/
.geMaximized #restore-button {
  display: flex !important;
}

.geMaximized #max-button {
  display: none;
}
[draggable="true"] {
    transform: translate(0, 0);
    z-index: 0;
}
