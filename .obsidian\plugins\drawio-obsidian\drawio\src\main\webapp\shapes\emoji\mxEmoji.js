// Unicode Emoji shape
function mxShapeEmoji(bounds, fill, stroke, strokewidth)
{
	mxShape.call(this);
	this.bounds = bounds;
	this.fill = fill;
	this.stroke = stroke;
	this.strokewidth = (strokewidth != null) ? strokewidth : 1;
};

/**
 * Extends mxShape.
 */
mxUtils.extend(mxShapeEmoji, mxShape);

mxShapeEmoji.prototype.EMOJI_NAME = 'emoji';

mxShapeEmoji.prototype.emojis = {
	'grinning_face': '😀',
	'grinning_face_with_big_eyes': '😃',
	'grinning_face_with_smiling_eyes': '😄',
	'beaming_face_with_smiling_eyes': '😁',
	'grinning_squinting_face': '😆',
	'grinning_face_with_sweat': '😅',
	'rolling_on_the_floor_laughing': '🤣',
	'face_with_tears_of_joy': '😂',
	'slightly_smiling_face': '🙂',
	'upside_down_face': '🙃',
	'melting_face': '🫠',
	'winking_face': '😉',
	'smiling_face_with_smiling_eyes': '😊',
	'smiling_face_with_halo': '😇',
	'smiling_face_with_hearts': '🥰',
	'smiling_face_with_heart_eyes': '😍',
	'star_struck': '🤩',
	'face_blowing_a_kiss': '😘',
	'kissing_face': '😗',
	'smiling_face': '☺️',
	'kissing_face_with_closed_eyes': '😚',
	'kissing_face_with_smiling_eyes': '😙',
	'smiling_face_with_tear': '🥲',
	'face_savoring_food': '😋',
	'face_with_tongue': '😛',
	'winking_face_with_tongue': '😜',
	'zany_face': '🤪',
	'squinting_face_with_tongue': '😝',
	'money_mouth_face': '🤑',
	'smiling_face_with_open_hands': '🤗',
	'face_with_hand_over_mouth': '🤭',
	'face_with_open_eyes_and_hand_over_mouth': '🫢',
	'face_with_peeking_eye': '🫣',
	'shushing_face': '🤫',
	'thinking_face': '🤔',
	'saluting_face': '🫡',
	'zipper_mouth_face': '🤐',
	'face_with_raised_eyebrow': '🤨',
	'neutral_face': '😐',
	'expressionless_face': '😑',
	'face_without_mouth': '😶',
	'dotted_line_face': '🫥',
	'face_in_clouds': '😶‍🌫️',
	'smirking_face': '😏',
	'unamused_face': '😒',
	'face_with_rolling_eyes': '🙄',
	'grimacing_face': '😬',
	'face_exhaling': '😮‍💨',
	'lying_face': '🤥',
	'shaking_face': '🫨',
	'relieved_face': '😌',
	'pensive_face': '😔',
	'sleepy_face': '😪',
	'drooling_face': '🤤',
	'sleeping_face': '😴',
	'face_with_medical_mask': '😷',
	'face_with_thermometer': '🤒',
	'face_with_head_bandage': '🤕',
	'nauseated_face': '🤢',
	'face_vomiting': '🤮',
	'sneezing_face': '🤧',
	'hot_face': '🥵',
	'cold_face': '🥶',
	'woozy_face': '🥴',
	'face_with_crossed_out_eyes': '😵',
	'face_with_spiral_eyes': '😵‍💫',
	'exploding_head': '🤯',
	'cowboy_hat_face': '🤠',
	'partying_face': '🥳',
	'disguised_face': '🥸',
	'smiling_face_with_sunglasses': '😎',
	'nerd_face': '🤓',
	'face_with_monocle': '🧐',
	'confused_face': '😕',
	'face_with_diagonal_mouth': '🫤',
	'worried_face': '😟',
	'slightly_frowning_face': '🙁',
	'frowning_face': '☹️',
	'face_with_open_mouth': '😮',
	'hushed_face': '😯',
	'astonished_face': '😲',
	'flushed_face': '😳',
	'pleading_face': '🥺',
	'face_holding_back_tears': '🥹',
	'frowning_face_with_open_mouth': '😦',
	'anguished_face': '😧',
	'fearful_face': '😨',
	'anxious_face_with_sweat': '😰',
	'sad_but_relieved_face': '😥',
	'crying_face': '😢',
	'loudly_crying_face': '😭',
	'face_screaming_in_fear': '😱',
	'confounded_face': '😖',
	'persevering_face': '😣',
	'disappointed_face': '😞',
	'downcast_face_with_sweat': '😓',
	'weary_face': '😩',
	'tired_face': '😫',
	'yawning_face': '🥱',
	'face_with_steam_from_nose': '😤',
	'enraged_face': '😡',
	'angry_face': '😠',
	'face_with_symbols_on_mouth': '🤬',
	'smiling_face_with_horns': '😈',
	'angry_face_with_horns': '👿',
	'skull': '💀',
	'skull_and_crossbones': '☠️',
	'pile_of_poo': '💩',
	'clown_face': '🤡',
	'ogre': '👹',
	'goblin': '👺',
	'ghost': '👻',
	'alien': '👽',
	'alien_monster': '👾',
	'robot': '🤖',
	'grinning_cat': '😺',
	'grinning_cat_with_smiling_eyes': '😸',
	'cat_with_tears_of_joy': '😹',
	'smiling_cat_with_heart_eyes': '😻',
	'cat_with_wry_smile': '😼',
	'kissing_cat': '😽',
	'weary_cat': '🙀',
	'crying_cat': '😿',
	'pouting_cat': '😾',
	'see_no_evil_monkey': '🙈',
	'hear_no_evil_monkey': '🙉',
	'speak_no_evil_monkey': '🙊',
	'love_letter': '💌',
	'heart_with_arrow': '💘',
	'heart_with_ribbon': '💝',
	'sparkling_heart': '💖',
	'growing_heart': '💗',
	'beating_heart': '💓',
	'revolving_hearts': '💞',
	'two_hearts': '💕',
	'heart_decoration': '💟',
	'heart_exclamation': '❣️',
	'broken_heart': '💔',
	'heart_on_fire': '❤️‍🔥',
	'mending_heart': '❤️‍🩹',
	'red_heart': '❤️',
	'pink_heart': '🩷',
	'orange_heart': '🧡',
	'yellow_heart': '💛',
	'green_heart': '💚',
	'blue_heart': '💙',
	'light_blue_heart': '🩵',
	'purple_heart': '💜',
	'brown_heart': '🤎',
	'black_heart': '🖤',
	'grey_heart': '🩶',
	'white_heart': '🤍',
	'kiss_mark': '💋',
	'hundred_points': '💯',
	'anger_symbol': '💢',
	'collision': '💥',
	'dizzy': '💫',
	'sweat_droplets': '💦',
	'dashing_away': '💨',
	'hole': '🕳️',
	'speech_balloon': '💬',
	'eye_in_speech_bubble': '👁️‍🗨️',
	'left_speech_bubble': '🗨️',
	'right_anger_bubble': '🗯️',
	'thought_balloon': '💭',
	'zzz': '💤',
	'waving_hand': '👋',
	'raised_back_of_hand': '🤚',
	'hand_with_fingers_splayed': '🖐️',
	'raised_hand': '✋',
	'vulcan_salute': '🖖',
	'rightwards_hand': '🫱',
	'leftwards_hand': '🫲',
	'palm_down_hand': '🫳',
	'palm_up_hand': '🫴',
	'leftwards_pushing_hand': '🫷',
	'rightwards_pushing_hand': '🫸',
	'ok_hand': '👌',
	'pinched_fingers': '🤌',
	'pinching_hand': '🤏',
	'victory_hand': '✌️',
	'crossed_fingers': '🤞',
	'hand_with_index_finger_and_thumb_crossed': '🫰',
	'love_you_gesture': '🤟',
	'sign_of_the_horns': '🤘',
	'call_me_hand': '🤙',
	'backhand_index_pointing_left': '👈',
	'backhand_index_pointing_right': '👉',
	'backhand_index_pointing_up': '👆',
	'middle_finger': '🖕',
	'backhand_index_pointing_down': '👇',
	'index_pointing_up': '☝️',
	'index_pointing_at_the_viewer': '🫵',
	'thumbs_up': '👍',
	'thumbs_down': '👎',
	'raised_fist': '✊',
	'oncoming_fist': '👊',
	'left_facing_fist': '🤛',
	'right_facing_fist': '🤜',
	'clapping_hands': '👏',
	'raising_hands': '🙌',
	'heart_hands': '🫶',
	'open_hands': '👐',
	'palms_up_together': '🤲',
	'handshake': '🤝',
	'folded_hands': '🙏',
	'writing_hand': '✍️',
	'nail_polish': '💅',
	'selfie': '🤳',
	'flexed_biceps': '💪',
	'mechanical_arm': '🦾',
	'mechanical_leg': '🦿',
	'leg': '🦵',
	'foot': '🦶',
	'ear': '👂',
	'ear_with_hearing_aid': '🦻',
	'nose': '👃',
	'brain': '🧠',
	'anatomical_heart': '🫀',
	'lungs': '🫁',
	'tooth': '🦷',
	'bone': '🦴',
	'eyes': '👀',
	'eye': '👁️',
	'tongue': '👅',
	'mouth': '👄',
	'biting_lip': '🫦',
	'baby': '👶',
	'child': '🧒',
	'boy': '👦',
	'girl': '👧',
	'person': '🧑',
	'person_blond_hair': '👱',
	'man': '👨',
	'person_beard': '🧔',
	'man_beard': '🧔‍♂️',
	'woman_beard': '🧔‍♀️',
	'man_red_hair': '👨‍🦰',
	'man_curly_hair': '👨‍🦱',
	'man_white_hair': '👨‍🦳',
	'man_bald': '👨‍🦲',
	'woman': '👩',
	'woman_red_hair': '👩‍🦰',
	'person_red_hair': '🧑‍🦰',
	'woman_curly_hair': '👩‍🦱',
	'person_curly_hair': '🧑‍🦱',
	'woman_white_hair': '👩‍🦳',
	'person_white_hair': '🧑‍🦳',
	'woman_bald': '👩‍🦲',
	'person_bald': '🧑‍🦲',
	'woman_blond_hair': '👱‍♀️',
	'man_blond_hair': '👱‍♂️',
	'older_person': '🧓',
	'old_man': '👴',
	'old_woman': '👵',
	'person_frowning': '🙍',
	'man_frowning': '🙍‍♂️',
	'woman_frowning': '🙍‍♀️',
	'person_pouting': '🙎',
	'man_pouting': '🙎‍♂️',
	'woman_pouting': '🙎‍♀️',
	'person_gesturing_no': '🙅',
	'man_gesturing_no': '🙅‍♂️',
	'woman_gesturing_no': '🙅‍♀️',
	'person_gesturing_ok': '🙆',
	'man_gesturing_ok': '🙆‍♂️',
	'woman_gesturing_ok': '🙆‍♀️',
	'person_tipping_hand': '💁',
	'man_tipping_hand': '💁‍♂️',
	'woman_tipping_hand': '💁‍♀️',
	'person_raising_hand': '🙋',
	'man_raising_hand': '🙋‍♂️',
	'woman_raising_hand': '🙋‍♀️',
	'deaf_person': '🧏',
	'deaf_man': '🧏‍♂️',
	'deaf_woman': '🧏‍♀️',
	'person_bowing': '🙇',
	'man_bowing': '🙇‍♂️',
	'woman_bowing': '🙇‍♀️',
	'person_facepalming': '🤦',
	'man_facepalming': '🤦‍♂️',
	'woman_facepalming': '🤦‍♀️',
	'person_shrugging': '🤷',
	'man_shrugging': '🤷‍♂️',
	'woman_shrugging': '🤷‍♀️',
	'health_worker': '🧑‍⚕️',
	'man_health_worker': '👨‍⚕️',
	'woman_health_worker': '👩‍⚕️',
	'student': '🧑‍🎓',
	'man_student': '👨‍🎓',
	'woman_student': '👩‍🎓',
	'teacher': '🧑‍🏫',
	'man_teacher': '👨‍🏫',
	'woman_teacher': '👩‍🏫',
	'judge': '🧑‍⚖️',
	'man_judge': '👨‍⚖️',
	'woman_judge': '👩‍⚖️',
	'farmer': '🧑‍🌾',
	'man_farmer': '👨‍🌾',
	'woman_farmer': '👩‍🌾',
	'cook': '🧑‍🍳',
	'man_cook': '👨‍🍳',
	'woman_cook': '👩‍🍳',
	'mechanic': '🧑‍🔧',
	'man_mechanic': '👨‍🔧',
	'woman_mechanic': '👩‍🔧',
	'factory_worker': '🧑‍🏭',
	'man_factory_worker': '👨‍🏭',
	'woman_factory_worker': '👩‍🏭',
	'office_worker': '🧑‍💼',
	'man_office_worker': '👨‍💼',
	'woman_office_worker': '👩‍💼',
	'scientist': '🧑‍🔬',
	'man_scientist': '👨‍🔬',
	'woman_scientist': '👩‍🔬',
	'technologist': '🧑‍💻',
	'man_technologist': '👨‍💻',
	'woman_technologist': '👩‍💻',
	'singer': '🧑‍🎤',
	'man_singer': '👨‍🎤',
	'woman_singer': '👩‍🎤',
	'artist': '🧑‍🎨',
	'man_artist': '👨‍🎨',
	'woman_artist': '👩‍🎨',
	'pilot': '🧑‍✈️',
	'man_pilot': '👨‍✈️',
	'woman_pilot': '👩‍✈️',
	'astronaut': '🧑‍🚀',
	'man_astronaut': '👨‍🚀',
	'woman_astronaut': '👩‍🚀',
	'firefighter': '🧑‍🚒',
	'man_firefighter': '👨‍🚒',
	'woman_firefighter': '👩‍🚒',
	'police_officer': '👮',
	'man_police_officer': '👮‍♂️',
	'woman_police_officer': '👮‍♀️',
	'detective': '🕵️',
	'man_detective': '🕵️‍♂️',
	'woman_detective': '🕵️‍♀️',
	'guard': '💂',
	'man_guard': '💂‍♂️',
	'woman_guard': '💂‍♀️',
	'ninja': '🥷',
	'construction_worker': '👷',
	'man_construction_worker': '👷‍♂️',
	'woman_construction_worker': '👷‍♀️',
	'person_with_crown': '🫅',
	'prince': '🤴',
	'princess': '👸',
	'person_wearing_turban': '👳',
	'man_wearing_turban': '👳‍♂️',
	'woman_wearing_turban': '👳‍♀️',
	'person_with_skullcap': '👲',
	'woman_with_headscarf': '🧕',
	'person_in_tuxedo': '🤵',
	'man_in_tuxedo': '🤵‍♂️',
	'woman_in_tuxedo': '🤵‍♀️',
	'person_with_veil': '👰',
	'man_with_veil': '👰‍♂️',
	'woman_with_veil': '👰‍♀️',
	'pregnant_woman': '🤰',
	'pregnant_man': '🫃',
	'pregnant_person': '🫄',
	'breast_feeding': '🤱',
	'woman_feeding_baby': '👩‍🍼',
	'man_feeding_baby': '👨‍🍼',
	'person_feeding_baby': '🧑‍🍼',
	'baby_angel': '👼',
	'santa_claus': '🎅',
	'mrs_claus': '🤶',
	'mx_claus': '🧑‍🎄',
	'superhero': '🦸',
	'man_superhero': '🦸‍♂️',
	'woman_superhero': '🦸‍♀️',
	'supervillain': '🦹',
	'man_supervillain': '🦹‍♂️',
	'woman_supervillain': '🦹‍♀️',
	'mage': '🧙',
	'man_mage': '🧙‍♂️',
	'woman_mage': '🧙‍♀️',
	'fairy': '🧚',
	'man_fairy': '🧚‍♂️',
	'woman_fairy': '🧚‍♀️',
	'vampire': '🧛',
	'man_vampire': '🧛‍♂️',
	'woman_vampire': '🧛‍♀️',
	'merperson': '🧜',
	'merman': '🧜‍♂️',
	'mermaid': '🧜‍♀️',
	'elf': '🧝',
	'man_elf': '🧝‍♂️',
	'woman_elf': '🧝‍♀️',
	'genie': '🧞',
	'man_genie': '🧞‍♂️',
	'woman_genie': '🧞‍♀️',
	'zombie': '🧟',
	'man_zombie': '🧟‍♂️',
	'woman_zombie': '🧟‍♀️',
	'troll': '🧌',
	'person_getting_massage': '💆',
	'man_getting_massage': '💆‍♂️',
	'woman_getting_massage': '💆‍♀️',
	'person_getting_haircut': '💇',
	'man_getting_haircut': '💇‍♂️',
	'woman_getting_haircut': '💇‍♀️',
	'person_walking': '🚶',
	'man_walking': '🚶‍♂️',
	'woman_walking': '🚶‍♀️',
	'person_standing': '🧍',
	'man_standing': '🧍‍♂️',
	'woman_standing': '🧍‍♀️',
	'person_kneeling': '🧎',
	'man_kneeling': '🧎‍♂️',
	'woman_kneeling': '🧎‍♀️',
	'person_with_white_cane': '🧑‍🦯',
	'man_with_white_cane': '👨‍🦯',
	'woman_with_white_cane': '👩‍🦯',
	'person_in_motorized_wheelchair': '🧑‍🦼',
	'man_in_motorized_wheelchair': '👨‍🦼',
	'woman_in_motorized_wheelchair': '👩‍🦼',
	'person_in_manual_wheelchair': '🧑‍🦽',
	'man_in_manual_wheelchair': '👨‍🦽',
	'woman_in_manual_wheelchair': '👩‍🦽',
	'person_running': '🏃',
	'man_running': '🏃‍♂️',
	'woman_running': '🏃‍♀️',
	'woman_dancing': '💃',
	'man_dancing': '🕺',
	'person_in_suit_levitating': '🕴️',
	'people_with_bunny_ears': '👯',
	'men_with_bunny_ears': '👯‍♂️',
	'women_with_bunny_ears': '👯‍♀️',
	'person_in_steamy_room': '🧖',
	'man_in_steamy_room': '🧖‍♂️',
	'woman_in_steamy_room': '🧖‍♀️',
	'person_climbing': '🧗',
	'man_climbing': '🧗‍♂️',
	'woman_climbing': '🧗‍♀️',
	'person_fencing': '🤺',
	'horse_racing': '🏇',
	'skier': '⛷️',
	'snowboarder': '🏂',
	'person_golfing': '🏌️',
	'man_golfing': '🏌️‍♂️',
	'woman_golfing': '🏌️‍♀️',
	'person_surfing': '🏄',
	'man_surfing': '🏄‍♂️',
	'woman_surfing': '🏄‍♀️',
	'person_rowing_boat': '🚣',
	'man_rowing_boat': '🚣‍♂️',
	'woman_rowing_boat': '🚣‍♀️',
	'person_swimming': '🏊',
	'man_swimming': '🏊‍♂️',
	'woman_swimming': '🏊‍♀️',
	'person_bouncing_ball': '⛹️',
	'man_bouncing_ball': '⛹️‍♂️',
	'woman_bouncing_ball': '⛹️‍♀️',
	'person_lifting_weights': '🏋️',
	'man_lifting_weights': '🏋️‍♂️',
	'woman_lifting_weights': '🏋️‍♀️',
	'person_biking': '🚴',
	'man_biking': '🚴‍♂️',
	'woman_biking': '🚴‍♀️',
	'person_mountain_biking': '🚵',
	'man_mountain_biking': '🚵‍♂️',
	'woman_mountain_biking': '🚵‍♀️',
	'person_cartwheeling': '🤸',
	'man_cartwheeling': '🤸‍♂️',
	'woman_cartwheeling': '🤸‍♀️',
	'people_wrestling': '🤼',
	'men_wrestling': '🤼‍♂️',
	'women_wrestling': '🤼‍♀️',
	'person_playing_water_polo': '🤽',
	'man_playing_water_polo': '🤽‍♂️',
	'woman_playing_water_polo': '🤽‍♀️',
	'person_playing_handball': '🤾',
	'man_playing_handball': '🤾‍♂️',
	'woman_playing_handball': '🤾‍♀️',
	'person_juggling': '🤹',
	'man_juggling': '🤹‍♂️',
	'woman_juggling': '🤹‍♀️',
	'person_in_lotus_position': '🧘',
	'man_in_lotus_position': '🧘‍♂️',
	'woman_in_lotus_position': '🧘‍♀️',
	'person_taking_bath': '🛀',
	'person_in_bed': '🛌',
	'people_holding_hands': '🧑‍🤝‍🧑',
	'women_holding_hands': '👭',
	'woman_and_man_holding_hands': '👫',
	'men_holding_hands': '👬',
	'kiss': '💏',
	'kiss_woman_man': '👩‍❤️‍💋‍👨',
	'kiss_man_man': '👨‍❤️‍💋‍👨',
	'kiss_woman_woman': '👩‍❤️‍💋‍👩',
	'couple_with_heart': '💑',
	'couple_with_heart_woman_man': '👩‍❤️‍👨',
	'couple_with_heart_man_man': '👨‍❤️‍👨',
	'couple_with_heart_woman_woman': '👩‍❤️‍👩',
	'family': '👪',
	'family_man_woman_boy': '👨‍👩‍👦',
	'family_man_woman_girl': '👨‍👩‍👧',
	'family_man_woman_girl_boy': '👨‍👩‍👧‍👦',
	'family_man_woman_boy_boy': '👨‍👩‍👦‍👦',
	'family_man_woman_girl_girl': '👨‍👩‍👧‍👧',
	'family_man_man_boy': '👨‍👨‍👦',
	'family_man_man_girl': '👨‍👨‍👧',
	'family_man_man_girl_boy': '👨‍👨‍👧‍👦',
	'family_man_man_boy_boy': '👨‍👨‍👦‍👦',
	'family_man_man_girl_girl': '👨‍👨‍👧‍👧',
	'family_woman_woman_boy': '👩‍👩‍👦',
	'family_woman_woman_girl': '👩‍👩‍👧',
	'family_woman_woman_girl_boy': '👩‍👩‍👧‍👦',
	'family_woman_woman_boy_boy': '👩‍👩‍👦‍👦',
	'family_woman_woman_girl_girl': '👩‍👩‍👧‍👧',
	'family_man_boy': '👨‍👦',
	'family_man_boy_boy': '👨‍👦‍👦',
	'family_man_girl': '👨‍👧',
	'family_man_girl_boy': '👨‍👧‍👦',
	'family_man_girl_girl': '👨‍👧‍👧',
	'family_woman_boy': '👩‍👦',
	'family_woman_boy_boy': '👩‍👦‍👦',
	'family_woman_girl': '👩‍👧',
	'family_woman_girl_boy': '👩‍👧‍👦',
	'family_woman_girl_girl': '👩‍👧‍👧',
	'speaking_head': '🗣️',
	'bust_in_silhouette': '👤',
	'busts_in_silhouette': '👥',
	'people_hugging': '🫂',
	'footprints': '👣',
	'monkey_face': '🐵',
	'monkey': '🐒',
	'gorilla': '🦍',
	'orangutan': '🦧',
	'dog_face': '🐶',
	'dog': '🐕',
	'guide_dog': '🦮',
	'service_dog': '🐕‍🦺',
	'poodle': '🐩',
	'wolf': '🐺',
	'fox': '🦊',
	'raccoon': '🦝',
	'cat_face': '🐱',
	'cat': '🐈',
	'black_cat': '🐈‍⬛',
	'lion': '🦁',
	'tiger_face': '🐯',
	'tiger': '🐅',
	'leopard': '🐆',
	'horse_face': '🐴',
	'moose': '🫎',
	'donkey': '🫏',
	'horse': '🐎',
	'unicorn': '🦄',
	'zebra': '🦓',
	'deer': '🦌',
	'bison': '🦬',
	'cow_face': '🐮',
	'ox': '🐂',
	'water_buffalo': '🐃',
	'cow': '🐄',
	'pig_face': '🐷',
	'pig': '🐖',
	'boar': '🐗',
	'pig_nose': '🐽',
	'ram': '🐏',
	'ewe': '🐑',
	'goat': '🐐',
	'camel': '🐪',
	'two_hump_camel': '🐫',
	'llama': '🦙',
	'giraffe': '🦒',
	'elephant': '🐘',
	'mammoth': '🦣',
	'rhinoceros': '🦏',
	'hippopotamus': '🦛',
	'mouse_face': '🐭',
	'mouse': '🐁',
	'rat': '🐀',
	'hamster': '🐹',
	'rabbit_face': '🐰',
	'rabbit': '🐇',
	'chipmunk': '🐿️',
	'beaver': '🦫',
	'hedgehog': '🦔',
	'bat': '🦇',
	'bear': '🐻',
	'polar_bear': '🐻‍❄️',
	'koala': '🐨',
	'panda': '🐼',
	'sloth': '🦥',
	'otter': '🦦',
	'skunk': '🦨',
	'kangaroo': '🦘',
	'badger': '🦡',
	'paw_prints': '🐾',
	'turkey': '🦃',
	'chicken': '🐔',
	'rooster': '🐓',
	'hatching_chick': '🐣',
	'baby_chick': '🐤',
	'front_facing_baby_chick': '🐥',
	'bird': '🐦',
	'penguin': '🐧',
	'dove': '🕊️',
	'eagle': '🦅',
	'duck': '🦆',
	'swan': '🦢',
	'owl': '🦉',
	'dodo': '🦤',
	'feather': '🪶',
	'flamingo': '🦩',
	'peacock': '🦚',
	'parrot': '🦜',
	'wing': '🪽',
	'black_bird': '🐦‍⬛',
	'goose': '🪿',
	'frog': '🐸',
	'crocodile': '🐊',
	'turtle': '🐢',
	'lizard': '🦎',
	'snake': '🐍',
	'dragon_face': '🐲',
	'dragon': '🐉',
	'sauropod': '🦕',
	't_rex': '🦖',
	'spouting_whale': '🐳',
	'whale': '🐋',
	'dolphin': '🐬',
	'seal': '🦭',
	'fish': '🐟',
	'tropical_fish': '🐠',
	'blowfish': '🐡',
	'shark': '🦈',
	'octopus': '🐙',
	'spiral_shell': '🐚',
	'coral': '🪸',
	'jellyfish': '🪼',
	'snail': '🐌',
	'butterfly': '🦋',
	'bug': '🐛',
	'ant': '🐜',
	'honeybee': '🐝',
	'beetle': '🪲',
	'lady_beetle': '🐞',
	'cricket': '🦗',
	'cockroach': '🪳',
	'spider': '🕷️',
	'spider_web': '🕸️',
	'scorpion': '🦂',
	'mosquito': '🦟',
	'fly': '🪰',
	'worm': '🪱',
	'microbe': '🦠',
	'bouquet': '💐',
	'cherry_blossom': '🌸',
	'white_flower': '💮',
	'lotus': '🪷',
	'rosette': '🏵️',
	'rose': '🌹',
	'wilted_flower': '🥀',
	'hibiscus': '🌺',
	'sunflower': '🌻',
	'blossom': '🌼',
	'tulip': '🌷',
	'hyacinth': '🪻',
	'seedling': '🌱',
	'potted_plant': '🪴',
	'evergreen_tree': '🌲',
	'deciduous_tree': '🌳',
	'palm_tree': '🌴',
	'cactus': '🌵',
	'sheaf_of_rice': '🌾',
	'herb': '🌿',
	'shamrock': '☘️',
	'four_leaf_clover': '🍀',
	'maple_leaf': '🍁',
	'fallen_leaf': '🍂',
	'leaf_fluttering_in_wind': '🍃',
	'empty_nest': '🪹',
	'nest_with_eggs': '🪺',
	'mushroom': '🍄',
	'grapes': '🍇',
	'melon': '🍈',
	'watermelon': '🍉',
	'tangerine': '🍊',
	'lemon': '🍋',
	'banana': '🍌',
	'pineapple': '🍍',
	'mango': '🥭',
	'red_apple': '🍎',
	'green_apple': '🍏',
	'pear': '🍐',
	'peach': '🍑',
	'cherries': '🍒',
	'strawberry': '🍓',
	'blueberries': '🫐',
	'kiwi_fruit': '🥝',
	'tomato': '🍅',
	'olive': '🫒',
	'coconut': '🥥',
	'avocado': '🥑',
	'eggplant': '🍆',
	'potato': '🥔',
	'carrot': '🥕',
	'ear_of_corn': '🌽',
	'hot_pepper': '🌶️',
	'bell_pepper': '🫑',
	'cucumber': '🥒',
	'leafy_green': '🥬',
	'broccoli': '🥦',
	'garlic': '🧄',
	'onion': '🧅',
	'peanuts': '🥜',
	'beans': '🫘',
	'chestnut': '🌰',
	'ginger_root': '🫚',
	'pea_pod': '🫛',
	'bread': '🍞',
	'croissant': '🥐',
	'baguette_bread': '🥖',
	'flatbread': '🫓',
	'pretzel': '🥨',
	'bagel': '🥯',
	'pancakes': '🥞',
	'waffle': '🧇',
	'cheese_wedge': '🧀',
	'meat_on_bone': '🍖',
	'poultry_leg': '🍗',
	'cut_of_meat': '🥩',
	'bacon': '🥓',
	'hamburger': '🍔',
	'french_fries': '🍟',
	'pizza': '🍕',
	'hot_dog': '🌭',
	'sandwich': '🥪',
	'taco': '🌮',
	'burrito': '🌯',
	'tamale': '🫔',
	'stuffed_flatbread': '🥙',
	'falafel': '🧆',
	'egg': '🥚',
	'cooking': '🍳',
	'shallow_pan_of_food': '🥘',
	'pot_of_food': '🍲',
	'fondue': '🫕',
	'bowl_with_spoon': '🥣',
	'green_salad': '🥗',
	'popcorn': '🍿',
	'butter': '🧈',
	'salt': '🧂',
	'canned_food': '🥫',
	'bento_box': '🍱',
	'rice_cracker': '🍘',
	'rice_ball': '🍙',
	'cooked_rice': '🍚',
	'curry_rice': '🍛',
	'steaming_bowl': '🍜',
	'spaghetti': '🍝',
	'roasted_sweet_potato': '🍠',
	'oden': '🍢',
	'sushi': '🍣',
	'fried_shrimp': '🍤',
	'fish_cake_with_swirl': '🍥',
	'moon_cake': '🥮',
	'dango': '🍡',
	'dumpling': '🥟',
	'fortune_cookie': '🥠',
	'takeout_box': '🥡',
	'crab': '🦀',
	'lobster': '🦞',
	'shrimp': '🦐',
	'squid': '🦑',
	'oyster': '🦪',
	'soft_ice_cream': '🍦',
	'shaved_ice': '🍧',
	'ice_cream': '🍨',
	'doughnut': '🍩',
	'cookie': '🍪',
	'birthday_cake': '🎂',
	'shortcake': '🍰',
	'cupcake': '🧁',
	'pie': '🥧',
	'chocolate_bar': '🍫',
	'candy': '🍬',
	'lollipop': '🍭',
	'custard': '🍮',
	'honey_pot': '🍯',
	'baby_bottle': '🍼',
	'glass_of_milk': '🥛',
	'hot_beverage': '☕',
	'teapot': '🫖',
	'teacup_without_handle': '🍵',
	'sake': '🍶',
	'bottle_with_popping_cork': '🍾',
	'wine_glass': '🍷',
	'cocktail_glass': '🍸',
	'tropical_drink': '🍹',
	'beer_mug': '🍺',
	'clinking_beer_mugs': '🍻',
	'clinking_glasses': '🥂',
	'tumbler_glass': '🥃',
	'pouring_liquid': '🫗',
	'cup_with_straw': '🥤',
	'bubble_tea': '🧋',
	'beverage_box': '🧃',
	'mate': '🧉',
	'ice': '🧊',
	'chopsticks': '🥢',
	'fork_and_knife_with_plate': '🍽️',
	'fork_and_knife': '🍴',
	'spoon': '🥄',
	'kitchen_knife': '🔪',
	'jar': '🫙',
	'amphora': '🏺',
	'globe_showing_europe_africa': '🌍',
	'globe_showing_americas': '🌎',
	'globe_showing_asia_australia': '🌏',
	'globe_with_meridians': '🌐',
	'world_map': '🗺️',
	'map_of_japan': '🗾',
	'compass': '🧭',
	'snow_capped_mountain': '🏔️',
	'mountain': '⛰️',
	'volcano': '🌋',
	'mount_fuji': '🗻',
	'camping': '🏕️',
	'beach_with_umbrella': '🏖️',
	'desert': '🏜️',
	'desert_island': '🏝️',
	'national_park': '🏞️',
	'stadium': '🏟️',
	'classical_building': '🏛️',
	'building_construction': '🏗️',
	'brick': '🧱',
	'rock': '🪨',
	'wood': '🪵',
	'hut': '🛖',
	'houses': '🏘️',
	'derelict_house': '🏚️',
	'house': '🏠',
	'house_with_garden': '🏡',
	'office_building': '🏢',
	'japanese_post_office': '🏣',
	'post_office': '🏤',
	'hospital': '🏥',
	'bank': '🏦',
	'hotel': '🏨',
	'love_hotel': '🏩',
	'convenience_store': '🏪',
	'school': '🏫',
	'department_store': '🏬',
	'factory': '🏭',
	'japanese_castle': '🏯',
	'castle': '🏰',
	'wedding': '💒',
	'tokyo_tower': '🗼',
	'statue_of_liberty': '🗽',
	'church': '⛪',
	'mosque': '🕌',
	'hindu_temple': '🛕',
	'synagogue': '🕍',
	'shinto_shrine': '⛩️',
	'kaaba': '🕋',
	'fountain': '⛲',
	'tent': '⛺',
	'foggy': '🌁',
	'night_with_stars': '🌃',
	'cityscape': '🏙️',
	'sunrise_over_mountains': '🌄',
	'sunrise': '🌅',
	'cityscape_at_dusk': '🌆',
	'sunset': '🌇',
	'bridge_at_night': '🌉',
	'hot_springs': '♨️',
	'carousel_horse': '🎠',
	'playground_slide': '🛝',
	'ferris_wheel': '🎡',
	'roller_coaster': '🎢',
	'barber_pole': '💈',
	'circus_tent': '🎪',
	'locomotive': '🚂',
	'railway_car': '🚃',
	'high_speed_train': '🚄',
	'bullet_train': '🚅',
	'train': '🚆',
	'metro': '🚇',
	'light_rail': '🚈',
	'station': '🚉',
	'tram': '🚊',
	'monorail': '🚝',
	'mountain_railway': '🚞',
	'tram_car': '🚋',
	'bus': '🚌',
	'oncoming_bus': '🚍',
	'trolleybus': '🚎',
	'minibus': '🚐',
	'ambulance': '🚑',
	'fire_engine': '🚒',
	'police_car': '🚓',
	'oncoming_police_car': '🚔',
	'taxi': '🚕',
	'oncoming_taxi': '🚖',
	'automobile': '🚗',
	'oncoming_automobile': '🚘',
	'sport_utility_vehicle': '🚙',
	'pickup_truck': '🛻',
	'delivery_truck': '🚚',
	'articulated_lorry': '🚛',
	'tractor': '🚜',
	'racing_car': '🏎️',
	'motorcycle': '🏍️',
	'motor_scooter': '🛵',
	'manual_wheelchair': '🦽',
	'motorized_wheelchair': '🦼',
	'auto_rickshaw': '🛺',
	'bicycle': '🚲',
	'kick_scooter': '🛴',
	'skateboard': '🛹',
	'roller_skate': '🛼',
	'bus_stop': '🚏',
	'motorway': '🛣️',
	'railway_track': '🛤️',
	'oil_drum': '🛢️',
	'fuel_pump': '⛽',
	'wheel': '🛞',
	'police_car_light': '🚨',
	'horizontal_traffic_light': '🚥',
	'vertical_traffic_light': '🚦',
	'stop_sign': '🛑',
	'construction': '🚧',
	'anchor': '⚓',
	'ring_buoy': '🛟',
	'sailboat': '⛵',
	'canoe': '🛶',
	'speedboat': '🚤',
	'passenger_ship': '🛳️',
	'ferry': '⛴️',
	'motor_boat': '🛥️',
	'ship': '🚢',
	'airplane': '✈️',
	'small_airplane': '🛩️',
	'airplane_departure': '🛫',
	'airplane_arrival': '🛬',
	'parachute': '🪂',
	'seat': '💺',
	'helicopter': '🚁',
	'suspension_railway': '🚟',
	'mountain_cableway': '🚠',
	'aerial_tramway': '🚡',
	'satellite': '🛰️',
	'rocket': '🚀',
	'flying_saucer': '🛸',
	'bellhop_bell': '🛎️',
	'luggage': '🧳',
	'hourglass_done': '⌛',
	'hourglass_not_done': '⏳',
	'watch': '⌚',
	'alarm_clock': '⏰',
	'stopwatch': '⏱️',
	'timer_clock': '⏲️',
	'mantelpiece_clock': '🕰️',
	'twelve_o_clock': '🕛',
	'twelve_thirty': '🕧',
	'one_o_clock': '🕐',
	'one_thirty': '🕜',
	'two_o_clock': '🕑',
	'two_thirty': '🕝',
	'three_o_clock': '🕒',
	'three_thirty': '🕞',
	'four_o_clock': '🕓',
	'four_thirty': '🕟',
	'five_o_clock': '🕔',
	'five_thirty': '🕠',
	'six_o_clock': '🕕',
	'six_thirty': '🕡',
	'seven_o_clock': '🕖',
	'seven_thirty': '🕢',
	'eight_o_clock': '🕗',
	'eight_thirty': '🕣',
	'nine_o_clock': '🕘',
	'nine_thirty': '🕤',
	'ten_o_clock': '🕙',
	'ten_thirty': '🕥',
	'eleven_o_clock': '🕚',
	'eleven_thirty': '🕦',
	'new_moon': '🌑',
	'waxing_crescent_moon': '🌒',
	'first_quarter_moon': '🌓',
	'waxing_gibbous_moon': '🌔',
	'full_moon': '🌕',
	'waning_gibbous_moon': '🌖',
	'last_quarter_moon': '🌗',
	'waning_crescent_moon': '🌘',
	'crescent_moon': '🌙',
	'new_moon_face': '🌚',
	'first_quarter_moon_face': '🌛',
	'last_quarter_moon_face': '🌜',
	'thermometer': '🌡️',
	'sun': '☀️',
	'full_moon_face': '🌝',
	'sun_with_face': '🌞',
	'ringed_planet': '🪐',
	'star': '⭐',
	'glowing_star': '🌟',
	'shooting_star': '🌠',
	'milky_way': '🌌',
	'cloud': '☁️',
	'sun_behind_cloud': '⛅',
	'cloud_with_lightning_and_rain': '⛈️',
	'sun_behind_small_cloud': '🌤️',
	'sun_behind_large_cloud': '🌥️',
	'sun_behind_rain_cloud': '🌦️',
	'cloud_with_rain': '🌧️',
	'cloud_with_snow': '🌨️',
	'cloud_with_lightning': '🌩️',
	'tornado': '🌪️',
	'fog': '🌫️',
	'wind_face': '🌬️',
	'cyclone': '🌀',
	'rainbow': '🌈',
	'closed_umbrella': '🌂',
	'umbrella': '☂️',
	'umbrella_with_rain_drops': '☔',
	'umbrella_on_ground': '⛱️',
	'high_voltage': '⚡',
	'snowflake': '❄️',
	'snowman': '☃️',
	'snowman_without_snow': '⛄',
	'comet': '☄️',
	'fire': '🔥',
	'droplet': '💧',
	'water_wave': '🌊',
	'jack_o_lantern': '🎃',
	'christmas_tree': '🎄',
	'fireworks': '🎆',
	'sparkler': '🎇',
	'firecracker': '🧨',
	'sparkles': '✨',
	'balloon': '🎈',
	'party_popper': '🎉',
	'confetti_ball': '🎊',
	'tanabata_tree': '🎋',
	'pine_decoration': '🎍',
	'japanese_dolls': '🎎',
	'carp_streamer': '🎏',
	'wind_chime': '🎐',
	'moon_viewing_ceremony': '🎑',
	'red_envelope': '🧧',
	'ribbon': '🎀',
	'wrapped_gift': '🎁',
	'reminder_ribbon': '🎗️',
	'admission_tickets': '🎟️',
	'ticket': '🎫',
	'military_medal': '🎖️',
	'trophy': '🏆',
	'sports_medal': '🏅',
	'1st_place_medal': '🥇',
	'2nd_place_medal': '🥈',
	'3rd_place_medal': '🥉',
	'soccer_ball': '⚽',
	'baseball': '⚾',
	'softball': '🥎',
	'basketball': '🏀',
	'volleyball': '🏐',
	'american_football': '🏈',
	'rugby_football': '🏉',
	'tennis': '🎾',
	'flying_disc': '🥏',
	'bowling': '🎳',
	'cricket_game': '🏏',
	'field_hockey': '🏑',
	'ice_hockey': '🏒',
	'lacrosse': '🥍',
	'ping_pong': '🏓',
	'badminton': '🏸',
	'boxing_glove': '🥊',
	'martial_arts_uniform': '🥋',
	'goal_net': '🥅',
	'flag_in_hole': '⛳',
	'ice_skate': '⛸️',
	'fishing_pole': '🎣',
	'diving_mask': '🤿',
	'running_shirt': '🎽',
	'skis': '🎿',
	'sled': '🛷',
	'curling_stone': '🥌',
	'bullseye': '🎯',
	'yo_yo': '🪀',
	'kite': '🪁',
	'water_pistol': '🔫',
	'pool_8_ball': '🎱',
	'crystal_ball': '🔮',
	'magic_wand': '🪄',
	'video_game': '🎮',
	'joystick': '🕹️',
	'slot_machine': '🎰',
	'game_die': '🎲',
	'puzzle_piece': '🧩',
	'teddy_bear': '🧸',
	'pinata': '🪅',
	'mirror_ball': '🪩',
	'nesting_dolls': '🪆',
	'spade_suit': '♠️',
	'heart_suit': '♥️',
	'diamond_suit': '♦️',
	'club_suit': '♣️',
	'chess_pawn': '♟️',
	'joker': '🃏',
	'mahjong_red_dragon': '🀄',
	'flower_playing_cards': '🎴',
	'performing_arts': '🎭',
	'framed_picture': '🖼️',
	'artist_palette': '🎨',
	'thread': '🧵',
	'sewing_needle': '🪡',
	'yarn': '🧶',
	'knot': '🪢',
	'glasses': '👓',
	'sunglasses': '🕶️',
	'goggles': '🥽',
	'lab_coat': '🥼',
	'safety_vest': '🦺',
	'necktie': '👔',
	't_shirt': '👕',
	'jeans': '👖',
	'scarf': '🧣',
	'gloves': '🧤',
	'coat': '🧥',
	'socks': '🧦',
	'dress': '👗',
	'kimono': '👘',
	'sari': '🥻',
	'one_piece_swimsuit': '🩱',
	'briefs': '🩲',
	'shorts': '🩳',
	'bikini': '👙',
	'woman_s_clothes': '👚',
	'folding_hand_fan': '🪭',
	'purse': '👛',
	'handbag': '👜',
	'clutch_bag': '👝',
	'shopping_bags': '🛍️',
	'backpack': '🎒',
	'thong_sandal': '🩴',
	'man_s_shoe': '👞',
	'running_shoe': '👟',
	'hiking_boot': '🥾',
	'flat_shoe': '🥿',
	'high_heeled_shoe': '👠',
	'woman_s_sandal': '👡',
	'ballet_shoes': '🩰',
	'woman_s_boot': '👢',
	'hair_pick': '🪮',
	'crown': '👑',
	'woman_s_hat': '👒',
	'top_hat': '🎩',
	'graduation_cap': '🎓',
	'billed_cap': '🧢',
	'military_helmet': '🪖',
	'rescue_worker_s_helmet': '⛑️',
	'prayer_beads': '📿',
	'lipstick': '💄',
	'ring': '💍',
	'gem_stone': '💎',
	'muted_speaker': '🔇',
	'speaker_low_volume': '🔈',
	'speaker_medium_volume': '🔉',
	'speaker_high_volume': '🔊',
	'loudspeaker': '📢',
	'megaphone': '📣',
	'postal_horn': '📯',
	'bell': '🔔',
	'bell_with_slash': '🔕',
	'musical_score': '🎼',
	'musical_note': '🎵',
	'musical_notes': '🎶',
	'studio_microphone': '🎙️',
	'level_slider': '🎚️',
	'control_knobs': '🎛️',
	'microphone': '🎤',
	'headphone': '🎧',
	'radio': '📻',
	'saxophone': '🎷',
	'accordion': '🪗',
	'guitar': '🎸',
	'musical_keyboard': '🎹',
	'trumpet': '🎺',
	'violin': '🎻',
	'banjo': '🪕',
	'drum': '🥁',
	'long_drum': '🪘',
	'maracas': '🪇',
	'flute': '🪈',
	'mobile_phone': '📱',
	'mobile_phone_with_arrow': '📲',
	'telephone': '☎️',
	'telephone_receiver': '📞',
	'pager': '📟',
	'fax_machine': '📠',
	'battery': '🔋',
	'low_battery': '🪫',
	'electric_plug': '🔌',
	'laptop': '💻',
	'desktop_computer': '🖥️',
	'printer': '🖨️',
	'keyboard': '⌨️',
	'computer_mouse': '🖱️',
	'trackball': '🖲️',
	'computer_disk': '💽',
	'floppy_disk': '💾',
	'optical_disk': '💿',
	'dvd': '📀',
	'abacus': '🧮',
	'movie_camera': '🎥',
	'film_frames': '🎞️',
	'film_projector': '📽️',
	'clapper_board': '🎬',
	'television': '📺',
	'camera': '📷',
	'camera_with_flash': '📸',
	'video_camera': '📹',
	'videocassette': '📼',
	'magnifying_glass_tilted_left': '🔍',
	'magnifying_glass_tilted_right': '🔎',
	'candle': '🕯️',
	'light_bulb': '💡',
	'flashlight': '🔦',
	'red_paper_lantern': '🏮',
	'diya_lamp': '🪔',
	'notebook_with_decorative_cover': '📔',
	'closed_book': '📕',
	'open_book': '📖',
	'green_book': '📗',
	'blue_book': '📘',
	'orange_book': '📙',
	'books': '📚',
	'notebook': '📓',
	'ledger': '📒',
	'page_with_curl': '📃',
	'scroll': '📜',
	'page_facing_up': '📄',
	'newspaper': '📰',
	'rolled_up_newspaper': '🗞️',
	'bookmark_tabs': '📑',
	'bookmark': '🔖',
	'label': '🏷️',
	'money_bag': '💰',
	'coin': '🪙',
	'yen_banknote': '💴',
	'dollar_banknote': '💵',
	'euro_banknote': '💶',
	'pound_banknote': '💷',
	'money_with_wings': '💸',
	'credit_card': '💳',
	'receipt': '🧾',
	'chart_increasing_with_yen': '💹',
	'envelope': '✉️',
	'e_mail': '📧',
	'incoming_envelope': '📨',
	'envelope_with_arrow': '📩',
	'outbox_tray': '📤',
	'inbox_tray': '📥',
	'package': '📦',
	'closed_mailbox_with_raised_flag': '📫',
	'closed_mailbox_with_lowered_flag': '📪',
	'open_mailbox_with_raised_flag': '📬',
	'open_mailbox_with_lowered_flag': '📭',
	'postbox': '📮',
	'ballot_box_with_ballot': '🗳️',
	'pencil': '✏️',
	'black_nib': '✒️',
	'fountain_pen': '🖋️',
	'pen': '🖊️',
	'paintbrush': '🖌️',
	'crayon': '🖍️',
	'memo': '📝',
	'briefcase': '💼',
	'file_folder': '📁',
	'open_file_folder': '📂',
	'card_index_dividers': '🗂️',
	'calendar': '📅',
	'tear_off_calendar': '📆',
	'spiral_notepad': '🗒️',
	'spiral_calendar': '🗓️',
	'card_index': '📇',
	'chart_increasing': '📈',
	'chart_decreasing': '📉',
	'bar_chart': '📊',
	'clipboard': '📋',
	'pushpin': '📌',
	'round_pushpin': '📍',
	'paperclip': '📎',
	'linked_paperclips': '🖇️',
	'straight_ruler': '📏',
	'triangular_ruler': '📐',
	'scissors': '✂️',
	'card_file_box': '🗃️',
	'file_cabinet': '🗄️',
	'wastebasket': '🗑️',
	'locked': '🔒',
	'unlocked': '🔓',
	'locked_with_pen': '🔏',
	'locked_with_key': '🔐',
	'key': '🔑',
	'old_key': '🗝️',
	'hammer': '🔨',
	'axe': '🪓',
	'pick': '⛏️',
	'hammer_and_pick': '⚒️',
	'hammer_and_wrench': '🛠️',
	'dagger': '🗡️',
	'crossed_swords': '⚔️',
	'bomb': '💣',
	'boomerang': '🪃',
	'bow_and_arrow': '🏹',
	'shield': '🛡️',
	'carpentry_saw': '🪚',
	'wrench': '🔧',
	'screwdriver': '🪛',
	'nut_and_bolt': '🔩',
	'gear': '⚙️',
	'clamp': '🗜️',
	'balance_scale': '⚖️',
	'white_cane': '🦯',
	'link': '🔗',
	'chains': '⛓️',
	'hook': '🪝',
	'toolbox': '🧰',
	'magnet': '🧲',
	'ladder': '🪜',
	'alembic': '⚗️',
	'test_tube': '🧪',
	'petri_dish': '🧫',
	'dna': '🧬',
	'microscope': '🔬',
	'telescope': '🔭',
	'satellite_antenna': '📡',
	'syringe': '💉',
	'drop_of_blood': '🩸',
	'pill': '💊',
	'adhesive_bandage': '🩹',
	'crutch': '🩼',
	'stethoscope': '🩺',
	'x_ray': '🩻',
	'door': '🚪',
	'elevator': '🛗',
	'mirror': '🪞',
	'window': '🪟',
	'bed': '🛏️',
	'couch_and_lamp': '🛋️',
	'chair': '🪑',
	'toilet': '🚽',
	'plunger': '🪠',
	'shower': '🚿',
	'bathtub': '🛁',
	'mouse_trap': '🪤',
	'razor': '🪒',
	'lotion_bottle': '🧴',
	'safety_pin': '🧷',
	'broom': '🧹',
	'basket': '🧺',
	'roll_of_paper': '🧻',
	'bucket': '🪣',
	'soap': '🧼',
	'bubbles': '🫧',
	'toothbrush': '🪥',
	'sponge': '🧽',
	'fire_extinguisher': '🧯',
	'shopping_cart': '🛒',
	'cigarette': '🚬',
	'coffin': '⚰️',
	'headstone': '🪦',
	'funeral_urn': '⚱️',
	'nazar_amulet': '🧿',
	'hamsa': '🪬',
	'moai': '🗿',
	'placard': '🪧',
	'identification_card': '🪪',
	'atm_sign': '🏧',
	'litter_in_bin_sign': '🚮',
	'potable_water': '🚰',
	'wheelchair_symbol': '♿',
	'men_s_room': '🚹',
	'women_s_room': '🚺',
	'restroom': '🚻',
	'baby_symbol': '🚼',
	'water_closet': '🚾',
	'passport_control': '🛂',
	'customs': '🛃',
	'baggage_claim': '🛄',
	'left_luggage': '🛅',
	'warning': '⚠️',
	'children_crossing': '🚸',
	'no_entry': '⛔',
	'prohibited': '🚫',
	'no_bicycles': '🚳',
	'no_smoking': '🚭',
	'no_littering': '🚯',
	'non_potable_water': '🚱',
	'no_pedestrians': '🚷',
	'no_mobile_phones': '📵',
	'no_one_under_eighteen': '🔞',
	'radioactive': '☢️',
	'biohazard': '☣️',
	'up_arrow': '⬆️',
	'up_right_arrow': '↗️',
	'right_arrow': '➡️',
	'down_right_arrow': '↘️',
	'down_arrow': '⬇️',
	'down_left_arrow': '↙️',
	'left_arrow': '⬅️',
	'up_left_arrow': '↖️',
	'up_down_arrow': '↕️',
	'left_right_arrow': '↔️',
	'right_arrow_curving_left': '↩️',
	'left_arrow_curving_right': '↪️',
	'right_arrow_curving_up': '⤴️',
	'right_arrow_curving_down': '⤵️',
	'clockwise_vertical_arrows': '🔃',
	'counterclockwise_arrows_button': '🔄',
	'back_arrow': '🔙',
	'end_arrow': '🔚',
	'on_arrow': '🔛',
	'soon_arrow': '🔜',
	'top_arrow': '🔝',
	'place_of_worship': '🛐',
	'atom_symbol': '⚛️',
	'om': '🕉️',
	'star_of_david': '✡️',
	'wheel_of_dharma': '☸️',
	'yin_yang': '☯️',
	'latin_cross': '✝️',
	'orthodox_cross': '☦️',
	'star_and_crescent': '☪️',
	'peace_symbol': '☮️',
	'menorah': '🕎',
	'dotted_six_pointed_star': '🔯',
	'khanda': '🪯',
	'aries': '♈',
	'taurus': '♉',
	'gemini': '♊',
	'cancer': '♋',
	'leo': '♌',
	'virgo': '♍',
	'libra': '♎',
	'scorpio': '♏',
	'sagittarius': '♐',
	'capricorn': '♑',
	'aquarius': '♒',
	'pisces': '♓',
	'ophiuchus': '⛎',
	'shuffle_tracks_button': '🔀',
	'repeat_button': '🔁',
	'repeat_single_button': '🔂',
	'play_button': '▶️',
	'fast_forward_button': '⏩',
	'next_track_button': '⏭️',
	'play_or_pause_button': '⏯️',
	'reverse_button': '◀️',
	'fast_reverse_button': '⏪',
	'last_track_button': '⏮️',
	'upwards_button': '🔼',
	'fast_up_button': '⏫',
	'downwards_button': '🔽',
	'fast_down_button': '⏬',
	'pause_button': '⏸️',
	'stop_button': '⏹️',
	'record_button': '⏺️',
	'eject_button': '⏏️',
	'cinema': '🎦',
	'dim_button': '🔅',
	'bright_button': '🔆',
	'antenna_bars': '📶',
	'wireless': '🛜',
	'vibration_mode': '📳',
	'mobile_phone_off': '📴',
	'female_sign': '♀️',
	'male_sign': '♂️',
	'transgender_symbol': '⚧️',
	'multiply': '✖️',
	'plus': '➕',
	'minus': '➖',
	'divide': '➗',
	'heavy_equals_sign': '🟰',
	'infinity': '♾️',
	'double_exclamation_mark': '‼️',
	'exclamation_question_mark': '⁉️',
	'red_question_mark': '❓',
	'white_question_mark': '❔',
	'white_exclamation_mark': '❕',
	'red_exclamation_mark': '❗',
	'wavy_dash': '〰️',
	'currency_exchange': '💱',
	'heavy_dollar_sign': '💲',
	'medical_symbol': '⚕️',
	'recycling_symbol': '♻️',
	'fleur_de_lis': '⚜️',
	'trident_emblem': '🔱',
	'name_badge': '📛',
	'japanese_symbol_for_beginner': '🔰',
	'hollow_red_circle': '⭕',
	'check_mark_button': '✅',
	'check_box_with_check': '☑️',
	'check_mark': '✔️',
	'cross_mark': '❌',
	'cross_mark_button': '❎',
	'curly_loop': '➰',
	'double_curly_loop': '➿',
	'part_alternation_mark': '〽️',
	'eight_spoked_asterisk': '✳️',
	'eight_pointed_star': '✴️',
	'sparkle': '❇️',
	'copyright': '©️',
	'registered': '®️',
	'trade_mark': '™️',
	'keycap_number_sign': '#️⃣',
	'keycap_asterisk': '*️⃣',
	'keycap_0': '0️⃣',
	'keycap_1': '1️⃣',
	'keycap_2': '2️⃣',
	'keycap_3': '3️⃣',
	'keycap_4': '4️⃣',
	'keycap_5': '5️⃣',
	'keycap_6': '6️⃣',
	'keycap_7': '7️⃣',
	'keycap_8': '8️⃣',
	'keycap_9': '9️⃣',
	'keycap_10': '🔟',
	'input_latin_uppercase': '🔠',
	'input_latin_lowercase': '🔡',
	'input_numbers': '🔢',
	'input_symbols': '🔣',
	'input_latin_letters': '🔤',
	'a_button': '🅰️',
	'ab_button': '🆎',
	'b_button': '🅱️',
	'cl_button': '🆑',
	'cool_button': '🆒',
	'free_button': '🆓',
	'information': 'ℹ️',
	'id_button': '🆔',
	'circled_m': 'Ⓜ️',
	'new_button': '🆕',
	'ng_button': '🆖',
	'o_button': '🅾️',
	'ok_button': '🆗',
	'p_button': '🅿️',
	'sos_button': '🆘',
	'up_button': '🆙',
	'vs_button': '🆚',
	'japanese_here_button': '🈁',
	'japanese_service_charge_button': '🈂️',
	'japanese_monthly_amount_button': '🈷️',
	'japanese_not_free_of_charge_button': '🈶',
	'japanese_reserved_button': '🈯',
	'japanese_bargain_button': '🉐',
	'japanese_discount_button': '🈹',
	'japanese_free_of_charge_button': '🈚',
	'japanese_prohibited_button': '🈲',
	'japanese_acceptable_button': '🉑',
	'japanese_application_button': '🈸',
	'japanese_passing_grade_button': '🈴',
	'japanese_vacancy_button': '🈳',
	'japanese_congratulations_button': '㊗️',
	'japanese_secret_button': '㊙️',
	'japanese_open_for_business_button': '🈺',
	'japanese_no_vacancy_button': '🈵',
	'red_circle': '🔴',
	'orange_circle': '🟠',
	'yellow_circle': '🟡',
	'green_circle': '🟢',
	'blue_circle': '🔵',
	'purple_circle': '🟣',
	'brown_circle': '🟤',
	'black_circle': '⚫',
	'white_circle': '⚪',
	'red_square': '🟥',
	'orange_square': '🟧',
	'yellow_square': '🟨',
	'green_square': '🟩',
	'blue_square': '🟦',
	'purple_square': '🟪',
	'brown_square': '🟫',
	'black_large_square': '⬛',
	'white_large_square': '⬜',
	'black_medium_square': '◼️',
	'white_medium_square': '◻️',
	'black_medium_small_square': '◾',
	'white_medium_small_square': '◽',
	'black_small_square': '▪️',
	'white_small_square': '▫️',
	'large_orange_diamond': '🔶',
	'large_blue_diamond': '🔷',
	'small_orange_diamond': '🔸',
	'small_blue_diamond': '🔹',
	'red_triangle_pointed_up': '🔺',
	'red_triangle_pointed_down': '🔻',
	'diamond_with_a_dot': '💠',
	'radio_button': '🔘',
	'white_square_button': '🔳',
	'black_square_button': '🔲',
	'chequered_flag': '🏁',
	'triangular_flag': '🚩',
	'crossed_flags': '🎌',
	'black_flag': '🏴',
	'white_flag': '🏳️',
	'rainbow_flag': '🏳️‍🌈',
	'transgender_flag': '🏳️‍⚧️',
	'pirate_flag': '🏴‍☠️',
	'flag_ascension_island': '🇦🇨',
	'flag_andorra': '🇦🇩',
	'flag_united_arab_emirates': '🇦🇪',
	'flag_afghanistan': '🇦🇫',
	'flag_antigua_barbuda': '🇦🇬',
	'flag_anguilla': '🇦🇮',
	'flag_albania': '🇦🇱',
	'flag_armenia': '🇦🇲',
	'flag_angola': '🇦🇴',
	'flag_antarctica': '🇦🇶',
	'flag_argentina': '🇦🇷',
	'flag_american_samoa': '🇦🇸',
	'flag_austria': '🇦🇹',
	'flag_australia': '🇦🇺',
	'flag_aruba': '🇦🇼',
	'flag_aland_islands': '🇦🇽',
	'flag_azerbaijan': '🇦🇿',
	'flag_bosnia_herzegovina': '🇧🇦',
	'flag_barbados': '🇧🇧',
	'flag_bangladesh': '🇧🇩',
	'flag_belgium': '🇧🇪',
	'flag_burkina_faso': '🇧🇫',
	'flag_bulgaria': '🇧🇬',
	'flag_bahrain': '🇧🇭',
	'flag_burundi': '🇧🇮',
	'flag_benin': '🇧🇯',
	'flag_st_barthelemy': '🇧🇱',
	'flag_bermuda': '🇧🇲',
	'flag_brunei': '🇧🇳',
	'flag_bolivia': '🇧🇴',
	'flag_caribbean_netherlands': '🇧🇶',
	'flag_brazil': '🇧🇷',
	'flag_bahamas': '🇧🇸',
	'flag_bhutan': '🇧🇹',
	'flag_bouvet_island': '🇧🇻',
	'flag_botswana': '🇧🇼',
	'flag_belarus': '🇧🇾',
	'flag_belize': '🇧🇿',
	'flag_canada': '🇨🇦',
	'flag_cocos_islands': '🇨🇨',
	'flag_congo_kinshasa': '🇨🇩',
	'flag_central_african_republic': '🇨🇫',
	'flag_congo_brazzaville': '🇨🇬',
	'flag_switzerland': '🇨🇭',
	'flag_cote_d_ivoire': '🇨🇮',
	'flag_cook_islands': '🇨🇰',
	'flag_chile': '🇨🇱',
	'flag_cameroon': '🇨🇲',
	'flag_china': '🇨🇳',
	'flag_colombia': '🇨🇴',
	'flag_clipperton_island': '🇨🇵',
	'flag_costa_rica': '🇨🇷',
	'flag_cuba': '🇨🇺',
	'flag_cape_verde': '🇨🇻',
	'flag_curacao': '🇨🇼',
	'flag_christmas_island': '🇨🇽',
	'flag_cyprus': '🇨🇾',
	'flag_czechia': '🇨🇿',
	'flag_germany': '🇩🇪',
	'flag_diego_garcia': '🇩🇬',
	'flag_djibouti': '🇩🇯',
	'flag_denmark': '🇩🇰',
	'flag_dominica': '🇩🇲',
	'flag_dominican_republic': '🇩🇴',
	'flag_algeria': '🇩🇿',
	'flag_ceuta_melilla': '🇪🇦',
	'flag_ecuador': '🇪🇨',
	'flag_estonia': '🇪🇪',
	'flag_egypt': '🇪🇬',
	'flag_western_sahara': '🇪🇭',
	'flag_eritrea': '🇪🇷',
	'flag_spain': '🇪🇸',
	'flag_ethiopia': '🇪🇹',
	'flag_european_union': '🇪🇺',
	'flag_finland': '🇫🇮',
	'flag_fiji': '🇫🇯',
	'flag_falkland_islands': '🇫🇰',
	'flag_micronesia': '🇫🇲',
	'flag_faroe_islands': '🇫🇴',
	'flag_france': '🇫🇷',
	'flag_gabon': '🇬🇦',
	'flag_united_kingdom': '🇬🇧',
	'flag_grenada': '🇬🇩',
	'flag_georgia': '🇬🇪',
	'flag_french_guiana': '🇬🇫',
	'flag_guernsey': '🇬🇬',
	'flag_ghana': '🇬🇭',
	'flag_gibraltar': '🇬🇮',
	'flag_greenland': '🇬🇱',
	'flag_gambia': '🇬🇲',
	'flag_guinea': '🇬🇳',
	'flag_guadeloupe': '🇬🇵',
	'flag_equatorial_guinea': '🇬🇶',
	'flag_greece': '🇬🇷',
	'flag_south_georgia_south_sandwich_islands': '🇬🇸',
	'flag_guatemala': '🇬🇹',
	'flag_guam': '🇬🇺',
	'flag_guinea_bissau': '🇬🇼',
	'flag_guyana': '🇬🇾',
	'flag_hong_kong_sar_china': '🇭🇰',
	'flag_heard_mcdonald_islands': '🇭🇲',
	'flag_honduras': '🇭🇳',
	'flag_croatia': '🇭🇷',
	'flag_haiti': '🇭🇹',
	'flag_hungary': '🇭🇺',
	'flag_canary_islands': '🇮🇨',
	'flag_indonesia': '🇮🇩',
	'flag_ireland': '🇮🇪',
	'flag_israel': '🇮🇱',
	'flag_isle_of_man': '🇮🇲',
	'flag_india': '🇮🇳',
	'flag_british_indian_ocean_territory': '🇮🇴',
	'flag_iraq': '🇮🇶',
	'flag_iran': '🇮🇷',
	'flag_iceland': '🇮🇸',
	'flag_italy': '🇮🇹',
	'flag_jersey': '🇯🇪',
	'flag_jamaica': '🇯🇲',
	'flag_jordan': '🇯🇴',
	'flag_japan': '🇯🇵',
	'flag_kenya': '🇰🇪',
	'flag_kyrgyzstan': '🇰🇬',
	'flag_cambodia': '🇰🇭',
	'flag_kiribati': '🇰🇮',
	'flag_comoros': '🇰🇲',
	'flag_st_kitts_nevis': '🇰🇳',
	'flag_north_korea': '🇰🇵',
	'flag_south_korea': '🇰🇷',
	'flag_kuwait': '🇰🇼',
	'flag_cayman_islands': '🇰🇾',
	'flag_kazakhstan': '🇰🇿',
	'flag_laos': '🇱🇦',
	'flag_lebanon': '🇱🇧',
	'flag_st_lucia': '🇱🇨',
	'flag_liechtenstein': '🇱🇮',
	'flag_sri_lanka': '🇱🇰',
	'flag_liberia': '🇱🇷',
	'flag_lesotho': '🇱🇸',
	'flag_lithuania': '🇱🇹',
	'flag_luxembourg': '🇱🇺',
	'flag_latvia': '🇱🇻',
	'flag_libya': '🇱🇾',
	'flag_morocco': '🇲🇦',
	'flag_monaco': '🇲🇨',
	'flag_moldova': '🇲🇩',
	'flag_montenegro': '🇲🇪',
	'flag_st_martin': '🇲🇫',
	'flag_madagascar': '🇲🇬',
	'flag_marshall_islands': '🇲🇭',
	'flag_north_macedonia': '🇲🇰',
	'flag_mali': '🇲🇱',
	'flag_myanmar': '🇲🇲',
	'flag_mongolia': '🇲🇳',
	'flag_macao_sar_china': '🇲🇴',
	'flag_northern_mariana_islands': '🇲🇵',
	'flag_martinique': '🇲🇶',
	'flag_mauritania': '🇲🇷',
	'flag_montserrat': '🇲🇸',
	'flag_malta': '🇲🇹',
	'flag_mauritius': '🇲🇺',
	'flag_maldives': '🇲🇻',
	'flag_malawi': '🇲🇼',
	'flag_mexico': '🇲🇽',
	'flag_malaysia': '🇲🇾',
	'flag_mozambique': '🇲🇿',
	'flag_namibia': '🇳🇦',
	'flag_new_caledonia': '🇳🇨',
	'flag_niger': '🇳🇪',
	'flag_norfolk_island': '🇳🇫',
	'flag_nigeria': '🇳🇬',
	'flag_nicaragua': '🇳🇮',
	'flag_netherlands': '🇳🇱',
	'flag_norway': '🇳🇴',
	'flag_nepal': '🇳🇵',
	'flag_nauru': '🇳🇷',
	'flag_niue': '🇳🇺',
	'flag_new_zealand': '🇳🇿',
	'flag_oman': '🇴🇲',
	'flag_panama': '🇵🇦',
	'flag_peru': '🇵🇪',
	'flag_french_polynesia': '🇵🇫',
	'flag_papua_new_guinea': '🇵🇬',
	'flag_philippines': '🇵🇭',
	'flag_pakistan': '🇵🇰',
	'flag_poland': '🇵🇱',
	'flag_st_pierre_miquelon': '🇵🇲',
	'flag_pitcairn_islands': '🇵🇳',
	'flag_puerto_rico': '🇵🇷',
	'flag_palestinian_territories': '🇵🇸',
	'flag_portugal': '🇵🇹',
	'flag_palau': '🇵🇼',
	'flag_paraguay': '🇵🇾',
	'flag_qatar': '🇶🇦',
	'flag_reunion': '🇷🇪',
	'flag_romania': '🇷🇴',
	'flag_serbia': '🇷🇸',
	'flag_russia': '🇷🇺',
	'flag_rwanda': '🇷🇼',
	'flag_saudi_arabia': '🇸🇦',
	'flag_solomon_islands': '🇸🇧',
	'flag_seychelles': '🇸🇨',
	'flag_sudan': '🇸🇩',
	'flag_sweden': '🇸🇪',
	'flag_singapore': '🇸🇬',
	'flag_st_helena': '🇸🇭',
	'flag_slovenia': '🇸🇮',
	'flag_svalbard_jan_mayen': '🇸🇯',
	'flag_slovakia': '🇸🇰',
	'flag_sierra_leone': '🇸🇱',
	'flag_san_marino': '🇸🇲',
	'flag_senegal': '🇸🇳',
	'flag_somalia': '🇸🇴',
	'flag_suriname': '🇸🇷',
	'flag_south_sudan': '🇸🇸',
	'flag_sao_tome_principe': '🇸🇹',
	'flag_el_salvador': '🇸🇻',
	'flag_sint_maarten': '🇸🇽',
	'flag_syria': '🇸🇾',
	'flag_eswatini': '🇸🇿',
	'flag_tristan_da_cunha': '🇹🇦',
	'flag_turks_caicos_islands': '🇹🇨',
	'flag_chad': '🇹🇩',
	'flag_french_southern_territories': '🇹🇫',
	'flag_togo': '🇹🇬',
	'flag_thailand': '🇹🇭',
	'flag_tajikistan': '🇹🇯',
	'flag_tokelau': '🇹🇰',
	'flag_timor_leste': '🇹🇱',
	'flag_turkmenistan': '🇹🇲',
	'flag_tunisia': '🇹🇳',
	'flag_tonga': '🇹🇴',
	'flag_turkey': '🇹🇷',
	'flag_trinidad_tobago': '🇹🇹',
	'flag_tuvalu': '🇹🇻',
	'flag_taiwan': '🇹🇼',
	'flag_tanzania': '🇹🇿',
	'flag_ukraine': '🇺🇦',
	'flag_uganda': '🇺🇬',
	'flag_u_s_outlying_islands': '🇺🇲',
	'flag_united_nations': '🇺🇳',
	'flag_united_states': '🇺🇸',
	'flag_uruguay': '🇺🇾',
	'flag_uzbekistan': '🇺🇿',
	'flag_vatican_city': '🇻🇦',
	'flag_st_vincent_grenadines': '🇻🇨',
	'flag_venezuela': '🇻🇪',
	'flag_british_virgin_islands': '🇻🇬',
	'flag_u_s_virgin_islands': '🇻🇮',
	'flag_vietnam': '🇻🇳',
	'flag_vanuatu': '🇻🇺',
	'flag_wallis_futuna': '🇼🇫',
	'flag_samoa': '🇼🇸',
	'flag_kosovo': '🇽🇰',
	'flag_yemen': '🇾🇪',
	'flag_mayotte': '🇾🇹',
	'flag_south_africa': '🇿🇦',
	'flag_zambia': '🇿🇲',
	'flag_zimbabwe': '🇿🇼',
	'flag_england': '🏴󠁧󠁢󠁥󠁮󠁧󠁿',
	'flag_scotland': '🏴󠁧󠁢󠁳󠁣󠁴󠁿',
	'flag_wales': '🏴󠁧󠁢󠁷󠁬󠁳󠁿'
};

mxShapeEmoji.prototype.emojiComponents = {
    'medium_light_skin_tone': '🏼', // Must be before 'light_skin_tone' as it ends with it
    'light_skin_tone': '🏻',
    'medium_skin_tone': '🏽',
    'medium_dark_skin_tone': '🏾',
    'dark_skin_tone': '🏿',
    'red_hair': String.fromCharCode(8205) + '🦰', // 8205 is zero width joiner
    'curly_hair': String.fromCharCode(8205) + '🦱',
    'white_hair': String.fromCharCode(8205) + '🦳',
    'bald': String.fromCharCode(8205) + '🦲'
};

// TODO Add custom properties (enum of emojis)
//mxShapeEmoji.prototype.customProperties = [
//	{name: '', dispName: 'Emoji', type: 'enum', defVal: '', 
//		enumList: [{val: '', dispName: ''}]
//}];

// TODO Add multi skin tones support
mxShapeEmoji.prototype.getUnicodeText = function(emoji)
{
    var emojiUnicode = '';

    if (emoji != '')
    {
        emojiUnicode = this.emojis[emoji];

        if (emojiUnicode == null)
        {
            for (var comp in this.emojiComponents)
            {
                if (emoji.length > comp.length && emoji.indexOf(comp) == emoji.length - comp.length)
                {
                    emoji = emoji.substring(0, emoji.length - comp.length - 1);
                    emojiUnicode = this.getUnicodeText(emoji);

                    if (emojiUnicode != null)
                    {
                        if (comp.indexOf('tone') > 0 && emojiUnicode.length > 2)
                        {
                            emojiUnicode = emojiUnicode.substring(0, 2) + this.emojiComponents[comp] + emojiUnicode.substring(2);
                        }
                        else
                        {
                            emojiUnicode += this.emojiComponents[comp];
                        }
                    }

                    break;
                }
            }
        }
    }

    return emojiUnicode;
}

/**
* Function: paintVertexShape
* 
* Paints the vertex shape.
*/
mxShapeEmoji.prototype.paintVertexShape = function(c, x, y, w, h)
{
    var emoji = mxUtils.getValue(this.style, this.EMOJI_NAME, '');
    var emojiUnicode = this.getUnicodeText(emoji) || '';

	c.rect(x, y, w, h);
    c.fillAndStroke();
    var origSize = c.state.fontSize;
    c.state.fontSize = Math.min(w, h);
    c.text(x, y - h * 0.12, w, h, emojiUnicode);
    c.state.fontSize = origSize;
}

mxCellRenderer.registerShape('mxgraph.emoji', mxShapeEmoji);
