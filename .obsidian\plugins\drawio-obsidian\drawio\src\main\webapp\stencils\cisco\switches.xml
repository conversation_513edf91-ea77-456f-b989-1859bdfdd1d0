<shapes name="mxgraph.cisco.switches">
<shape name="ATM Fast Gigabit Etherswitch" h="40" w="40.33" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
<constraint x="0.15" y="0.1" perimeter="0" name="NW"/>
<constraint x="0" y="1" perimeter="0" name="SW"/>
<constraint x="1" y="0" perimeter="0" name="NE"/>
<constraint x="0.895" y="0.82" perimeter="0" name="SE"/>
</connections>
<background>
<save/>
<path>
<move x="30.33" y="40"/>
<line x="30.33" y="9"/>
<line x="0" y="9"/>
<line x="0" y="40"/>
<close/>
<move x="40.33" y="0"/>
<line x="30.33" y="9"/>
<line x="0" y="9"/>
<line x="11" y="0"/>
<close/>
<move x="40.33" y="28"/>
<line x="40.33" y="0"/>
<line x="30.33" y="9"/>
<line x="30.33" y="40"/>
<close/>
</path>
</background>
<foreground>
<linejoin join="round"/>
<fillstroke/>
<restore/>
<rect/>
<stroke/>
<strokecolor color="#000000"/>
<strokewidth width="2"/>
<path>
<move x="5.67" y="33"/>
<line x="12" y="33"/>
<line x="20.67" y="15"/>
<line x="27" y="15"/>
</path>
<stroke/>
<fillcolor color="#000000"/>
<path>
<move x="25" y="11.67"/>
<line x="25" y="18.33"/>
<line x="29" y="15"/>
<close/>
<move x="6.67" y="36.33"/>
<line x="6.67" y="30"/>
<line x="3" y="33"/>
<close/>
</path>
<fill/>
<fillcolor color="#ffffff"/>
<path>
<move x="5.67" y="37"/>
<line x="5.67" y="30.67"/>
<line x="2" y="33.67"/>
<close/>
<move x="24.33" y="12.33"/>
<line x="24.33" y="18.67"/>
<line x="28.33" y="15.67"/>
<close/>
</path>
<fill/>
<fillcolor color="#000000"/>
<path>
<move x="6.67" y="18.33"/>
<line x="6.67" y="12"/>
<line x="3" y="15"/>
<close/>
</path>
<fill/>
<fillcolor color="#ffffff"/>
<path>
<move x="5.67" y="19"/>
<line x="5.67" y="12.67"/>
<line x="2" y="15.67"/>
<close/>
</path>
<fill/>
<strokecolor color="#000000"/>
<path>
<move x="27" y="33"/>
<line x="20.67" y="33"/>
<line x="11.67" y="15"/>
<line x="6.33" y="15"/>
</path>
<stroke/>
<strokecolor color="#ffffff"/>
<path>
<move x="27" y="33.67"/>
<line x="20.33" y="33.67"/>
<line x="11.33" y="15.67"/>
<line x="4.33" y="15.67"/>
</path>
<stroke/>
<strokecolor color="#ffffff"/>
<strokewidth width="2"/>
<path>
<move x="4.33" y="33.67"/>
<line x="11" y="33.67"/>
<line x="19.67" y="15.67"/>
<line x="26.67" y="15.67"/>
</path>
<stroke/>
<fillcolor color="#000000"/>
<path>
<move x="25" y="30"/>
<line x="25" y="36.33"/>
<line x="29" y="33.33"/>
<close/>
</path>
<fill/>
<fillcolor color="#ffffff"/>
<path>
<move x="24.33" y="30.33"/>
<line x="24.33" y="36.67"/>
<line x="28.33" y="33.67"/>
<close/>
</path>
<fill/>
<fillcolor color="#000000"/>
<path>
<move x="30.33" y="1.67"/>
<line x="31.67" y="0.67"/>
<line x="35.67" y="2"/>
<line x="28.67" y="3.67"/>
<line x="30.33" y="2"/>
<line x="23.33" y="2"/>
<line x="23.67" y="1.67"/>
<close/>
</path>
<fill/>
<fillcolor color="#ffffff"/>
<path>
<move x="25" y="5.67"/>
<line x="26.33" y="4.67"/>
<line x="29.67" y="5.67"/>
<line x="22.67" y="7.33"/>
<line x="24" y="6"/>
<line x="17" y="6"/>
<line x="18" y="5.67"/>
<close/>
<move x="21.67" y="3"/>
<line x="21" y="3.67"/>
<line x="15.33" y="3.67"/>
<line x="14" y="4.33"/>
<line x="10.67" y="3.33"/>
<line x="17.67" y="1.67"/>
<line x="15.67" y="3"/>
<close/>
<move x="16.67" y="7"/>
<line x="15.67" y="7.67"/>
<line x="10" y="7.67"/>
<line x="9" y="8.33"/>
<line x="5.67" y="7"/>
<line x="12.67" y="5.67"/>
<line x="10.67" y="7"/>
<close/>
<move x="30.33" y="2"/>
<line x="31.67" y="1"/>
<line x="35.33" y="2.33"/>
<line x="28.33" y="3.67"/>
<line x="29.33" y="2.67"/>
<line x="22.67" y="2.67"/>
<line x="23.33" y="2"/>
<close/>
</path>
<fill/>
</foreground>
</shape>
<shape name="ATM Switch" h="35.33" w="34.67" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
<constraint x="0.11" y="0.08" perimeter="0" name="NW"/>
<constraint x="0" y="1" perimeter="0" name="SW"/>
<constraint x="1" y="0" perimeter="0" name="NE"/>
<constraint x="0.92" y="0.91" perimeter="0" name="SE"/>
</connections>
<background>
<save/>
<path>
<move x="28.67" y="35.33"/>
<line x="28.67" y="6"/>
<line x="0" y="6"/>
<line x="0" y="35.33"/>
<close/>
<move x="28.67" y="6"/>
<line x="0" y="6"/>
<line x="7.34" y="0"/>
<line x="34.67" y="0"/>
<line x="34.67" y="0"/>
<line x="34.67" y="29.33"/>
<line x="28.67" y="35.33"/>
<close/>
<move x="28.67" y="6"/>
<line x="34.67" y="0"/>
</path>
</background>
<foreground>
<linejoin join="round"/>
<fillstroke/>
<restore/>
<rect/>
<stroke/>
<strokewidth width="2"/>
<strokecolor color="#ffffff"/>
<path>
<move x="25" y="29.33"/>
<line x="18.67" y="29.33"/>
<line x="10" y="12"/>
<line x="4.34" y="12"/>
<move x="4" y="29.33"/>
<line x="10" y="29.33"/>
<line x="18.67" y="12"/>
<line x="25.34" y="12"/>
</path>
<stroke/>
<strokewidth width="1"/>
<fillcolor color="#ffffff"/>
<path>
<move x="23" y="9"/>
<line x="23" y="15"/>
<line x="26.67" y="12"/>
<close/>
<move x="23" y="26"/>
<line x="23" y="32.33"/>
<line x="26.67" y="29.33"/>
<close/>
<move x="5.34" y="15.33"/>
<line x="5.34" y="9"/>
<line x="1.67" y="12"/>
<close/>
<move x="5.34" y="32.33"/>
<line x="5.34" y="26.33"/>
<line x="1.67" y="29.33"/>
<close/>
</path>
<fill/>
</foreground>
</shape>
<shape name="Class 4 5 Switch" h="56.67" w="43" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
<constraint x="0.1" y="0.1" perimeter="0" name="NW"/>
<constraint x="0" y="1" perimeter="0" name="SW"/>
<constraint x="1" y="0" perimeter="0" name="NE"/>
<constraint x="0.88" y="0.92" perimeter="0" name="SE"/>
</connections>
<background>
<path>
<move x="33.67" y="9.34"/>
<line x="43" y="0"/>
<line x="10.67" y="0"/>
<line x="0" y="9.34"/>
<close/>
<move x="33.67" y="56.67"/>
<line x="33.67" y="9.34"/>
<line x="0" y="9.34"/>
<line x="0" y="56.67"/>
<close/>
<move x="43" y="46.67"/>
<line x="43" y="0.34"/>
<line x="33.67" y="9.34"/>
<line x="33.67" y="56.67"/>
<close/>
</path>
</background>
<foreground>
<linejoin join="round"/>
<fillstroke/>
<strokewidth width="0.67"/>
<path>
<move x="33.67" y="31.34"/>
<line x="0" y="31.34"/>
<move x="4" y="42.67"/>
<line x="14.67" y="42.67"/>
<move x="4" y="44.67"/>
<line x="14.67" y="44.67"/>
<move x="4" y="47"/>
<line x="14.67" y="47"/>
<move x="4" y="49.34"/>
<line x="14.67" y="49.34"/>
<move x="4" y="51.67"/>
<line x="14.67" y="51.67"/>
</path>
<stroke/>
<path>
<move x="33.67" y="32"/>
<line x="0" y="32"/>
</path>
<stroke/>
</foreground>
</shape>
<shape name="Content Service Switch 1100" h="40.34" w="59" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="0.99" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
<constraint x="0.13" y="0.13" perimeter="0" name="NW"/>
<constraint x="0" y="0.99" perimeter="0" name="SW"/>
<constraint x="1" y="0" perimeter="0" name="NE"/>
<constraint x="0.91" y="0.83" perimeter="0" name="SE"/>
</connections>
<background>
<save/>
<path>
<move x="47.67" y="40.34"/>
<line x="47.67" y="11"/>
<line x="0" y="11"/>
<line x="0" y="40.34"/>
<close/>
<move x="0" y="11"/>
<line x="14.34" y="0"/>
<line x="59" y="0"/>
<line x="47.67" y="11"/>
<close/>
<move x="47.67" y="40.34"/>
<line x="59" y="27.67"/>
<line x="59" y="0"/>
<line x="47.67" y="11"/>
<close/>
</path>
</background>
<foreground>
<linejoin join="round"/>
<fillstroke/>
<fillcolor color="#ffffff"/>
<path>
<move x="18.34" y="21.34"/>
<curve x1="19.34" y1="17" x2="16.67" y2="14.67" x3="14.34" y3="15"/>
<curve x1="14" y1="15" x2="14" y2="15" x3="14" y3="15"/>
<curve x1="13.34" y1="11.67" x2="8.34" y2="12.67" x3="8.34" y3="14.34"/>
<curve x1="8.34" y1="14.34" x2="8.34" y2="14.34" x3="8.34" y3="14.34"/>
<curve x1="6" y1="14" x2="3.34" y2="18" x3="4.67" y3="20.34"/>
<curve x1="5" y1="20.34" x2="5" y2="20.34" x3="5" y3="20.34"/>
<curve x1="2.67" y1="21.34" x2="2.34" y2="24" x3="2.34" y3="26.67"/>
<curve x1="2.34" y1="29.34" x2="2.34" y2="30.67" x3="3.67" y3="31.67"/>
<curve x1="3.67" y1="32" x2="3.67" y2="32" x3="3.67" y3="32"/>
<curve x1="3.34" y1="35.67" x2="6.67" y2="37.67" x3="9" y3="36.67"/>
<curve x1="9.34" y1="37" x2="9.34" y2="37" x3="9.34" y3="37"/>
<curve x1="9.67" y1="38.34" x2="13.34" y2="38.34" x3="14" y3="36"/>
<curve x1="14.34" y1="36.34" x2="14.34" y2="36.34" x3="14.34" y3="36.34"/>
<curve x1="16.67" y1="37.34" x2="19" y2="35" x3="18.67" y3="31.67"/>
<curve x1="18.67" y1="31.34" x2="18.67" y2="31.34" x3="18.67" y3="31.34"/>
<curve x1="21.34" y1="29" x2="21" y2="22.34" x3="18.34" y3="21.67"/>
<close/>
<move x="42.67" y="21.34"/>
<curve x1="43.67" y1="17" x2="41" y2="14.67" x3="38.34" y3="15"/>
<curve x1="38.34" y1="15" x2="38.34" y2="15" x3="38.34" y3="15"/>
<curve x1="37.67" y1="11.67" x2="32.34" y2="12.67" x3="32.34" y3="14.34"/>
<curve x1="32.34" y1="14.34" x2="32.34" y2="14.34" x3="32.34" y3="14.34"/>
<curve x1="30" y1="14" x2="27.67" y2="18" x3="29" y3="20.34"/>
<curve x1="29" y1="20.34" x2="29" y2="20.34" x3="29" y3="20.34"/>
<curve x1="27" y1="21.34" x2="26.67" y2="24" x3="26.34" y3="26.67"/>
<curve x1="26.34" y1="29.34" x2="26.67" y2="30.67" x3="28" y3="31.67"/>
<curve x1="28" y1="32" x2="28" y2="32" x3="28" y3="32"/>
<curve x1="27.34" y1="35.67" x2="31" y2="37.67" x3="33.34" y3="36.67"/>
<curve x1="33.34" y1="37" x2="33.34" y2="37" x3="33.34" y3="37"/>
<curve x1="33.67" y1="38.34" x2="37.67" y2="38.34" x3="38.34" y3="36"/>
<curve x1="38.34" y1="36.34" x2="38.34" y2="36.34" x3="38.34" y3="36.34"/>
<curve x1="41" y1="37.34" x2="43.34" y2="35" x3="42.67" y3="31.67"/>
<curve x1="43" y1="31.34" x2="43" y2="31.34" x3="43" y3="31.34"/>
<curve x1="45.67" y1="29" x2="45" y2="22.34" x3="42.67" y3="21.67"/>
<close/>
</path>
<fill/>
<restore/>
<rect/>
<stroke/>
<path>
<move x="12.67" y="22"/>
<line x="10" y="19"/>
<line x="8.67" y="20.34"/>
<line x="8.34" y="16.34"/>
<line x="12.34" y="16.67"/>
<line x="11" y="18"/>
<line x="13.67" y="20.67"/>
<close/>
<move x="12.67" y="30"/>
<line x="10" y="32.67"/>
<line x="8.67" y="31.34"/>
<line x="8.34" y="35.34"/>
<line x="12.34" y="35.34"/>
<line x="11" y="33.67"/>
<line x="13.67" y="31"/>
<close/>
<move x="10" y="26.67"/>
<line x="6" y="26.67"/>
<line x="6" y="28.67"/>
<line x="3" y="26"/>
<line x="6" y="23"/>
<line x="6" y="25"/>
<line x="10" y="25"/>
<close/>
<move x="34.67" y="22"/>
<line x="37.34" y="19"/>
<line x="39" y="20.34"/>
<line x="39" y="16.34"/>
<line x="35" y="16.67"/>
<line x="36.34" y="18"/>
<line x="33.67" y="20.67"/>
<close/>
<move x="34.67" y="30"/>
<line x="37.34" y="32.67"/>
<line x="39" y="31.34"/>
<line x="39" y="35.34"/>
<line x="35" y="35.34"/>
<line x="36.34" y="33.67"/>
<line x="33.67" y="31"/>
<close/>
<move x="37" y="26.67"/>
<line x="41" y="26.67"/>
<line x="41" y="28.67"/>
<line x="44" y="26"/>
<line x="41" y="23"/>
<line x="41" y="25"/>
<line x="37" y="25"/>
<close/>
</path>
<fill/>
<save/>
<fillcolor color="#ffffff"/>
<path>
<move x="19.67" y="25.34"/>
<line x="27.34" y="25.34"/>
<line x="27.34" y="26.67"/>
<line x="19.67" y="26.67"/>
<close/>
</path>
<fill/>
<restore/>
<path>
<move x="26.34" y="26.67"/>
<line x="30.34" y="26.67"/>
<line x="30.34" y="28.67"/>
<line x="33.34" y="26"/>
<line x="30.34" y="23"/>
<line x="30.34" y="25"/>
<line x="26.34" y="25"/>
<close/>
<move x="20.34" y="26.67"/>
<line x="16.34" y="26.67"/>
<line x="16.34" y="28.67"/>
<line x="13.34" y="26"/>
<line x="16.34" y="23"/>
<line x="16.34" y="25"/>
<line x="20.34" y="25"/>
<close/>
</path>
<fill/>
</foreground>
</shape>
<shape name="Content Switch" h="54" w="40" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
<constraint x="0.12" y="0.05" perimeter="0" name="NW"/>
<constraint x="0" y="1" perimeter="0" name="SW"/>
<constraint x="1" y="0" perimeter="0" name="NE"/>
<constraint x="0.92" y="0.94" perimeter="0" name="SE"/>
</connections>
<background>
<path>
<move x="40" y="47.66"/>
<line x="40" y="0"/>
<line x="33.33" y="6.33"/>
<line x="33.33" y="54"/>
<close/>
<move x="40" y="0"/>
<line x="33.33" y="6.33"/>
<line x="0" y="6.33"/>
<line x="8.66" y="0"/>
<close/>
<move x="33.33" y="39.33"/>
<line x="33.33" y="6.33"/>
<line x="0" y="6.33"/>
<line x="0" y="39.33"/>
<close/>
<move x="33.33" y="54"/>
<line x="33.33" y="39.66"/>
<line x="0" y="39.66"/>
<line x="0" y="54"/>
<close/>
</path>
</background>
<foreground>
<linejoin join="round"/>
<fillstroke/>
<fillcolor color="#ffffff"/>
<path>
<move x="13.66" y="19"/>
<line x="8.66" y="13.66"/>
<line x="9.66" y="13"/>
<line x="6.33" y="12.33"/>
<line x="7" y="15.33"/>
<line x="7.66" y="14.66"/>
<line x="13" y="19.66"/>
<close/>
<move x="12" y="22"/>
<line x="4.66" y="22"/>
<line x="4.66" y="21"/>
<line x="2" y="22.66"/>
<line x="4.66" y="24.66"/>
<line x="4.66" y="23.33"/>
<line x="12" y="23.33"/>
<close/>
<move x="13" y="25.66"/>
<line x="7.66" y="31"/>
<line x="7" y="30"/>
<line x="6.33" y="33.33"/>
<line x="9.66" y="32.66"/>
<line x="8.66" y="31.66"/>
<line x="13.66" y="26.66"/>
<close/>
<move x="16.33" y="27.66"/>
<line x="16.33" y="34.66"/>
<line x="15" y="34.66"/>
<line x="16.66" y="37.33"/>
<line x="18.66" y="34.66"/>
<line x="17.33" y="34.66"/>
<line x="17.33" y="27.66"/>
<close/>
<move x="16.33" y="18"/>
<line x="16.33" y="11"/>
<line x="15" y="11"/>
<line x="16.66" y="8.33"/>
<line x="18.66" y="11"/>
<line x="17.33" y="11"/>
<line x="17.33" y="18"/>
<close/>
<move x="19.66" y="26.66"/>
<line x="25" y="31.66"/>
<line x="24" y="32.66"/>
<line x="27.33" y="33.33"/>
<line x="26.66" y="30"/>
<line x="25.66" y="31"/>
<line x="20.66" y="25.66"/>
<close/>
<move x="21.66" y="23.33"/>
<line x="28.66" y="23.33"/>
<line x="28.66" y="24.66"/>
<line x="31.66" y="22.66"/>
<line x="28.66" y="21"/>
<line x="28.66" y="22"/>
<line x="21.66" y="22"/>
<close/>
<move x="20.66" y="19.66"/>
<line x="25.66" y="14.66"/>
<line x="26.66" y="15.33"/>
<line x="27.33" y="12.33"/>
<line x="24" y="13"/>
<line x="25" y="13.66"/>
<line x="19.66" y="19"/>
<close/>
</path>
<fill/>
<strokecolor color="#000000"/>
<strokewidth width="0.67"/>
<path>
<move x="21" y="28.66"/>
<curve x1="24.66" y1="26.33" x2="25.33" y2="21.66" x3="23" y3="18.33"/>
<curve x1="20.66" y1="15" x2="16" y2="14.33" x3="12.66" y3="16.66"/>
<curve x1="9.33" y1="19" x2="8.33" y2="23.66" x3="10.66" y3="27"/>
<curve x1="13" y1="30.33" x2="17.66" y2="31" x3="21" y3="28.66"/>
<close/>
</path>
<fillstroke/>
<path>
<move x="17" y="47.33"/>
<line x="24.33" y="47.33"/>
<line x="24.33" y="48.66"/>
<line x="27" y="46.66"/>
<line x="24.33" y="45"/>
<line x="24.33" y="46"/>
<line x="17" y="46"/>
<close/>
<move x="17" y="45.33"/>
<line x="23.66" y="43"/>
<line x="24" y="44"/>
<line x="26" y="41.33"/>
<line x="22.66" y="40.66"/>
<line x="23.33" y="41.66"/>
<line x="16.66" y="44.33"/>
<close/>
<move x="17" y="48"/>
<line x="23.66" y="50.66"/>
<line x="24" y="49.33"/>
<line x="26" y="52"/>
<line x="22.66" y="52.66"/>
<line x="23.33" y="51.66"/>
<line x="16.66" y="49"/>
<close/>
<move x="6" y="47.33"/>
<line x="13" y="47.33"/>
<line x="13" y="48.66"/>
<line x="15.66" y="46.66"/>
<line x="13" y="45"/>
<line x="13" y="46"/>
<line x="6" y="46"/>
<close/>
</path>
<fill/>
</foreground>
</shape>
<shape name="Fibre Channel Fabric Switch" h="46.66" w="63.67" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
<constraint x="0.15" y="0.17" perimeter="0" name="NW"/>
<constraint x="0" y="1" perimeter="0" name="SW"/>
<constraint x="1" y="0" perimeter="0" name="NE"/>
<constraint x="0.89" y="0.8" perimeter="0" name="SE"/>
</connections>
<background>
<path>
<move x="47.67" y="31"/>
<line x="47.67" y="15.66"/>
<line x="0" y="15.66"/>
<line x="0" y="31"/>
<close/>
<move x="0" y="15.66"/>
<line x="19.34" y="0"/>
<line x="63.67" y="0"/>
<line x="47.67" y="15.66"/>
<close/>
<move x="47.67" y="31"/>
<line x="63.67" y="15"/>
<line x="63.67" y="0"/>
<line x="47.67" y="15.66"/>
<close/>
<move x="47.67" y="46"/>
<line x="47.67" y="31"/>
<line x="0" y="31"/>
<line x="0" y="46"/>
<close/>
<move x="47.67" y="46"/>
<line x="63.67" y="29.33"/>
<line x="63.67" y="15"/>
<line x="47.67" y="31"/>
<close/>
</path>
</background>
<foreground>
<linejoin join="round"/>
<fillstroke/>
<fillcolor color="#ffffff"/>
<path>
<move x="28" y="11.66"/>
<line x="27" y="12.33"/>
<line x="15" y="12.33"/>
<line x="14" y="13.33"/>
<line x="10.67" y="12"/>
<line x="17.34" y="10.33"/>
<line x="16" y="11.66"/>
<close/>
<move x="37.34" y="5.66"/>
<line x="36.34" y="6.33"/>
<line x="24.34" y="6.33"/>
<line x="23.34" y="7.66"/>
<line x="20" y="6.33"/>
<line x="26.67" y="4.33"/>
<line x="25.34" y="5.66"/>
<close/>
<move x="30" y="9.33"/>
<line x="31" y="8.66"/>
<line x="42.67" y="8.66"/>
<line x="44" y="7.66"/>
<line x="47.34" y="9"/>
<line x="40.67" y="10.66"/>
<line x="42" y="9.33"/>
<close/>
<move x="36.34" y="3.66"/>
<line x="37" y="3"/>
<line x="49" y="3"/>
<line x="50.34" y="1.66"/>
<line x="53.34" y="3"/>
<line x="47" y="5"/>
<line x="48" y="3.66"/>
<close/>
</path>
<fill/>
<strokewidth width="1"/>
<fillcolor color="#ffffff"/>
<path>
<move x="18.34" y="22.66"/>
<line x="9.34" y="22.66"/>
<line x="9.34" y="22"/>
<line x="6" y="23.33"/>
<line x="9.34" y="24.33"/>
<line x="9.34" y="23.66"/>
<line x="18.34" y="23.66"/>
<close/>
<move x="23.34" y="25.33"/>
<line x="23.34" y="28.66"/>
<line x="21" y="28.66"/>
<line x="24.34" y="29.66"/>
<line x="28" y="28.66"/>
<line x="25.67" y="28.66"/>
<line x="25.67" y="25.33"/>
<close/>
<move x="23.34" y="21"/>
<line x="23.34" y="18"/>
<line x="21" y="18"/>
<line x="24.34" y="16.66"/>
<line x="28" y="18"/>
<line x="25.67" y="18"/>
<line x="25.67" y="21"/>
<close/>
<move x="30.67" y="23.66"/>
<line x="39.34" y="23.66"/>
<line x="39.34" y="24.33"/>
<line x="43" y="23.33"/>
<line x="39.34" y="22"/>
<line x="39.34" y="22.66"/>
<line x="30.67" y="22.66"/>
<close/>
</path>
<fill/>
<strokewidth width="0.67"/>
<strokecolor color="#ffffff"/>
<path>
<move x="38.67" y="28.33"/>
<curve x1="37" y1="29" x2="29.34" y2="27.33" x3="21.67" y3="24.33"/>
<curve x1="13.67" y1="21.66" x2="9" y2="19" x3="10.67" y3="18.33"/>
<curve x1="12.34" y1="17.66" x2="20" y2="19.33" x3="27.67" y3="22.33"/>
<curve x1="35.34" y1="25" x2="40.34" y2="27.66" x3="38.67" y3="28.33"/>
<close/>
</path>
<stroke/>
<path>
<move x="38.34" y="18.33"/>
<curve x1="40" y1="19" x2="35.34" y2="21.66" x3="27.34" y3="24.33"/>
<curve x1="19.67" y1="27" x2="12" y2="29" x3="10.34" y3="28.33"/>
<curve x1="8.34" y1="27.66" x2="13.34" y2="25" x3="21" y3="22.33"/>
<curve x1="29" y1="19.33" x2="36.67" y2="17.66" x3="38.34" y3="18.33"/>
<close/>
</path>
<stroke/>
<fillcolor color="#b5b5b5"/>
<path>
<move x="28" y="25"/>
<curve x1="31" y1="24.33" x2="31.67" y2="23" x3="29.67" y3="22"/>
<curve x1="27.67" y1="20.66" x2="23.67" y2="20.66" x3="20.67" y3="21.33"/>
<curve x1="17.67" y1="22" x2="17" y2="23.33" x3="19" y3="24.33"/>
<curve x1="21" y1="25.66" x2="25" y2="25.66" x3="28" y3="25"/>
<close/>
</path>
<fill/>
</foreground>
</shape>
<shape name="Generic Softswitch" h="47.34" w="31" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
<constraint x="0.12" y="0.05" perimeter="0" name="NW"/>
<constraint x="0" y="1" perimeter="0" name="SW"/>
<constraint x="1" y="0" perimeter="0" name="NE"/>
<constraint x="0.91" y="0.92" perimeter="0" name="SE"/>
</connections>
<background>
<path>
<move x="0" y="47.34"/>
<line x="0" y="5"/>
<line x="24.67" y="5"/>
<line x="24.67" y="47.34"/>
<close/>
<move x="7.34" y="0"/>
<line x="0" y="5"/>
<line x="24.67" y="5"/>
<line x="31" y="0"/>
<close/>
<move x="31" y="40.67"/>
<line x="31" y="0"/>
<line x="24.67" y="5"/>
<line x="24.67" y="47.34"/>
<close/>
</path>
</background>
<foreground>
<linejoin join="round"/>
<fillstroke/>
<fillcolor color="#ffffff"/>
<path>
<move x="9.67" y="12.67"/>
<line x="20.67" y="12.67"/>
<line x="20.67" y="14"/>
<line x="9.67" y="14"/>
<line x="9.67" y="16"/>
<line x="4.67" y="13.34"/>
<line x="9.67" y="10.34"/>
<close/>
<move x="16.67" y="24.34"/>
<line x="5.67" y="24.34"/>
<line x="5.67" y="22.67"/>
<line x="16.67" y="22.67"/>
<line x="16.67" y="20.67"/>
<line x="21.67" y="23.34"/>
<line x="16.67" y="26.34"/>
<close/>
</path>
<fill/>
</foreground>
</shape>
<shape name="Intelliswitch Stack" h="43.99" w="41.67" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
<constraint x="0.15" y="0.09" perimeter="0" name="NW"/>
<constraint x="0" y="1" perimeter="0" name="SW"/>
<constraint x="1" y="0" perimeter="0" name="NE"/>
<constraint x="0.89" y="0.88" perimeter="0" name="SE"/>
</connections>
<background>
</background>
<foreground>
<rect/>
<stroke/>
<linejoin join="round"/>
<save/>
<path>
<move x="31.33" y="43.99"/>
<line x="31.33" y="34"/>
<line x="0" y="34"/>
<line x="0" y="43.99"/>
<close/>
</path>
<fillstroke/>
<fillcolor color="#000000"/>
<path>
<move x="41.67" y="24.66"/>
<line x="31.33" y="34"/>
<line x="0" y="34"/>
<line x="11.33" y="24.66"/>
<close/>
</path>
<fillstroke/>
<restore/>
<save/>
<path>
<move x="41.67" y="34.33"/>
<line x="41.67" y="24.66"/>
<line x="31.33" y="34"/>
<line x="31.33" y="43.99"/>
<close/>
<move x="31.33" y="31.66"/>
<line x="31.33" y="21.66"/>
<line x="0" y="21.66"/>
<line x="0" y="31.66"/>
<close/>
</path>
<fillstroke/>
<fillcolor color="#000000"/>
<path>
<move x="41.67" y="12"/>
<line x="31.33" y="21.33"/>
<line x="0" y="21.33"/>
<line x="11.33" y="12"/>
<close/>
</path>
<fillstroke/>
<restore/>
<path>
<move x="41.67" y="22.67"/>
<line x="41.67" y="12"/>
<line x="31.33" y="21.33"/>
<line x="31.33" y="31.67"/>
<close/>
<move x="31.33" y="19.33"/>
<line x="31.33" y="9.33"/>
<line x="0" y="9.33"/>
<line x="0" y="19.33"/>
<close/>
<move x="41.67" y="0"/>
<line x="31.33" y="9.33"/>
<line x="0" y="9.33"/>
<line x="11.33" y="0"/>
<close/>
<move x="41.67" y="10"/>
<line x="41.67" y="0"/>
<line x="31.33" y="9.33"/>
<line x="31.33" y="19.33"/>
<close/>
</path>
<fillstroke/>
<fillcolor color="#000000"/>
<path>
<move x="25.67" y="5.33"/>
<line x="27" y="4.33"/>
<line x="30.67" y="5.33"/>
<line x="23.33" y="7"/>
<line x="24.67" y="5.66"/>
<line x="17.67" y="5.66"/>
<line x="18.67" y="5.33"/>
<close/>
<move x="22.33" y="2.66"/>
<line x="21.67" y="3.33"/>
<line x="15.67" y="3.33"/>
<line x="14.67" y="4"/>
<line x="11" y="2.66"/>
<line x="18" y="1.33"/>
<line x="16.33" y="2.66"/>
<close/>
<move x="17" y="6.66"/>
<line x="16.33" y="7.33"/>
<line x="10.33" y="7.33"/>
<line x="9.33" y="8"/>
<line x="5.67" y="7"/>
<line x="13" y="5.33"/>
<line x="11.33" y="6.66"/>
<close/>
<move x="31.33" y="1.66"/>
<line x="32.67" y="0.66"/>
<line x="36.33" y="1.66"/>
<line x="29" y="3.33"/>
<line x="30.33" y="2.33"/>
<line x="23.33" y="2.33"/>
<line x="24.33" y="1.66"/>
<close/>
</path>
<fill/>
<fillcolor color="#ffffff"/>
<path>
<move x="25.67" y="5.66"/>
<line x="27" y="4.66"/>
<line x="30.67" y="5.66"/>
<line x="23.33" y="7.33"/>
<line x="24.67" y="6"/>
<line x="17.67" y="6"/>
<line x="18.67" y="5.66"/>
<close/>
<move x="22.33" y="3"/>
<line x="21.67" y="3.66"/>
<line x="15.67" y="3.66"/>
<line x="14.67" y="4.33"/>
<line x="11" y="3"/>
<line x="18" y="1.66"/>
<line x="16.33" y="3"/>
<close/>
<move x="17" y="7"/>
<line x="16.33" y="7.66"/>
<line x="10.33" y="7.66"/>
<line x="9.33" y="8.33"/>
<line x="5.67" y="7.33"/>
<line x="13" y="5.66"/>
<line x="11.33" y="7"/>
<close/>
<move x="31.33" y="2"/>
<line x="32.67" y="1"/>
<line x="36.33" y="2"/>
<line x="29" y="3.66"/>
<line x="30.33" y="2.66"/>
<line x="23.33" y="2.66"/>
<line x="24.33" y="2"/>
<close/>
<move x="17" y="20.66"/>
<line x="17" y="13.66"/>
<line x="18" y="13.66"/>
<line x="16.33" y="11"/>
<line x="14.67" y="13.66"/>
<line x="15.67" y="13.66"/>
<line x="15.67" y="20.66"/>
<close/>
<move x="13.33" y="23"/>
<line x="8.33" y="18"/>
<line x="9.33" y="17.33"/>
<line x="6.33" y="16.66"/>
<line x="6.67" y="19.66"/>
<line x="7.67" y="19"/>
<line x="12.67" y="23.66"/>
<close/>
<move x="11.67" y="26.33"/>
<line x="4.67" y="26.33"/>
<line x="4.67" y="25"/>
<line x="2" y="26.66"/>
<line x="4.67" y="28.66"/>
<line x="4.67" y="27.33"/>
<line x="11.67" y="27.33"/>
<close/>
<move x="12.67" y="29.66"/>
<line x="7.67" y="34.66"/>
<line x="6.67" y="33.66"/>
<line x="6.33" y="37"/>
<line x="9.33" y="36.33"/>
<line x="8.33" y="35.33"/>
<line x="13.33" y="30.66"/>
<close/>
<move x="15.67" y="31.66"/>
<line x="15.67" y="38.33"/>
<line x="14.67" y="38.33"/>
<line x="16.33" y="41"/>
<line x="18" y="38.33"/>
<line x="17" y="38.33"/>
<line x="17" y="31.66"/>
<close/>
<move x="19.33" y="30.66"/>
<line x="24.33" y="35.33"/>
<line x="23.33" y="36.33"/>
<line x="26.33" y="37"/>
<line x="25.67" y="33.66"/>
<line x="25" y="34.66"/>
<line x="20" y="29.66"/>
<close/>
<move x="21" y="27.33"/>
<line x="28" y="27.33"/>
<line x="28" y="28.66"/>
<line x="30.67" y="26.66"/>
<line x="28" y="25"/>
<line x="28" y="26.33"/>
<line x="21" y="26.33"/>
<close/>
<move x="20" y="23.66"/>
<line x="25" y="19"/>
<line x="25.67" y="19.66"/>
<line x="26.33" y="16.66"/>
<line x="23.33" y="17.33"/>
<line x="24.33" y="18"/>
<line x="19.33" y="23"/>
<close/>
</path>
<fill/>
<strokecolor color="#000000"/>
<path>
<move x="19.33" y="31"/>
<curve x1="21.67" y1="29.33" x2="22.33" y2="26.33" x3="20.67" y3="24"/>
<curve x1="19" y1="21.66" x2="15.67" y2="21" x3="13.67" y3="22.66"/>
<curve x1="11.33" y1="24.33" x2="10.67" y2="27.33" x3="12.33" y3="29.66"/>
<curve x1="14" y1="32" x2="17" y2="32.66" x3="19.33" y3="31"/>
<close/>
</path>
<fill/>
<strokecolor color="#ffffff"/>
<strokewidth width="1.33"/>
<path>
<move x="40" y="22.66"/>
<curve x1="39.67" y1="29" x2="37.67" y2="34" x3="35.67" y3="34"/>
<curve x1="34" y1="34" x2="32.67" y2="28.33" x3="33.33" y3="22"/>
<curve x1="33.67" y1="15.33" x2="35.67" y2="10.33" x3="37.67" y3="10.33"/>
<curve x1="39.33" y1="10.66" x2="40.67" y2="16" x3="40" y3="22.66"/>
<close/>
</path>
<stroke/>
</foreground>
</shape>
<shape name="ISDN Switch" h="37" w="36.33" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
<constraint x="0.09" y="0.08" perimeter="0" name="NW"/>
<constraint x="0" y="1" perimeter="0" name="SW"/>
<constraint x="1" y="0" perimeter="0" name="NE"/>
<constraint x="0.92" y="0.89" perimeter="0" name="SE"/>
</connections>
<background>
<path>
<move x="30.33" y="37"/>
<line x="30.33" y="6"/>
<line x="0" y="6"/>
<line x="0" y="37"/>
<close/>
<move x="36.33" y="0"/>
<line x="30.33" y="6"/>
<line x="0" y="6"/>
<line x="6.67" y="0"/>
<line x="36.33" y="0"/>
<close/>
<move x="36.33" y="29.34"/>
<line x="36.33" y="0"/>
<line x="30.33" y="6"/>
<line x="30.33" y="37"/>
<line x="36.33" y="29.34"/>
<close/>
</path>
</background>
<foreground>
<linejoin join="round"/>
<fillstroke/>
<fillcolor color="#ffffff"/>
<path>
<move x="4.67" y="16.67"/>
<line x="9.33" y="12"/>
<line x="4.33" y="11.67"/>
<close/>
<move x="24.67" y="27"/>
<line x="20" y="31.67"/>
<line x="25" y="32"/>
<close/>
</path>
<fill/>
<strokecolor color="#ffffff"/>
<strokewidth width="2"/>
<dashed dashed="1"/>
<dashpattern dash="12 4"/>
<path>
<move x="6.67" y="14"/>
<line x="22.67" y="29.67"/>
</path>
<stroke/>
</foreground>
</shape>
<shape name="Layer 2 Remote Switch" h="31" w="63.67" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
<constraint x="0" y="0" perimeter="0" name="NW"/>
<constraint x="0" y="1" perimeter="0" name="SW"/>
<constraint x="1" y="0" perimeter="0" name="NE"/>
<constraint x="1" y="1" perimeter="0" name="SE"/>
</connections>
<background>
<save/>
<path>
<move x="0" y="15.34"/>
<line x="19" y="0"/>
<line x="63.67" y="0"/>
<line x="47.67" y="15.34"/>
<close/>
</path>
</background>
<foreground>
<linejoin join="round"/>
<fillstroke/>
<restore/>
<rect/>
<stroke/>
<save/>
<save/>
<fillcolor color="#ffffff"/>
<path>
<move x="43.67" y="8.34"/>
<line x="44.67" y="7.34"/>
<line x="48" y="8.67"/>
<line x="41.67" y="10.67"/>
<line x="43.34" y="9"/>
<line x="31.34" y="9"/>
<line x="31.67" y="8.34"/>
<close/>
<move x="36" y="4.67"/>
<line x="35.67" y="5"/>
<line x="23.67" y="5"/>
<line x="22.67" y="6"/>
<line x="19.34" y="4.67"/>
<line x="25.67" y="3"/>
<line x="24" y="4.67"/>
<close/>
</path>
<fill/>
<restore/>
<linejoin join="round"/>
<path>
<move x="47.67" y="31"/>
<line x="63.67" y="13.67"/>
<line x="63.67" y="0"/>
<line x="47.67" y="15.34"/>
<close/>
</path>
<fillstroke/>
<fillcolor color="#ffffff"/>
<path>
<move x="29.34" y="11.34"/>
<line x="29" y="11.67"/>
<line x="17" y="11.67"/>
<line x="16" y="12.67"/>
<line x="12.67" y="11.34"/>
<line x="19" y="9.67"/>
<line x="17.34" y="11.34"/>
<close/>
<move x="50.34" y="3"/>
<line x="51.67" y="1.67"/>
<line x="54.67" y="3"/>
<line x="48.34" y="5"/>
<line x="50" y="3.34"/>
<line x="38" y="3.34"/>
<line x="38.34" y="3"/>
<close/>
</path>
<fill/>
<restore/>
<path>
<move x="47.67" y="31"/>
<line x="47.67" y="15.34"/>
<line x="0" y="15.34"/>
<line x="0" y="31"/>
<close/>
</path>
<fillstroke/>
<fillcolor color="#ffffff"/>
<path>
<move x="12.67" y="21.34"/>
<line x="12.67" y="18.34"/>
<line x="13.34" y="18.34"/>
<line x="12.34" y="17"/>
<line x="11.67" y="18.34"/>
<line x="12" y="18.34"/>
<line x="12" y="21.34"/>
<close/>
<move x="11" y="21.67"/>
<line x="9" y="19.67"/>
<line x="9.34" y="19.34"/>
<line x="8" y="19"/>
<line x="8" y="20.34"/>
<line x="8.67" y="20"/>
<line x="10.67" y="22"/>
<close/>
<move x="10.34" y="23"/>
<line x="7.34" y="23"/>
<line x="7.34" y="22.67"/>
<line x="6" y="23.34"/>
<line x="7.34" y="24.34"/>
<line x="7.34" y="23.67"/>
<line x="10.34" y="23.67"/>
<close/>
<move x="10.67" y="24.67"/>
<line x="8.67" y="27"/>
<line x="8" y="26.67"/>
<line x="8" y="28"/>
<line x="9.34" y="27.67"/>
<line x="9" y="27.34"/>
<line x="11" y="25"/>
<close/>
<move x="12" y="25.67"/>
<line x="12" y="28.67"/>
<line x="11.67" y="28.67"/>
<line x="12.34" y="29.67"/>
<line x="13.34" y="28.67"/>
<line x="12.67" y="28.67"/>
<line x="12.67" y="25.67"/>
<close/>
<move x="14" y="22"/>
<line x="16.34" y="20"/>
<line x="16.67" y="20.34"/>
<line x="17" y="19"/>
<line x="15.67" y="19.34"/>
<line x="16" y="19.67"/>
<line x="13.67" y="21.67"/>
<close/>
</path>
<fill/>
<path>
<move x="14.34" y="26"/>
<curve x1="15.67" y1="25" x2="16" y2="23" x3="15" y3="21.67"/>
<curve x1="14" y1="20" x2="12" y2="19.67" x3="10.67" y3="20.67"/>
<curve x1="9.34" y1="21.67" x2="8.67" y2="23.67" x3="10" y3="25.34"/>
<curve x1="11" y1="26.67" x2="13" y2="27" x3="14.34" y3="26"/>
<close/>
</path>
<fill/>
<path>
<move x="14" y="25"/>
<line x="16.34" y="27.34"/>
<line x="16" y="27.67"/>
<line x="17.34" y="28"/>
<line x="17" y="26.67"/>
<line x="16.67" y="27"/>
<line x="14.34" y="24.67"/>
<close/>
</path>
<fill/>
<strokecolor color="#ffffff"/>
<strokewidth width="1.33"/>
<path>
<move x="14.34" y="23.34"/>
<line x="34.67" y="23.34"/>
</path>
<stroke/>
<fillcolor color="#ffffff"/>
<path>
<move x="36" y="24.67"/>
<line x="36" y="27"/>
<line x="35.67" y="27"/>
<line x="36" y="27.67"/>
<line x="36.67" y="27"/>
<line x="36.34" y="27"/>
<line x="36.34" y="24.67"/>
<close/>
<move x="37" y="24.67"/>
<line x="38.67" y="26"/>
<line x="38.34" y="26.34"/>
<line x="39.34" y="26.34"/>
<line x="39" y="25.67"/>
<line x="38.67" y="25.67"/>
<line x="37.34" y="24.34"/>
<close/>
<move x="37.67" y="23.34"/>
<line x="39.67" y="23.34"/>
<line x="39.67" y="24"/>
<line x="40.67" y="23.34"/>
<line x="39.67" y="22.67"/>
<line x="39.67" y="23"/>
<line x="37.67" y="23"/>
<close/>
<move x="37.34" y="22.34"/>
<line x="38.67" y="21"/>
<line x="39" y="21"/>
<line x="39.34" y="20"/>
<line x="38.34" y="20.34"/>
<line x="38.67" y="20.67"/>
<line x="37" y="22"/>
<close/>
<move x="36.34" y="22"/>
<line x="36.34" y="19.67"/>
<line x="36.67" y="19.67"/>
<line x="36" y="19"/>
<line x="35.67" y="19.67"/>
<line x="36" y="19.67"/>
<line x="36" y="22"/>
<close/>
<move x="35" y="24.34"/>
<line x="33.34" y="25.67"/>
<line x="33" y="25.67"/>
<line x="33" y="26.34"/>
<line x="34" y="26.34"/>
<line x="33.67" y="26"/>
<line x="35.34" y="24.67"/>
<close/>
</path>
<fill/>
<path>
<move x="34.67" y="21.67"/>
<curve x1="33.67" y1="22.34" x2="33.67" y2="23.67" x3="34.34" y3="24.67"/>
<curve x1="35" y1="25.67" x2="36.34" y2="26" x3="37.34" y3="25.34"/>
<curve x1="38.34" y1="24.34" x2="38.67" y2="23" x3="38" y3="22"/>
<curve x1="37.34" y1="21" x2="35.67" y2="20.67" x3="34.67" y3="21.67"/>
<close/>
</path>
<fill/>
<path>
<move x="35" y="22"/>
<line x="33.34" y="20.67"/>
<line x="33.67" y="20.34"/>
<line x="32.67" y="20"/>
<line x="33" y="21"/>
<line x="33.34" y="21"/>
<line x="34.67" y="22.34"/>
<close/>
</path>
<fill/>
</foreground>
</shape>
<shape name="Layer 3 Switch" h="40" w="40.33" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
<constraint x="0.08" y="0.05" perimeter="0" name="NW"/>
<constraint x="0" y="1" perimeter="0" name="SW"/>
<constraint x="1" y="0" perimeter="0" name="NE"/>
<constraint x="0.95" y="0.94" perimeter="0" name="SE"/>
</connections>
<background>
<save/>
<path>
<move x="0" y="40"/>
<line x="0" y="4.67"/>
<line x="35.67" y="4.67"/>
<line x="35.67" y="40"/>
<close/>
<move x="40.33" y="35.67"/>
<line x="40.33" y="0"/>
<line x="35.67" y="4.67"/>
<line x="35.67" y="40"/>
<close/>
<move x="6.33" y="0"/>
<line x="0" y="4.67"/>
<line x="35.67" y="4.67"/>
<line x="40.33" y="0"/>
<close/>
</path>
</background>
<foreground>
<linejoin join="round"/>
<fillstroke/>
<restore/>
<fillcolor color="#ffffff"/>
<path>
<move x="18.67" y="17"/>
<line x="18.67" y="9.34"/>
<line x="20" y="9.34"/>
<line x="18" y="6.67"/>
<line x="16.33" y="9.34"/>
<line x="17.33" y="9.34"/>
<line x="17.33" y="17"/>
<close/>
<move x="14.67" y="18.34"/>
<line x="9.33" y="12.67"/>
<line x="10.33" y="12"/>
<line x="7" y="11.34"/>
<line x="7.67" y="14.67"/>
<line x="8.33" y="13.67"/>
<line x="14" y="19"/>
<close/>
<move x="12.67" y="22"/>
<line x="5" y="22"/>
<line x="5" y="20.67"/>
<line x="2.33" y="22.34"/>
<line x="5" y="24.34"/>
<line x="5" y="23"/>
<line x="12.67" y="23"/>
<close/>
<move x="14" y="25.67"/>
<line x="8.33" y="31.34"/>
<line x="7.67" y="30.34"/>
<line x="7" y="33.67"/>
<line x="10.33" y="33"/>
<line x="9.33" y="32"/>
<line x="14.67" y="26.67"/>
<close/>
<move x="17.33" y="27.67"/>
<line x="17.33" y="35.34"/>
<line x="16.33" y="35.34"/>
<line x="18" y="38.34"/>
<line x="20" y="35.34"/>
<line x="18.67" y="35.34"/>
<line x="18.67" y="27.67"/>
<close/>
<move x="21.33" y="26.67"/>
<line x="27" y="32"/>
<line x="26" y="33"/>
<line x="29.33" y="33.67"/>
<line x="28.67" y="30.34"/>
<line x="27.67" y="31.34"/>
<line x="22.33" y="25.67"/>
<close/>
<move x="23.33" y="23"/>
<line x="31" y="23"/>
<line x="31" y="24.34"/>
<line x="34" y="22.34"/>
<line x="31" y="20.67"/>
<line x="31" y="22"/>
<line x="23.33" y="22"/>
<close/>
<move x="22.33" y="19"/>
<line x="27.67" y="13.67"/>
<line x="28.67" y="14.67"/>
<line x="29.33" y="11.34"/>
<line x="26" y="12"/>
<line x="27" y="12.67"/>
<line x="21.33" y="18.34"/>
<close/>
</path>
<fill/>
<strokecolor color="#036c9b"/>
<strokewidth width="0.67"/>
<path>
<move x="22.67" y="29"/>
<curve x1="26.33" y1="26.34" x2="27.33" y2="21.34" x3="24.67" y3="17.67"/>
<curve x1="22.33" y1="14" x2="17.33" y2="13.34" x3="13.67" y3="15.67"/>
<curve x1="10" y1="18.34" x2="9" y2="23.34" x3="11.67" y3="27"/>
<curve x1="14.33" y1="30.67" x2="19.33" y2="31.67" x3="22.67" y3="29"/>
<close/>
</path>
<fillstroke/>
</foreground>
</shape>
<shape name="MGX 8000 Multiservice Switch" h="49.33" w="49" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
<constraint x="0.12" y="0.07" perimeter="0" name="NW"/>
<constraint x="0.12" y="0.93" perimeter="0" name="SW"/>
<constraint x="0.88" y="0.07" perimeter="0" name="NE"/>
<constraint x="0.88" y="0.93" perimeter="0" name="SE"/>
</connections>
<background>
<save/>
<path>
<move x="49" y="9.67"/>
<curve x1="49" y1="15" x2="38" y2="19" x3="24.67" y3="19"/>
<curve x1="11" y1="19" x2="0" y2="15" x3="0" y3="9.67"/>
<curve x1="0" y1="40" x2="0" y2="40" x3="0" y3="40"/>
<curve x1="0" y1="45" x2="11" y2="49.33" x3="24.67" y3="49.33"/>
<curve x1="38" y1="49.33" x2="49" y2="45" x3="49" y3="40"/>
<close/>
<move x="24.67" y="19"/>
<curve x1="38" y1="19" x2="49" y2="15" x3="49" y3="9.67"/>
<curve x1="49" y1="4.33" x2="38" y2="0" x3="24.67" y3="0"/>
<curve x1="11" y1="0" x2="0" y2="4.33" x3="0" y3="9.67"/>
<curve x1="0" y1="15" x2="11" y2="19" x3="24.67" y3="19"/>
<close/>
</path>
</background>
<foreground>
<linejoin join="round"/>
<fillstroke/>
<restore/>
<rect/>
<stroke/>
<fillcolor color="#ffffff"/>
<path>
<move x="26" y="4.33"/>
<line x="33.67" y="2"/>
<line x="33.67" y="5.33"/>
<line x="31.67" y="5"/>
<line x="28" y="8.33"/>
<line x="24.34" y="7.67"/>
<line x="28.34" y="4.67"/>
<close/>
<move x="29.67" y="13.33"/>
<line x="28.34" y="10"/>
<line x="35.34" y="8.67"/>
<line x="34" y="9.67"/>
<line x="45.67" y="11.67"/>
<line x="42.67" y="14"/>
<line x="31.34" y="12"/>
<close/>
<move x="22.67" y="16"/>
<line x="15.34" y="17.33"/>
<line x="15" y="14"/>
<line x="17" y="14.67"/>
<line x="21" y="11"/>
<line x="24.67" y="11.67"/>
<line x="20.34" y="15.33"/>
<close/>
<move x="19" y="6"/>
<line x="21" y="9"/>
<line x="13.34" y="10.67"/>
<line x="15" y="9.33"/>
<line x="3" y="7.33"/>
<line x="6" y="5"/>
<line x="17.67" y="7"/>
<close/>
</path>
<fill/>
<strokecolor color="#000000"/>
<strokewidth width="2"/>
<path>
<move x="14" y="42"/>
<line x="21" y="42"/>
<line x="29.67" y="24"/>
<line x="35.34" y="24"/>
</path>
<stroke/>
<fillcolor color="#000000"/>
<path>
<move x="33.34" y="20.67"/>
<line x="33.34" y="27"/>
<line x="37.34" y="24"/>
<close/>
<move x="15" y="45.33"/>
<line x="15" y="38.67"/>
<line x="11.34" y="42"/>
<close/>
</path>
<fill/>
<fillcolor color="#ffffff"/>
<path>
<move x="15" y="45.67"/>
<line x="15" y="39.33"/>
<line x="11.34" y="42.33"/>
<close/>
<move x="33.67" y="21"/>
<line x="33.67" y="27.33"/>
<line x="37.34" y="24.33"/>
<close/>
</path>
<fill/>
<fillcolor color="#000000"/>
<path>
<move x="15" y="27.33"/>
<line x="15" y="20.67"/>
<line x="11.34" y="24"/>
<close/>
</path>
<fill/>
<fillcolor color="#ffffff"/>
<path>
<move x="15" y="27.67"/>
<line x="15" y="21.33"/>
<line x="11.34" y="24.33"/>
<close/>
</path>
<fill/>
<strokecolor color="#000000"/>
<path>
<move x="35.34" y="42"/>
<line x="29.34" y="42"/>
<line x="20.34" y="24"/>
<line x="14.67" y="24"/>
</path>
<stroke/>
<strokecolor color="#ffffff"/>
<path>
<move x="36" y="42.33"/>
<line x="29.67" y="42.33"/>
<line x="20.67" y="24.33"/>
<line x="13.67" y="24.33"/>
<move x="13.67" y="42.33"/>
<line x="20" y="42.33"/>
<line x="29" y="24.33"/>
<line x="36" y="24.33"/>
</path>
<stroke/>
<fillcolor color="#000000"/>
<path>
<move x="33.34" y="38.67"/>
<line x="33.34" y="45"/>
<line x="37.34" y="42"/>
<close/>
</path>
<fill/>
<fillcolor color="#ffffff"/>
<path>
<move x="33.67" y="39"/>
<line x="33.67" y="45.67"/>
<line x="37.34" y="42.33"/>
<close/>
</path>
<fill/>
</foreground>
</shape>
<shape name="Multi-Fabric Server Switch" h="47.67" w="35" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
<constraint x="0.11" y="0.06" perimeter="0" name="NW"/>
<constraint x="0" y="1" perimeter="0" name="SW"/>
<constraint x="1" y="0" perimeter="0" name="NE"/>
<constraint x="0.92" y="0.92" perimeter="0" name="SE"/>
</connections>
<background>
<path>
<move x="29" y="47.67"/>
<line x="29" y="35.33"/>
<line x="0" y="35.33"/>
<line x="0" y="47.67"/>
<close/>
<move x="17.67" y="35.33"/>
<line x="17.67" y="47.67"/>
<move x="29" y="35.33"/>
<line x="29" y="6"/>
<line x="0" y="6"/>
<line x="0" y="35.33"/>
<close/>
<move x="35" y="0"/>
<line x="35" y="41.33"/>
<line x="29" y="47.67"/>
<line x="29" y="6"/>
<close/>
<move x="29" y="6"/>
<line x="0" y="6"/>
<line x="7.33" y="0"/>
<line x="35" y="0"/>
<close/>
</path>
</background>
<foreground>
<linejoin join="round"/>
<fillstroke/>
<strokecolor color="#ffffff"/>
<path>
<move x="29" y="35.67"/>
<line x="29" y="35.67"/>
<move x="0" y="35.67"/>
<line x="0" y="35.67"/>
<move x="4" y="13.33"/>
<line x="27.33" y="13.33"/>
<move x="3" y="20.67"/>
<line x="26.33" y="20.67"/>
<move x="2" y="27.33"/>
<line x="25.33" y="27.33"/>
<move x="5.33" y="31.33"/>
<line x="10" y="9.33"/>
<move x="20" y="31.33"/>
<line x="25" y="9.33"/>
<move x="12.67" y="31"/>
<line x="17.33" y="9"/>
</path>
<stroke/>
<fillcolor color="#ffffff"/>
<path>
<move x="11" y="13.33"/>
<curve x1="11" y1="14" x2="10" y2="14.67" x3="9" y3="14.67"/>
<curve x1="8" y1="14.67" x2="7.33" y2="14" x3="7.33" y3="13.33"/>
<curve x1="7.33" y1="12.67" x2="8" y2="12.33" x3="9" y3="12.33"/>
<curve x1="10" y1="12.33" x2="11" y2="12.67" x3="11" y3="13.33"/>
<close/>
<move x="18.33" y="13"/>
<curve x1="18.33" y1="13.67" x2="17.67" y2="14.33" x3="16.67" y3="14.33"/>
<curve x1="15.67" y1="14.33" x2="14.67" y2="13.67" x3="14.67" y3="13"/>
<curve x1="14.67" y1="12.33" x2="15.67" y2="12" x3="16.67" y3="12"/>
<curve x1="17.67" y1="12" x2="18.33" y2="12.33" x3="18.33" y3="13"/>
<close/>
<move x="26" y="13.67"/>
<curve x1="26" y1="14.33" x2="25" y2="14.67" x3="24" y3="14.67"/>
<curve x1="23" y1="14.67" x2="22.33" y2="14.33" x3="22.33" y3="13.67"/>
<curve x1="22.33" y1="13" x2="23" y2="12.33" x3="24" y3="12.33"/>
<curve x1="25" y1="12.33" x2="26" y2="13" x3="26" y3="13.67"/>
<close/>
<move x="9" y="20.67"/>
<curve x1="9" y1="21.33" x2="8.33" y2="21.67" x3="7.33" y3="21.67"/>
<curve x1="6.33" y1="21.67" x2="5.67" y2="21.33" x3="5.67" y3="20.67"/>
<curve x1="5.67" y1="20" x2="6.33" y2="19.33" x3="7.33" y3="19.33"/>
<curve x1="8.33" y1="19.33" x2="9" y2="20" x3="9" y3="20.67"/>
<close/>
<move x="16.67" y="20.67"/>
<curve x1="16.67" y1="21.33" x2="16" y2="22" x3="15" y3="22"/>
<curve x1="14" y1="22" x2="13" y2="21.33" x3="13" y3="20.67"/>
<curve x1="13" y1="20" x2="14" y2="19.67" x3="15" y3="19.67"/>
<curve x1="16" y1="19.67" x2="16.67" y2="20" x3="16.67" y3="20.67"/>
<close/>
<move x="24.33" y="20.33"/>
<curve x1="24.33" y1="21" x2="23.33" y2="21.67" x3="22.33" y3="21.67"/>
<curve x1="21.33" y1="21.67" x2="20.67" y2="21" x3="20.67" y3="20.33"/>
<curve x1="20.67" y1="19.67" x2="21.33" y2="19.33" x3="22.33" y3="19.33"/>
<curve x1="23.33" y1="19.33" x2="24.33" y2="19.67" x3="24.33" y3="20.33"/>
<close/>
<move x="7.67" y="27.33"/>
<curve x1="7.67" y1="28" x2="6.67" y2="28.67" x3="5.67" y3="28.67"/>
<curve x1="4.67" y1="28.67" x2="4" y2="28" x3="4" y3="27.33"/>
<curve x1="4" y1="26.67" x2="4.67" y2="26.33" x3="5.67" y3="26.33"/>
<curve x1="6.67" y1="26.33" x2="7.67" y2="26.67" x3="7.67" y3="27.33"/>
<close/>
<move x="15.33" y="27.33"/>
<curve x1="15.33" y1="28" x2="14.33" y2="28.67" x3="13.33" y3="28.67"/>
<curve x1="12.33" y1="28.67" x2="11.67" y2="28" x3="11.67" y3="27.33"/>
<curve x1="11.67" y1="26.67" x2="12.33" y2="26.33" x3="13.33" y3="26.33"/>
<curve x1="14.33" y1="26.33" x2="15.33" y2="26.67" x3="15.33" y3="27.33"/>
<close/>
<move x="22.67" y="27.33"/>
<curve x1="22.67" y1="28" x2="22" y2="28.67" x3="21" y3="28.67"/>
<curve x1="20" y1="28.67" x2="19" y2="28" x3="19" y3="27.33"/>
<curve x1="19" y1="26.67" x2="20" y2="26.33" x3="21" y3="26.33"/>
<curve x1="22" y1="26.33" x2="22.67" y2="26.67" x3="22.67" y3="27.33"/>
<close/>
</path>
<fill/>
<path>
<move x="27" y="38.33"/>
<curve x1="27" y1="39" x2="25.33" y2="39.67" x3="23.33" y3="39.67"/>
<curve x1="21.67" y1="39.67" x2="20" y2="39" x3="20" y3="38.33"/>
<curve x1="20" y1="45" x2="20" y2="45" x3="20" y3="45"/>
<curve x1="20" y1="45.67" x2="21.67" y2="46" x3="23.33" y3="46"/>
<curve x1="25.33" y1="46" x2="27" y2="45.67" x3="27" y3="45"/>
<close/>
<move x="23.33" y="39.67"/>
<curve x1="25.33" y1="39.67" x2="27" y2="39" x3="27" y3="38.33"/>
<curve x1="27" y1="37.67" x2="25.33" y2="37" x3="23.33" y3="37"/>
<curve x1="21.67" y1="37" x2="20" y2="37.67" x3="20" y3="38.33"/>
<curve x1="20" y1="39" x2="21.67" y2="39.67" x3="23.33" y3="39.67"/>
<close/>
</path>
<stroke/>
<fillcolor color="#ffffff"/>
<path>
<move x="4" y="44.67"/>
<line x="11" y="44.67"/>
<line x="11" y="46.33"/>
<line x="14" y="44.33"/>
<line x="11" y="42"/>
<line x="11" y="43.67"/>
<line x="4" y="43.67"/>
<close/>
</path>
<fill/>
<path>
<move x="13.67" y="38.67"/>
<line x="6.67" y="38.67"/>
<line x="6.67" y="37.33"/>
<line x="3.67" y="39.33"/>
<line x="6.67" y="41.67"/>
<line x="6.67" y="40"/>
<line x="13.67" y="40"/>
<close/>
</path>
<fill/>
</foreground>
</shape>
<shape name="Multilayer Remote Switch" h="55" w="40.33" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
<constraint x="0" y="0" perimeter="0" name="NW"/>
<constraint x="0" y="1" perimeter="0" name="SW"/>
<constraint x="1" y="0" perimeter="0" name="NE"/>
<constraint x="1" y="1" perimeter="0" name="SE"/>
</connections>
<background>
<save/>
<path>
<move x="0" y="55"/>
<line x="0" y="19"/>
<line x="35.66" y="19"/>
<line x="35.66" y="55"/>
<close/>
<move x="40.33" y="50.34"/>
<line x="40.33" y="14.67"/>
<line x="35.66" y="19"/>
<line x="35.66" y="55"/>
<close/>
<move x="6.66" y="0"/>
<line x="0" y="4.67"/>
<line x="35.66" y="4.67"/>
<line x="40.33" y="0"/>
<close/>
<move x="0" y="19"/>
<line x="0" y="4.67"/>
<line x="35.66" y="4.67"/>
<line x="35.66" y="19"/>
<close/>
<move x="40.33" y="14.67"/>
<line x="40.33" y="0"/>
<line x="35.66" y="4.67"/>
<line x="35.66" y="19"/>
<close/>
</path>
</background>
<foreground>
<linejoin join="round"/>
<fillstroke/>
<path>
<move x="35.66" y="19"/>
<line x="0" y="19"/>
</path>
<stroke/>
<path>
<move x="35.66" y="19"/>
<line x="40.33" y="14.67"/>
</path>
<stroke/>
<restore/>
<rect/>
<stroke/>
<fillcolor color="#ffffff"/>
<path>
<move x="11.66" y="9.67"/>
<line x="11.66" y="6.67"/>
<line x="6" y="6.67"/>
<line x="6" y="9.67"/>
<close/>
<move x="24.66" y="9.67"/>
<line x="24.66" y="6.67"/>
<line x="30.66" y="6.67"/>
<line x="30.66" y="9.67"/>
<close/>
<move x="11.66" y="17"/>
<line x="11.66" y="14"/>
<line x="6" y="14"/>
<line x="6" y="17"/>
<close/>
<move x="24.66" y="17"/>
<line x="24.66" y="14"/>
<line x="30.66" y="14"/>
<line x="30.66" y="17"/>
<close/>
</path>
<fill/>
<strokecolor color="#ffffff"/>
<strokewidth width="0.67"/>
<path>
<move x="9.66" y="8"/>
<line x="27.66" y="8"/>
<move x="9.66" y="15.67"/>
<line x="27.66" y="15.67"/>
<move x="8.66" y="15.67"/>
<line x="27.66" y="8"/>
<move x="8.66" y="8"/>
<line x="27.66" y="15.67"/>
</path>
<stroke/>
<path>
<move x="19.66" y="29"/>
<line x="22.33" y="29"/>
<line x="22.33" y="29.34"/>
<line x="23" y="28.67"/>
<line x="22.33" y="28"/>
<line x="22.33" y="28.34"/>
<line x="19.66" y="28.34"/>
<close/>
<move x="19.33" y="27.67"/>
<line x="21" y="26"/>
<line x="21.33" y="26.34"/>
<line x="21.66" y="25"/>
<line x="20.66" y="25.34"/>
<line x="21" y="25.67"/>
<line x="19" y="27.34"/>
<close/>
<move x="18.33" y="27"/>
<line x="18.33" y="24.67"/>
<line x="18.66" y="24.67"/>
<line x="18" y="23.67"/>
<line x="17.33" y="24.67"/>
<line x="18" y="24.67"/>
<line x="18" y="27"/>
<close/>
<move x="17" y="27.34"/>
<line x="15.33" y="25.67"/>
<line x="15.66" y="25.34"/>
<line x="14.33" y="25"/>
<line x="14.66" y="26.34"/>
<line x="15" y="26"/>
<line x="16.66" y="27.67"/>
<close/>
<move x="16.33" y="28.34"/>
<line x="14" y="28.34"/>
<line x="14" y="28"/>
<line x="13" y="28.67"/>
<line x="14" y="29.34"/>
<line x="14" y="29"/>
<line x="16.33" y="29"/>
<close/>
<move x="19" y="30"/>
<line x="21" y="31.67"/>
<line x="20.66" y="32"/>
<line x="21.66" y="32.34"/>
<line x="21.33" y="31.34"/>
<line x="21" y="31.67"/>
<line x="19.33" y="29.67"/>
<close/>
</path>
<fillstroke/>
<save/>
<path>
<move x="16" y="30.34"/>
<curve x1="16.66" y1="31.34" x2="18.33" y2="31.67" x3="19.66" y3="31"/>
<curve x1="20.66" y1="30" x2="21" y2="28.34" x3="20" y3="27.34"/>
<curve x1="19.33" y1="26" x2="17.66" y2="25.67" x3="16.66" y3="26.67"/>
<curve x1="15.33" y1="27.34" x2="15" y2="29" x3="16" y3="30.34"/>
<close/>
</path>
<fillstroke/>
<restore/>
<strokewidth width="1"/>
<path>
<move x="18" y="30"/>
<line x="18" y="46.34"/>
</path>
<stroke/>
<strokewidth width="0.1"/>
<fillcolor color="#ffffff"/>
<path>
<move x="17" y="47.67"/>
<line x="15.33" y="47.67"/>
<line x="15.33" y="47.34"/>
<line x="14.66" y="47.67"/>
<line x="15.33" y="48"/>
<line x="15.33" y="47.67"/>
<close/>
<move x="17.33" y="48.34"/>
<line x="16" y="49.67"/>
<line x="15.66" y="49.34"/>
<line x="15.66" y="50.34"/>
<line x="16.33" y="50"/>
<line x="16" y="49.67"/>
<line x="17.33" y="48.67"/>
<close/>
<move x="18" y="49"/>
<line x="18" y="50.67"/>
<line x="17.66" y="50.67"/>
<line x="18" y="51.34"/>
<line x="18.66" y="50.67"/>
<line x="18.33" y="50.67"/>
<line x="18.33" y="49"/>
<close/>
<move x="19" y="48.67"/>
<line x="20" y="49.67"/>
<line x="20" y="50"/>
<line x="20.66" y="50.34"/>
<line x="20.33" y="49.34"/>
<line x="20.33" y="49.67"/>
<line x="19" y="48.34"/>
<close/>
<move x="19.33" y="47.67"/>
<line x="21" y="47.67"/>
<line x="21" y="48"/>
<line x="21.66" y="47.67"/>
<line x="21" y="47.34"/>
<line x="21" y="47.67"/>
<close/>
<move x="17.33" y="46.67"/>
<line x="16" y="45.34"/>
<line x="16.33" y="45.34"/>
<line x="15.66" y="45"/>
<line x="15.66" y="46"/>
<line x="16" y="45.67"/>
<line x="17.33" y="47"/>
<close/>
</path>
<fillstroke/>
<path>
<move x="19.66" y="46.67"/>
<curve x1="19" y1="45.67" x2="18" y2="45.67" x3="17" y3="46"/>
<curve x1="16.33" y1="46.67" x2="16" y2="48" x3="16.66" y3="48.67"/>
<curve x1="17.33" y1="49.34" x2="18.33" y2="49.67" x3="19" y3="49"/>
<curve x1="20" y1="48.67" x2="20" y2="47.34" x3="19.66" y3="46.67"/>
<close/>
</path>
<fillstroke/>
<path>
<move x="19" y="46.67"/>
<line x="20.33" y="45.33"/>
<line x="20.33" y="45.67"/>
<line x="20.66" y="45"/>
<line x="20" y="45"/>
<line x="20" y="45.34"/>
<close/>
</path>
<fillstroke/>
</foreground>
</shape>
<shape name="Multiswitch Device" h="40.67" w="63.66" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
<constraint x="0" y="0" perimeter="0" name="NW"/>
<constraint x="0" y="1" perimeter="0" name="SW"/>
<constraint x="1" y="0" perimeter="0" name="NE"/>
<constraint x="1" y="1" perimeter="0" name="SE"/>
</connections>
<background>
<save/>
<path>
<move x="0" y="15.34"/>
<line x="19" y="0"/>
<line x="63.66" y="0"/>
<line x="47.66" y="15.34"/>
<close/>
</path>
</background>
<foreground>
<linejoin join="round"/>
<fillstroke/>
<restore/>
<rect/>
<stroke/>
<save/>
<strokewidth width="1"/>
<fillcolor color="#ffffff"/>
<path>
<move x="35.33" y="5.34"/>
<line x="35" y="5.67"/>
<line x="21.66" y="5.67"/>
<line x="20.66" y="7"/>
<line x="17" y="5.67"/>
<line x="24" y="3.34"/>
<line x="22.33" y="5.34"/>
<close/>
</path>
<fill/>
<restore/>
<linejoin join="round"/>
<path>
<move x="47.66" y="40.67"/>
<line x="63.66" y="24.67"/>
<line x="63.66" y="0"/>
<line x="47.66" y="15.34"/>
<close/>
</path>
<fillstroke/>
<save/>
<fillcolor color="#ffffff"/>
<path>
<move x="50.33" y="3"/>
<line x="51.66" y="1.67"/>
<line x="55.33" y="3"/>
<line x="48" y="5.34"/>
<line x="50" y="3.34"/>
<line x="36.66" y="3.34"/>
<line x="37.33" y="3"/>
<close/>
<move x="28.33" y="12.67"/>
<line x="28" y="13"/>
<line x="14.66" y="13"/>
<line x="13.66" y="14.34"/>
<line x="10" y="13"/>
<line x="17" y="10.67"/>
<line x="15.33" y="12.67"/>
<close/>
<move x="43.33" y="10.34"/>
<line x="44.66" y="9"/>
<line x="48.33" y="10.34"/>
<line x="41" y="12.67"/>
<line x="43" y="10.67"/>
<line x="29.66" y="10.67"/>
<line x="30.33" y="10.34"/>
<close/>
</path>
<fill/>
<restore/>
<strokewidth width="0.67"/>
<path>
<move x="47.66" y="40.67"/>
<line x="47.66" y="15.34"/>
<line x="0" y="15.34"/>
<line x="0" y="40.67"/>
<close/>
</path>
<fillstroke/>
<fillcolor color="#ffffff"/>
<path>
<move x="14.66" y="26.34"/>
<line x="14.66" y="22.67"/>
<line x="7.66" y="22.67"/>
<line x="7.66" y="26.34"/>
<close/>
<move x="31.33" y="26.34"/>
<line x="31.33" y="22.67"/>
<line x="38.33" y="22.67"/>
<line x="38.33" y="26.34"/>
<close/>
<move x="14.66" y="35.67"/>
<line x="14.66" y="32"/>
<line x="7.66" y="32"/>
<line x="7.66" y="35.67"/>
<close/>
<move x="31.33" y="35.67"/>
<line x="31.33" y="32"/>
<line x="38.33" y="32"/>
<line x="38.33" y="35.67"/>
<close/>
</path>
<fill/>
<save/>
<strokecolor color="#ffffff"/>
<strokewidth width="0.67"/>
<path>
<move x="12.33" y="24.34"/>
<line x="34.66" y="24.34"/>
<move x="12.33" y="33.67"/>
<line x="34.66" y="33.67"/>
<move x="11.33" y="33.67"/>
<line x="34.66" y="24.34"/>
<move x="11.33" y="24.34"/>
<line x="34.66" y="33.67"/>
</path>
<stroke/>
<restore/>
<fillcolor color="#ffffff"/>
<path>
<move x="55.33" y="18.67"/>
<line x="57" y="10"/>
<line x="58.66" y="8"/>
<line x="58.66" y="6"/>
<line x="61.66" y="6"/>
<line x="59" y="11.67"/>
<line x="59" y="9"/>
<line x="57.66" y="10.67"/>
<line x="56.33" y="19"/>
<line x="55.66" y="21"/>
<line x="53.66" y="30.67"/>
<line x="52" y="32.67"/>
<line x="52" y="34.67"/>
<line x="49" y="35.34"/>
<line x="51.66" y="29"/>
<line x="51.66" y="31.34"/>
<line x="52.66" y="30"/>
<line x="55" y="20.67"/>
<close/>
<move x="51.33" y="17"/>
<line x="51.33" y="17"/>
<line x="52.66" y="16"/>
<line x="54.66" y="20"/>
<line x="55.66" y="21.34"/>
<line x="57.66" y="25.34"/>
<line x="59.66" y="24"/>
<line x="59.66" y="26.34"/>
<line x="62.33" y="21"/>
<line x="59.66" y="21.34"/>
<line x="59.66" y="23"/>
<line x="58" y="24.34"/>
<line x="55.66" y="20"/>
<line x="55" y="18.67"/>
<line x="52.66" y="14.67"/>
<line x="51.33" y="16"/>
<line x="51.33" y="13.34"/>
<line x="48.66" y="19.34"/>
<line x="51.33" y="18.67"/>
<close/>
</path>
<fill/>
</foreground>
</shape>
<shape name="PBX Switch" h="33.33" w="36.67" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
<constraint x="0.1" y="0.085" perimeter="0" name="NW"/>
<constraint x="0" y="1" perimeter="0" name="SW"/>
<constraint x="1" y="0" perimeter="0" name="NE"/>
<constraint x="0.92" y="0.89" perimeter="0" name="SE"/>
</connections>
<background>
<path>
<move x="30.33" y="33.33"/>
<line x="36.67" y="26.67"/>
<line x="36.67" y="0"/>
<line x="6.67" y="0"/>
<line x="0" y="6.67"/>
<line x="30.33" y="6.67"/>
<close/>
<move x="30.33" y="33.33"/>
<line x="30.33" y="6.67"/>
<line x="0" y="6.67"/>
<line x="0" y="33.33"/>
<close/>
<move x="30.33" y="6.67"/>
<line x="36.67" y="0"/>
<move x="30.33" y="20"/>
<line x="0" y="20"/>
</path>
</background>
<foreground>
<linejoin join="round"/>
<fillstroke/>
<path>
<move x="3" y="23.33"/>
<line x="10.33" y="23.33"/>
<move x="3" y="25"/>
<line x="10.33" y="25"/>
<move x="3" y="26.67"/>
<line x="10.33" y="26.67"/>
<move x="3" y="28.33"/>
<line x="10.33" y="28.33"/>
<move x="3" y="29.67"/>
<line x="10.33" y="29.67"/>
</path>
<stroke/>
</foreground>
</shape>
<shape name="Programmable Switch" h="35.34" w="34.67" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
<constraint x="0.11" y="0.08" perimeter="0" name="NW"/>
<constraint x="0" y="1" perimeter="0" name="SW"/>
<constraint x="1" y="0" perimeter="0" name="NE"/>
<constraint x="0.93" y="0.9" perimeter="0" name="SE"/>
</connections>
<background>
<save/>
<path>
<move x="28.67" y="35.34"/>
<line x="28.67" y="6"/>
<line x="0" y="6"/>
<line x="0" y="35.34"/>
<close/>
<move x="28.67" y="6"/>
<line x="0" y="6"/>
<line x="7.34" y="0"/>
<line x="34.67" y="0"/>
<line x="34.67" y="0.34"/>
<line x="34.67" y="29.34"/>
<line x="28.67" y="35.34"/>
<line x="28.67" y="6"/>
<line x="28.67" y="6"/>
<close/>
<move x="28.67" y="6"/>
<line x="34.67" y="0"/>
</path>
</background>
<foreground>
<linejoin join="round"/>
<fillstroke/>
<strokecolor color="#ffffff"/>
<strokewidth width="1.67"/>
<path>
<move x="14.34" y="15.34"/>
<line x="23.34" y="15.34"/>
</path>
<stroke/>
<fillcolor color="#ffffff"/>
<path>
<move x="23.67" y="12.67"/>
<line x="23.67" y="18"/>
<line x="26.67" y="15.34"/>
<close/>
<move x="23.67" y="23"/>
<line x="23.67" y="28"/>
<line x="26.67" y="25.67"/>
<close/>
<move x="6.67" y="11.34"/>
<line x="11.67" y="11.34"/>
<line x="9.34" y="8.34"/>
<close/>
<move x="16.67" y="11.34"/>
<line x="22" y="11.34"/>
<line x="19.34" y="8.34"/>
<close/>
<move x="22" y="29.67"/>
<line x="17" y="29.67"/>
<line x="19.34" y="32.67"/>
<close/>
<move x="12" y="29.67"/>
<line x="7" y="29.67"/>
<line x="9.34" y="32.67"/>
<line x="12" y="29.67"/>
<close/>
<move x="5.34" y="28.34"/>
<line x="5.34" y="23.34"/>
<line x="2.34" y="25.67"/>
<close/>
<move x="5.34" y="18"/>
<line x="5.34" y="13"/>
<line x="2.34" y="15.34"/>
<close/>
</path>
<fill/>
<path>
<move x="14.34" y="25.67"/>
<line x="23.34" y="25.67"/>
<move x="9.34" y="20.67"/>
<line x="9.34" y="11.67"/>
<move x="19.34" y="20.67"/>
<line x="19.34" y="11.67"/>
<move x="19.34" y="20.67"/>
<line x="19.34" y="29.34"/>
<move x="9.34" y="20.67"/>
<line x="9.34" y="29.34"/>
<move x="14.34" y="25.67"/>
<line x="5.67" y="25.67"/>
<move x="14.34" y="15.34"/>
<line x="5.67" y="15.34"/>
</path>
<stroke/>
<restore/>
<rect/>
<stroke/>
<strokecolor color="#ffffff"/>
<strokewidth width="0.67"/>
<path>
<move x="14.34" y="26.67"/>
<curve x1="17.67" y1="26.67" x2="20.34" y2="24" x3="20.34" y3="20.67"/>
<curve x1="20.34" y1="17.34" x2="17.67" y2="14.34" x3="14.34" y3="14.34"/>
<curve x1="11" y1="14.34" x2="8.34" y2="17.34" x3="8.34" y3="20.67"/>
<curve x1="8.34" y1="24" x2="11" y2="26.67" x3="14.34" y3="26.67"/>
<close/>
</path>
<fillstroke/>
</foreground>
</shape>
<shape name="Server Switch" h="35" w="35" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
<constraint x="0.1" y="0.08" perimeter="0" name="NW"/>
<constraint x="0" y="1" perimeter="0" name="SW"/>
<constraint x="1" y="0" perimeter="0" name="NE"/>
<constraint x="0.92" y="0.91" perimeter="0" name="SE"/>
</connections>
<background>
<path>
<move x="29" y="35"/>
<line x="29" y="5.67"/>
<line x="0" y="5.67"/>
<line x="0" y="35"/>
<close/>
<move x="29" y="5.67"/>
<line x="0.33" y="5.67"/>
<line x="7.33" y="0"/>
<line x="35" y="0"/>
<line x="35" y="0"/>
<line x="35" y="29"/>
<line x="29" y="35"/>
<close/>
<move x="29" y="5.67"/>
<line x="35" y="0"/>
</path>
</background>
<foreground>
<linejoin join="round"/>
<fillstroke/>
<strokecolor color="#ffffff"/>
<strokewidth width="0.67"/>
<path>
<move x="4" y="13.34"/>
<line x="27.33" y="13.34"/>
<move x="3" y="20.34"/>
<line x="26.33" y="20.34"/>
<move x="2" y="27.34"/>
<line x="25.33" y="27.34"/>
<move x="5.33" y="31"/>
<line x="10" y="9"/>
<move x="20" y="31"/>
<line x="25" y="9"/>
<move x="12.66" y="31"/>
<line x="17.33" y="9"/>
</path>
<stroke/>
<fillcolor color="#ffffff"/>
<path>
<move x="11" y="13.34"/>
<curve x1="11" y1="14" x2="10" y2="14.34" x3="9" y3="14.34"/>
<curve x1="8" y1="14.34" x2="7.33" y2="14" x3="7.33" y3="13.34"/>
<curve x1="7.33" y1="12.67" x2="8" y2="12" x3="9" y3="12"/>
<curve x1="10" y1="12" x2="11" y2="12.67" x3="11" y3="13.34"/>
<close/>
<move x="18.33" y="13"/>
<curve x1="18.33" y1="13.67" x2="17.66" y2="14.34" x3="16.66" y3="14.34"/>
<curve x1="15.66" y1="14.34" x2="14.66" y2="13.67" x3="14.66" y3="13"/>
<curve x1="14.66" y1="12.34" x2="15.66" y2="11.67" x3="16.66" y3="11.67"/>
<curve x1="17.66" y1="11.67" x2="18.33" y2="12.34" x3="18.33" y3="13"/>
<close/>
<move x="26" y="13.67"/>
<curve x1="26" y1="14.34" x2="25" y2="14.67" x3="24" y3="14.67"/>
<curve x1="23" y1="14.67" x2="22.33" y2="14.34" x3="22.33" y3="13.67"/>
<curve x1="22.33" y1="13" x2="23" y2="12.34" x3="24" y3="12.34"/>
<curve x1="25" y1="12.34" x2="26" y2="13" x3="26" y3="13.67"/>
<close/>
<move x="9" y="20.34"/>
<curve x1="9" y1="21" x2="8.33" y2="21.67" x3="7.33" y3="21.67"/>
<curve x1="6.33" y1="21.67" x2="5.66" y2="21" x3="5.66" y3="20.34"/>
<curve x1="5.66" y1="19.67" x2="6.33" y2="19.34" x3="7.33" y3="19.34"/>
<curve x1="8.33" y1="19.34" x2="9" y2="19.67" x3="9" y3="20.34"/>
<close/>
<move x="16.66" y="20.67"/>
<curve x1="16.66" y1="21.34" x2="16" y2="22" x3="15" y3="22"/>
<curve x1="14" y1="22" x2="13" y2="21.34" x3="13" y3="20.67"/>
<curve x1="13" y1="20" x2="14" y2="19.34" x3="15" y3="19.34"/>
<curve x1="16" y1="19.34" x2="16.66" y2="20" x3="16.66" y3="20.67"/>
<close/>
<move x="24.33" y="20.34"/>
<curve x1="24.33" y1="21" x2="23.33" y2="21.67" x3="22.33" y3="21.67"/>
<curve x1="21.33" y1="21.67" x2="20.66" y2="21" x3="20.66" y3="20.34"/>
<curve x1="20.66" y1="19.67" x2="21.33" y2="19" x3="22.33" y3="19"/>
<curve x1="23.33" y1="19" x2="24.33" y2="19.67" x3="24.33" y3="20.34"/>
<close/>
<move x="7.66" y="27.34"/>
<curve x1="7.66" y1="28" x2="6.66" y2="28.67" x3="5.66" y3="28.67"/>
<curve x1="4.66" y1="28.67" x2="4" y2="28" x3="4" y3="27.34"/>
<curve x1="4" y1="26.67" x2="4.66" y2="26" x3="5.66" y3="26"/>
<curve x1="6.66" y1="26" x2="7.66" y2="26.67" x3="7.66" y3="27.34"/>
<close/>
<move x="15.33" y="27.34"/>
<curve x1="15.33" y1="28" x2="14.33" y2="28.67" x3="13.33" y3="28.67"/>
<curve x1="12.33" y1="28.67" x2="11.66" y2="28" x3="11.66" y3="27.34"/>
<curve x1="11.66" y1="26.67" x2="12.33" y2="26" x3="13.33" y3="26"/>
<curve x1="14.33" y1="26" x2="15.33" y2="26.67" x3="15.33" y3="27.34"/>
<close/>
<move x="22.66" y="27.34"/>
<curve x1="22.66" y1="28" x2="22" y2="28.67" x3="21" y3="28.67"/>
<curve x1="20" y1="28.67" x2="19" y2="28" x3="19" y3="27.34"/>
<curve x1="19" y1="26.67" x2="20" y2="26" x3="21" y3="26"/>
<curve x1="22" y1="26" x2="22.66" y2="26.67" x3="22.66" y3="27.34"/>
<close/>
</path>
<fill/>
</foreground>
</shape>
<shape name="Simultilayer Switch" h="36.66" w="36.66" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0.01" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
<constraint x="0.09" y="0.08" perimeter="0" name="NW"/>
<constraint x="0.01" y="1" perimeter="0" name="SW"/>
<constraint x="1" y="0" perimeter="0" name="NE"/>
<constraint x="0.92" y="0.89" perimeter="0" name="SE"/>
</connections>
<background>
<save/>
<path>
<move x="30.33" y="36.66"/>
<line x="30.33" y="6"/>
<line x="0" y="6"/>
<line x="0" y="36.66"/>
<close/>
<move x="36.66" y="0"/>
<line x="30.33" y="6"/>
<line x="0" y="6"/>
<line x="6.66" y="0"/>
<close/>
<move x="36.66" y="29.33"/>
<line x="36.66" y="0"/>
<line x="30.33" y="6"/>
<line x="30.33" y="36.66"/>
<close/>
</path>
</background>
<foreground>
<linejoin join="round"/>
<fillstroke/>
<restore/>
<rect/>
<stroke/>
<fillcolor color="#ffffff"/>
<strokecolor color="#ffffff"/>
<path>
<move x="15.33" y="15"/>
<curve x1="18.66" y1="15" x2="21.66" y2="18" x3="21.66" y3="21.33"/>
<curve x1="21.66" y1="24.66" x2="18.66" y2="27.33" x3="15.33" y3="27.33"/>
<curve x1="12" y1="27.33" x2="9.33" y2="24.66" x3="9.33" y3="21.33"/>
<curve x1="9.33" y1="18" x2="12" y2="15" x3="15.33" y3="15"/>
<close/>
</path>
<fill/>
<strokewidth width="0.67"/>
<path>
<move x="15.33" y="15"/>
<line x="15.33" y="9"/>
<move x="11" y="17"/>
<line x="6.66" y="12.66"/>
<move x="9.33" y="21.33"/>
<line x="3" y="21.33"/>
<move x="11" y="25.66"/>
<line x="6.66" y="30"/>
<move x="15.33" y="27.33"/>
<line x="15.33" y="33.66"/>
<move x="19.66" y="25.66"/>
<line x="24" y="30"/>
<move x="21.66" y="21.33"/>
<line x="27.66" y="21.33"/>
<move x="19.66" y="17"/>
<line x="24" y="12.66"/>
</path>
<stroke/>
<path>
<move x="15.33" y="7.33"/>
<line x="17.66" y="12"/>
<line x="13.33" y="12"/>
<close/>
<move x="5.66" y="11.33"/>
<line x="10.33" y="13"/>
<line x="7.33" y="16"/>
<close/>
<move x="1.66" y="21.33"/>
<line x="6" y="19"/>
<line x="6" y="23.33"/>
<close/>
<move x="5.66" y="31"/>
<line x="7" y="26.33"/>
<line x="10.33" y="29.33"/>
<line x="5.66" y="31"/>
<close/>
<move x="15.33" y="35"/>
<line x="13" y="30.66"/>
<line x="17.66" y="30.66"/>
<close/>
<move x="25" y="31"/>
<line x="20.33" y="29.33"/>
<line x="23.66" y="26.33"/>
<close/>
<move x="29.33" y="21.33"/>
<line x="24.66" y="23.66"/>
<line x="24.66" y="19"/>
<close/>
<move x="25.33" y="11.66"/>
<line x="23.66" y="16.33"/>
<line x="20.33" y="13"/>
<close/>
</path>
<fill/>
<fontcolor color="#0e6f9c"/>
<fontsize size="7"/>
<text str="Si" x="15.5" y="20.5" valign="middle" align="center"/>
</foreground>
</shape>
<shape name="Softswitch PGW MGC" h="40.67" w="35.66" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0.11" y="0.5" perimeter="0" name="W"/>
<constraint x="0.99" y="0.5" perimeter="0" name="E"/>
<constraint x="0.15" y="0.08" perimeter="0" name="NW"/>
<constraint x="0.15" y="1" perimeter="0" name="SW"/>
<constraint x="0.85" y="0" perimeter="0" name="NE"/>
<constraint x="0.97" y="0.86" perimeter="0" name="SE"/>
</connections>
<background>
<save/>
</background>
<foreground>
<linejoin join="round"/>
<strokecolor color="#0c6d9c"/>
<strokewidth width="0.67"/>
<fillcolor color="#ffffff"/>
<path>
<move x="30" y="35"/>
<line x="34.66" y="35"/>
<line x="34.66" y="30.33"/>
<line x="30" y="30.33"/>
</path>
<fillstroke/>
<path>
<move x="6.33" y="9.67"/>
<line x="5.66" y="9.67"/>
<line x="0.66" y="9.67"/>
<line x="0.66" y="14"/>
<line x="5.66" y="14"/>
<line x="6" y="14"/>
</path>
<fillstroke/>
<path>
<move x="29.33" y="14"/>
<curve x1="30" y1="14" x2="30" y2="14" x3="30" y3="14"/>
<curve x1="35" y1="14" x2="35.66" y2="18.33" x3="35.66" y3="18.33"/>
<curve x1="35.66" y1="14" x2="35.66" y2="14" x3="35.66" y3="14"/>
<curve x1="35.66" y1="14" x2="35" y2="9.67" x3="30" y3="9.67"/>
<curve x1="29" y1="9.67" x2="29" y2="9.67" x3="29" y3="9.67"/>
</path>
<fillstroke/>
<path>
<move x="6.33" y="30.67"/>
<curve x1="5.33" y1="30.67" x2="5.33" y2="30.67" x3="5.33" y3="30.67"/>
<curve x1="0.66" y1="30.67" x2="0" y2="26.33" x3="0" y3="26.33"/>
<curve x1="0" y1="30.67" x2="0" y2="30.67" x3="0" y3="30.67"/>
<curve x1="0" y1="30.67" x2="0.66" y2="35" x3="5.33" y3="35"/>
<curve x1="6.33" y1="35" x2="6.33" y2="35" x3="6.33" y3="35"/>
</path>
<fillstroke/>
<path>
<move x="6.33" y="30.67"/>
<curve x1="5.33" y1="30.67" x2="5.33" y2="30.67" x3="5.33" y3="30.67"/>
<curve x1="0.66" y1="30.67" x2="0" y2="26.33" x3="0" y3="26.33"/>
<curve x1="0" y1="30.67" x2="0" y2="30.67" x3="0" y3="30.67"/>
<curve x1="0" y1="30.67" x2="0.66" y2="35" x3="5.33" y3="35"/>
<curve x1="6.33" y1="35" x2="6.33" y2="35" x3="6.33" y3="35"/>
</path>
<fillstroke/>
<restore/>
<linejoin join="round"/>
<path>
<move x="27" y="40.67"/>
<line x="27" y="3.33"/>
<line x="5.33" y="3.33"/>
<line x="5.33" y="40.67"/>
<line x="27" y="40.67"/>
<close/>
<move x="5.33" y="3.33"/>
<line x="9.33" y="0"/>
<line x="30.33" y="0"/>
<line x="27" y="3.33"/>
<line x="5.33" y="3.33"/>
<close/>
<move x="27" y="40.67"/>
<line x="30.33" y="36.67"/>
<line x="30.33" y="0"/>
<line x="27" y="3.33"/>
<line x="27" y="40.67"/>
<close/>
</path>
<fillstroke/>
<fillcolor color="#ffffff"/>
<strokecolor color="#0c6d9c"/>
<strokewidth width="0.67"/>
<path>
<move x="35.66" y="13.67"/>
<curve x1="35.66" y1="19" x2="35.66" y2="19" x3="35.66" y3="19"/>
<curve x1="35" y1="25.33" x2="24" y2="25" x3="24" y3="25"/>
<curve x1="24" y1="25.33" x2="24" y2="25.33" x3="24" y3="25.33"/>
<curve x1="24" y1="29.67" x2="24" y2="29.67" x3="24" y3="29.67"/>
<curve x1="16.66" y1="22.33" x2="16.66" y2="22.33" x3="16.66" y3="22.33"/>
<curve x1="24" y1="15" x2="24" y2="15" x3="24" y3="15"/>
<curve x1="24" y1="19.67" x2="24" y2="19.67" x3="24" y3="19.67"/>
<curve x1="24" y1="19.67" x2="35.33" y2="20" x3="35.66" y3="13.67"/>
</path>
<fillstroke/>
<path>
<move x="0" y="31"/>
<curve x1="0" y1="25.67" x2="0" y2="25.67" x3="0" y3="25.67"/>
<curve x1="0.33" y1="19.33" x2="8" y2="19.67" x3="8" y3="19.67"/>
<curve x1="8" y1="19.33" x2="8" y2="19.33" x3="8" y3="19.33"/>
<curve x1="8" y1="15" x2="8" y2="15" x3="8" y3="15"/>
<curve x1="15.33" y1="22.33" x2="15.33" y2="22.33" x3="15.33" y3="22.33"/>
<curve x1="8" y1="29.67" x2="8" y2="29.67" x3="8" y3="29.67"/>
<curve x1="8" y1="25" x2="8" y2="25" x3="8" y3="25"/>
<curve x1="8" y1="25" x2="0.33" y2="24.67" x3="0" y3="31"/>
</path>
<fillstroke/>
</foreground>
</shape>
<shape name="Virtual Layer Switch" h="54.66" w="40.33" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
<constraint x="0.09" y="0.04" perimeter="0" name="NW"/>
<constraint x="0" y="1" perimeter="0" name="SW"/>
<constraint x="1" y="0" perimeter="0" name="NE"/>
<constraint x="0.94" y="0.96" perimeter="0" name="SE"/>
</connections>
<background>
<path>
<move x="0" y="54.66"/>
<line x="0" y="19"/>
<line x="35.66" y="19"/>
<line x="35.66" y="54.66"/>
<close/>
<move x="40.33" y="50.33"/>
<line x="40.33" y="14.66"/>
<line x="35.66" y="19"/>
<line x="35.66" y="54.66"/>
<close/>
<move x="6.66" y="0"/>
<line x="0" y="4.66"/>
<line x="35.66" y="4.66"/>
<line x="40.33" y="0"/>
<close/>
<move x="0" y="19"/>
<line x="0" y="4.66"/>
<line x="35.66" y="4.66"/>
<line x="35.66" y="19"/>
<close/>
<move x="40.33" y="14.66"/>
<line x="40.33" y="0"/>
<line x="35.66" y="4.66"/>
<line x="35.66" y="19"/>
<close/>
</path>
</background>
<foreground>
<linejoin join="round"/>
<fillstroke/>
<path>
<move x="40.33" y="14.66"/>
<line x="35.66" y="19"/>
<line x="0" y="19"/>
</path>
<stroke/>
<fillcolor color="#ffffff"/>
<path>
<move x="11.66" y="9.66"/>
<line x="11.66" y="6.66"/>
<line x="6" y="6.66"/>
<line x="6" y="9.66"/>
<close/>
<move x="24.66" y="9.66"/>
<line x="24.66" y="6.66"/>
<line x="30.66" y="6.66"/>
<line x="30.66" y="9.66"/>
<close/>
<move x="11.66" y="17"/>
<line x="11.66" y="14"/>
<line x="6" y="14"/>
<line x="6" y="17"/>
<line x="11.66" y="17"/>
<close/>
<move x="24.66" y="17"/>
<line x="24.66" y="14"/>
<line x="30.66" y="14"/>
<line x="30.66" y="17"/>
<line x="24.66" y="17"/>
<close/>
</path>
<fill/>
<strokecolor color="#ffffff"/>
<strokewidth width="0.67"/>
<path>
<move x="27.66" y="15.66"/>
<line x="27.66" y="15.66"/>
<move x="9.66" y="8"/>
<line x="27.66" y="8"/>
<move x="9.66" y="15.66"/>
<line x="27.66" y="15.66"/>
<move x="8.66" y="15.66"/>
<line x="27.66" y="8"/>
<move x="8.66" y="8"/>
<line x="27.66" y="15.66"/>
</path>
<stroke/>
<path>
<move x="9.66" y="27"/>
<line x="9.66" y="24"/>
<line x="10.33" y="24"/>
<line x="9.66" y="23"/>
<line x="8.66" y="24"/>
<line x="9.33" y="24"/>
<line x="9.33" y="27"/>
<close/>
<move x="8.33" y="27.66"/>
<line x="6" y="25.33"/>
<line x="6.33" y="25"/>
<line x="5" y="24.66"/>
<line x="5.33" y="26"/>
<line x="5.66" y="25.66"/>
<line x="8" y="28"/>
<close/>
<move x="7.33" y="29"/>
<line x="4.33" y="29"/>
<line x="4.33" y="28.66"/>
<line x="3" y="29.33"/>
<line x="4.33" y="30"/>
<line x="4.33" y="29.66"/>
<line x="7.33" y="29.66"/>
<close/>
<move x="8" y="30.66"/>
<line x="5.66" y="32.66"/>
<line x="5.33" y="32.33"/>
<line x="5" y="33.66"/>
<line x="6.33" y="33.66"/>
<line x="6" y="33"/>
<line x="8.33" y="31"/>
<close/>
<move x="9.33" y="31.33"/>
<line x="9.33" y="34.66"/>
<line x="8.66" y="34.66"/>
<line x="9.66" y="35.66"/>
<line x="10.33" y="34.66"/>
<line x="9.66" y="34.66"/>
<line x="9.66" y="31.33"/>
<close/>
<move x="11.66" y="29.66"/>
<line x="14.66" y="29.66"/>
<line x="14.66" y="30"/>
<line x="16" y="29.33"/>
<line x="14.66" y="28.66"/>
<line x="14.66" y="29"/>
<line x="11.66" y="29"/>
<close/>
<move x="11.33" y="28"/>
<line x="13.33" y="25.66"/>
<line x="13.66" y="26"/>
<line x="14" y="24.66"/>
<line x="12.66" y="25"/>
<line x="13" y="25.33"/>
<line x="11" y="27.66"/>
<close/>
</path>
<fill/>
<path>
<move x="11.33" y="32"/>
<curve x1="13" y1="31" x2="13.33" y2="29" x3="12.33" y3="27.33"/>
<curve x1="11.33" y1="26" x2="9.33" y2="25.66" x3="7.66" y3="26.66"/>
<curve x1="6.33" y1="27.66" x2="6" y2="29.66" x3="7" y3="31"/>
<curve x1="8" y1="32.66" x2="10" y2="33" x3="11.33" y3="32"/>
<close/>
</path>
<fill/>
<path>
<move x="9.66" y="44"/>
<line x="9.66" y="41"/>
<line x="10.33" y="41"/>
<line x="9.66" y="39.66"/>
<line x="8.66" y="41"/>
<line x="9.33" y="41"/>
<line x="9.33" y="44"/>
<close/>
<move x="8.33" y="44.33"/>
<line x="6" y="42.33"/>
<line x="6.33" y="42"/>
<line x="5" y="41.66"/>
<line x="5.33" y="43"/>
<line x="5.66" y="42.66"/>
<line x="8" y="45"/>
<close/>
<move x="7.33" y="46"/>
<line x="4.33" y="46"/>
<line x="4.33" y="45.33"/>
<line x="3" y="46.33"/>
<line x="4.33" y="47"/>
<line x="4.33" y="46.33"/>
<line x="7.33" y="46.33"/>
<close/>
<move x="8" y="47.66"/>
<line x="5.66" y="49.66"/>
<line x="5.33" y="49.33"/>
<line x="5" y="50.66"/>
<line x="6.33" y="50.33"/>
<line x="6" y="50"/>
<line x="8.33" y="48"/>
<close/>
<move x="9.33" y="48.33"/>
<line x="9.33" y="51.33"/>
<line x="8.66" y="51.33"/>
<line x="9.66" y="52.66"/>
<line x="10.33" y="51.33"/>
<line x="9.66" y="51.33"/>
<line x="9.66" y="48.33"/>
<close/>
<move x="11" y="48"/>
<line x="13" y="50"/>
<line x="12.66" y="50.33"/>
<line x="14" y="50.66"/>
<line x="13.66" y="49.33"/>
<line x="13.33" y="49.66"/>
<line x="11.33" y="47.66"/>
<close/>
<move x="11.66" y="46.33"/>
<line x="14.66" y="46.33"/>
<line x="14.66" y="47"/>
<line x="16" y="46.33"/>
<line x="14.66" y="45.33"/>
<line x="14.66" y="46"/>
<line x="11.66" y="46"/>
<close/>
</path>
<fill/>
<path>
<move x="11.33" y="48.66"/>
<curve x1="13" y1="47.66" x2="13.33" y2="45.66" x3="12.33" y3="44.33"/>
<curve x1="11.33" y1="43" x2="9.33" y2="42.66" x3="7.66" y3="43.66"/>
<curve x1="6.33" y1="44.66" x2="6" y2="46.66" x3="7" y3="48"/>
<curve x1="8" y1="49.33" x2="10" y2="49.66" x3="11.33" y3="48.66"/>
<close/>
</path>
<fill/>
<path>
<move x="26.66" y="27"/>
<line x="26.66" y="24"/>
<line x="27.33" y="24"/>
<line x="26.33" y="23"/>
<line x="25.66" y="24"/>
<line x="26.33" y="24"/>
<line x="26.33" y="27"/>
<close/>
<move x="25" y="27.66"/>
<line x="23" y="25.33"/>
<line x="23.33" y="25"/>
<line x="22" y="24.66"/>
<line x="22.33" y="26"/>
<line x="22.66" y="25.66"/>
<line x="24.66" y="28"/>
<close/>
<move x="24.33" y="29"/>
<line x="21.33" y="29"/>
<line x="21.33" y="28.66"/>
<line x="20" y="29.33"/>
<line x="21.33" y="30"/>
<line x="21.33" y="29.66"/>
<line x="24.33" y="29.66"/>
<close/>
<move x="26.33" y="31.33"/>
<line x="26.33" y="34.66"/>
<line x="25.66" y="34.66"/>
<line x="26.33" y="35.66"/>
<line x="27.33" y="34.66"/>
<line x="26.66" y="34.66"/>
<line x="26.66" y="31.33"/>
<close/>
<move x="27.66" y="31"/>
<line x="30" y="33"/>
<line x="29.66" y="33.66"/>
<line x="31" y="33.66"/>
<line x="30.66" y="32.33"/>
<line x="30.33" y="32.66"/>
<line x="28" y="30.66"/>
<close/>
<move x="28.66" y="29.66"/>
<line x="31.66" y="29.66"/>
<line x="31.66" y="30"/>
<line x="32.66" y="29.33"/>
<line x="31.66" y="28.66"/>
<line x="31.66" y="29"/>
<line x="28.66" y="29"/>
<close/>
<move x="28" y="28"/>
<line x="30.33" y="25.66"/>
<line x="30.66" y="26"/>
<line x="31" y="24.66"/>
<line x="29.66" y="25"/>
<line x="30" y="25.33"/>
<line x="27.66" y="27.66"/>
<close/>
</path>
<fill/>
<path>
<move x="28.33" y="32"/>
<curve x1="29.66" y1="31" x2="30" y2="29" x3="29" y3="27.33"/>
<curve x1="28" y1="26" x2="26" y2="25.66" x3="24.66" y3="26.66"/>
<curve x1="23.33" y1="27.66" x2="23" y2="29.66" x3="24" y3="31"/>
<curve x1="25" y1="32.66" x2="27" y2="33" x3="28.33" y3="32"/>
<close/>
</path>
<fill/>
<path>
<move x="26.66" y="44"/>
<line x="26.66" y="41"/>
<line x="27.33" y="41"/>
<line x="26.33" y="39.66"/>
<line x="25.66" y="41"/>
<line x="26.33" y="41"/>
<line x="26.33" y="44"/>
<close/>
<move x="24.33" y="46"/>
<line x="21.33" y="46"/>
<line x="21.33" y="45.33"/>
<line x="20" y="46.33"/>
<line x="21.33" y="47"/>
<line x="21.33" y="46.33"/>
<line x="24.33" y="46.33"/>
<close/>
<move x="24.66" y="47.66"/>
<line x="22.66" y="49.66"/>
<line x="22.33" y="49.33"/>
<line x="22" y="50.66"/>
<line x="23.33" y="50.33"/>
<line x="23" y="50"/>
<line x="25" y="48"/>
<close/>
<move x="26.33" y="48.33"/>
<line x="26.33" y="51.33"/>
<line x="25.66" y="51.33"/>
<line x="26.33" y="52.66"/>
<line x="27.33" y="51.33"/>
<line x="26.66" y="51.33"/>
<line x="26.66" y="48.33"/>
<close/>
<move x="27.66" y="48"/>
<line x="30" y="50"/>
<line x="29.66" y="50.33"/>
<line x="31" y="50.66"/>
<line x="30.66" y="49.33"/>
<line x="30.33" y="49.66"/>
<line x="28" y="47.66"/>
<close/>
<move x="28.66" y="46.33"/>
<line x="31.66" y="46.33"/>
<line x="31.66" y="47"/>
<line x="32.66" y="46.33"/>
<line x="31.66" y="45.33"/>
<line x="31.66" y="46"/>
<line x="28.66" y="46"/>
<close/>
<move x="28" y="45"/>
<line x="30.33" y="42.66"/>
<line x="30.66" y="43"/>
<line x="31" y="41.66"/>
<line x="29.66" y="42"/>
<line x="30" y="42.33"/>
<line x="27.66" y="44.33"/>
<close/>
</path>
<fill/>
<path>
<move x="28.33" y="48.66"/>
<curve x1="29.66" y1="47.66" x2="30" y2="45.66" x3="29" y3="44.33"/>
<curve x1="28" y1="43" x2="26" y2="42.66" x3="24.66" y3="43.66"/>
<curve x1="23.33" y1="44.66" x2="23" y2="46.66" x3="24" y3="48"/>
<curve x1="25" y1="49.33" x2="27" y2="49.66" x3="28.33" y3="48.66"/>
<close/>
</path>
<fill/>
<strokewidth width="1.33"/>
<path>
<move x="11.33" y="31"/>
<line x="25" y="44.66"/>
<move x="11" y="44.66"/>
<line x="25.33" y="30.66"/>
</path>
<stroke/>
</foreground>
</shape>
<shape name="Voice ATM Switch" h="35.34" w="34.66" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
<constraint x="0.1" y="0.09" perimeter="0" name="NW"/>
<constraint x="0" y="1" perimeter="0" name="SW"/>
<constraint x="1" y="0" perimeter="0" name="NE"/>
<constraint x="0.92" y="0.91" perimeter="0" name="SE"/>
</connections>
<background>
<save/>
<path>
<move x="28.66" y="35.34"/>
<line x="28.66" y="6"/>
<line x="0" y="6"/>
<line x="0" y="35.34"/>
<close/>
<move x="28.66" y="6"/>
<line x="0" y="6"/>
<line x="7.33" y="0"/>
<line x="34.66" y="0"/>
<line x="34.66" y="0"/>
<line x="34.66" y="29.34"/>
<line x="28.66" y="35.34"/>
<close/>
</path>
</background>
<foreground>
<linejoin join="round"/>
<fillstroke/>
<path>
<move x="28.66" y="6"/>
<line x="34.66" y="0"/>
</path>
<stroke/>
<strokecolor color="#ffffff"/>
<strokewidth width="2"/>
<path>
<move x="25" y="29.34"/>
<line x="18.66" y="29.34"/>
<line x="10" y="12"/>
<line x="4.33" y="12"/>
<move x="4" y="29.34"/>
<line x="10" y="29.34"/>
<line x="18.66" y="12"/>
<line x="25" y="12"/>
</path>
<stroke/>
<fillcolor color="#ffffff"/>
<path>
<move x="23" y="9"/>
<line x="23" y="15"/>
<line x="26.66" y="12"/>
<close/>
<move x="23" y="26"/>
<line x="23" y="32.34"/>
<line x="26.66" y="29.34"/>
<close/>
<move x="5.33" y="15.34"/>
<line x="5.33" y="9"/>
<line x="1.66" y="12"/>
<close/>
<move x="5.33" y="32.34"/>
<line x="5.33" y="26.34"/>
<line x="1.66" y="29.34"/>
<close/>
</path>
<fill/>
<restore/>
<rect/>
<stroke/>
<strokecolor color="#ffffff"/>
<path>
<move x="20" y="26"/>
<line x="20" y="14.67"/>
<line x="9" y="14.67"/>
<line x="9" y="26"/>
<close/>
</path>
<fillstroke/>
<fontcolor color="#ffffff"/>
<fontsize size="11"/>
<text str="V" x="14.33" y="19" valign="middle" align="center"/>
</foreground>
</shape>
<shape name="Voice Switch" h="31.67" w="63.67" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="0.98" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="0.98" y="0.5" perimeter="0" name="E"/>
<constraint x="0" y="0.98" perimeter="0" name="SW"/>
<constraint x="1" y="0" perimeter="0" name="NE"/>
</connections>
<background>
<save/>
<path>
<move x="47.67" y="31.67"/>
<line x="47.67" y="15.67"/>
<line x="0" y="15.67"/>
<line x="0" y="31.67"/>
<close/>
<move x="0" y="15.67"/>
<line x="19.34" y="0"/>
<line x="63.67" y="0"/>
<line x="47.67" y="15.67"/>
<close/>
<move x="47.67" y="31.67"/>
<line x="63.67" y="14.34"/>
<line x="63.67" y="0"/>
<line x="47.67" y="15.67"/>
<close/>
</path>
</background>
<foreground>
<linejoin join="round"/>
<fillstroke/>
<restore/>
<fillcolor color="#ffffff"/>
<path>
<move x="28" y="11.67"/>
<line x="27" y="12.34"/>
<line x="15" y="12.34"/>
<line x="14" y="13.67"/>
<line x="10.67" y="12.34"/>
<line x="17.34" y="10.34"/>
<line x="16" y="11.67"/>
<close/>
<move x="37.34" y="6"/>
<line x="36.34" y="6.67"/>
<line x="24.34" y="6.67"/>
<line x="23.34" y="7.67"/>
<line x="20" y="6.34"/>
<line x="26.67" y="4.67"/>
<line x="25.34" y="6"/>
<close/>
<move x="30" y="9.67"/>
<line x="31" y="9"/>
<line x="43" y="9"/>
<line x="44" y="7.67"/>
<line x="47.34" y="9"/>
<line x="40.67" y="11"/>
<line x="42" y="9.67"/>
<close/>
<move x="36.34" y="3.67"/>
<line x="37" y="3"/>
<line x="49" y="3"/>
<line x="50.34" y="2"/>
<line x="53.34" y="3.34"/>
<line x="47" y="5"/>
<line x="48" y="3.67"/>
<close/>
</path>
<fill/>
<strokecolor color="#ffffff"/>
<fillcolor color="#000000"/>
<path>
<move x="29" y="29.34"/>
<line x="29" y="18.34"/>
<line x="17.67" y="18.34"/>
<line x="17.67" y="29.34"/>
<close/>
</path>
<fillstroke/>
<fontcolor color="#ffffff"/>
<fontsize size="13"/>
<text str="V" x="23.33" y="23" valign="middle" align="center"/>
</foreground>
</shape>
<shape name="Workgroup Switch" h="31.66" w="63.67" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="0.98" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="0.98" y="0.5" perimeter="0" name="E"/>
<constraint x="0.16" y="0.23" perimeter="0" name="NW"/>
<constraint x="0" y="0.98" perimeter="0" name="SW"/>
<constraint x="1" y="0" perimeter="0" name="NE"/>
<constraint x="0.87" y="0.74" perimeter="0" name="SE"/>
</connections>
<background>
<save/>
<path>
<move x="48" y="31.66"/>
<line x="48" y="15.66"/>
<line x="0" y="15.66"/>
<line x="0" y="31.66"/>
<close/>
<move x="0" y="15.66"/>
<line x="19.33" y="0"/>
<line x="63.67" y="0"/>
<line x="47.67" y="15.66"/>
<close/>
<move x="48" y="31.66"/>
<line x="63.67" y="14.33"/>
<line x="63.67" y="0"/>
<line x="48" y="15.66"/>
<close/>
</path>
</background>
<foreground>
<linejoin join="round"/>
<fillstroke/>
<restore/>
<rect/>
<stroke/>
<fillcolor color="#ffffff"/>
<path>
<move x="28" y="11.66"/>
<line x="27" y="12.33"/>
<line x="15.33" y="12.33"/>
<line x="14" y="13.66"/>
<line x="10.67" y="12.33"/>
<line x="17.33" y="10.33"/>
<line x="16" y="11.66"/>
<close/>
<move x="37.33" y="5.66"/>
<line x="36.33" y="6.66"/>
<line x="24.67" y="6.66"/>
<line x="23.33" y="7.66"/>
<line x="20" y="6.33"/>
<line x="26.67" y="4.66"/>
<line x="25.33" y="5.66"/>
<close/>
<move x="30" y="9.66"/>
<line x="31" y="9"/>
<line x="43" y="9"/>
<line x="44" y="7.66"/>
<line x="47.33" y="9"/>
<line x="41" y="11"/>
<line x="42" y="9.66"/>
<close/>
<move x="36.33" y="3.66"/>
<line x="37.33" y="3"/>
<line x="49" y="3"/>
<line x="50.33" y="1.66"/>
<line x="53.67" y="3.33"/>
<line x="47" y="5"/>
<line x="48.33" y="3.66"/>
<close/>
</path>
<fill/>
</foreground>
</shape>
</shapes>