<shapes name="mxgraph.electrical.electro-mechanical">
<shape aspect="variable" h="26" name="2-Way Switch" strokewidth="inherit" w="75">
    <connections>
        <constraint name="in" perimeter="0" x="0" y="0.885"/>
        <constraint name="out1" perimeter="0" x="1" y="0.115"/>
        <constraint name="out2" perimeter="0" x="1" y="0.885"/>
    </connections>
    <foreground>
        <path>
            <move x="0" y="23"/>
            <line x="15" y="23"/>
            <move x="60" y="23"/>
            <line x="75" y="23"/>
            <move x="21" y="22"/>
            <line x="57" y="7"/>
            <move x="60" y="3"/>
            <line x="75" y="3"/>
        </path>
        <fillstroke/>
        <ellipse h="6" w="6" x="54" y="20"/>
        <fillstroke/>
        <ellipse h="6" w="6" x="15" y="20"/>
        <fillstroke/>
        <ellipse h="6" w="6" x="54" y="0"/>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="70" name="2 Position Switch" strokewidth="inherit" w="75">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0"/>
        <constraint name="S" perimeter="0" x="0.5" y="1"/>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="E" perimeter="0" x="1" y="0.5"/>
    </connections>
    <foreground>
        <path>
            <move x="0" y="35"/>
            <line x="15" y="35"/>
            <move x="60" y="35"/>
            <line x="75" y="35"/>
            <move x="37.5" y="0"/>
            <line x="37.5" y="12.5"/>
            <move x="37.5" y="57.5"/>
            <line x="37.5" y="70"/>
            <move x="33.5" y="43"/>
            <line x="45.5" y="31.2"/>
            <move x="29.5" y="38.8"/>
            <line x="41.5" y="26.8"/>
        </path>
        <fillstroke/>
        <ellipse h="6" w="6" x="54" y="32"/>
        <fillstroke/>
        <ellipse h="6" w="6" x="15" y="32"/>
        <fillstroke/>
        <ellipse h="6" w="6" x="34.5" y="12.5"/>
        <fillstroke/>
        <ellipse h="6" w="6" x="34.5" y="51.5"/>
        <fillstroke/>
        <path>
            <move x="18" y="35"/>
            <arc large-arc-flag="0" rx="19.5" ry="19.5" sweep-flag="1" x="37.5" x-axis-rotation="0" y="54.5"/>
            <move x="37.5" y="15.5"/>
            <arc large-arc-flag="0" rx="19.5" ry="19.5" sweep-flag="0" x="57" x-axis-rotation="0" y="35"/>
            <move x="47.5" y="10"/>
            <arc large-arc-flag="0" rx="15" ry="15" sweep-flag="1" x="62.5" x-axis-rotation="0" y="25"/>
        </path>
        <stroke/>
        <fillcolor color="#000000"/>
        <path>
            <move x="47.5" y="7"/>
            <line x="42" y="10"/>
            <line x="47.5" y="13"/>
            <close/>
            <move x="59.5" y="25"/>
            <line x="65.5" y="25"/>
            <line x="62.5" y="30.5"/>
            <close/>
        </path>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="60" name="3 Position Switch" strokewidth="inherit" w="60">
    <connections>
        <constraint name="W" perimeter="0" x="0" y="1"/>
        <constraint name="E" perimeter="0" x="1" y="1"/>
        <constraint name="N" perimeter="0" x="0.5" y="0"/>
    </connections>
    <foreground>
        <path>
            <move x="0" y="60"/>
            <line x="12.7" y="49.5"/>
            <move x="47.3" y="49.5"/>
            <line x="60" y="60"/>
            <move x="30" y="0"/>
            <line x="30" y="12.5"/>
        </path>
        <stroke/>
        <ellipse h="6" w="6" x="42" y="44.5"/>
        <fillstroke/>
        <ellipse h="6" w="6" x="12" y="44.5"/>
        <fillstroke/>
        <ellipse h="6" w="6" x="27" y="12.5"/>
        <fillstroke/>
        <path>
            <move x="30" y="15.5"/>
            <arc large-arc-flag="0" rx="40" ry="40" sweep-flag="1" x="15" x-axis-rotation="0" y="47.5"/>
        </path>
        <stroke/>
        <fillcolor color="#000000"/>
        <path>
            <move x="20.57" y="17.75"/>
            <line x="23.3" y="12.12"/>
            <line x="17.08" y="12.87"/>
            <close/>
            <move x="4.48" y="37.75"/>
            <line x="10.4" y="36.77"/>
            <line x="8.34" y="42.69"/>
            <close/>
        </path>
        <fillstroke/>
        <strokecolor color="#000000"/>
        <path>
            <move x="7.5" y="38"/>
            <arc large-arc-flag="0" rx="22.5" ry="22.5" sweep-flag="1" x="19.5" x-axis-rotation="0" y="15"/>
        </path>
        <stroke/>
    </foreground>
</shape>
<shape aspect="variable" h="70" name="4 Position Switch" strokewidth="inherit" w="75">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0"/>
        <constraint name="S" perimeter="0" x="0.5" y="1"/>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="E" perimeter="0" x="1" y="0.5"/>
    </connections>
    <foreground>
        <path>
            <move x="0" y="35"/>
            <line x="15" y="35"/>
            <move x="60" y="35"/>
            <line x="75" y="35"/>
            <move x="37.5" y="0"/>
            <line x="37.5" y="12.5"/>
            <move x="37.5" y="57.5"/>
            <line x="37.5" y="70"/>
            <move x="23.82" y="48.9"/>
            <arc large-arc-flag="0" rx="19.5" ry="19.5" sweep-flag="1" x="51.4" x-axis-rotation="-45.47" y="48.68"/>
            <move x="23.6" y="21.32"/>
            <arc large-arc-flag="0" rx="19.5" ry="19.5" sweep-flag="0" x="51.18" x-axis-rotation="-45.47" y="21.1"/>
            <move x="27.5" y="10"/>
            <arc large-arc-flag="0" rx="15" ry="15" sweep-flag="0" x="12.5" x-axis-rotation="0" y="25"/>
            <move x="40.4" y="43.46"/>
            <line x="40.4" y="26.63"/>
            <move x="34.6" y="43.37"/>
            <line x="34.46" y="26.4"/>
        </path>
        <stroke/>
        <ellipse h="6" w="6" x="54" y="32"/>
        <fillstroke/>
        <ellipse h="6" w="6" x="15" y="32"/>
        <fillstroke/>
        <ellipse h="6" w="6" x="34.5" y="12.5"/>
        <fillstroke/>
        <ellipse h="6" w="6" x="34.5" y="51.5"/>
        <fillstroke/>
        <fillcolor color="#000000"/>
        <path>
            <move x="27.5" y="7"/>
            <line x="33" y="10"/>
            <line x="27.5" y="13"/>
            <close/>
            <move x="15.5" y="25"/>
            <line x="9.5" y="25"/>
            <line x="12.5" y="30.5"/>
            <close/>
        </path>
        <fillstroke/>
        <path>
            <move x="18" y="35"/>
            <line x="57" y="35"/>
        </path>
        <stroke/>
    </foreground>
</shape>
<shape aspect="variable" h="60" name="Bell" strokewidth="inherit" w="47">
    <connections>
        <constraint name="E" perimeter="0" x="1" y="0.5"/>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
    </connections>
    <foreground>
        <path>
            <move x="17" y="0"/>
            <arc large-arc-flag="1" rx="20" ry="20" sweep-flag="1" x="17" x-axis-rotation="0" y="60"/>
            <close/>
            <move x="0" y="30"/>
            <line x="17" y="30"/>
        </path>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="10" name="Break Contact" strokewidth="inherit" w="75">
    <connections>
        <constraint name="W" perimeter="0" x="0" y="1"/>
        <constraint name="E" perimeter="0" x="1" y="1"/>
    </connections>
    <foreground>
        <path>
            <move x="0" y="10"/>
            <line x="18.5" y="10"/>
            <line x="55" y="3"/>
            <move x="50" y="0"/>
            <line x="50" y="10"/>
            <line x="75" y="10"/>
        </path>
        <stroke/>
    </foreground>
</shape>
<shape aspect="variable" h="60" name="Buzzer" strokewidth="inherit" w="45">
    <connections>
        <constraint name="in" perimeter="0" x="0" y="0.5"/>
        <constraint name="out1" perimeter="0" x="1" y="0.335"/>
        <constraint name="out2" perimeter="0" x="1" y="0.665"/>
    </connections>
    <background>
        <path>
            <move x="0" y="0"/>
            <arc large-arc-flag="1" rx="20" ry="20" sweep-flag="1" x="0" x-axis-rotation="0" y="60"/>
            <close/>
            <move x="28" y="20"/>
            <line x="45" y="20"/>
            <move x="28" y="40"/>
            <line x="45" y="40"/>
        </path>
    </background>
    <foreground>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="20" name="Changeover Contact" strokewidth="inherit" w="75">
    <connections>
        <constraint name="W" perimeter="0" x="0" y="1"/>
        <constraint name="NE" perimeter="0" x="1" y="0"/>
        <constraint name="SE" perimeter="0" x="1" y="1"/>
    </connections>
    <foreground>
        <path>
            <move x="60" y="20"/>
            <line x="75" y="20"/>
            <move x="0" y="20"/>
            <line x="18.5" y="20"/>
            <line x="57" y="4"/>
            <move x="75" y="0"/>
            <line x="50" y="0"/>
            <line x="50" y="10"/>
        </path>
        <stroke/>
    </foreground>
</shape>
<shape aspect="variable" h="20" name="Circuit Breaker" strokewidth="inherit" w="75">
    <connections>
        <constraint name="W" perimeter="0" x="0" y="0.8"/>
        <constraint name="E" perimeter="0" x="1" y="0.8"/>
    </connections>
    <foreground>
        <path>
            <move x="60" y="16"/>
            <line x="75" y="16"/>
            <move x="56" y="12"/>
            <line x="64" y="20"/>
            <move x="64" y="12"/>
            <line x="56" y="20"/>
            <move x="0" y="16"/>
            <line x="18.5" y="16"/>
            <line x="57" y="0"/>
        </path>
        <stroke/>
    </foreground>
</shape>
<shape aspect="variable" h="32" name="DPDT" strokewidth="inherit" w="75">
    <connections>
        <constraint name="NW" perimeter="0" x="0" y="0.28"/>
        <constraint name="NE" perimeter="0" x="1" y="0.28"/>
        <constraint name="SW" perimeter="0" x="0" y="0.905"/>
        <constraint name="SE" perimeter="0" x="1" y="0.905"/>
    </connections>
    <foreground>
        <ellipse h="6" w="6" x="54" y="6"/>
        <fillstroke/>
        <ellipse h="6" w="6" x="15" y="6"/>
        <fillstroke/>
        <ellipse h="6" w="6" x="54" y="26"/>
        <fillstroke/>
        <ellipse h="6" w="6" x="15" y="26"/>
        <fillstroke/>
        <ellipse h="6" w="6" x="32.75" y="25.5"/>
        <fillstroke/>
        <ellipse h="6" w="6" x="32.75" y="5.5"/>
        <fillstroke/>
        <path>
            <move x="0" y="9"/>
            <line x="15" y="9"/>
            <move x="0" y="29"/>
            <line x="15" y="29"/>
            <move x="60" y="9"/>
            <line x="75" y="9"/>
            <move x="60" y="29"/>
            <line x="75" y="29"/>
            <move x="38" y="7"/>
            <line x="57" y="0"/>
            <move x="38" y="27"/>
            <line x="57" y="20"/>
        </path>
        <stroke/>
        <dashpattern pattern="5 2"/>
        <dashed dashed="1"/>
        <path>
            <move x="47" y="4"/>
            <line x="47" y="24"/>
        </path>
        <stroke/>
    </foreground>
</shape>
<shape aspect="variable" h="62.25" name="DPDT2" strokewidth="inherit" w="57.75">
    <connections>
        <constraint name="NW" perimeter="0" x="0" y="0.227"/>
        <constraint name="NE" perimeter="0" x="1" y="0.05"/>
        <constraint name="E1" perimeter="0" x="1" y="0.39"/>
        <constraint name="E2" perimeter="0" x="1" y="0.61"/>
        <constraint name="SW" perimeter="0" x="0" y="0.793"/>
        <constraint name="SE" perimeter="0" x="1" y="0.95"/>
    </connections>
    <foreground>
        <rect/>
        <stroke/>
        <ellipse h="6" w="6" x="36.75" y="21.25"/>
        <fillstroke/>
        <ellipse h="6" w="6" x="15.25" y="11.25"/>
        <fillstroke/>
        <path>
            <move x="0" y="14.25"/>
            <line x="15" y="14.25"/>
            <move x="42.75" y="24.25"/>
            <line x="57.75" y="24.25"/>
            <move x="20.75" y="13.5"/>
            <line x="39.75" y="6.5"/>
            <move x="42.75" y="3"/>
            <line x="57.75" y="3"/>
            <move x="0.25" y="49.25"/>
            <line x="15.25" y="49.25"/>
            <move x="20.75" y="48.5"/>
            <line x="39.75" y="41.5"/>
            <move x="42.75" y="59.25"/>
            <line x="57.75" y="59.25"/>
            <move x="42.75" y="38"/>
            <line x="57.75" y="38"/>
        </path>
        <stroke/>
        <ellipse h="6" w="6" x="36.75" y="0"/>
        <fillstroke/>
        <ellipse h="6" w="6" x="15.25" y="46.25"/>
        <fillstroke/>
        <ellipse h="6" w="6" x="36.75" y="56.25"/>
        <fillstroke/>
        <ellipse h="6" w="6" x="36.75" y="35"/>
        <fillstroke/>
        <dashpattern pattern="5 2"/>
        <dashed dashed="1"/>
        <path>
            <move x="29.75" y="10.25"/>
            <line x="29.75" y="45.25"/>
        </path>
        <stroke/>
    </foreground>
</shape>
<shape aspect="variable" h="26" name="DPST" strokewidth="inherit" w="75">
    <connections>
        <constraint name="NW" perimeter="0" x="0" y="0.115"/>
        <constraint name="SW" perimeter="0" x="0" y="0.885"/>
        <constraint name="NE" perimeter="0" x="1" y="0.115"/>
        <constraint name="SE" perimeter="0" x="1" y="0.885"/>
    </connections>
    <foreground>
        <path>
            <move x="0" y="3"/>
            <line x="75" y="3"/>
            <move x="0" y="23"/>
            <line x="75" y="23"/>
        </path>
        <stroke/>
        <ellipse h="6" w="6" x="54" y="0"/>
        <fillstroke/>
        <ellipse h="6" w="6" x="15" y="0"/>
        <fillstroke/>
        <ellipse h="6" w="6" x="54" y="20"/>
        <fillstroke/>
        <ellipse h="6" w="6" x="15" y="20"/>
        <fillstroke/>
        <dashpattern pattern="5 2"/>
        <dashed dashed="1"/>
        <path>
            <move x="37" y="3"/>
            <line x="37" y="23"/>
        </path>
        <stroke/>
    </foreground>
</shape>
<shape aspect="variable" h="31" name="Flow Actuated" strokewidth="inherit" w="75">
    <connections>
        <constraint name="W" perimeter="0" x="0" y="0.095"/>
        <constraint name="E" perimeter="0" x="1" y="0.095"/>
    </connections>
    <foreground>
        <path>
            <move x="0" y="3"/>
            <line x="15" y="3"/>
            <move x="60" y="3"/>
            <line x="75" y="3"/>
            <move x="21" y="4"/>
            <line x="57" y="19"/>
        </path>
        <stroke/>
        <ellipse h="6" w="6" x="54" y="0"/>
        <fillstroke/>
        <ellipse h="6" w="6" x="15" y="0"/>
        <fillstroke/>
        <path>
            <move x="37" y="10.5"/>
            <line x="37" y="31"/>
            <line x="45" y="31"/>
            <line x="37" y="20"/>
        </path>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="16" name="Fuse" strokewidth="inherit" w="75">
    <connections>
        <constraint name="W" perimeter="0" x="0" y="1"/>
        <constraint name="E" perimeter="0" x="1" y="1"/>
    </connections>
    <foreground>
        <path>
            <move x="26" y="9"/>
            <line x="29" y="15.5"/>
            <line x="45.2" y="8.8"/>
            <line x="42.2" y="2.4"/>
            <close/>
        </path>
        <fillstroke/>
        <path>
            <move x="60" y="16"/>
            <line x="75" y="16"/>
            <move x="0" y="16"/>
            <line x="18.5" y="16"/>
            <line x="57" y="0"/>
        </path>
        <stroke/>
    </foreground>
</shape>
<shape aspect="variable" h="32" name="Gas Flow Actuated" strokewidth="inherit" w="75">
    <connections>
        <constraint name="W" perimeter="0" x="0" y="1"/>
        <constraint name="E" perimeter="0" x="1" y="1"/>
    </connections>
    <foreground>
        <path>
            <move x="60" y="32"/>
            <line x="75" y="32"/>
            <move x="0" y="32"/>
            <line x="18.5" y="32"/>
            <line x="57" y="16"/>
        </path>
        <stroke/>
        <rect h="12" w="12" x="31" y="0"/>
        <fillstroke/>
        <path>
            <move x="40" y="10"/>
            <line x="40" y="3"/>
            <line x="34" y="3"/>
            <line x="34" y="8"/>
            <line x="40" y="8"/>
        </path>
        <stroke/>
        <ellipse h="2" w="2" x="35" y="5"/>
        <stroke/>
        <dashpattern pattern="5 2"/>
        <dashed dashed="1"/>
        <path>
            <move x="37" y="24.5"/>
            <line x="37" y="12"/>
        </path>
        <stroke/>
    </foreground>
</shape>
<shape aspect="variable" h="19" name="Inertia Switch" strokewidth="inherit" w="75">
    <connections>
        <constraint name="W" perimeter="0" x="0" y="0.84"/>
        <constraint name="E" perimeter="0" x="1" y="0.84"/>
    </connections>
    <foreground>
        <path>
            <move x="0" y="16"/>
            <line x="15" y="16"/>
            <move x="60" y="16"/>
            <line x="75" y="16"/>
            <move x="21" y="15"/>
            <line x="57" y="0"/>
            <move x="32" y="5"/>
            <line x="37" y="5"/>
            <line x="37" y="8.5"/>
        </path>
        <stroke/>
        <ellipse h="6" w="6" x="54" y="13"/>
        <fillstroke/>
        <ellipse h="6" w="6" x="15" y="13"/>
        <fillstroke/>
        <ellipse h="6" w="6" x="26" y="2"/>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="20" name="Isolator" strokewidth="inherit" w="75">
    <connections>
        <constraint name="W" perimeter="0" x="0" y="0.8"/>
        <constraint name="E" perimeter="0" x="1" y="0.8"/>
    </connections>
    <foreground>
        <path>
            <move x="60" y="16"/>
            <line x="75" y="16"/>
            <move x="0" y="16"/>
            <line x="18.5" y="16"/>
            <line x="57" y="0"/>
            <move x="60" y="12"/>
            <line x="60" y="20"/>
        </path>
        <stroke/>
    </foreground>
</shape>
<shape aspect="variable" h="16" name="Limit Switch" strokewidth="inherit" w="75">
    <connections>
        <constraint name="W" perimeter="0" x="0" y="1"/>
        <constraint name="E" perimeter="0" x="1" y="1"/>
    </connections>
    <foreground>
        <path>
            <move x="60" y="16"/>
            <line x="75" y="16"/>
            <move x="43" y="6"/>
            <line x="41" y="1"/>
            <line x="35" y="9.2"/>
            <move x="0" y="16"/>
            <line x="18.5" y="16"/>
            <line x="57" y="0"/>
        </path>
        <stroke/>
    </foreground>
</shape>
<shape aspect="variable" h="13" name="Limit Switch NC" strokewidth="inherit" w="75">
    <connections>
        <constraint name="W" perimeter="0" x="0" y="0.31"/>
        <constraint name="E" perimeter="0" x="1" y="0.31"/>
    </connections>
    <foreground>
        <path>
            <move x="0" y="4"/>
            <line x="15" y="4"/>
            <move x="60" y="4"/>
            <line x="75" y="4"/>
            <move x="57" y="0"/>
            <line x="18" y="4"/>
            <line x="46" y="13"/>
            <line x="54" y="0.5"/>
        </path>
        <stroke/>
        <ellipse h="6" w="6" x="54" y="1"/>
        <fillstroke/>
        <ellipse h="6" w="6" x="15" y="1"/>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="24.3" name="Limit Switch NO" strokewidth="inherit" w="75">
    <connections>
        <constraint name="W" perimeter="0" x="0" y="0.12"/>
        <constraint name="E" perimeter="0" x="1" y="0.12"/>
    </connections>
    <foreground>
        <path>
            <move x="0" y="3"/>
            <line x="15" y="3"/>
            <move x="60" y="3"/>
            <line x="75" y="3"/>
            <move x="54.2" y="17.9"/>
            <line x="18" y="3"/>
            <line x="38.5" y="24.3"/>
            <line x="51.5" y="17.1"/>
        </path>
        <stroke/>
        <ellipse h="6" w="6" x="54" y="0"/>
        <fillstroke/>
        <ellipse h="6" w="6" x="15" y="0"/>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="28" name="Liquid Level Actuated" strokewidth="inherit" w="75">
    <connections>
        <constraint name="W" perimeter="0" x="0" y="0.14"/>
        <constraint name="E" perimeter="0" x="1" y="0.14"/>
    </connections>
    <foreground>
        <path>
            <move x="0" y="4"/>
            <line x="15" y="4"/>
            <move x="60" y="4"/>
            <line x="75" y="4"/>
            <move x="21" y="3"/>
            <line x="57" y="0"/>
            <move x="37" y="2"/>
            <line x="37" y="20"/>
        </path>
        <fillstroke/>
        <ellipse h="6" w="6" x="54" y="1"/>
        <fillstroke/>
        <ellipse h="6" w="6" x="15" y="1"/>
        <fillstroke/>
        <ellipse h="8" w="8" x="33" y="20"/>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="32" name="Liquid Level Actuated 2" strokewidth="inherit" w="75">
    <connections>
        <constraint name="W" perimeter="0" x="0" y="1"/>
        <constraint name="E" perimeter="0" x="1" y="1"/>
    </connections>
    <foreground>
        <path>
            <move x="60" y="32"/>
            <line x="75" y="32"/>
            <move x="0" y="32"/>
            <line x="18.5" y="32"/>
            <line x="57" y="16"/>
        </path>
        <stroke/>
        <rect h="12" w="12" x="31" y="0"/>
        <fillstroke/>
        <path>
            <move x="34" y="8"/>
            <arc large-arc-flag="0" rx="4" ry="4" sweep-flag="1" x="40" x-axis-rotation="0" y="8"/>
            <arc large-arc-flag="0" rx="4" ry="4" sweep-flag="1" x="34" x-axis-rotation="0" y="8"/>
            <close/>
            <move x="37" y="4"/>
            <line x="37" y="6.5"/>
        </path>
        <stroke/>
        <dashpattern pattern="5 2"/>
        <dashed dashed="1"/>
        <path>
            <move x="37" y="24.5"/>
            <line x="37" y="12"/>
        </path>
        <stroke/>
    </foreground>
</shape>
<shape aspect="variable" h="50" name="Loudspeaker" strokewidth="inherit" w="25">
    <connections>
        <constraint name="W1" perimeter="0" x="0" y="0.4"/>
        <constraint name="W2" perimeter="0" x="0" y="0.6"/>
    </connections>
    <background>
        <path>
            <move x="25" y="50"/>
            <line x="25" y="0"/>
            <line x="15" y="15"/>
            <line x="10" y="15"/>
            <line x="10" y="35"/>
            <line x="15" y="35"/>
            <close/>
            <move x="0" y="30"/>
            <line x="10" y="30"/>
            <move x="0" y="20"/>
            <line x="10" y="20"/>
        </path>
    </background>
    <foreground>
        <fillstroke/>
        <path>
            <move x="15" y="15"/>
            <line x="15" y="35"/>
        </path>
        <stroke/>
    </foreground>
</shape>
<shape aspect="variable" h="16" name="Make Contact" strokewidth="inherit" w="75">
    <connections>
        <constraint name="W" perimeter="0" x="0" y="1"/>
        <constraint name="E" perimeter="0" x="1" y="1"/>
    </connections>
    <foreground>
        <path>
            <move x="60" y="16"/>
            <line x="75" y="16"/>
            <move x="0" y="16"/>
            <line x="18.5" y="16"/>
            <line x="57" y="0"/>
        </path>
        <stroke/>
    </foreground>
</shape>
<shape aspect="variable" h="18" name="Manual Switch" strokewidth="inherit" w="75">
    <connections>
        <constraint name="W" perimeter="0" x="0" y="1"/>
        <constraint name="E" perimeter="0" x="1" y="1"/>
    </connections>
    <foreground>
        <path>
            <move x="60" y="18"/>
            <line x="75" y="18"/>
            <move x="31" y="0"/>
            <line x="43" y="0"/>
            <move x="0" y="18"/>
            <line x="18.5" y="18"/>
            <line x="57" y="2"/>
        </path>
        <stroke/>
        <dashpattern pattern="5 2"/>
        <dashed dashed="1"/>
        <path>
            <move x="37" y="10.5"/>
            <line x="37" y="0"/>
        </path>
        <stroke/>
    </foreground>
</shape>
<shape aspect="variable" h="80" name="Mercury Switch" strokewidth="inherit" w="80">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0"/>
        <constraint name="S" perimeter="0" x="0.5" y="1"/>
    </connections>
    <foreground>
        <path>
            <move x="40" y="0"/>
            <line x="40" y="80"/>
            <move x="23" y="48"/>
            <arc large-arc-flag="0" rx="8" ry="8" sweep-flag="1" x="15" x-axis-rotation="0" y="40"/>
            <arc large-arc-flag="0" rx="8" ry="8" sweep-flag="1" x="23" x-axis-rotation="0" y="32"/>
            <line x="57" y="32"/>
            <arc large-arc-flag="0" rx="8" ry="8" sweep-flag="1" x="65" x-axis-rotation="0" y="40"/>
            <arc large-arc-flag="0" rx="8" ry="8" sweep-flag="1" x="57" x-axis-rotation="0" y="48"/>
            <close/>
        </path>
        <stroke/>
        <ellipse h="8" w="8" x="0" y="0"/>
        <stroke/>
        <ellipse h="8" w="8" x="72" y="0"/>
        <stroke/>
        <ellipse h="8" w="8" x="72" y="72"/>
        <stroke/>
        <ellipse h="8" w="8" x="0" y="72"/>
        <stroke/>
    </foreground>
</shape>
<shape aspect="variable" h="90" name="Mercury Switch 2" strokewidth="inherit" w="50">
    <connections>
        <constraint name="NW" perimeter="0" x="0.1" y="0"/>
        <constraint name="NE" perimeter="0" x="0.9" y="0"/>
        <constraint name="SW" perimeter="0" x="0.1" y="1"/>
        <constraint name="SE" perimeter="0" x="0.9" y="1"/>
    </connections>
    <foreground>
        <path>
            <move x="25" y="5"/>
            <line x="25" y="85"/>
            <move x="8" y="53"/>
            <arc large-arc-flag="0" rx="8" ry="8" sweep-flag="1" x="0" x-axis-rotation="0" y="45"/>
            <arc large-arc-flag="0" rx="8" ry="8" sweep-flag="1" x="8" x-axis-rotation="0" y="37"/>
            <line x="42" y="37"/>
            <arc large-arc-flag="0" rx="8" ry="8" sweep-flag="1" x="50" x-axis-rotation="0" y="45"/>
            <arc large-arc-flag="0" rx="8" ry="8" sweep-flag="1" x="42" x-axis-rotation="0" y="53"/>
            <close/>
            <move x="5" y="0"/>
            <line x="5" y="19"/>
            <move x="45" y="0"/>
            <line x="45" y="19"/>
            <move x="45" y="71"/>
            <line x="45" y="90"/>
            <move x="5" y="71"/>
            <line x="5" y="90"/>
        </path>
        <stroke/>
    </foreground>
</shape>
<shape aspect="variable" h="60" name="Motor 1" strokewidth="inherit" w="100">
    <connections>
        <constraint name="in" perimeter="0" x="0" y="0.5"/>
        <constraint name="out" perimeter="0" x="1" y="0.5"/>
    </connections>
    <background>
        <ellipse h="60" w="60" x="20" y="0"/>
    </background>
    <foreground>
        <fillstroke/>
        <path>
            <move x="0" y="30"/>
            <line x="20" y="30"/>
            <move x="80" y="30"/>
            <line x="100" y="30"/>
        </path>
        <stroke/>
        <fontsize size="30"/>
        <fontstyle style="0"/>
        <fontcolor color="#000000"/>
        <text align="center" str="M" valign="bottom" x="50" y="45"/>
    </foreground>
</shape>
<shape aspect="variable" h="60.09" name="Motor 2" strokewidth="inherit" w="100">
    <connections>
        <constraint name="in" perimeter="0" x="0" y="0.5"/>
        <constraint name="out" perimeter="0" x="1" y="0.5"/>
    </connections>
    <background>
        <path>
            <move x="15" y="20.04"/>
            <line x="21.7" y="20.04"/>
            <arc large-arc-flag="0" rx="30" ry="30" sweep-flag="1" x="78.3" x-axis-rotation="0" y="20.04"/>
            <line x="85" y="20.04"/>
            <line x="85" y="40.04"/>
            <line x="78.3" y="40.04"/>
            <arc large-arc-flag="0" rx="30" ry="30" sweep-flag="1" x="21.7" x-axis-rotation="0" y="40.04"/>
            <line x="15" y="40.04"/>
            <close/>
        </path>
    </background>
    <foreground>
        <fillstroke/>
        <path>
            <move x="0" y="30.04"/>
            <line x="15" y="30.04"/>
            <move x="85" y="30.04"/>
            <line x="100" y="30.04"/>
            <move x="21.7" y="20.04"/>
            <arc large-arc-flag="0" rx="30" ry="30" sweep-flag="0" x="21.7" x-axis-rotation="0" y="40.04"/>
            <move x="78.3" y="20.04"/>
            <arc large-arc-flag="0" rx="30" ry="30" sweep-flag="1" x="78.3" x-axis-rotation="0" y="40.04"/>
        </path>
        <stroke/>
    </foreground>
</shape>
<shape aspect="variable" h="23" name="Passing Make Contact" strokewidth="inherit" w="75">
    <connections>
        <constraint name="W" perimeter="0" x="0" y="0.7"/>
        <constraint name="E" perimeter="0" x="1" y="0.7"/>
    </connections>
    <foreground>
        <path>
            <move x="0" y="16"/>
            <line x="18.5" y="16"/>
            <line x="57" y="0"/>
            <move x="75" y="16"/>
            <line x="58" y="16"/>
            <line x="70" y="23"/>
        </path>
        <stroke/>
    </foreground>
</shape>
<shape aspect="variable" h="40" name="Piezo Sounder" strokewidth="inherit" w="100">
    <connections>
        <constraint name="in" perimeter="0" x="0" y="0.5"/>
        <constraint name="out" perimeter="0" x="1" y="0.5"/>
    </connections>
    <background>
        <path>
            <move x="0" y="20"/>
            <line x="39" y="20"/>
            <move x="61" y="20"/>
            <line x="100" y="20"/>
            <move x="39" y="0"/>
            <line x="39" y="40"/>
            <move x="61" y="0"/>
            <line x="61" y="40"/>
        </path>
    </background>
    <foreground>
        <stroke/>
        <rect h="40" w="14" x="43" y="0"/>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="100" name="Pilot Light" strokewidth="inherit" w="100">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0.15"/>
        <constraint name="S" perimeter="0" x="0.5" y="0.85"/>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="E" perimeter="0" x="1" y="0.5"/>
        <constraint name="NW" perimeter="0" x="0.18" y="0"/>
        <constraint name="NE" perimeter="0" x="0.82" y="0"/>
        <constraint name="SW" perimeter="0" x="0.18" y="1"/>
        <constraint name="SE" perimeter="0" x="0.82" y="1"/>
    </connections>
    <foreground>
        <path>
            <move x="0" y="50"/>
            <line x="100" y="50"/>
            <move x="18" y="0"/>
            <line x="82" y="100"/>
            <move x="82" y="0"/>
            <line x="18" y="100"/>
        </path>
        <stroke/>
        <ellipse h="70" w="70" x="15" y="15"/>
        <stroke/>
    </foreground>
</shape>
<shape aspect="variable" h="31" name="Pressure Actuated" strokewidth="inherit" w="75">
    <connections>
        <constraint name="W" perimeter="0" x="0" y="0.1"/>
        <constraint name="E" perimeter="0" x="1" y="0.1"/>
    </connections>
    <foreground>
        <path>
            <move x="0" y="3"/>
            <line x="15" y="3"/>
            <move x="60" y="3"/>
            <line x="75" y="3"/>
            <move x="21" y="4"/>
            <line x="57" y="19"/>
            <move x="37" y="10.5"/>
            <line x="37" y="23"/>
        </path>
        <stroke/>
        <ellipse h="6" w="6" x="54" y="0"/>
        <fillstroke/>
        <ellipse h="6" w="6" x="15" y="0"/>
        <fillstroke/>
        <path>
            <move x="29" y="31"/>
            <arc large-arc-flag="0" rx="8" ry="8" sweep-flag="1" x="45" x-axis-rotation="0" y="31"/>
            <close/>
        </path>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="55" name="Proximity Limit Switch" strokewidth="inherit" w="75">
    <connections>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="E" perimeter="0" x="1" y="0.5"/>
    </connections>
    <foreground>
        <path>
            <move x="0" y="27.5"/>
            <line x="15" y="27.5"/>
            <move x="60" y="27.5"/>
            <line x="75" y="27.5"/>
            <move x="57" y="23.5"/>
            <line x="18" y="27.5"/>
            <line x="46" y="36.5"/>
            <line x="54" y="24"/>
            <move x="10" y="27.5"/>
            <line x="37.5" y="0"/>
            <line x="65" y="27.5"/>
            <line x="37.5" y="55"/>
            <close/>
        </path>
        <stroke/>
        <ellipse h="6" w="6" x="54" y="24.5"/>
        <fillstroke/>
        <ellipse h="6" w="6" x="15" y="24.5"/>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="73" name="Pushbutton 2 Circuit" strokewidth="inherit" w="75">
    <connections>
        <constraint name="W" perimeter="0" x="0" y="0.685"/>
        <constraint name="SW" perimeter="0" x="0" y="0.96"/>
        <constraint name="E" perimeter="0" x="1" y="0.685"/>
        <constraint name="SE" perimeter="0" x="1" y="0.96"/>
    </connections>
    <foreground>
        <path>
            <move x="0" y="50"/>
            <line x="15" y="50"/>
            <move x="60" y="50"/>
            <line x="75" y="50"/>
            <move x="18" y="54"/>
            <line x="57" y="54"/>
            <move x="37" y="0"/>
            <line x="37" y="54"/>
            <move x="60" y="70"/>
            <line x="75" y="70"/>
            <move x="0" y="70"/>
            <line x="15" y="70"/>
        </path>
        <stroke/>
        <ellipse h="6" w="6" x="54" y="47"/>
        <fillstroke/>
        <ellipse h="6" w="6" x="15" y="47"/>
        <fillstroke/>
        <ellipse h="6" w="6" x="54" y="67"/>
        <fillstroke/>
        <ellipse h="6" w="6" x="15" y="67"/>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="54" name="Pushbutton Break" strokewidth="inherit" w="75">
    <connections>
        <constraint name="W" perimeter="0" x="0" y="0.925"/>
        <constraint name="E" perimeter="0" x="1" y="0.925"/>
    </connections>
    <foreground>
        <path>
            <move x="0" y="50"/>
            <line x="15" y="50"/>
            <move x="60" y="50"/>
            <line x="75" y="50"/>
            <move x="18" y="54"/>
            <line x="57" y="54"/>
            <move x="37" y="0"/>
            <line x="37" y="54"/>
        </path>
        <stroke/>
        <ellipse h="6" w="6" x="54" y="47"/>
        <fillstroke/>
        <ellipse h="6" w="6" x="15" y="47"/>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="53" name="Pushbutton Make" strokewidth="inherit" w="75">
    <connections>
        <constraint name="W" perimeter="0" x="0" y="0.945"/>
        <constraint name="E" perimeter="0" x="1" y="0.945"/>
    </connections>
    <foreground>
        <path>
            <move x="0" y="50"/>
            <line x="15" y="50"/>
            <move x="60" y="50"/>
            <line x="75" y="50"/>
            <move x="18" y="42"/>
            <line x="57" y="42"/>
            <move x="37.5" y="0"/>
            <line x="37.5" y="42"/>
        </path>
        <fillstroke/>
        <ellipse h="6" w="6" x="54" y="47"/>
        <fillstroke/>
        <ellipse h="6" w="6" x="15" y="47"/>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="10" name="Push Switch NC" strokewidth="inherit" w="75">
    <connections>
        <constraint name="W" perimeter="0" x="0" y="0.6"/>
        <constraint name="E" perimeter="0" x="1" y="0.6"/>
    </connections>
    <foreground>
        <path>
            <move x="0" y="6"/>
            <line x="17" y="6"/>
            <move x="58" y="6"/>
            <line x="75" y="6"/>
            <move x="18" y="10"/>
            <line x="59" y="10"/>
            <move x="37.5" y="0"/>
            <line x="37.5" y="10"/>
            <move x="35" y="0"/>
            <line x="40" y="0"/>
        </path>
        <fillstroke/>
        <ellipse h="6" w="6" x="54" y="3"/>
        <fillstroke/>
        <ellipse h="6" w="6" x="17" y="3"/>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="19" name="Push Switch NO" strokewidth="inherit" w="75">
    <connections>
        <constraint name="in" perimeter="0" x="0" y="0.84"/>
        <constraint name="out" perimeter="0" x="1" y="0.84"/>
    </connections>
    <foreground>
        <path>
            <move x="0" y="16"/>
            <line x="17" y="16"/>
            <move x="58" y="16"/>
            <line x="75" y="16"/>
            <move x="18" y="10"/>
            <line x="59" y="10"/>
            <move x="37.5" y="0"/>
            <line x="37.5" y="10"/>
            <move x="35" y="0"/>
            <line x="40" y="0"/>
        </path>
        <fillstroke/>
        <ellipse h="6" w="6" x="54" y="13"/>
        <fillstroke/>
        <ellipse h="6" w="6" x="17" y="13"/>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="20" name="Reed Switch" strokewidth="inherit" w="75">
    <connections>
        <constraint name="in" perimeter="0" x="0" y="0.5"/>
        <constraint name="out" perimeter="0" x="1" y="0.5"/>
    </connections>
    <background>
        <roundrect arcsize="50" h="20" w="48" x="13.5" y="0"/>
    </background>
    <foreground>
        <fillstroke/>
        <path>
            <move x="49" y="10"/>
            <line x="75" y="10"/>
            <move x="0" y="10"/>
            <line x="26" y="10"/>
            <line x="49" y="5"/>
        </path>
        <stroke/>
    </foreground>
</shape>
<shape aspect="variable" h="50" name="Relay" strokewidth="inherit" w="99">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0"/>
        <constraint name="S" perimeter="0" x="0.5" y="1"/>
    </connections>
    <foreground>
        <rect h="30" w="99" x="0" y="10"/>
        <fillstroke/>
        <path>
            <move x="49.5" y="0"/>
            <line x="49.5" y="10"/>
            <move x="49.5" y="40"/>
            <line x="49.5" y="50"/>
            <move x="25.5" y="10"/>
            <line x="25.5" y="40"/>
            <move x="5.5" y="22"/>
            <arc large-arc-flag="0" rx="4" ry="9" sweep-flag="1" x="12.5" x-axis-rotation="0" y="25"/>
            <arc large-arc-flag="0" rx="4" ry="9" sweep-flag="0" x="19.5" x-axis-rotation="0" y="28"/>
        </path>
        <stroke/>
    </foreground>
</shape>
<shape aspect="variable" h="100" name="Relay Changeover Contact" strokewidth="inherit" w="50">
    <connections>
        <constraint name="in" perimeter="0" x="0.5" y="1"/>
        <constraint name="out1" perimeter="0" x="0.1" y="0"/>
        <constraint name="out2" perimeter="0" x="0.9" y="0"/>
    </connections>
    <foreground>
        <path>
            <move x="45" y="0"/>
            <line x="45" y="17"/>
            <move x="25" y="83"/>
            <line x="25" y="100"/>
            <move x="5" y="0"/>
            <line x="5" y="17"/>
            <move x="40" y="25"/>
            <line x="27" y="73.5"/>
        </path>
        <fillstroke/>
        <ellipse h="10" w="10" x="20" y="73"/>
        <fillstroke/>
        <ellipse h="10" w="10" x="40" y="17"/>
        <fillstroke/>
        <ellipse h="10" w="10" x="0" y="17"/>
        <fillstroke/>
        <strokewidth width="2"/>
    </foreground>
</shape>
<shape aspect="variable" h="50" name="Relay Coil" strokewidth="inherit" w="99">
    <connections>
        <constraint name="in" perimeter="0" x="0.5" y="0"/>
        <constraint name="out" perimeter="0" x="0.5" y="1"/>
    </connections>
    <background>
        <rect h="30" w="99" x="0" y="10"/>
    </background>
    <foreground>
        <fillstroke/>
        <path>
            <move x="49.5" y="0"/>
            <line x="49.5" y="10"/>
            <move x="49.5" y="40"/>
            <line x="49.5" y="50"/>
        </path>
        <stroke/>
    </foreground>
</shape>
<shape aspect="variable" h="70" name="Relay Coil 2" strokewidth="inherit" w="100">
    <connections>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="E" perimeter="0" x="1" y="0.5"/>
    </connections>
    <foreground>
        <ellipse h="70" w="70" x="15" y="0"/>
        <stroke/>
        <path>
            <move x="0" y="35"/>
            <line x="15" y="35"/>
            <move x="85" y="35"/>
            <line x="100" y="35"/>
        </path>
        <stroke/>
    </foreground>
</shape>
<shape aspect="variable" h="24" name="Relay Contacts" strokewidth="inherit" w="30">
    <connections>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="E" perimeter="0" x="1" y="0.5"/>
    </connections>
    <foreground>
        <path>
            <move x="10" y="0"/>
            <line x="10" y="24"/>
            <move x="20" y="0"/>
            <line x="20" y="24"/>
            <move x="20" y="12"/>
            <line x="30" y="12"/>
            <move x="10" y="12"/>
            <line x="0" y="12"/>
        </path>
        <stroke/>
    </foreground>
</shape>
<shape aspect="variable" h="100" name="Relay Contact NC" strokewidth="inherit" w="10">
    <connections>
        <constraint name="in" perimeter="0" x="0.5" y="0"/>
        <constraint name="out" perimeter="0" x="0.5" y="1"/>
    </connections>
    <foreground>
        <path>
            <move x="5" y="0"/>
            <line x="5" y="17"/>
            <move x="5" y="83"/>
            <line x="5" y="100"/>
            <move x="10" y="24"/>
            <line x="7" y="73.5"/>
        </path>
        <fillstroke/>
        <ellipse h="10" w="10" x="0" y="73"/>
        <fillstroke/>
        <ellipse h="10" w="10" x="0" y="17"/>
        <fillstroke/>
        <strokewidth width="2"/>
    </foreground>
</shape>
<shape aspect="variable" h="100" name="Relay Contact NO" strokewidth="inherit" w="20">
    <connections>
        <constraint name="in" perimeter="0" x="0.25" y="0"/>
        <constraint name="out" perimeter="0" x="0.25" y="1"/>
    </connections>
    <foreground>
        <path>
            <move x="5" y="0"/>
            <line x="5" y="17"/>
            <move x="5" y="83"/>
            <line x="5" y="100"/>
            <move x="20" y="26.5"/>
            <line x="7" y="73.5"/>
        </path>
        <fillstroke/>
        <ellipse h="10" w="10" x="0" y="73"/>
        <fillstroke/>
        <ellipse h="10" w="10" x="0" y="17"/>
        <fillstroke/>
        <strokewidth width="2"/>
    </foreground>
</shape>
<shape aspect="fixed" h="5" name="Relay Pole" strokewidth="inherit" w="100">
    <connections>
        <constraint name="in" perimeter="0" x="0" y="0.5"/>
        <constraint name="out" perimeter="0" x="1" y="0.5"/>
    </connections>
    <foreground>
        <path>
            <move x="0" y="2.5"/>
            <line x="20" y="2.5"/>
            <move x="40" y="2.5"/>
            <line x="60" y="2.5"/>
            <move x="80" y="2.5"/>
            <line x="100" y="2.5"/>
        </path>
        <stroke/>
    </foreground>
</shape>
<shape aspect="variable" h="50" name="Resonator" strokewidth="inherit" w="100">
    <connections>
        <constraint name="in" perimeter="0" x="0" y="0.6"/>
        <constraint name="out" perimeter="0" x="1" y="0.6"/>
        <constraint name="control" perimeter="0" x="0.5" y="0"/>
    </connections>
    <background>
        <path>
            <move x="50" y="0"/>
            <line x="50" y="10"/>
            <move x="0" y="30"/>
            <line x="39" y="30"/>
            <move x="61" y="30"/>
            <line x="100" y="30"/>
            <move x="39" y="10"/>
            <line x="39" y="50"/>
            <move x="61" y="10"/>
            <line x="61" y="50"/>
        </path>
    </background>
    <foreground>
        <stroke/>
        <rect h="40" w="14" x="43" y="10"/>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="37" name="Safety Interlock" strokewidth="inherit" w="75">
    <connections>
        <constraint name="W" perimeter="0" x="0" y="0.405"/>
        <constraint name="E" perimeter="0" x="1" y="0.405"/>
    </connections>
    <foreground>
        <path>
            <move x="0" y="15"/>
            <line x="15" y="15"/>
            <move x="60" y="15"/>
            <line x="75" y="15"/>
        </path>
        <stroke/>
        <ellipse h="6" w="6" x="54" y="12"/>
        <fillstroke/>
        <ellipse h="6" w="6" x="15" y="12"/>
        <fillstroke/>
        <path>
            <move x="19" y="0"/>
            <line x="56" y="0"/>
            <line x="37.5" y="37"/>
            <close/>
        </path>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="66" name="Selector Switch" strokewidth="inherit" w="75">
    <connections>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="NE" perimeter="0" x="1" y="0.045"/>
        <constraint name="E1" perimeter="0" x="1" y="0.35"/>
        <constraint name="E2" perimeter="0" x="1" y="0.65"/>
        <constraint name="SE" perimeter="0" x="1" y="0.955"/>
    </connections>
    <foreground>
        <path>
            <move x="0" y="33"/>
            <line x="15" y="33"/>
            <move x="60" y="43"/>
            <line x="75" y="43"/>
            <move x="60" y="23"/>
            <line x="75" y="23"/>
            <move x="51" y="3"/>
            <line x="75" y="3"/>
            <move x="51" y="63"/>
            <line x="75" y="63"/>
            <move x="18" y="33"/>
            <line x="47" y="4"/>
        </path>
        <stroke/>
        <ellipse h="6" w="6" x="54" y="40"/>
        <fillstroke/>
        <ellipse h="6" w="6" x="54" y="20"/>
        <fillstroke/>
        <ellipse h="6" w="6" x="45" y="0"/>
        <fillstroke/>
        <ellipse h="6" w="6" x="45" y="60"/>
        <fillstroke/>
        <ellipse h="6" w="6" x="15" y="30"/>
        <fillstroke/>
        <fillcolor color="#000000"/>
        <path>
            <move x="40.67" y="6.33"/>
            <line x="47" y="4"/>
            <line x="45" y="10"/>
            <close/>
        </path>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="46" name="Selector Switch 3 position" strokewidth="inherit" w="75">
    <connections>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="NE" perimeter="0" x="1" y="0.065"/>
        <constraint name="E" perimeter="0" x="1" y="0.5"/>
        <constraint name="SE" perimeter="0" x="1" y="0.935"/>
    </connections>
    <foreground>
        <path>
            <move x="0" y="23"/>
            <line x="15" y="23"/>
            <move x="55.4" y="23"/>
            <line x="75" y="23"/>
            <move x="51" y="3"/>
            <line x="75" y="3"/>
            <move x="51" y="43"/>
            <line x="75" y="43"/>
            <move x="18.592" y="22.68"/>
            <line x="46.465" y="3.877"/>
        </path>
        <stroke/>
        <ellipse h="6" w="6" x="50" y="20"/>
        <fillstroke/>
        <ellipse h="6" w="6" x="45" y="0"/>
        <fillstroke/>
        <ellipse h="6" w="6" x="45" y="40"/>
        <fillstroke/>
        <ellipse h="6" w="6" x="15" y="20"/>
        <fillstroke/>
        <fillcolor color="#000000"/>
        <path>
            <move x="43.268" y="9.436"/>
            <line x="39.746" y="4.984"/>
            <line x="46.416" y="3.95"/>
            <close/>
        </path>
        <fill/>
    </foreground>
</shape>
<shape aspect="variable" h="106" name="Selector Switch 6 position" strokewidth="inherit" w="74.85">
    <connections>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="NE" perimeter="0" x="1" y="0.025"/>
        <constraint name="E1" perimeter="0" x="1" y="0.22"/>
        <constraint name="E2" perimeter="0" x="1" y="0.405"/>
        <constraint name="E3" perimeter="0" x="1" y="0.595"/>
        <constraint name="E4" perimeter="0" x="1" y="0.78"/>
        <constraint name="SE" perimeter="0" x="1" y="0.97"/>
    </connections>
    <background>
        <path>
            <move x="0" y="53"/>
            <line x="15" y="53"/>
            <move x="60" y="63"/>
            <line x="74.85" y="63"/>
            <move x="60" y="43"/>
            <line x="74.85" y="43"/>
            <move x="51" y="23"/>
            <line x="74.85" y="23"/>
            <move x="28.99" y="3"/>
            <line x="74.85" y="3"/>
            <move x="28.99" y="103"/>
            <line x="74.85" y="103"/>
            <move x="51" y="83"/>
            <line x="74.85" y="83"/>
            <move x="18" y="53"/>
            <line x="47" y="24"/>
        </path>
    </background>
    <foreground>
        <stroke/>
        <ellipse h="6" w="6" x="54" y="60"/>
        <fillstroke/>
        <ellipse h="6" w="6" x="54" y="40"/>
        <fillstroke/>
        <ellipse h="6" w="6" x="45" y="20"/>
        <fillstroke/>
        <ellipse h="6" w="6" x="23" y="0"/>
        <fillstroke/>
        <ellipse h="6" w="6" x="23" y="100"/>
        <fillstroke/>
        <ellipse h="6" w="6" x="45" y="80"/>
        <fillstroke/>
        <ellipse h="6" w="6" x="15" y="50"/>
        <fillstroke/>
        <fillcolor color="#000000"/>
        <path>
            <move x="47" y="24"/>
            <line x="45" y="30"/>
            <line x="40.67" y="26.34"/>
            <close/>
        </path>
        <fill/>
    </foreground>
</shape>
<shape aspect="variable" h="66" name="Shorting Selector" strokewidth="inherit" w="60">
    <connections>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="NE" perimeter="0" x="0.85" y="0.045"/>
        <constraint name="E1" perimeter="0" x="1" y="0.35"/>
        <constraint name="E2" perimeter="0" x="1" y="0.65"/>
        <constraint name="SE" perimeter="0" x="0.85" y="0.955"/>
    </connections>
    <foreground>
        <path>
            <move x="0" y="33"/>
            <line x="15" y="33"/>
            <move x="18" y="33"/>
            <line x="43" y="8"/>
        </path>
        <stroke/>
        <ellipse h="6" w="6" x="54" y="40"/>
        <fillstroke/>
        <ellipse h="6" w="6" x="54" y="20"/>
        <fillstroke/>
        <ellipse h="6" w="6" x="45" y="0"/>
        <fillstroke/>
        <ellipse h="6" w="6" x="45" y="60"/>
        <fillstroke/>
        <ellipse h="6" w="6" x="15" y="30"/>
        <fillstroke/>
        <path>
            <move x="37" y="6"/>
            <line x="37" y="3"/>
            <arc large-arc-flag="0" rx="12" ry="12" sweep-flag="1" x="49" x-axis-rotation="0" y="12"/>
            <line x="46" y="13"/>
            <arc large-arc-flag="0" rx="9" ry="9" sweep-flag="0" x="37" x-axis-rotation="0" y="6"/>
            <close/>
        </path>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="19" name="Simple Switch" strokewidth="inherit" w="75">
    <connections>
        <constraint name="in" perimeter="0" x="0" y="0.84"/>
        <constraint name="out" perimeter="0" x="1" y="0.84"/>
    </connections>
    <foreground>
        <path>
            <move x="0" y="16"/>
            <line x="15" y="16"/>
            <move x="60" y="16"/>
            <line x="75" y="16"/>
            <move x="21" y="15"/>
            <line x="57" y="0"/>
        </path>
        <stroke/>
        <ellipse h="6" w="6" x="54" y="13"/>
        <fillstroke/>
        <ellipse h="6" w="6" x="15" y="13"/>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="10" name="Spring Return" strokewidth="inherit" w="75">
    <connections>
        <constraint name="W" perimeter="0" x="0" y="0"/>
        <constraint name="E" perimeter="0" x="1" y="0"/>
    </connections>
    <foreground>
        <path>
            <move x="0" y="0"/>
            <line x="18.5" y="0"/>
            <line x="57" y="10"/>
        </path>
        <stroke/>
        <path>
            <move x="75" y="0"/>
            <line x="47" y="0"/>
            <line x="52" y="8"/>
            <line x="57" y="0"/>
        </path>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="19" name="Spring Return 2" strokewidth="inherit" w="75">
    <connections>
        <constraint name="W" perimeter="0" x="0" y="0.845"/>
        <constraint name="E" perimeter="0" x="1" y="0.845"/>
    </connections>
    <foreground>
        <path>
            <move x="75" y="16"/>
            <line x="47" y="16"/>
            <line x="52" y="8"/>
            <line x="57" y="16"/>
        </path>
        <fillstroke/>
        <path>
            <move x="0" y="16"/>
            <line x="18.5" y="16"/>
            <line x="57" y="0"/>
        </path>
        <stroke/>
        <ellipse h="6" w="6" x="15.5" y="13"/>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="7" name="Stay Put" strokewidth="inherit" w="75">
    <connections>
        <constraint name="W" perimeter="0" x="0" y="0.43"/>
        <constraint name="E" perimeter="0" x="1" y="0.43"/>
    </connections>
    <foreground>
        <path>
            <move x="0" y="3"/>
            <line x="15" y="3"/>
            <move x="60" y="3"/>
            <line x="75" y="3"/>
            <move x="21" y="4"/>
            <line x="57" y="7"/>
        </path>
        <stroke/>
        <ellipse h="6" w="6" x="54" y="0"/>
        <fillstroke/>
        <ellipse h="6" w="6" x="15" y="0"/>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="fixed" h="16" name="Switch Contact" strokewidth="inherit" w="16">
    <connections>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="E" perimeter="0" x="1" y="0.5"/>
    </connections>
    <background>
        <ellipse h="16" w="16" x="0" y="0"/>
    </background>
    <foreground>
        <strokewidth width="2"/>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="19" name="Switch Disconnector" strokewidth="inherit" w="75">
    <connections>
        <constraint name="W" perimeter="0" x="0" y="0.845"/>
        <constraint name="E" perimeter="0" x="1" y="0.845"/>
    </connections>
    <foreground>
        <path>
            <move x="60" y="16"/>
            <line x="75" y="16"/>
            <move x="60" y="13"/>
            <line x="60" y="19"/>
            <move x="0" y="16"/>
            <line x="18.5" y="16"/>
            <line x="57" y="0"/>
        </path>
        <stroke/>
        <ellipse h="6" w="6" x="54" y="13"/>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="31" name="Temperature Actuated" strokewidth="inherit" w="75">
    <connections>
        <constraint name="W" perimeter="0" x="0" y="0.1"/>
        <constraint name="E" perimeter="0" x="1" y="0.1"/>
    </connections>
    <foreground>
        <path>
            <move x="0" y="3"/>
            <line x="15" y="3"/>
            <move x="60" y="3"/>
            <line x="75" y="3"/>
            <move x="21" y="4"/>
            <line x="57" y="19"/>
            <move x="37" y="31"/>
            <line x="37" y="25"/>
            <line x="42" y="25"/>
            <line x="42" y="20"/>
            <line x="37" y="20"/>
            <line x="37" y="10.5"/>
        </path>
        <stroke/>
        <ellipse h="6" w="6" x="54" y="0"/>
        <fillstroke/>
        <ellipse h="6" w="6" x="15" y="0"/>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="18" name="Temperature Switch" strokewidth="inherit" w="75">
    <connections>
        <constraint name="W" perimeter="0" x="0" y="1"/>
        <constraint name="E" perimeter="0" x="1" y="1"/>
    </connections>
    <foreground>
        <ellipse h="4" w="8" x="32" y="0"/>
        <fillstroke/>
        <path>
            <move x="60" y="18"/>
            <line x="75" y="18"/>
            <move x="36" y="0"/>
            <line x="36" y="4"/>
            <move x="0" y="18"/>
            <line x="18.5" y="18"/>
            <line x="57" y="2"/>
        </path>
        <stroke/>
    </foreground>
</shape>
<shape aspect="variable" h="7" name="Thermostat" strokewidth="inherit" w="75">
    <connections>
        <constraint name="W" perimeter="0" x="0" y="0.57"/>
        <constraint name="E" perimeter="0" x="1" y="0.57"/>
    </connections>
    <foreground>
        <path>
            <move x="0" y="4"/>
            <line x="15" y="4"/>
            <move x="60" y="4"/>
            <line x="75" y="4"/>
            <move x="21" y="3"/>
            <line x="57" y="0"/>
        </path>
        <stroke/>
        <ellipse h="6" w="6" x="54" y="1"/>
        <fillstroke/>
        <ellipse h="6" w="6" x="15" y="1"/>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="32" name="Time Delay Break" strokewidth="inherit" w="75">
    <connections>
        <constraint name="W" perimeter="0" x="0" y="0.125"/>
        <constraint name="E" perimeter="0" x="1" y="0.125"/>
    </connections>
    <foreground>
        <path>
            <move x="0" y="4"/>
            <line x="15" y="4"/>
            <move x="60" y="4"/>
            <line x="75" y="4"/>
            <move x="21" y="3"/>
            <line x="57" y="0"/>
            <move x="32" y="32"/>
            <line x="37" y="26"/>
            <line x="42" y="32"/>
            <move x="37" y="26"/>
            <line x="37" y="2"/>
        </path>
        <stroke/>
        <ellipse h="6" w="6" x="54" y="1"/>
        <fillstroke/>
        <ellipse h="6" w="6" x="15" y="1"/>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="30" name="Time Delay Break 2" strokewidth="inherit" w="75">
    <connections>
        <constraint name="W" perimeter="0" x="0" y="0.33"/>
        <constraint name="E" perimeter="0" x="1" y="0.33"/>
    </connections>
    <foreground>
        <path>
            <move x="30" y="30"/>
            <arc large-arc-flag="0" rx="8" ry="8" sweep-flag="1" x="45" x-axis-rotation="0" y="30"/>
            <move x="40" y="25"/>
            <line x="40" y="6"/>
            <move x="35" y="25"/>
            <line x="35" y="7"/>
            <move x="0" y="10"/>
            <line x="18.5" y="10"/>
            <line x="55" y="3"/>
            <move x="50" y="0"/>
            <line x="50" y="10"/>
            <line x="75" y="10"/>
        </path>
        <stroke/>
    </foreground>
</shape>
<shape aspect="variable" h="31" name="Time Delay Make" strokewidth="inherit" w="75">
    <connections>
        <constraint name="W" perimeter="0" x="0" y="0.1"/>
        <constraint name="E" perimeter="0" x="1" y="0.1"/>
    </connections>
    <foreground>
        <path>
            <move x="0" y="3"/>
            <line x="15" y="3"/>
            <move x="60" y="3"/>
            <line x="75" y="3"/>
            <move x="21" y="4"/>
            <line x="57" y="19"/>
            <move x="32" y="31"/>
            <line x="37" y="25"/>
            <line x="42" y="31"/>
            <move x="37" y="25"/>
            <line x="37" y="10.5"/>
        </path>
        <stroke/>
        <ellipse h="6" w="6" x="54" y="0"/>
        <fillstroke/>
        <ellipse h="6" w="6" x="15" y="0"/>
        <fillstroke/>
        <stroke/>
    </foreground>
</shape>
<shape aspect="variable" h="36" name="Time Delay Make 2" strokewidth="inherit" w="75">
    <connections>
        <constraint name="W" perimeter="0" x="0" y="0.445"/>
        <constraint name="E" perimeter="0" x="1" y="0.445"/>
    </connections>
    <foreground>
        <path>
            <move x="60" y="16"/>
            <line x="75" y="16"/>
            <move x="0" y="16"/>
            <line x="18.5" y="16"/>
            <line x="57" y="0"/>
            <move x="30" y="36"/>
            <arc large-arc-flag="0" rx="8" ry="8" sweep-flag="1" x="45" x-axis-rotation="0" y="36"/>
            <move x="40" y="31"/>
            <line x="40" y="7"/>
            <move x="35" y="31"/>
            <line x="35" y="9"/>
        </path>
        <stroke/>
    </foreground>
</shape>
<shape aspect="variable" h="30" name="Two Way Contact" strokewidth="inherit" w="75">
    <connections>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="NE" perimeter="0" x="1" y="0"/>
        <constraint name="SE" perimeter="0" x="1" y="1"/>
    </connections>
    <foreground>
        <path>
            <move x="50" y="0"/>
            <line x="75" y="0"/>
            <move x="50" y="30"/>
            <line x="75" y="30"/>
            <move x="0" y="15"/>
            <line x="50" y="15"/>
        </path>
        <stroke/>
        <ellipse h="6" w="6" x="15.5" y="12"/>
        <fillstroke/>
        <stroke/>
    </foreground>
</shape>
</shapes>