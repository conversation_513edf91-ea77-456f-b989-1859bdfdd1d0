<shapes name="mxgraph.cisco.storage">
<shape name="Cisco File Engine" h="40.33" w="56.67" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
<constraint x="0.08" y="0.08" perimeter="0" name="NW"/>
<constraint x="0" y="1" perimeter="0" name="SW"/>
<constraint x="1" y="0" perimeter="0" name="NE"/>
<constraint x="0.96" y="0.87" perimeter="0" name="SE"/>
</connections>
<background>
<save/>
<path>
<move x="0" y="40.33"/>
<line x="51" y="40.33"/>
<line x="51" y="7"/>
<line x="0" y="7"/>
<close/>
<move x="56.67" y="31.66"/>
<line x="51" y="40.33"/>
<line x="51" y="7"/>
<line x="56.67" y="0"/>
<close/>
<move x="8.67" y="0"/>
<line x="56.67" y="0"/>
<line x="51" y="7"/>
<line x="0" y="7"/>
<close/>
</path>
</background>
<foreground>
<linejoin join="round"/>
<fillstroke/>
<fillcolor color="#ffffff"/>
<path>
<move x="7.33" y="15.66"/>
<line x="3" y="15.66"/>
<line x="3" y="14.33"/>
<line x="7.33" y="14.33"/>
<line x="7.33" y="12.33"/>
<line x="12.33" y="15"/>
<line x="7.33" y="18"/>
<close/>
<move x="7.33" y="24.33"/>
<line x="3" y="24.33"/>
<line x="3" y="22.66"/>
<line x="7.33" y="22.66"/>
<line x="7.33" y="20.66"/>
<line x="12.33" y="23.66"/>
<line x="7.33" y="26.33"/>
<close/>
<move x="7.33" y="32.66"/>
<line x="3" y="32.66"/>
<line x="3" y="31.33"/>
<line x="7.33" y="31.33"/>
<line x="7.33" y="29"/>
<line x="12.33" y="32"/>
<line x="7.33" y="34.66"/>
<close/>
<move x="42" y="23"/>
<line x="48" y="23"/>
<line x="48" y="24.33"/>
<line x="42" y="24.33"/>
<line x="42" y="26.66"/>
<line x="37" y="23.66"/>
<line x="42" y="21"/>
<close/>
<move x="45.33" y="24.33"/>
<line x="39.67" y="24.33"/>
<line x="39.67" y="23"/>
<line x="45.33" y="23"/>
<line x="45.33" y="20.66"/>
<line x="50.33" y="23.66"/>
<line x="45.33" y="26.33"/>
<close/>
</path>
<fill/>
<restore/>
<rect/>
<stroke/>
<strokecolor color="#ffffff"/>
<strokewidth width="0.67"/>
<path>
<move x="17" y="26.66"/>
<curve x1="35.67" y1="26.66" x2="35.67" y2="26.66" x3="35.67" y3="26.66"/>
<curve x1="35.67" y1="16" x2="35.67" y2="16" x3="35.67" y3="16"/>
<curve x1="35.67" y1="15.33" x2="34.33" y2="15.33" x3="34.33" y3="15.33"/>
<curve x1="25" y1="15.33" x2="25" y2="15.33" x3="25" y3="15.33"/>
<curve x1="25" y1="13.33" x2="25" y2="13.33" x3="25" y3="13.33"/>
<curve x1="18.67" y1="13.33" x2="18.67" y2="13.33" x3="18.67" y3="13.33"/>
<curve x1="18.67" y1="15" x2="18.67" y2="15" x3="18.67" y3="15"/>
<curve x1="17" y1="15" x2="17" y2="15" x3="17" y3="15"/>
<close/>
</path>
<fillstroke/>
<path>
<move x="15.67" y="28.33"/>
<curve x1="34.33" y1="28.33" x2="34.33" y2="28.33" x3="34.33" y3="28.33"/>
<curve x1="34.33" y1="17.66" x2="34.33" y2="17.66" x3="34.33" y3="17.66"/>
<curve x1="34.33" y1="17" x2="32.67" y2="17" x3="32.67" y3="17"/>
<curve x1="23.33" y1="17" x2="23.33" y2="17" x3="23.33" y3="17"/>
<curve x1="23.33" y1="15" x2="23.33" y2="15" x3="23.33" y3="15"/>
<curve x1="17.33" y1="15" x2="17.33" y2="15" x3="17.33" y3="15"/>
<curve x1="17.33" y1="16.66" x2="17.33" y2="16.66" x3="17.33" y3="16.66"/>
<curve x1="15.67" y1="16.66" x2="15.67" y2="16.66" x3="15.67" y3="16.66"/>
<close/>
</path>
<fillstroke/>
<path>
<move x="13.67" y="30"/>
<curve x1="32.33" y1="30" x2="32.33" y2="30" x3="32.33" y3="30"/>
<curve x1="32.33" y1="19.66" x2="32.33" y2="19.66" x3="32.33" y3="19.66"/>
<curve x1="32.33" y1="18.66" x2="31" y2="19" x3="31" y3="19"/>
<curve x1="21.67" y1="19" x2="21.67" y2="19" x3="21.67" y3="19"/>
<curve x1="21.67" y1="17" x2="21.67" y2="17" x3="21.67" y3="17"/>
<curve x1="15.33" y1="17" x2="15.33" y2="17" x3="15.33" y3="17"/>
<curve x1="15.33" y1="18.33" x2="15.33" y2="18.33" x3="15.33" y3="18.33"/>
<curve x1="13.67" y1="18.33" x2="13.67" y2="18.33" x3="13.67" y3="18.33"/>
<close/>
</path>
<fillstroke/>
</foreground>
</shape>
<shape name="Cloud" h="66.66" w="116.34" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0.05" perimeter="0" name="N"/>
<constraint x="0.5" y="0.99" perimeter="0" name="S"/>
<constraint x="0.04" y="0.5" perimeter="0" name="W"/>
<constraint x="0.98" y="0.5" perimeter="0" name="E"/>
<constraint x="0.2" y="0.17" perimeter="0" name="NW"/>
<constraint x="0.2" y="0.87" perimeter="0" name="SW"/>
<constraint x="0.89" y="0.2" perimeter="0" name="NE"/>
<constraint x="0.89" y="0.87" perimeter="0" name="SE"/>
</connections>
<background>
<path>
<move x="42.67" y="10.33"/>
<curve x1="23.67" y1="6.66" x2="13.34" y2="16.33" x3="15" y3="25"/>
<curve x1="15" y1="25.33" x2="15" y2="25.33" x3="15" y3="25.33"/>
<curve x1="0" y1="28" x2="4" y2="45.66" x3="11.34" y3="45.66"/>
<curve x1="12" y1="45.66" x2="12" y2="45.66" x3="12" y3="45.66"/>
<curve x1="10" y1="54" x2="27.67" y2="62.33" x3="37.34" y3="58"/>
<curve x1="38" y1="57.33" x2="38" y2="57.33" x3="38" y3="57.33"/>
<curve x1="41.34" y1="65" x2="54" y2="66" x3="66" y3="66.33"/>
<curve x1="76.67" y1="66.66" x2="82.67" y2="66" x3="87.34" y3="61.33"/>
<curve x1="88" y1="61.33" x2="88" y2="61.33" x3="88" y3="61.33"/>
<curve x1="105" y1="62.66" x2="112.67" y2="51" x3="109" y3="42.66"/>
<curve x1="110" y1="42.33" x2="110" y2="42.33" x3="110" y3="42.33"/>
<curve x1="115.67" y1="41" x2="116.34" y2="27.66" x3="106.34" y3="25.66"/>
<curve x1="106.67" y1="25" x2="106.67" y2="25" x3="106.67" y3="25"/>
<curve x1="111" y1="16.33" x2="100.67" y2="8.33" x3="86.67" y3="9.66"/>
<curve x1="85.67" y1="9.33" x2="85.67" y2="9.33" x3="85.67" y3="9.33"/>
<curve x1="75.67" y1="0" x2="46.67" y2="1.66" x3="43.34" y3="10.66"/>
<close/>
</path>
</background>
<foreground>
<fillstroke/>
</foreground>
</shape>
<shape name="Diskette" h="31.33" w="32" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
<constraint x="0" y="0" perimeter="0" name="NW"/>
<constraint x="0.02" y="0.98" perimeter="0" name="SW"/>
<constraint x="1" y="0" perimeter="0" name="NE"/>
<constraint x="0.98" y="0.99" perimeter="0" name="SE"/>
</connections>
<background>
<path>
<move x="32" y="0"/>
<line x="31" y="0.33"/>
<line x="0" y="0.33"/>
<line x="0.67" y="0"/>
<close/>
<move x="32" y="30.67"/>
<line x="32" y="0"/>
<line x="31" y="0"/>
<line x="31" y="31.33"/>
<close/>
<move x="1.33" y="31.33"/>
<line x="31" y="31.33"/>
<line x="31" y="0.33"/>
<line x="0" y="0.33"/>
<line x="0" y="30"/>
<close/>
</path>
</background>
<foreground>
<linejoin join="round"/>
<fillstroke/>
<strokewidth width="0.67"/>
<path>
<move x="28.67" y="0.33"/>
<curve x1="28.67" y1="18.33" x2="28.67" y2="18.33" x3="28.67" y3="18.33"/>
<curve x1="28.67" y1="19.67" x2="27.67" y2="19.33" x3="27.67" y3="19.33"/>
<curve x1="3.33" y1="19.33" x2="3.33" y2="19.33" x3="3.33" y3="19.33"/>
<curve x1="3.33" y1="19.33" x2="2.33" y2="19.33" x3="2.33" y3="18.33"/>
<curve x1="2.33" y1="0.33" x2="2.33" y2="0.33" x3="2.33" y3="0.33"/>
</path>
<stroke/>
<save/>
<path>
<move x="1.67" y="3.67"/>
<line x="1.67" y="2"/>
<line x="0.33" y="2"/>
<line x="0.33" y="3.67"/>
<close/>
<move x="30.67" y="3.67"/>
<line x="30.67" y="2"/>
<line x="29.33" y="2"/>
<line x="29.33" y="3.67"/>
<close/>
</path>
<stroke/>
<fillcolor color="#ffffff"/>
<path>
<move x="23.67" y="22.33"/>
<curve x1="23.67" y1="31" x2="23.67" y2="31" x3="23.67" y3="31"/>
<curve x1="5" y1="31" x2="5" y2="31" x3="5" y3="31"/>
<curve x1="5" y1="22.33" x2="5" y2="22.33" x3="5" y3="22.33"/>
<curve x1="5" y1="21.33" x2="6.33" y2="21.33" x3="6.33" y3="21.33"/>
<curve x1="22.67" y1="21.33" x2="22.67" y2="21.33" x3="22.67" y3="21.33"/>
<curve x1="23.67" y1="21.33" x2="23.67" y2="22.33" x3="23.67" y3="22.33"/>
<close/>
</path>
<fillstroke/>
<path>
<move x="28.67" y="31.33"/>
<curve x1="28.67" y1="22.33" x2="28.67" y2="22.33" x3="28.67" y3="22.33"/>
<curve x1="28.67" y1="22.33" x2="28.67" y2="21.33" x3="27.67" y3="21.33"/>
<curve x1="6.33" y1="21.33" x2="6.33" y2="21.33" x3="6.33" y3="21.33"/>
<curve x1="6.33" y1="21.33" x2="5" y2="21.33" x3="5" y3="22.33"/>
<curve x1="5" y1="31" x2="5" y2="31" x3="5" y3="31"/>
</path>
<stroke/>
<restore/>
<path>
<move x="12.33" y="30.67"/>
<line x="12.33" y="22"/>
<line x="8.67" y="22"/>
<line x="8.67" y="30.67"/>
<close/>
</path>
<fill/>
<path>
<move x="31" y="0.33"/>
<line x="32" y="0"/>
</path>
<stroke/>
</foreground>
</shape>
<shape name="FC Storage" h="27.33" w="48" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
<constraint x="0.04" y="0.06" perimeter="0" name="NW"/>
<constraint x="0" y="1" perimeter="0" name="SW"/>
<constraint x="1" y="0" perimeter="0" name="NE"/>
<constraint x="0.97" y="0.94" perimeter="0" name="SE"/>
</connections>
<background>
<save/>
<path>
<move x="0" y="3.66"/>
<line x="3.67" y="0"/>
<line x="48" y="0"/>
<line x="44.33" y="3.66"/>
<close/>
<move x="44.33" y="27.33"/>
<line x="44.33" y="3.66"/>
<line x="0" y="3.66"/>
<line x="0" y="27.33"/>
<close/>
<move x="44.33" y="27.33"/>
<line x="48" y="24.66"/>
<line x="48" y="0"/>
<line x="44.33" y="3.66"/>
<close/>
</path>
</background>
<foreground>
<linejoin join="round"/>
<fillstroke/>
<restore/>
<rect/>
<stroke/>
<strokewidth width="0.67"/>
<strokecolor color="#ffffff"/>
<path>
<move x="3.67" y="11.66"/>
<line x="3.67" y="19.66"/>
<line x="4.67" y="20.66"/>
<line x="12.67" y="20.66"/>
<line x="13.67" y="19.66"/>
<line x="13.67" y="11.66"/>
<line x="12.67" y="10.66"/>
<line x="4.67" y="10.66"/>
<line x="3.67" y="11.66"/>
<move x="3.67" y="11.66"/>
<line x="4.67" y="12.66"/>
<line x="9" y="13.33"/>
<line x="12.67" y="12.66"/>
<line x="13.67" y="11.66"/>
<move x="17.33" y="11.66"/>
<line x="17.33" y="19.66"/>
<line x="18" y="20.66"/>
<line x="26.33" y="20.66"/>
<line x="27" y="19.66"/>
<line x="27" y="11.66"/>
<line x="26.33" y="10.66"/>
<line x="18" y="10.66"/>
<line x="17.33" y="11.66"/>
<move x="17.33" y="11.66"/>
<line x="18" y="12.66"/>
<line x="22.67" y="13.33"/>
<line x="26.33" y="12.66"/>
<line x="27" y="11.66"/>
<move x="30.67" y="12.66"/>
<line x="30.67" y="19.66"/>
<line x="31.67" y="20.66"/>
<line x="36" y="21.66"/>
<line x="39.67" y="20.66"/>
<line x="40.67" y="19.66"/>
<line x="40.67" y="12.66"/>
<line x="39.67" y="11.66"/>
<line x="36" y="10.66"/>
<line x="31.67" y="11.66"/>
<line x="30.67" y="12.66"/>
<move x="30.67" y="12.66"/>
<line x="31.67" y="13.33"/>
<line x="36" y="13.33"/>
<line x="39.67" y="13.33"/>
<line x="40.67" y="12.66"/>
</path>
<stroke/>
</foreground>
</shape>
<shape name="Fibre Channel Disk Subsystem" h="39" w="27" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
<constraint x="0.11" y="0.09" perimeter="0" name="NW"/>
<constraint x="0" y="1" perimeter="0" name="SW"/>
<constraint x="1" y="0" perimeter="0" name="NE"/>
<constraint x="0.87" y="0.93" perimeter="0" name="SE"/>
</connections>
<background>
<path>
<move x="20.34" y="6.33"/>
<line x="0" y="6.33"/>
<line x="0" y="39"/>
<line x="20.34" y="39"/>
<close/>
<move x="20.34" y="39"/>
<line x="27" y="32.66"/>
<line x="27" y="0"/>
<line x="6.67" y="0"/>
<line x="0" y="6.33"/>
<line x="20.34" y="6.33"/>
<line x="20.34" y="39"/>
<close/>
<move x="20.34" y="6.33"/>
<line x="27" y="0"/>
</path>
</background>
<foreground>
<linejoin join="round"/>
<fillstroke/>
<strokecolor color="#000000"/>
<strokewidth width="0.4"/>
<fillcolor color="#ffffff"/>
<linecap cap="round"/>
<path>
<move x="9.67" y="31.33"/>
<curve x1="9.67" y1="32" x2="8" y2="32.66" x3="5.67" y3="32.66"/>
<curve x1="3.67" y1="32.66" x2="2" y2="32" x3="2" y3="31.33"/>
<curve x1="2" y1="30.33" x2="3.67" y2="29.66" x3="5.67" y3="29.66"/>
<curve x1="8" y1="29.66" x2="9.67" y2="30.33" x3="9.67" y3="31.33"/>
<close/>
</path>
<fillstroke/>
<strokecolor color="none"/>
<fillcolor color="#000000"/>
<linejoin join="miter"/>
<linecap cap="butt"/>
<path>
<move x="7" y="31"/>
<curve x1="7" y1="31.33" x2="6.34" y2="31.66" x3="5.67" y3="31.66"/>
<curve x1="5" y1="31.66" x2="4.67" y2="31.33" x3="4.67" y3="31"/>
<curve x1="4.67" y1="31" x2="5" y2="30.66" x3="5.67" y3="30.66"/>
<curve x1="6.34" y1="30.66" x2="7" y2="31" x3="7" y3="31"/>
<close/>
</path>
<fillstroke/>
<save/>
<path>
<move x="7" y="30.33"/>
<curve x1="7" y1="30.66" x2="6.34" y2="30.66" x3="5.67" y3="30.66"/>
<curve x1="5" y1="30.66" x2="4.67" y2="30.66" x3="4.67" y3="30.33"/>
<curve x1="4.67" y1="30" x2="5" y2="30" x3="5.67" y3="30"/>
<curve x1="6.34" y1="30" x2="7" y2="30" x3="7" y3="30.33"/>
<close/>
</path>
<fillstroke/>
<strokecolor color="#000000"/>
<fillcolor color="#ffffff"/>
<linejoin join="round"/>
<linecap cap="round"/>
<path>
<move x="9.67" y="29.66"/>
<curve x1="9.67" y1="30.33" x2="8" y2="31" x3="5.67" y3="31"/>
<curve x1="3.67" y1="31" x2="2" y2="30.33" x3="2" y3="29.66"/>
<curve x1="2" y1="28.66" x2="3.67" y2="28.33" x3="5.67" y3="28.33"/>
<curve x1="8" y1="28.33" x2="9.67" y2="28.66" x3="9.67" y3="29.66"/>
<close/>
</path>
<fillstroke/>
<restore/>
<fillcolor color="#000000"/>
<linejoin join="miter"/>
<linecap cap="butt"/>
<path>
<move x="7" y="29.66"/>
<curve x1="7" y1="29.66" x2="6.34" y2="30" x3="5.67" y3="30"/>
<curve x1="5" y1="30" x2="4.67" y2="29.66" x3="4.67" y3="29.66"/>
<curve x1="4.67" y1="29.33" x2="5" y2="29" x3="5.67" y3="29"/>
<curve x1="6.34" y1="29" x2="7" y2="29.33" x3="7" y3="29.66"/>
<close/>
</path>
<fillstroke/>
<save/>
<path>
<move x="7" y="28.66"/>
<curve x1="7" y1="29" x2="6.34" y2="29.33" x3="5.67" y3="29.33"/>
<curve x1="5" y1="29.33" x2="4.67" y2="29" x3="4.67" y3="28.66"/>
<curve x1="4.67" y1="28.66" x2="5" y2="28.33" x3="5.67" y3="28.33"/>
<curve x1="6.34" y1="28.33" x2="7" y2="28.66" x3="7" y3="28.66"/>
<close/>
</path>
<fillstroke/>
<strokecolor color="#000000"/>
<fillcolor color="#ffffff"/>
<linejoin join="round"/>
<linecap cap="round"/>
<path>
<move x="9.67" y="28"/>
<curve x1="9.67" y1="29" x2="8" y2="29.66" x3="5.67" y3="29.66"/>
<curve x1="3.67" y1="29.66" x2="2" y2="29" x3="2" y3="28"/>
<curve x1="2" y1="27.33" x2="3.67" y2="26.66" x3="5.67" y3="26.66"/>
<curve x1="8" y1="26.66" x2="9.67" y2="27.33" x3="9.67" y3="28"/>
<close/>
</path>
<fillstroke/>
<restore/>
<fillcolor color="#000000"/>
<linejoin join="miter"/>
<linecap cap="butt"/>
<path>
<move x="7" y="28"/>
<curve x1="7" y1="28.33" x2="6.34" y2="28.33" x3="5.67" y3="28.33"/>
<curve x1="5" y1="28.33" x2="4.67" y2="28.33" x3="4.67" y3="28"/>
<curve x1="4.67" y1="27.66" x2="5" y2="27.66" x3="5.67" y3="27.66"/>
<curve x1="6.34" y1="27.66" x2="7" y2="27.66" x3="7" y3="28"/>
<close/>
</path>
<fillstroke/>
<save/>
<strokecolor color="#000000"/>
<fillcolor color="#ffffff"/>
<linejoin join="round"/>
<linecap cap="round"/>
<path>
<move x="9.67" y="25"/>
<curve x1="9.67" y1="25.66" x2="8" y2="26.33" x3="5.67" y3="26.33"/>
<curve x1="3.67" y1="26.33" x2="2" y2="25.66" x3="2" y3="25"/>
<curve x1="2" y1="24" x2="3.67" y2="23.33" x3="5.67" y3="23.33"/>
<curve x1="8" y1="23.33" x2="9.67" y2="24" x3="9.67" y3="25"/>
<close/>
</path>
<fillstroke/>
<restore/>
<strokecolor color="none"/>
<fillcolor color="#000000"/>
<strokecolor color="none"/>
<fillcolor color="#000000"/>
<linejoin join="miter"/>
<linecap cap="butt"/>
<path>
<move x="7" y="24.66"/>
<curve x1="7" y1="25" x2="6.34" y2="25.33" x3="5.67" y3="25.33"/>
<curve x1="5" y1="25.33" x2="4.67" y2="25" x3="4.67" y3="24.66"/>
<curve x1="4.67" y1="24.66" x2="5" y2="24.33" x3="5.67" y3="24.33"/>
<curve x1="6.34" y1="24.33" x2="7" y2="24.66" x3="7" y3="24.66"/>
<close/>
</path>
<fillstroke/>
<save/>
<path>
<move x="7" y="24"/>
<curve x1="7" y1="24.33" x2="6.34" y2="24.33" x3="5.67" y3="24.33"/>
<curve x1="5" y1="24.33" x2="4.67" y2="24.33" x3="4.67" y3="24"/>
<curve x1="4.67" y1="23.66" x2="5" y2="23.66" x3="5.67" y3="23.66"/>
<curve x1="6.34" y1="23.66" x2="7" y2="23.66" x3="7" y3="24"/>
<close/>
</path>
<fillstroke/>
<strokecolor color="#000000"/>
<fillcolor color="#ffffff"/>
<linejoin join="round"/>
<linecap cap="round"/>
<path>
<move x="9.67" y="23.33"/>
<curve x1="9.67" y1="24" x2="8" y2="24.66" x3="5.67" y3="24.66"/>
<curve x1="3.67" y1="24.66" x2="2" y2="24" x3="2" y3="23.33"/>
<curve x1="2" y1="22.33" x2="3.67" y2="21.66" x3="5.67" y3="21.66"/>
<curve x1="8" y1="21.66" x2="9.67" y2="22.33" x3="9.67" y3="23.33"/>
<close/>
</path>
<fillstroke/>
<restore/>
<strokecolor color="none"/>
<fillcolor color="#000000"/>
<linejoin join="miter"/>
<linecap cap="butt"/>
<path>
<move x="7" y="23"/>
<curve x1="7" y1="23.33" x2="6.34" y2="23.66" x3="5.67" y3="23.66"/>
<curve x1="5" y1="23.66" x2="4.67" y2="23.33" x3="4.67" y3="23"/>
<curve x1="4.67" y1="23" x2="5" y2="22.66" x3="5.67" y3="22.66"/>
<curve x1="6.34" y1="22.66" x2="7" y2="23" x3="7" y3="23"/>
<close/>
</path>
<fillstroke/>
<save/>
<path>
<move x="7" y="22.33"/>
<curve x1="7" y1="22.66" x2="6.34" y2="22.66" x3="5.67" y3="22.66"/>
<curve x1="5" y1="22.66" x2="4.67" y2="22.66" x3="4.67" y3="22.33"/>
<curve x1="4.67" y1="22" x2="5" y2="22" x3="5.67" y3="22"/>
<curve x1="6.34" y1="22" x2="7" y2="22" x3="7" y3="22.33"/>
<close/>
</path>
<fillstroke/>
<strokecolor color="#000000"/>
<fillcolor color="#ffffff"/>
<linejoin join="round"/>
<linecap cap="round"/>
<path>
<move x="9.67" y="21.66"/>
<curve x1="9.67" y1="22.33" x2="8" y2="23" x3="5.67" y3="23"/>
<curve x1="3.67" y1="23" x2="2" y2="22.33" x3="2" y3="21.66"/>
<curve x1="2" y1="20.66" x2="3.67" y2="20.33" x3="5.67" y3="20.33"/>
<curve x1="8" y1="20.33" x2="9.67" y2="20.66" x3="9.67" y3="21.66"/>
<close/>
</path>
<fillstroke/>
<restore/>
<strokecolor color="none"/>
<fillcolor color="#000000"/>
<linejoin join="miter"/>
<linecap cap="butt"/>
<path>
<move x="7" y="21.66"/>
<curve x1="7" y1="21.66" x2="6.34" y2="22" x3="5.67" y3="22"/>
<curve x1="5" y1="22" x2="4.67" y2="21.66" x3="4.67" y3="21.66"/>
<curve x1="4.67" y1="21.33" x2="5" y2="21" x3="5.67" y3="21"/>
<curve x1="6.34" y1="21" x2="7" y2="21.33" x3="7" y3="21.66"/>
<close/>
</path>
<fillstroke/>
<save/>
<strokecolor color="#000000"/>
<fillcolor color="#ffffff"/>
<linejoin join="round"/>
<linecap cap="round"/>
<path>
<move x="9.67" y="18.33"/>
<curve x1="9.67" y1="19.33" x2="8" y2="20" x3="5.67" y3="20"/>
<curve x1="3.67" y1="20" x2="2" y2="19.33" x3="2" y3="18.33"/>
<curve x1="2" y1="17.66" x2="3.67" y2="17" x3="5.67" y3="17"/>
<curve x1="8" y1="17" x2="9.67" y2="17.66" x3="9.67" y3="18.33"/>
<close/>
</path>
<fillstroke/>
<restore/>
<strokecolor color="none"/>
<fillcolor color="#000000"/>
<linejoin join="miter"/>
<linecap cap="butt"/>
<path>
<move x="7" y="18.33"/>
<curve x1="7" y1="18.66" x2="6.34" y2="18.66" x3="5.67" y3="18.66"/>
<curve x1="5" y1="18.66" x2="4.67" y2="18.66" x3="4.67" y3="18.33"/>
<curve x1="4.67" y1="18" x2="5" y2="18" x3="5.67" y3="18"/>
<curve x1="6.34" y1="18" x2="7" y2="18" x3="7" y3="18.33"/>
<close/>
</path>
<fillstroke/>
<save/>
<path>
<move x="7" y="17.66"/>
<curve x1="7" y1="17.66" x2="6.34" y2="18" x3="5.67" y3="18"/>
<curve x1="5" y1="18" x2="4.67" y2="17.66" x3="4.67" y3="17.66"/>
<curve x1="4.67" y1="17.33" x2="5" y2="17" x3="5.67" y3="17"/>
<curve x1="6.34" y1="17" x2="7" y2="17.33" x3="7" y3="17.66"/>
<close/>
</path>
<fillstroke/>
<strokecolor color="#000000"/>
<fillcolor color="#ffffff"/>
<linejoin join="round"/>
<linecap cap="round"/>
<path>
<move x="9.67" y="17"/>
<curve x1="9.67" y1="17.66" x2="8" y2="18.33" x3="5.67" y3="18.33"/>
<curve x1="3.67" y1="18.33" x2="2" y2="17.66" x3="2" y3="17"/>
<curve x1="2" y1="16" x2="3.67" y2="15.33" x3="5.67" y3="15.33"/>
<curve x1="8" y1="15.33" x2="9.67" y2="16" x3="9.67" y3="17"/>
<close/>
</path>
<fillstroke/>
<restore/>
<strokecolor color="none"/>
<fillcolor color="#000000"/>
<linejoin join="miter"/>
<linecap cap="butt"/>
<path>
<move x="7" y="16.66"/>
<curve x1="7" y1="17" x2="6.34" y2="17.33" x3="5.67" y3="17.33"/>
<curve x1="5" y1="17.33" x2="4.67" y2="17" x3="4.67" y3="16.66"/>
<curve x1="4.67" y1="16.66" x2="5" y2="16.33" x3="5.67" y3="16.33"/>
<curve x1="6.34" y1="16.33" x2="7" y2="16.66" x3="7" y3="16.66"/>
<close/>
</path>
<fillstroke/>
<save/>
<path>
<move x="7" y="16"/>
<curve x1="7" y1="16.33" x2="6.34" y2="16.33" x3="5.67" y3="16.33"/>
<curve x1="5" y1="16.33" x2="4.67" y2="16.33" x3="4.67" y3="16"/>
<curve x1="4.67" y1="15.66" x2="5" y2="15.66" x3="5.67" y3="15.66"/>
<curve x1="6.34" y1="15.66" x2="7" y2="15.66" x3="7" y3="16"/>
<close/>
</path>
<fillstroke/>
<strokecolor color="#000000"/>
<fillcolor color="#ffffff"/>
<linejoin join="round"/>
<linecap cap="round"/>
<path>
<move x="9.67" y="15.33"/>
<curve x1="9.67" y1="16" x2="8" y2="16.66" x3="5.67" y3="16.66"/>
<curve x1="3.67" y1="16.66" x2="2" y2="16" x3="2" y3="15.33"/>
<curve x1="2" y1="14.33" x2="3.67" y2="13.66" x3="5.67" y3="13.66"/>
<curve x1="8" y1="13.66" x2="9.67" y2="14.33" x3="9.67" y3="15.33"/>
<close/>
</path>
<fillstroke/>
<restore/>
<strokecolor color="none"/>
<fillcolor color="#000000"/>
<linejoin join="miter"/>
<linecap cap="butt"/>
<path>
<move x="7" y="15"/>
<curve x1="7" y1="15.33" x2="6.34" y2="15.66" x3="5.67" y3="15.66"/>
<curve x1="5" y1="15.66" x2="4.67" y2="15.33" x3="4.67" y3="15"/>
<curve x1="4.67" y1="15" x2="5" y2="14.66" x3="5.67" y3="14.66"/>
<curve x1="6.34" y1="14.66" x2="7" y2="15" x3="7" y3="15"/>
<close/>
</path>
<fillstroke/>
<save/>
<strokecolor color="#000000"/>
<fillcolor color="#ffffff"/>
<linejoin join="round"/>
<linecap cap="round"/>
<path>
<move x="18.34" y="31.33"/>
<curve x1="18.34" y1="32" x2="16.67" y2="32.66" x3="14.34" y3="32.66"/>
<curve x1="12.34" y1="32.66" x2="10.67" y2="32" x3="10.67" y3="31.33"/>
<curve x1="10.67" y1="30.33" x2="12.34" y2="29.66" x3="14.34" y3="29.66"/>
<curve x1="16.67" y1="29.66" x2="18.34" y2="30.33" x3="18.34" y3="31.33"/>
<close/>
</path>
<fillstroke/>
<restore/>
<strokecolor color="none"/>
<fillcolor color="#000000"/>
<linejoin join="miter"/>
<linecap cap="butt"/>
<path>
<move x="15.67" y="31"/>
<curve x1="15.67" y1="31.33" x2="15" y2="31.66" x3="14.34" y3="31.66"/>
<curve x1="13.67" y1="31.66" x2="13.34" y2="31.33" x3="13.34" y3="31"/>
<curve x1="13.34" y1="31" x2="13.67" y2="30.66" x3="14.34" y3="30.66"/>
<curve x1="15" y1="30.66" x2="15.67" y2="31" x3="15.67" y3="31"/>
<close/>
</path>
<fillstroke/>
<save/>
<path>
<move x="15.67" y="30.33"/>
<curve x1="15.67" y1="30.66" x2="15" y2="30.66" x3="14.34" y3="30.66"/>
<curve x1="13.67" y1="30.66" x2="13.34" y2="30.66" x3="13.34" y3="30.33"/>
<curve x1="13.34" y1="30" x2="13.67" y2="30" x3="14.34" y3="30"/>
<curve x1="15" y1="30" x2="15.67" y2="30" x3="15.67" y3="30.33"/>
<close/>
</path>
<fillstroke/>
<strokecolor color="#000000"/>
<fillcolor color="#ffffff"/>
<linejoin join="round"/>
<linecap cap="round"/>
<path>
<move x="18.34" y="29.66"/>
<curve x1="18.34" y1="30.33" x2="16.67" y2="31" x3="14.34" y3="31"/>
<curve x1="12.34" y1="31" x2="10.67" y2="30.33" x3="10.67" y3="29.66"/>
<curve x1="10.67" y1="28.66" x2="12.34" y2="28.33" x3="14.34" y3="28.33"/>
<curve x1="16.67" y1="28.33" x2="18.34" y2="28.66" x3="18.34" y3="29.66"/>
<close/>
</path>
<fillstroke/>
<restore/>
<strokecolor color="none"/>
<fillcolor color="#000000"/>
<linejoin join="miter"/>
<linecap cap="butt"/>
<path>
<move x="15.67" y="29.66"/>
<curve x1="15.67" y1="29.66" x2="15" y2="30" x3="14.34" y3="30"/>
<curve x1="13.67" y1="30" x2="13.34" y2="29.66" x3="13.34" y3="29.66"/>
<curve x1="13.34" y1="29.33" x2="13.67" y2="29" x3="14.34" y3="29"/>
<curve x1="15" y1="29" x2="15.67" y2="29.33" x3="15.67" y3="29.66"/>
<close/>
</path>
<fillstroke/>
<save/>
<path>
<move x="15.67" y="28.66"/>
<curve x1="15.67" y1="29" x2="15" y2="29.33" x3="14.34" y3="29.33"/>
<curve x1="13.67" y1="29.33" x2="13.34" y2="29" x3="13.34" y3="28.66"/>
<curve x1="13.34" y1="28.66" x2="13.67" y2="28.33" x3="14.34" y3="28.33"/>
<curve x1="15" y1="28.33" x2="15.67" y2="28.66" x3="15.67" y3="28.66"/>
<close/>
</path>
<fillstroke/>
<strokecolor color="#000000"/>
<fillcolor color="#ffffff"/>
<linejoin join="round"/>
<linecap cap="round"/>
<path>
<move x="18.34" y="28"/>
<curve x1="18.34" y1="29" x2="16.67" y2="29.66" x3="14.34" y3="29.66"/>
<curve x1="12.34" y1="29.66" x2="10.67" y2="29" x3="10.67" y3="28"/>
<curve x1="10.67" y1="27.33" x2="12.34" y2="26.66" x3="14.34" y3="26.66"/>
<curve x1="16.67" y1="26.66" x2="18.34" y2="27.33" x3="18.34" y3="28"/>
<close/>
</path>
<fillstroke/>
<restore/>
<strokecolor color="none"/>
<fillcolor color="#000000"/>
<linejoin join="miter"/>
<linecap cap="butt"/>
<path>
<move x="15.67" y="28"/>
<curve x1="15.67" y1="28.33" x2="15" y2="28.33" x3="14.34" y3="28.33"/>
<curve x1="13.67" y1="28.33" x2="13.34" y2="28.33" x3="13.34" y3="28"/>
<curve x1="13.34" y1="27.66" x2="13.67" y2="27.66" x3="14.34" y3="27.66"/>
<curve x1="15" y1="27.66" x2="15.67" y2="27.66" x3="15.67" y3="28"/>
<close/>
</path>
<fillstroke/>
<save/>
<strokecolor color="#000000"/>
<fillcolor color="#ffffff"/>
<linejoin join="round"/>
<linecap cap="round"/>
<path>
<move x="18.34" y="25"/>
<curve x1="18.34" y1="25.66" x2="16.67" y2="26.33" x3="14.34" y3="26.33"/>
<curve x1="12.34" y1="26.33" x2="10.67" y2="25.66" x3="10.67" y3="25"/>
<curve x1="10.67" y1="24" x2="12.34" y2="23.33" x3="14.34" y3="23.33"/>
<curve x1="16.67" y1="23.33" x2="18.34" y2="24" x3="18.34" y3="25"/>
<close/>
</path>
<fillstroke/>
<restore/>
<strokecolor color="none"/>
<fillcolor color="#000000"/>
<linejoin join="miter"/>
<linecap cap="butt"/>
<path>
<move x="15.67" y="24.66"/>
<curve x1="15.67" y1="25" x2="15" y2="25.33" x3="14.34" y3="25.33"/>
<curve x1="13.67" y1="25.33" x2="13.34" y2="25" x3="13.34" y3="24.66"/>
<curve x1="13.34" y1="24.66" x2="13.67" y2="24.33" x3="14.34" y3="24.33"/>
<curve x1="15" y1="24.33" x2="15.67" y2="24.66" x3="15.67" y3="24.66"/>
<close/>
</path>
<fillstroke/>
<save/>
<path>
<move x="15.67" y="24"/>
<curve x1="15.67" y1="24.33" x2="15" y2="24.33" x3="14.34" y3="24.33"/>
<curve x1="13.67" y1="24.33" x2="13.34" y2="24.33" x3="13.34" y3="24"/>
<curve x1="13.34" y1="23.66" x2="13.67" y2="23.66" x3="14.34" y3="23.66"/>
<curve x1="15" y1="23.66" x2="15.67" y2="23.66" x3="15.67" y3="24"/>
<close/>
</path>
<fillstroke/>
<strokecolor color="#000000"/>
<fillcolor color="#ffffff"/>
<linejoin join="round"/>
<linecap cap="round"/>
<path>
<move x="18.34" y="23.33"/>
<curve x1="18.34" y1="24" x2="16.67" y2="24.66" x3="14.34" y3="24.66"/>
<curve x1="12.34" y1="24.66" x2="10.67" y2="24" x3="10.67" y3="23.33"/>
<curve x1="10.67" y1="22.33" x2="12.34" y2="21.66" x3="14.34" y3="21.66"/>
<curve x1="16.67" y1="21.66" x2="18.34" y2="22.33" x3="18.34" y3="23.33"/>
<close/>
</path>
<fillstroke/>
<restore/>
<strokecolor color="none"/>
<fillcolor color="#000000"/>
<linejoin join="miter"/>
<linecap cap="butt"/>
<path>
<move x="15.67" y="23"/>
<curve x1="15.67" y1="23.33" x2="15" y2="23.66" x3="14.34" y3="23.66"/>
<curve x1="13.67" y1="23.66" x2="13.34" y2="23.33" x3="13.34" y3="23"/>
<curve x1="13.34" y1="23" x2="13.67" y2="22.66" x3="14.34" y3="22.66"/>
<curve x1="15" y1="22.66" x2="15.67" y2="23" x3="15.67" y3="23"/>
<close/>
</path>
<fillstroke/>
<save/>
<path>
<move x="15.67" y="22.33"/>
<curve x1="15.67" y1="22.66" x2="15" y2="22.66" x3="14.34" y3="22.66"/>
<curve x1="13.67" y1="22.66" x2="13.34" y2="22.66" x3="13.34" y3="22.33"/>
<curve x1="13.34" y1="22" x2="13.67" y2="22" x3="14.34" y3="22"/>
<curve x1="15" y1="22" x2="15.67" y2="22" x3="15.67" y3="22.33"/>
<close/>
</path>
<fillstroke/>
<strokecolor color="#000000"/>
<fillcolor color="#ffffff"/>
<linejoin join="round"/>
<linecap cap="round"/>
<path>
<move x="18.34" y="21.66"/>
<curve x1="18.34" y1="22.33" x2="16.67" y2="23" x3="14.34" y3="23"/>
<curve x1="12.34" y1="23" x2="10.67" y2="22.33" x3="10.67" y3="21.66"/>
<curve x1="10.67" y1="20.66" x2="12.34" y2="20.33" x3="14.34" y3="20.33"/>
<curve x1="16.67" y1="20.33" x2="18.34" y2="20.66" x3="18.34" y3="21.66"/>
<close/>
</path>
<fillstroke/>
<restore/>
<strokecolor color="none"/>
<fillcolor color="#000000"/>
<linejoin join="miter"/>
<linecap cap="butt"/>
<path>
<move x="15.67" y="21.66"/>
<curve x1="15.67" y1="21.66" x2="15" y2="22" x3="14.34" y3="22"/>
<curve x1="13.67" y1="22" x2="13.34" y2="21.66" x3="13.34" y3="21.66"/>
<curve x1="13.34" y1="21.33" x2="13.67" y2="21" x3="14.34" y3="21"/>
<curve x1="15" y1="21" x2="15.67" y2="21.33" x3="15.67" y3="21.66"/>
<close/>
</path>
<fillstroke/>
<save/>
<strokecolor color="#000000"/>
<fillcolor color="#ffffff"/>
<linejoin join="round"/>
<linecap cap="round"/>
<path>
<move x="18.34" y="18.33"/>
<curve x1="18.34" y1="19.33" x2="16.67" y2="20" x3="14.34" y3="20"/>
<curve x1="12.34" y1="20" x2="10.67" y2="19.33" x3="10.67" y3="18.33"/>
<curve x1="10.67" y1="17.66" x2="12.34" y2="17" x3="14.34" y3="17"/>
<curve x1="16.67" y1="17" x2="18.34" y2="17.66" x3="18.34" y3="18.33"/>
<close/>
</path>
<fillstroke/>
<restore/>
<strokecolor color="none"/>
<fillcolor color="#000000"/>
<linejoin join="miter"/>
<linecap cap="butt"/>
<path>
<move x="15.67" y="18.33"/>
<curve x1="15.67" y1="18.66" x2="15" y2="18.66" x3="14.34" y3="18.66"/>
<curve x1="13.67" y1="18.66" x2="13.34" y2="18.66" x3="13.34" y3="18.33"/>
<curve x1="13.34" y1="18" x2="13.67" y2="18" x3="14.34" y3="18"/>
<curve x1="15" y1="18" x2="15.67" y2="18" x3="15.67" y3="18.33"/>
<close/>
</path>
<fillstroke/>
<save/>
<path>
<move x="15.67" y="17.66"/>
<curve x1="15.67" y1="17.66" x2="15" y2="18" x3="14.34" y3="18"/>
<curve x1="13.67" y1="18" x2="13.34" y2="17.66" x3="13.34" y3="17.66"/>
<curve x1="13.34" y1="17.33" x2="13.67" y2="17" x3="14.34" y3="17"/>
<curve x1="15" y1="17" x2="15.67" y2="17.33" x3="15.67" y3="17.66"/>
<close/>
</path>
<fillstroke/>
<strokecolor color="#000000"/>
<fillcolor color="#ffffff"/>
<linejoin join="round"/>
<linecap cap="round"/>
<path>
<move x="18.34" y="17"/>
<curve x1="18.34" y1="17.66" x2="16.67" y2="18.33" x3="14.34" y3="18.33"/>
<curve x1="12.34" y1="18.33" x2="10.67" y2="17.66" x3="10.67" y3="17"/>
<curve x1="10.67" y1="16" x2="12.34" y2="15.33" x3="14.34" y3="15.33"/>
<curve x1="16.67" y1="15.33" x2="18.34" y2="16" x3="18.34" y3="17"/>
<close/>
</path>
<fillstroke/>
<restore/>
<strokecolor color="none"/>
<fillcolor color="#000000"/>
<linejoin join="miter"/>
<linecap cap="butt"/>
<path>
<move x="15.67" y="16.66"/>
<curve x1="15.67" y1="17" x2="15" y2="17.33" x3="14.34" y3="17.33"/>
<curve x1="13.67" y1="17.33" x2="13.34" y2="17" x3="13.34" y3="16.66"/>
<curve x1="13.34" y1="16.66" x2="13.67" y2="16.33" x3="14.34" y3="16.33"/>
<curve x1="15" y1="16.33" x2="15.67" y2="16.66" x3="15.67" y3="16.66"/>
<close/>
</path>
<fillstroke/>
<save/>
<path>
<move x="15.67" y="16"/>
<curve x1="15.67" y1="16.33" x2="15" y2="16.33" x3="14.34" y3="16.33"/>
<curve x1="13.67" y1="16.33" x2="13.34" y2="16.33" x3="13.34" y3="16"/>
<curve x1="13.34" y1="15.66" x2="13.67" y2="15.66" x3="14.34" y3="15.66"/>
<curve x1="15" y1="15.66" x2="15.67" y2="15.66" x3="15.67" y3="16"/>
<close/>
</path>
<fillstroke/>
<strokecolor color="#000000"/>
<fillcolor color="#ffffff"/>
<linejoin join="round"/>
<linecap cap="round"/>
<path>
<move x="18.34" y="15.33"/>
<curve x1="18.34" y1="16" x2="16.67" y2="16.66" x3="14.34" y3="16.66"/>
<curve x1="12.34" y1="16.66" x2="10.67" y2="16" x3="10.67" y3="15.33"/>
<curve x1="10.67" y1="14.33" x2="12.34" y2="13.66" x3="14.34" y3="13.66"/>
<curve x1="16.67" y1="13.66" x2="18.34" y2="14.33" x3="18.34" y3="15.33"/>
<close/>
</path>
<fillstroke/>
<restore/>
<strokecolor color="none"/>
<fillcolor color="#000000"/>
<linejoin join="miter"/>
<linecap cap="butt"/>
<path>
<move x="15.67" y="15"/>
<curve x1="15.67" y1="15.33" x2="15" y2="15.66" x3="14.34" y3="15.66"/>
<curve x1="13.67" y1="15.66" x2="13.34" y2="15.33" x3="13.34" y3="15"/>
<curve x1="13.34" y1="15" x2="13.67" y2="14.66" x3="14.34" y3="14.66"/>
<curve x1="15" y1="14.66" x2="15.67" y2="15" x3="15.67" y3="15"/>
<close/>
</path>
<fillstroke/>
</foreground>
</shape>
<shape name="File Cabinet" h="39.34" w="32" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.535" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
<constraint x="0.22" y="0.11" perimeter="0" name="NW"/>
<constraint x="0.16" y="1" perimeter="0" name="SW"/>
<constraint x="1" y="0" perimeter="0" name="NE"/>
<constraint x="0.9" y="0.91" perimeter="0" name="SE"/>
</connections>
<background>
<save/>
<path>
<move x="25.33" y="6.67"/>
<line x="5" y="6.67"/>
<line x="5" y="39.34"/>
<line x="25.33" y="39.34"/>
<close/>
<move x="25.33" y="39.34"/>
<line x="32" y="32.67"/>
<line x="32" y="0"/>
<line x="11.33" y="0"/>
<line x="5" y="6.67"/>
<line x="25.33" y="6.67"/>
<line x="25.33" y="39.34"/>
<close/>
</path>
</background>
<foreground>
<linejoin join="round"/>
<fillstroke/>
<restore/>
<rect/>
<stroke/>
<path>
<move x="5.33" y="16.67"/>
<line x="25.33" y="16.67"/>
</path>
<stroke/>
<path>
<move x="12.66" y="11.34"/>
<line x="18.33" y="11.34"/>
</path>
<stroke/>
<path>
<move x="20.66" y="13.34"/>
<line x="20.66" y="9.34"/>
<line x="10.33" y="9.34"/>
<line x="10.33" y="13.34"/>
<close/>
</path>
<stroke/>
<path>
<move x="12.66" y="32.34"/>
<line x="18.33" y="32.34"/>
</path>
<stroke/>
<path>
<move x="20.66" y="34.34"/>
<line x="20.66" y="30"/>
<line x="10.33" y="30"/>
<line x="10.33" y="34.34"/>
<close/>
</path>
<stroke/>
<path>
<move x="5.33" y="37"/>
<line x="25.33" y="37"/>
</path>
<stroke/>
<save/>
<linejoin join="round"/>
<path>
<move x="20.66" y="31.34"/>
<line x="25.33" y="27"/>
<line x="25.33" y="19.34"/>
<line x="4.66" y="19.34"/>
<line x="0" y="24"/>
<line x="20.66" y="24"/>
<close/>
</path>
<fillstroke/>
<path>
<move x="20.66" y="24"/>
<line x="25.33" y="19.34"/>
</path>
<stroke/>
<restore/>
<path>
<move x="20.66" y="31.34"/>
<line x="0" y="31.34"/>
<line x="0" y="21"/>
<line x="20.66" y="21"/>
<close/>
</path>
<fillstroke/>
<path>
<move x="8" y="26.34"/>
<line x="13.66" y="26.34"/>
</path>
<stroke/>
<path>
<move x="16" y="28.34"/>
<line x="16" y="24.34"/>
<line x="5.66" y="24.34"/>
<line x="5.66" y="28.34"/>
<close/>
</path>
<stroke/>
<path>
<move x="25.2" y="7"/>
<line x="32" y="0"/>
</path>
<stroke/>
</foreground>
</shape>
<shape name="Relational Database" h="33.34" w="41.33" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
<constraint x="0.12" y="0.08" perimeter="0" name="NW"/>
<constraint x="0.12" y="0.92" perimeter="0" name="SW"/>
<constraint x="0.88" y="0.08" perimeter="0" name="NE"/>
<constraint x="0.88" y="0.92" perimeter="0" name="SE"/>
</connections>
<background>
<path>
<move x="41.33" y="7.67"/>
<curve x1="41.33" y1="12" x2="32" y2="15.34" x3="20.67" y3="15.34"/>
<curve x1="9.33" y1="15.34" x2="0" y2="12" x3="0" y3="7.67"/>
<curve x1="0" y1="25.67" x2="0" y2="25.67" x3="0" y3="25.67"/>
<curve x1="0" y1="29.67" x2="9.33" y2="33.34" x3="20.67" y3="33.34"/>
<curve x1="32" y1="33.34" x2="41.33" y2="29.67" x3="41.33" y3="25.67"/>
<close/>
<move x="20.67" y="15.34"/>
<curve x1="32" y1="15.34" x2="41.33" y2="12" x3="41.33" y3="7.67"/>
<curve x1="41.33" y1="3.67" x2="32" y2="0" x3="20.67" y3="0"/>
<curve x1="9.33" y1="0" x2="0" y2="3.67" x3="0" y3="7.67"/>
<curve x1="0" y1="12" x2="9.33" y2="15.34" x3="20.67" y3="15.34"/>
<close/>
</path>
</background>
<foreground>
<linejoin join="round"/>
<fillstroke/>
</foreground>
</shape>
<shape name="Tape Array" h="39.33" w="27" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
<constraint x="0.12" y="0.08" perimeter="0" name="NW"/>
<constraint x="0" y="1" perimeter="0" name="SW"/>
<constraint x="1" y="0" perimeter="0" name="NE"/>
<constraint x="0.9" y="0.9" perimeter="0" name="SE"/>
</connections>
<background>
<save/>
<path>
<move x="20.67" y="6.33"/>
<line x="0" y="6.33"/>
<line x="0" y="39.33"/>
<line x="20.67" y="39.33"/>
<close/>
</path>
</background>
<foreground>
<linejoin join="round"/>
<fillstroke/>
<path>
<move x="20.67" y="39.33"/>
<line x="27" y="32.67"/>
<line x="27" y="0"/>
<line x="6.67" y="0"/>
<line x="0" y="6.33"/>
<line x="20.67" y="6.33"/>
<close/>
<move x="20.67" y="6.33"/>
<line x="27" y="0"/>
</path>
<fillstroke/>
<restore/>
<rect/>
<stroke/>
<strokewidth width="0.67"/>
<strokecolor color="#ffffff"/>
<fillcolor color="#000000"/>
<path>
<move x="17.67" y="16"/>
<line x="17.67" y="10.33"/>
<line x="3.67" y="10.33"/>
<line x="3.67" y="16"/>
<close/>
</path>
<stroke/>
<path>
<move x="8.67" y="13"/>
<curve x1="8.67" y1="14" x2="8" y2="14.67" x3="7.33" y3="14.67"/>
<curve x1="6.33" y1="14.67" x2="5.67" y2="14" x3="5.67" y3="13"/>
<curve x1="5.67" y1="12.33" x2="6.33" y2="11.67" x3="7.33" y3="11.67"/>
<curve x1="8" y1="11.67" x2="8.67" y2="12.33" x3="8.67" y3="13"/>
<close/>
</path>
<fillstroke/>
<path>
<move x="15" y="13"/>
<curve x1="15" y1="14" x2="14.33" y2="14.67" x3="13.67" y3="14.67"/>
<curve x1="13" y1="14.67" x2="12.33" y2="14" x3="12.33" y3="13"/>
<curve x1="12.33" y1="12.33" x2="13" y2="11.67" x3="13.67" y3="11.67"/>
<curve x1="14.33" y1="11.67" x2="15" y2="12.33" x3="15" y3="13"/>
<close/>
</path>
<fillstroke/>
<path>
<move x="17.67" y="26"/>
<line x="17.67" y="20.33"/>
<line x="3.67" y="20.33"/>
<line x="3.67" y="26"/>
<close/>
</path>
<stroke/>
<path>
<move x="8.67" y="23"/>
<curve x1="8.67" y1="24" x2="8" y2="24.67" x3="7.33" y3="24.67"/>
<curve x1="6.33" y1="24.67" x2="5.67" y2="24" x3="5.67" y3="23"/>
<curve x1="5.67" y1="22.33" x2="6.33" y2="21.67" x3="7.33" y3="21.67"/>
<curve x1="8" y1="21.67" x2="8.67" y2="22.33" x3="8.67" y3="23"/>
<close/>
</path>
<fillstroke/>
<path>
<move x="15" y="23"/>
<curve x1="15" y1="24" x2="14.33" y2="24.67" x3="13.67" y3="24.67"/>
<curve x1="13" y1="24.67" x2="12.33" y2="24" x3="12.33" y3="23"/>
<curve x1="12.33" y1="22.33" x2="13" y2="21.67" x3="13.67" y3="21.67"/>
<curve x1="14.33" y1="21.67" x2="15" y2="22.33" x3="15" y3="23"/>
<close/>
</path>
<fillstroke/>
<path>
<move x="17.67" y="36"/>
<line x="17.67" y="30.33"/>
<line x="3.67" y="30.33"/>
<line x="3.67" y="36"/>
<close/>
</path>
<stroke/>
<path>
<move x="8.67" y="33"/>
<curve x1="8.67" y1="34" x2="8" y2="34.67" x3="7.33" y3="34.67"/>
<curve x1="6.33" y1="34.67" x2="5.67" y2="34" x3="5.67" y3="33"/>
<curve x1="5.67" y1="32.33" x2="6.33" y2="31.67" x3="7.33" y3="31.67"/>
<curve x1="8" y1="31.67" x2="8.67" y2="32.33" x3="8.67" y3="33"/>
<close/>
</path>
<fillstroke/>
<path>
<move x="15" y="33"/>
<curve x1="15" y1="34" x2="14.33" y2="34.67" x3="13.67" y3="34.67"/>
<curve x1="13" y1="34.67" x2="12.33" y2="34" x3="12.33" y3="33"/>
<curve x1="12.33" y1="32.33" x2="13" y2="31.67" x3="13.67" y3="31.67"/>
<curve x1="14.33" y1="31.67" x2="15" y2="32.33" x3="15" y3="33"/>
<close/>
</path>
<fillstroke/>
<path>
<move x="7.33" y="11.67"/>
<line x="13.67" y="11.67"/>
<move x="7.33" y="21.67"/>
<line x="13.67" y="21.67"/>
<move x="7.33" y="31.67"/>
<line x="13.67" y="31.67"/>
</path>
<stroke/>
</foreground>
</shape>
<shape name="Web Cluster" h="66.67" w="116.33" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0.05" perimeter="0" name="N"/>
<constraint x="0.5" y="0.99" perimeter="0" name="S"/>
<constraint x="0.04" y="0.5" perimeter="0" name="W"/>
<constraint x="0.98" y="0.5" perimeter="0" name="E"/>
<constraint x="0.18" y="0.19" perimeter="0" name="NW"/>
<constraint x="0.18" y="0.85" perimeter="0" name="SW"/>
<constraint x="0.82" y="0.15" perimeter="0" name="NE"/>
<constraint x="0.82" y="0.92" perimeter="0" name="SE"/>
</connections>
<background>
<save/>
<path>
<move x="42.67" y="10.34"/>
<curve x1="23.33" y1="6.67" x2="13.33" y2="16.34" x3="14.67" y3="25"/>
<curve x1="15" y1="25.34" x2="15" y2="25.34" x3="15" y3="25.34"/>
<curve x1="0" y1="28" x2="4" y2="45.67" x3="11" y3="45.67"/>
<curve x1="11.67" y1="45.34" x2="11.67" y2="45.34" x3="11.67" y3="45.34"/>
<curve x1="10" y1="53.67" x2="27.33" y2="62.34" x3="37.33" y3="58"/>
<curve x1="37.67" y1="57.34" x2="37.67" y2="57.34" x3="37.67" y3="57.34"/>
<curve x1="41.33" y1="64.67" x2="53.67" y2="66" x3="66" y3="66.34"/>
<curve x1="76.33" y1="66.67" x2="82.67" y2="65.67" x3="87" y3="61"/>
<curve x1="88" y1="61.34" x2="88" y2="61.34" x3="88" y3="61.34"/>
<curve x1="104.67" y1="62.67" x2="112.67" y2="51" x3="108.67" y3="42.67"/>
<curve x1="109.67" y1="42.34" x2="109.67" y2="42.34" x3="109.67" y3="42.34"/>
<curve x1="115.67" y1="40.67" x2="116.33" y2="27.34" x3="106.33" y3="25.67"/>
<curve x1="106.33" y1="25" x2="106.33" y2="25" x3="106.33" y3="25"/>
<curve x1="110.67" y1="16.34" x2="100.67" y2="8" x3="86.67" y3="9.67"/>
<curve x1="85.67" y1="9.34" x2="85.67" y2="9.34" x3="85.67" y3="9.34"/>
<curve x1="75.33" y1="0" x2="46.33" y2="1.67" x3="43" y3="10.67"/>
<close/>
</path>
</background>
<foreground>
<strokecolor color="#036897"/>
<fillcolor color="#ffffff"/>
<fillstroke/>
<restore/>
<strokewidth width="1"/>
<linejoin join="round"/>
<path>
<move x="72.67" y="49"/>
<line x="70" y="52.34"/>
<line x="48.67" y="52.34"/>
<line x="52.33" y="49"/>
<close/>
</path>
<fillstroke/>
<path>
<move x="48.67" y="56.34"/>
<line x="48.67" y="52.34"/>
<line x="69.33" y="52.34"/>
<line x="69.33" y="56.34"/>
<close/>
</path>
<fillstroke/>
<path>
<move x="68.67" y="61.34"/>
<line x="68.67" y="60"/>
<line x="71.33" y="56.34"/>
<line x="71.33" y="58.67"/>
<close/>
</path>
<fillstroke/>
<path>
<move x="68.67" y="60"/>
<line x="68.67" y="61.34"/>
<line x="45" y="61.34"/>
<line x="45" y="60"/>
<close/>
</path>
<fillstroke/>
<path>
<move x="48.67" y="36.34"/>
<line x="69" y="36.34"/>
<line x="69" y="51.67"/>
<line x="48.67" y="51.67"/>
<close/>
</path>
<fillstroke/>
<path>
<move x="69" y="51.67"/>
<line x="72.67" y="48"/>
<line x="72.67" y="32.67"/>
<line x="69" y="36.34"/>
<close/>
</path>
<fillstroke/>
<path>
<move x="68.67" y="60"/>
<line x="45" y="60"/>
<line x="47.67" y="56.34"/>
<line x="71.33" y="56.34"/>
<close/>
</path>
<fillstroke/>
<path>
<move x="69" y="56.34"/>
<line x="69" y="52.34"/>
<line x="72.67" y="49"/>
<line x="72.67" y="53"/>
<close/>
</path>
<fillstroke/>
<path>
<move x="52" y="38"/>
<curve x1="65.67" y1="38" x2="65.67" y2="38" x3="65.67" y3="38"/>
<curve x1="66.67" y1="38" x2="67.33" y2="38.67" x3="67.33" y3="39.67"/>
<curve x1="67.33" y1="48.34" x2="67.33" y2="48.34" x3="67.33" y3="48.34"/>
<curve x1="67.33" y1="49" x2="66.67" y2="49.67" x3="65.67" y3="49.67"/>
<curve x1="52" y1="49.67" x2="52" y2="49.67" x3="52" y3="49.67"/>
<curve x1="51" y1="49.67" x2="50.33" y2="49" x3="50.33" y3="48.34"/>
<curve x1="50.33" y1="39.67" x2="50.33" y2="39.67" x3="50.33" y3="39.67"/>
<curve x1="50.33" y1="38.67" x2="51" y2="38" x3="52" y3="38"/>
<close/>
</path>
<stroke/>
<path>
<move x="72.67" y="32.67"/>
<line x="52.67" y="32.67"/>
<line x="48.67" y="36.34"/>
<line x="69" y="36.34"/>
<close/>
</path>
<fillstroke/>
<save/>
<fillcolor color="#ffffff"/>
<path>
<move x="57" y="40.67"/>
<curve x1="54.67" y1="40.34" x2="53.33" y2="41.67" x3="53.67" y3="42.67"/>
<curve x1="53.67" y1="42.67" x2="53.67" y2="42.67" x3="53.67" y3="42.67"/>
<curve x1="51.67" y1="43" x2="52" y2="45.34" x3="53" y3="45.34"/>
<curve x1="53" y1="45.34" x2="53" y2="45.34" x3="53" y3="45.34"/>
<curve x1="53" y1="47" x2="55.33" y2="48" x3="56.33" y3="47"/>
<curve x1="56.67" y1="47" x2="56.67" y2="47" x3="56.67" y3="47"/>
<curve x1="57" y1="48.34" x2="62" y2="48.34" x3="63" y3="47.34"/>
<curve x1="63" y1="47.34" x2="63" y2="47.34" x3="63" y3="47.34"/>
<curve x1="65.33" y1="47.67" x2="66.33" y2="46" x3="65.67" y3="45"/>
<curve x1="66" y1="45" x2="66" y2="45" x3="66" y3="45"/>
<curve x1="66.67" y1="44.67" x2="66.67" y2="43" x3="65.33" y3="42.67"/>
<curve x1="65.33" y1="42.67" x2="65.33" y2="42.67" x3="65.33" y3="42.67"/>
<curve x1="66" y1="41.67" x2="64.67" y2="40.34" x3="63" y3="40.67"/>
<curve x1="62.67" y1="40.67" x2="62.67" y2="40.67" x3="62.67" y3="40.67"/>
<curve x1="61.33" y1="39.34" x2="57.67" y2="39.67" x3="57.33" y3="40.67"/>
<close/>
</path>
<fill/>
<restore/>
<path>
<move x="103.33" y="31.67"/>
<line x="100.67" y="35"/>
<line x="79.33" y="35"/>
<line x="83" y="31.67"/>
<close/>
</path>
<fillstroke/>
<save/>
<path>
<move x="79.33" y="39"/>
<line x="79.33" y="35"/>
<line x="100" y="35"/>
<line x="100" y="39"/>
<close/>
</path>
<fillstroke/>
<path>
<move x="99.33" y="44"/>
<line x="99.33" y="42.67"/>
<line x="102" y="39"/>
<line x="102" y="41.34"/>
<close/>
</path>
<fillstroke/>
<path>
<move x="99.33" y="42.67"/>
<line x="99.33" y="44"/>
<line x="75.67" y="44"/>
<line x="75.67" y="42.67"/>
<close/>
</path>
<fillstroke/>
<path>
<move x="79.33" y="19"/>
<line x="99.67" y="19"/>
<line x="99.67" y="34.34"/>
<line x="79.33" y="34.34"/>
<close/>
</path>
<fillstroke/>
<path>
<move x="99.67" y="34.34"/>
<line x="103.33" y="30.67"/>
<line x="103.33" y="15.34"/>
<line x="99.67" y="19"/>
<close/>
</path>
<fillstroke/>
<path>
<move x="99.33" y="42.67"/>
<line x="75.67" y="42.67"/>
<line x="78.33" y="39"/>
<line x="102" y="39"/>
<close/>
</path>
<fillstroke/>
<path>
<move x="99.67" y="39"/>
<line x="99.67" y="35"/>
<line x="103.33" y="31.67"/>
<line x="103.33" y="35.67"/>
<close/>
</path>
<fillstroke/>
<path>
<move x="82.67" y="20.67"/>
<curve x1="96.33" y1="20.67" x2="96.33" y2="20.67" x3="96.33" y3="20.67"/>
<curve x1="97.33" y1="20.67" x2="98" y2="21.34" x3="98" y3="22.34"/>
<curve x1="98" y1="31" x2="98" y2="31" x3="98" y3="31"/>
<curve x1="98" y1="31.67" x2="97.33" y2="32.34" x3="96.33" y3="32.34"/>
<curve x1="82.67" y1="32.34" x2="82.67" y2="32.34" x3="82.67" y3="32.34"/>
<curve x1="81.67" y1="32.34" x2="81" y2="31.67" x3="81" y3="31"/>
<curve x1="81" y1="22.34" x2="81" y2="22.34" x3="81" y3="22.34"/>
<curve x1="81" y1="21.34" x2="81.67" y2="20.67" x3="82.67" y3="20.67"/>
<close/>
</path>
<fillstroke/>
<path>
<move x="103.33" y="15.34"/>
<line x="83.33" y="15.34"/>
<line x="79.33" y="19"/>
<line x="99.67" y="19"/>
<close/>
</path>
<fillstroke/>
<fillcolor color="#ffffff"/>
<path>
<move x="87.67" y="23.34"/>
<curve x1="85.33" y1="23" x2="84" y2="24.34" x3="84.33" y3="25.34"/>
<curve x1="84.33" y1="25.34" x2="84.33" y2="25.34" x3="84.33" y3="25.34"/>
<curve x1="82.33" y1="25.67" x2="82.67" y2="28" x3="83.67" y3="28"/>
<curve x1="83.67" y1="28" x2="83.67" y2="28" x3="83.67" y3="28"/>
<curve x1="83.67" y1="29.67" x2="86" y2="30.67" x3="87" y3="29.67"/>
<curve x1="87.33" y1="29.67" x2="87.33" y2="29.67" x3="87.33" y3="29.67"/>
<curve x1="87.67" y1="31" x2="92.67" y2="31" x3="93.67" y3="30"/>
<curve x1="93.67" y1="30" x2="93.67" y2="30" x3="93.67" y3="30"/>
<curve x1="96" y1="30.34" x2="97" y2="28.67" x3="96.33" y3="27.67"/>
<curve x1="96.67" y1="27.67" x2="96.67" y2="27.67" x3="96.67" y3="27.67"/>
<curve x1="97.33" y1="27.34" x2="97.33" y2="25.67" x3="96" y3="25.34"/>
<curve x1="96" y1="25.34" x2="96" y2="25.34" x3="96" y3="25.34"/>
<curve x1="96.67" y1="24.34" x2="95.33" y2="23" x3="93.67" y3="23.34"/>
<curve x1="93.33" y1="23.34" x2="93.33" y2="23.34" x3="93.33" y3="23.34"/>
<curve x1="92" y1="22" x2="88.33" y2="22.34" x3="88" y3="23.34"/>
<close/>
</path>
<fill/>
<restore/>
<path>
<move x="44" y="30.34"/>
<line x="41.33" y="33.67"/>
<line x="20" y="33.67"/>
<line x="23.67" y="30.34"/>
<close/>
</path>
<fillstroke/>
<path>
<move x="20" y="37.67"/>
<line x="20" y="33.67"/>
<line x="40.67" y="33.67"/>
<line x="40.67" y="37.67"/>
<close/>
</path>
<fillstroke/>
<path>
<move x="40" y="42.67"/>
<line x="40" y="41.34"/>
<line x="42.67" y="37.67"/>
<line x="42.67" y="40"/>
<close/>
</path>
<fillstroke/>
<path>
<move x="40" y="41.34"/>
<line x="40" y="42.67"/>
<line x="16.33" y="42.67"/>
<line x="16.33" y="41.34"/>
<close/>
</path>
<fillstroke/>
<path>
<move x="20" y="17.67"/>
<line x="40.33" y="17.67"/>
<line x="40.33" y="33"/>
<line x="20" y="33"/>
<close/>
</path>
<fillstroke/>
<path>
<move x="40.33" y="33"/>
<line x="44" y="29.34"/>
<line x="44" y="14"/>
<line x="40.33" y="17.67"/>
<close/>
</path>
<fillstroke/>
<path>
<move x="40" y="41.34"/>
<line x="16.33" y="41.34"/>
<line x="19" y="37.67"/>
<line x="42.67" y="37.67"/>
<close/>
</path>
<fillstroke/>
<path>
<move x="40.33" y="37.67"/>
<line x="40.33" y="33.67"/>
<line x="44" y="30.34"/>
<line x="44" y="34.34"/>
<close/>
</path>
<fillstroke/>
<path>
<move x="23.33" y="19.34"/>
<curve x1="37" y1="19.34" x2="37" y2="19.34" x3="37" y3="19.34"/>
<curve x1="38" y1="19.34" x2="38.67" y2="20" x3="38.67" y3="21"/>
<curve x1="38.67" y1="29.67" x2="38.67" y2="29.67" x3="38.67" y3="29.67"/>
<curve x1="38.67" y1="30.34" x2="38" y2="31" x3="37" y3="31"/>
<curve x1="23.33" y1="31" x2="23.33" y2="31" x3="23.33" y3="31"/>
<curve x1="22.33" y1="31" x2="21.67" y2="30.34" x3="21.67" y3="29.67"/>
<curve x1="21.67" y1="21" x2="21.67" y2="21" x3="21.67" y3="21"/>
<curve x1="21.67" y1="20" x2="22.33" y2="19.34" x3="23.33" y3="19.34"/>
<close/>
</path>
<fillstroke/>
<path>
<move x="44" y="14"/>
<line x="24" y="14"/>
<line x="20" y="17.67"/>
<line x="40.33" y="17.67"/>
<line x="44" y="14"/>
<close/>
</path>
<fillstroke/>
<fillcolor color="#ffffff"/>
<path>
<move x="27.67" y="22"/>
<curve x1="25.33" y1="21.67" x2="24" y2="23" x3="24.33" y3="24"/>
<curve x1="24.33" y1="24" x2="24.33" y2="24" x3="24.33" y3="24"/>
<curve x1="22.33" y1="24.34" x2="22.67" y2="26.67" x3="23.67" y3="26.67"/>
<curve x1="23.67" y1="26.67" x2="23.67" y2="26.67" x3="23.67" y3="26.67"/>
<curve x1="23.67" y1="28.34" x2="26" y2="29.34" x3="27" y3="28.34"/>
<curve x1="27.33" y1="28.34" x2="27.33" y2="28.34" x3="27.33" y3="28.34"/>
<curve x1="27.67" y1="29.67" x2="32.67" y2="29.67" x3="33.67" y3="28.67"/>
<curve x1="33.67" y1="28.67" x2="33.67" y2="28.67" x3="33.67" y3="28.67"/>
<curve x1="36" y1="29" x2="37" y2="27.34" x3="36.33" y3="26.34"/>
<curve x1="36.67" y1="26.34" x2="36.67" y2="26.34" x3="36.67" y3="26.34"/>
<curve x1="37.33" y1="26" x2="37.33" y2="24.34" x3="36" y3="24"/>
<curve x1="36" y1="24" x2="36" y2="24" x3="36" y3="24"/>
<curve x1="36.67" y1="23" x2="35.33" y2="21.67" x3="33.67" y3="22"/>
<curve x1="33.33" y1="22" x2="33.33" y2="22" x3="33.33" y3="22"/>
<curve x1="32" y1="20.67" x2="28.33" y2="21" x3="28" y3="22"/>
<close/>
</path>
<fill/>
<strokecolor color="#036897"/>
<path>
<move x="50.33" y="50.67"/>
<line x="37.67" y="42.34"/>
<move x="70.67" y="48.67"/>
<line x="78.67" y="43.34"/>
<move x="42.33" y="22.67"/>
<line x="80.33" y="22.67"/>
</path>
<stroke/>
</foreground>
</shape>
</shapes>