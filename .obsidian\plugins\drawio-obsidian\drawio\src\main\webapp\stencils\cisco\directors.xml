<shapes name="mxgraph.cisco.directors">
<shape name="Content Engine (Cache Director)" h="40.33" w="56.66" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
<constraint x="0.08" y="0.08" perimeter="0" name="NW"/>
<constraint x="0" y="1" perimeter="0" name="SW"/>
<constraint x="1" y="0" perimeter="0" name="NE"/>
<constraint x="0.96" y="0.88" perimeter="0" name="SE"/>
</connections>
<background>
<save/>
<path>
<move x="0" y="40.33"/>
<line x="51" y="40.33"/>
<line x="51" y="7"/>
<line x="0" y="7"/>
<close/>
<move x="56.66" y="32"/>
<line x="51" y="40.33"/>
<line x="51" y="7"/>
<line x="56.66" y="0"/>
<close/>
<move x="8.66" y="0"/>
<line x="56.66" y="0"/>
<line x="51" y="7"/>
<line x="0" y="7"/>
<close/>
</path>
</background>
<foreground>
<linejoin join="round"/>
<fillstroke/>
<fillcolor color="#ffffff"/>
<path>
<move x="11.33" y="15.67"/>
<line x="3" y="16"/>
<line x="3" y="14.33"/>
<line x="11.33" y="14.33"/>
<line x="11.33" y="12.33"/>
<line x="16.33" y="15"/>
<line x="11.33" y="18"/>
<close/>
<move x="11.33" y="24.33"/>
<line x="3" y="24.33"/>
<line x="3" y="23"/>
<line x="11.33" y="23"/>
<line x="11.33" y="20.67"/>
<line x="16.33" y="23.67"/>
<line x="11.33" y="26.33"/>
<close/>
<move x="11.33" y="32.66"/>
<line x="3" y="32.66"/>
<line x="3" y="31.33"/>
<line x="11.33" y="31.33"/>
<line x="11.33" y="29.33"/>
<line x="16.33" y="32"/>
<line x="11.33" y="35"/>
<close/>
<move x="40" y="23.33"/>
<line x="48.33" y="23"/>
<line x="48.33" y="24.67"/>
<line x="40" y="24.67"/>
<line x="40" y="26.67"/>
<line x="35" y="24"/>
<line x="40" y="21"/>
<close/>
</path>
<fill/>
<path>
<move x="33" y="19"/>
<curve x1="33" y1="20" x2="30" y2="21" x3="26.33" y3="21"/>
<curve x1="22.33" y1="21" x2="19.33" y2="20" x3="19.33" y3="19"/>
<curve x1="19.33" y1="27.66" x2="19.33" y2="27.66" x3="19.33" y3="27.66"/>
<curve x1="19.33" y1="28.66" x2="22.33" y2="29.66" x3="26.33" y3="29.66"/>
<curve x1="30" y1="29.66" x2="33" y2="28.66" x3="33" y3="27.66"/>
<close/>
</path>
<fill/>
<restore/>
<rect/>
<stroke/>
<strokecolor color="#ffffff"/>
<strokewidth width="1"/>
<path>
<move x="26.33" y="21"/>
<curve x1="30" y1="21" x2="33" y2="20" x3="33" y3="19"/>
<curve x1="33" y1="17.67" x2="30" y2="17" x3="26.33" y3="17"/>
<curve x1="22.33" y1="17" x2="19.33" y2="17.67" x3="19.33" y3="19"/>
<curve x1="19.33" y1="20" x2="22.33" y2="21" x3="26.33" y3="21"/>
<close/>
</path>
<fillstroke/>
<fillcolor color="#ffffff"/>
<path>
<move x="26.33" y="30.66"/>
<curve x1="30" y1="30.66" x2="33" y2="29.66" x3="33" y3="28.66"/>
<curve x1="33" y1="27.33" x2="30" y2="26.33" x3="26.33" y3="26.33"/>
<curve x1="22.33" y1="26.33" x2="19.33" y2="27.33" x3="19.33" y3="28.66"/>
<curve x1="19.33" y1="29.66" x2="22.33" y2="30.66" x3="26.33" y3="30.66"/>
<close/>
</path>
<fillstroke/>
</foreground>
</shape>
<shape name="Director-Class Fibre Channel Director" h="43.33" w="32" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="0.985" perimeter="0" name="S"/>
<constraint x="0.01" y="0.5" perimeter="0" name="W"/>
<constraint x="0.98" y="0.5" perimeter="0" name="E"/>
<constraint x="0.12" y="0.05" perimeter="0" name="NW"/>
<constraint x="0.01" y="0.985" perimeter="0" name="SW"/>
<constraint x="0.98" y="0" perimeter="0" name="NE"/>
<constraint x="0.91" y="0.92" perimeter="0" name="SE"/>
</connections>
<background>
<path>
<move x="32" y="0"/>
<line x="26.33" y="5.33"/>
<line x="0" y="5.33"/>
<line x="6.67" y="0"/>
<close/>
<move x="26.33" y="31.33"/>
<line x="26.33" y="5.33"/>
<line x="0" y="5.33"/>
<line x="0" y="31.33"/>
<close/>
<move x="26.33" y="42.66"/>
<line x="26.33" y="31.33"/>
<line x="0" y="31.33"/>
<line x="0" y="42.66"/>
<close/>
<move x="32" y="26.33"/>
<line x="32" y="0"/>
<line x="26.33" y="5.33"/>
<line x="26.33" y="31.33"/>
<close/>
<move x="32" y="37.66"/>
<line x="32" y="26.33"/>
<line x="26.33" y="31.33"/>
<line x="26.33" y="42.66"/>
<close/>
</path>
</background>
<foreground>
<linejoin join="round"/>
<fillstroke/>
<fillcolor color="#ffffff"/>
<path>
<move x="9.33" y="17.66"/>
<line x="3.67" y="17.66"/>
<line x="3.67" y="16.33"/>
<line x="1.33" y="18.33"/>
<line x="3.67" y="20.66"/>
<line x="3.67" y="19"/>
<line x="9.33" y="19"/>
<close/>
<move x="12.67" y="22.33"/>
<line x="12.67" y="28"/>
<line x="11" y="28"/>
<line x="13.33" y="30.33"/>
<line x="15.33" y="28"/>
<line x="14" y="28"/>
<line x="14" y="22.33"/>
<close/>
<move x="12.67" y="14.66"/>
<line x="12.67" y="9"/>
<line x="11" y="9"/>
<line x="13.33" y="6.66"/>
<line x="15.33" y="9"/>
<line x="14" y="9"/>
<line x="14" y="14.66"/>
<close/>
<move x="17" y="19"/>
<line x="23" y="19"/>
<line x="23" y="20.66"/>
<line x="25" y="18.33"/>
<line x="23" y="16.33"/>
<line x="23" y="17.66"/>
<line x="17" y="17.66"/>
<close/>
</path>
<fill/>
<strokecolor color="#ffffff"/>
<strokewidth width="0.67"/>
<path>
<move x="22.33" y="27.66"/>
<curve x1="21.33" y1="28.66" x2="16.33" y2="25.66" x3="11.33" y3="20.66"/>
<curve x1="6.33" y1="15.66" x2="3.33" y2="10.66" x3="4.33" y3="9.66"/>
<curve x1="5.33" y1="8.33" x2="10.33" y2="11.66" x3="15.33" y3="16.33"/>
<curve x1="20.33" y1="21.33" x2="23.33" y2="26.33" x3="22.33" y3="27.66"/>
<close/>
</path>
<stroke/>
<path>
<move x="22" y="9.33"/>
<curve x1="23.33" y1="10.66" x2="20" y2="15.33" x3="15" y3="20.33"/>
<curve x1="10.33" y1="25.33" x2="5.33" y2="28.66" x3="4" y3="27.66"/>
<curve x1="3" y1="26.66" x2="6" y2="21.66" x3="11" y3="16.66"/>
<curve x1="16" y1="11.66" x2="21" y2="8.33" x3="22" y3="9.33"/>
<close/>
</path>
<stroke/>
<fillcolor color="#b5b5b5"/>
<path>
<move x="15.67" y="21.66"/>
<curve x1="17.33" y1="20.33" x2="18" y2="17.66" x3="16.67" y3="16"/>
<curve x1="15.33" y1="14" x2="12.67" y2="13.66" x3="10.67" y3="15"/>
<curve x1="9" y1="16.33" x2="8.33" y2="18.66" x3="9.67" y3="20.66"/>
<curve x1="11" y1="22.66" x2="13.67" y2="23" x3="15.67" y3="21.66"/>
<close/>
</path>
<fill/>
</foreground>
</shape>
<shape name="Distributed Director" h="40.66" w="57" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
<constraint x="0.09" y="0.08" perimeter="0" name="NW"/>
<constraint x="0" y="1" perimeter="0" name="SW"/>
<constraint x="1" y="0" perimeter="0" name="NE"/>
<constraint x="0.95" y="0.89" perimeter="0" name="SE"/>
</connections>
<background>
<save/>
<linejoin join="round"/>
<path>
<move x="0" y="40.66"/>
<line x="51" y="40.66"/>
<line x="51" y="7.33"/>
<line x="0" y="7.33"/>
<close/>
<move x="57" y="32"/>
<line x="51" y="40.66"/>
<line x="51" y="7.33"/>
<line x="57" y="0"/>
<close/>
<move x="9" y="0"/>
<line x="57" y="0"/>
<line x="51" y="7.33"/>
<line x="0" y="7.33"/>
<close/>
</path>
</background>
<foreground>
<fillstroke/>
<fillcolor color="#ffffff"/>
<path>
<move x="15.67" y="25.33"/>
<line x="7.34" y="25.33"/>
<line x="7.34" y="24"/>
<line x="15.67" y="24"/>
<line x="15.67" y="21.66"/>
<line x="20.67" y="24.66"/>
<line x="15.67" y="27.33"/>
<close/>
<move x="33.67" y="25.33"/>
<line x="25" y="25.33"/>
<line x="25" y="24"/>
<line x="33.67" y="24"/>
<line x="33.67" y="21.66"/>
<line x="38.34" y="24.66"/>
<line x="33.67" y="27.33"/>
<close/>
<move x="32" y="18.33"/>
<line x="25" y="23"/>
<line x="24.34" y="22"/>
<line x="31.34" y="17.33"/>
<line x="30" y="15.66"/>
<line x="35.67" y="15.33"/>
<line x="33.34" y="20.33"/>
<close/>
<move x="32" y="30.66"/>
<line x="25" y="26"/>
<line x="24.34" y="27.33"/>
<line x="31.34" y="31.66"/>
<line x="30" y="33.66"/>
<line x="35.67" y="34"/>
<line x="33.34" y="29"/>
<close/>
</path>
<fill/>
<restore/>
<rect/>
<stroke/>
<strokecolor color="#ffffff"/>
<path>
<move x="18.34" y="13.66"/>
<curve x1="10" y1="12" x2="5.67" y2="16.33" x3="6.34" y3="19.66"/>
<curve x1="6.67" y1="20" x2="6.67" y2="20" x3="6.67" y3="20"/>
<curve x1="0" y1="21" x2="1.67" y2="28.66" x3="5" y3="28.66"/>
<curve x1="5" y1="28.66" x2="5" y2="28.66" x3="5" y3="28.66"/>
<curve x1="4.34" y1="32.33" x2="12" y2="35.66" x3="16" y3="34"/>
<curve x1="16.34" y1="33.66" x2="16.34" y2="33.66" x3="16.34" y3="33.66"/>
<curve x1="17.67" y1="37" x2="23" y2="37.33" x3="28.34" y3="37.66"/>
<curve x1="32.67" y1="37.66" x2="35.34" y2="37.33" x3="37.34" y3="35.33"/>
<curve x1="37.67" y1="35.33" x2="37.67" y2="35.33" x3="37.67" y3="35.33"/>
<curve x1="45" y1="36" x2="48.34" y2="31" x3="46.67" y3="27.33"/>
<curve x1="47" y1="27.33" x2="47" y2="27.33" x3="47" y3="27.33"/>
<curve x1="49.67" y1="26.66" x2="49.67" y2="21" x3="45.67" y3="20"/>
<curve x1="45.67" y1="20" x2="45.67" y2="20" x3="45.67" y3="20"/>
<curve x1="47.34" y1="16" x2="43" y2="12.66" x3="37" y3="13.33"/>
<curve x1="36.67" y1="13" x2="36.67" y2="13" x3="36.67" y3="13"/>
<curve x1="32.34" y1="9.33" x2="20" y2="10" x3="18.67" y3="13.66"/>
<close/>
</path>
<stroke/>
</foreground>
</shape>
<shape name="Localdirector" h="32.34" w="49.67" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
<constraint x="0.09" y="0.09" perimeter="0" name="NW"/>
<constraint x="0" y="1" perimeter="0" name="SW"/>
<constraint x="1" y="0" perimeter="0" name="NE"/>
<constraint x="0.95" y="0.87" perimeter="0" name="SE"/>
</connections>
<background>
<save/>
<path>
<move x="0" y="32.34"/>
<line x="44.33" y="32.34"/>
<line x="44.33" y="6.34"/>
<line x="0" y="6.34"/>
<close/>
<move x="49.67" y="24.67"/>
<line x="44.33" y="32.34"/>
<line x="44.33" y="6.34"/>
<line x="49.67" y="0"/>
<close/>
<move x="8" y="0"/>
<line x="49.67" y="0"/>
<line x="44.33" y="6.34"/>
<line x="0" y="6.34"/>
<close/>
</path>
</background>
<foreground>
<linejoin join="round"/>
<fillstroke/>
<restore/>
<rect/>
<stroke/>
<fillcolor color="#ffffff"/>
<path>
<move x="15.33" y="20"/>
<line x="5.67" y="20"/>
<line x="5.67" y="18.34"/>
<line x="15.33" y="18.34"/>
<line x="15.33" y="16"/>
<line x="21" y="19"/>
<line x="15.33" y="22.34"/>
<close/>
<move x="35" y="20"/>
<line x="25.67" y="20"/>
<line x="25.67" y="18.34"/>
<line x="35" y="18.34"/>
<line x="35" y="16"/>
<line x="40.67" y="19"/>
<line x="35" y="22.34"/>
<close/>
<move x="33.67" y="12.34"/>
<line x="25.67" y="17.34"/>
<line x="24.67" y="16.34"/>
<line x="32.67" y="11"/>
<line x="31.33" y="9"/>
<line x="37.67" y="8.67"/>
<line x="34.67" y="14.34"/>
<close/>
<move x="33.67" y="26"/>
<line x="25.67" y="20.67"/>
<line x="24.67" y="22"/>
<line x="32.67" y="27.34"/>
<line x="31.33" y="29.34"/>
<line x="37.67" y="29.67"/>
<line x="34.67" y="24"/>
<close/>
</path>
<fill/>
</foreground>
</shape>
<shape name="Workgroup Director" h="42.33" w="52.33" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0.105" y="0.5" perimeter="0" name="W"/>
<constraint x="0.78" y="0.5" perimeter="0" name="E"/>
<constraint x="0.105" y="0.12" perimeter="0" name="NW"/>
<constraint x="0" y="1" perimeter="0" name="SW"/>
<constraint x="0.78" y="0" perimeter="0" name="NE"/>
<constraint x="0.9" y="0.985" perimeter="0" name="SE"/>
</connections>
<background>
<save/>
<fillcolor color="#000000"/>
<path>
<move x="41" y="24"/>
<line x="37.33" y="29.33"/>
<line x="5.33" y="29.33"/>
<line x="10.66" y="24"/>
<close/>
</path>
</background>
<foreground>
<linejoin join="round"/>
<fillstroke/>
<restore/>
<linejoin join="round"/>
<path>
<move x="5.33" y="35"/>
<line x="5.33" y="29.33"/>
<line x="36" y="29.33"/>
<line x="36" y="35"/>
<close/>
<move x="5.33" y="5"/>
<line x="35.66" y="5"/>
<line x="35.66" y="28"/>
<line x="5.33" y="28"/>
<close/>
<move x="35.66" y="28"/>
<line x="41" y="23"/>
<line x="41" y="0"/>
<line x="35.66" y="5"/>
<close/>
<move x="47.33" y="40"/>
<line x="49.66" y="35.33"/>
<line x="44.33" y="35.33"/>
<line x="42" y="40"/>
<close/>
<move x="42" y="40"/>
<line x="47.33" y="40"/>
<line x="47.33" y="41.67"/>
<line x="42" y="41.67"/>
<close/>
<move x="47.33" y="41.67"/>
<line x="47.33" y="40"/>
<line x="49.66" y="35.33"/>
<line x="49.66" y="38.67"/>
<close/>
<move x="36" y="35"/>
<line x="36" y="29.33"/>
<line x="41" y="24.33"/>
<line x="41" y="30"/>
<close/>
</path>
<fillstroke/>
<save/>
<path>
<move x="10.33" y="7.67"/>
<curve x1="30.66" y1="7.67" x2="30.66" y2="7.67" x3="30.66" y3="7.67"/>
<curve x1="32" y1="7.67" x2="33" y2="8.67" x3="33" y3="10"/>
<curve x1="33" y1="23" x2="33" y2="23" x3="33" y3="23"/>
<curve x1="33" y1="24.33" x2="32" y2="25.33" x3="30.66" y3="25.33"/>
<curve x1="10.33" y1="25.33" x2="10.33" y2="25.33" x3="10.33" y3="25.33"/>
<curve x1="9" y1="25.33" x2="8" y2="24.33" x3="8" y3="23"/>
<curve x1="8" y1="10" x2="8" y2="10" x3="8" y3="10"/>
<curve x1="8" y1="8.67" x2="9" y2="7.67" x3="10.33" y3="7.67"/>
<close/>
</path>
<stroke/>
<path>
<move x="41" y="0"/>
<line x="11.33" y="0"/>
<line x="5.33" y="5"/>
<line x="35.66" y="5"/>
<line x="41" y="0"/>
<close/>
</path>
<fillstroke/>
<path>
<move x="35" y="42.33"/>
<line x="35" y="40.67"/>
<line x="39" y="35"/>
<line x="39" y="38.33"/>
<line x="35" y="42.33"/>
<close/>
</path>
<fillstroke/>
<path>
<move x="35" y="40.67"/>
<line x="0" y="40.67"/>
<line x="4" y="35"/>
<line x="39" y="35"/>
<line x="35" y="40.67"/>
<close/>
</path>
<fillstroke/>
<path>
<move x="35" y="40.67"/>
<line x="35" y="42.33"/>
<line x="0" y="42.33"/>
<line x="0" y="40.67"/>
<line x="35" y="40.67"/>
<close/>
</path>
<fillstroke/>
<strokecolor color="#126d99"/>
<path>
<move x="48.33" y="35.67"/>
<curve x1="52.33" y1="33.67" x2="49" y2="25" x3="40.66" y3="29.33"/>
</path>
<stroke/>
<restore/>
<path>
<move x="20.33" y="20"/>
<curve x1="24.66" y1="20" x2="28.33" y2="18.33" x3="28.33" y3="16.67"/>
<curve x1="28.33" y1="14.67" x2="24.66" y2="13" x3="20.33" y3="13"/>
<curve x1="16" y1="13" x2="12.66" y2="14.67" x3="12.66" y3="16.67"/>
<curve x1="12.66" y1="18.33" x2="16" y2="20" x3="20.33" y3="20"/>
<close/>
</path>
<stroke/>
<path>
<move x="20.33" y="21.33"/>
<curve x1="26" y1="21.33" x2="30.33" y2="19" x3="30.33" y3="16.33"/>
<curve x1="30.33" y1="13.67" x2="26" y2="11.67" x3="20.33" y3="11.67"/>
<curve x1="15" y1="11.67" x2="10.33" y2="13.67" x3="10.33" y3="16.33"/>
<curve x1="10.33" y1="19" x2="15" y2="21.33" x3="20.33" y3="21.33"/>
<close/>
</path>
<stroke/>
</foreground>
</shape>
</shapes>