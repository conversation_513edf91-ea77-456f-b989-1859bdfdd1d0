# *DO NOT DIRECTLY EDIT THIS FILE, IT IS AUTOMATICALLY GENERATED AND IT IS BASED ON:*
# https://docs.google.com/spreadsheet/ccc?key=0AmQEO36liL4FdDJLWVNMaVV2UmRKSnpXU09MYkdGbEE
about=About
aboutDrawio=About draw.io
accessDenied=Access Denied
accounts=Accounts
action=Action
actualSize=Actual Size
add=Add
addAccount=Add account
addedFile=Added {1}
addImages=Add Images
addImageUrl=Add Image URL
addLayer=Add Layer
addProperty=Add Property
address=Address
addToExistingDrawing=Add to Existing Drawing
addToScratchpad=Add to Scratchpad
addWaypoint=Add Waypoint
adjustTo=Adjust to
advanced=Advanced
smartTemplate=Smart Template
align=Align
alignment=Alignment
allChangesLost=All changes will be lost!
allPages=All Pages
allProjects=All Projects
allSpaces=All Spaces
allTags=All Tags
anchor=Anchor
android=Android
angle=Angle
arc=Arc
areYouSure=Are you sure?
ensureDataSaved=Please ensure your data is saved before closing.
allChangesSaved=All changes saved
allChangesSavedInDrive=All changes saved in Drive
allowPopups=Allow pop-ups to avoid this dialog.
allowRelativeUrl=Allow relative URL
alreadyConnected=Nodes already connected
appearance=Appearance
apply=Apply
archiMate21=ArchiMate 2.1
arrange=Arrange
arrow=Arrow
arrows=Arrows
asNew=As New
atlas=Atlas
author=Author
authorizationRequired=Authorization required
authorizeThisAppIn=Authorize this app in {1}:
authorize=Authorize
authorizing=Authorizing
automatic=Automatic
autosave=Autosave
autosize=Autosize
attachments=Attachments
aws=AWS
aws3d=AWS 3D
azure=Azure
back=Back
background=Background
backgroundColor=Background Color
backgroundImage=Background Image
basic=Basic
beta=beta
blankDrawing=Blank Drawing
blankDiagram=Blank Diagram
block=Block
blockquote=Blockquote
blog=Blog
bold=Bold
bootstrap=Bootstrap
border=Border
borderColor=Border Color
borderWidth=Border Width
bottom=Bottom
bottomAlign=Bottom Align
bottomLeft=Bottom Left
bottomRight=Bottom Right
bpmn=BPMN
bringForward=Bring Forward
browser=Browser
bulletedList=Bulleted List
business=Business
busy=Operation in progress
cabinets=Cabinets
cancel=Cancel
center=Center
cannotLoad=Load attempts failed. Please try again later.
cannotLogin=Log in attempts failed. Please try again later.
cannotOpenFile=Cannot open file
change=Change
changeOrientation=Change Orientation
changeUser=Change user
changeStorage=Change storage
changesNotSaved=Changes have not been saved
classDiagram=Class Diagram
userJoined={1} has joined
userLeft={1} has left
chatWindowTitle=Chat
chooseAnOption=Choose an option
chromeApp=Chrome App
collaborativeEditingNotice=Important Notice for Collaborative Editing
compare=Compare
compressed=Compressed
commitMessage=Commit Message
configLinkWarn=This link configures draw.io. Only click OK if you trust whoever gave you it!
configLinkConfirm=Click OK to configure and restart draw.io.
container=Container
csv=CSV
dark=Dark
diagramLanguage=Diagram Language
diagramType=Diagram type
diagramXmlDesc=XML File
diagramHtmlDesc=HTML File
diagramPngDesc=Editable Bitmap Image
diagramSvgDesc=Editable Vector Image
didYouMeanToExportToPdf=Did you mean to export to PDF?
disabled=Disabled
draftFound=A draft for '{1}' has been found. Load it into the editor or discard it to continue.
draftRevisionMismatch=There is a different version of this diagram on a shared draft of this page. Please edit the diagram from the draft to ensure you are working with the latest version.
selectDraft=Select a draft to continue editing:
dragAndDropNotSupported=Drag and drop not supported for images. Would you like to import instead?
dropboxCharsNotAllowed=The following characters are not allowed: \ / : ? * " |
check=Check
checksum=Checksum
circle=Circle
cisco=Cisco
classic=Classic
clearDefaultStyle=Clear Default Style
clearWaypoints=Clear Waypoints
clipart=Clipart
close=Close
closingFile=Closing file
realtimeCollaboration=Real-Time Collaboration
collaborate=Collaborate
collaborator=Collaborator
collaborators=Collaborators
collapse=Collapse
collapseExpand=Collapse/Expand
collapse-expand=Click to collapse/expand\nShift-click to move neighbors \nAlt-click to protect group size
collapsible=Collapsible
comic=Comic
comment=Comment
commentsNotes=Comments/Notes
compress=Compress
configuration=Configuration
connect=Connect
connecting=Connecting
connectWithDrive=Connect with Google Drive
connection=Connection
connectionArrows=Connection Arrows
connectionPoints=Connection Points
constrainProportions=Constrain Proportions
containsValidationErrors=Contains validation errors
copiedToClipboard=Copied to clipboard
copy=Copy
copyConnect=Copy on connect
copyCreated=A copy of the file was created.
copyData=Copy Data
copyOf=Copy of {1}
copyOfDrawing=Copy of Drawing
copySize=Copy Size
copyStyle=Copy Style
create=Create
createBlankDiagram=Create Blank Diagram
createNewDiagram=Create New Diagram
createRevision=Create Revision
createShape=Create Shape
crop=Crop
curved=Curved
custom=Custom
current=Current
currentPage=Current page
cut=Cut
dashed=Dashed
decideLater=Decide later
default=Default
delete=Delete
deleteColumn=Delete Column
deleteLibrary401=Insufficient permissions to delete this library
deleteLibrary404=Selected library could not be found
deleteLibrary500=Error deleting library
deleteLibraryConfirm=You are about to permanently delete this library. Are you sure you want to do this?
deleteRow=Delete Row
description=Description
describeYourDiagram=Describe your diagram
device=Device
diagram=Diagram
diagramContent=Diagram Content
diagramLocked=Diagram has been locked to prevent further data loss.
diagramLockedBySince=The diagram is locked by {1} since {2} ago
diagramName=Diagram Name
diagramIsPublic=Diagram is public
diagramIsNotPublic=Diagram is not public
diamond=Diamond
diamondThin=Diamond (thin)
didYouKnow=Did you know...
direction=Direction
discard=Discard
discardChangesAndReconnect=Discard Changes and Reconnect
googleDriveMissingClickHere=Google Drive missing? Click here!
discardChanges=Discard Changes
disconnected=Disconnected
distribute=Distribute
done=Done
doNotShowAgain=Do not show again
dotted=Dotted
doubleClickOrientation=Doubleclick to change orientation
doubleClickTooltip=Doubleclick to insert text
doubleClickChangeProperty=Doubleclick to change property name
download=Download
downloadDesktop=Get Desktop
downloadAs=Download as
clickHereToSave=Click here to save.
dpi=DPI
draftDiscarded=Draft discarded
draftSaved=Draft saved
dragElementsHere=Drag elements here
dragImagesHere=Drag images or URLs here
dragUrlsHere=Drag URLs here
draw.io=draw.io
drawing=Drawing{1}
drawingEmpty=Drawing is empty
drawingTooLarge=Drawing is too large
drawioForWork=Draw.io for GSuite
dropbox=Dropbox
duplicate=Duplicate
duplicateIt=Duplicate {1}
divider=Divider
dx=Dx
dy=Dy
east=East
edit=Edit
editData=Edit Data
editDiagram=Edit Diagram
editGeometry=Edit Geometry
editImage=Edit Image
editImageUrl=Edit Image URL
editLink=Edit Link
editShape=Edit Shape
editStyle=Edit Style
editText=Edit Text
editTooltip=Edit Tooltip
glass=Glass
googleImages=Google Images
imageSearch=Image Search
eip=EIP
embed=Embed
embedFonts=Embed Fonts
embedImages=Embed Images
mainEmbedNotice=Paste this into the page
electrical=Electrical
ellipse=Ellipse
embedNotice=Paste this once at the end of the page
enterGroup=Enter Group
enterName=Enter Name
enterPropertyName=Enter Property Name
enterValue=Enter Value
entityRelation=Entity Relation
entityRelationshipDiagram=Entity Relationship Diagram
error=Error
errorDeletingFile=Error deleting file
errorLoadingFile=Error loading file
errorRenamingFile=Error renaming file
errorRenamingFileNotFound=Error renaming file. File was not found.
errorRenamingFileForbidden=Error renaming file. Insufficient access rights.
errorSavingDraft=Error saving draft
errorSavingFile=Error saving file
errorSavingFileUnknown=Error authorizing with Google's servers. Please refresh the page to re-attempt.
errorSavingFileForbidden=Error saving file. Insufficient access rights.
errorSavingFileNameConflict=Could not save diagram. Current page already contains file named '{1}'.
errorSavingFileNotFound=Error saving file. File was not found.
errorSavingFileReadOnlyMode=Could not save diagram while read-only mode is active.
errorSavingFileSessionTimeout=Your session has ended. Please <a target='_blank' href='{1}'>{2}</a> and return to this tab to try to save again.
errorSendingFeedback=Error sending feedback.
errorUpdatingPreview=Error updating preview.
exit=Exit
exitGroup=Exit Group
expand=Expand
export=Export
exporting=Exporting
exportAs=Export as
exportOptionsDisabled=Export options disabled
exportOptionsDisabledDetails=The owner has disabled options to download, print or copy for commenters and viewers on this file.
externalChanges=External Changes
extras=Extras
facebook=Facebook
failedToSaveTryReconnect=Failed to save, trying to reconnect
featureRequest=Feature Request
feedback=Feedback
feedbackSent=Feedback successfully sent.
floorplans=Floorplans
file=File
fileChangedOverwriteDialog=The file has been modified. Do you want to save the file and overwrite those changes?
fileChangedSyncDialog=The file has been modified.
fileChangedSync=The file has been modified. Click here to synchronize.
overwrite=Overwrite
synchronize=Synchronize
filename=Filename
fileExists=File already exists
fileMovedToTrash=File was moved to trash
fileNearlyFullSeeFaq=File nearly full, please see FAQ
fileNotFound=File not found
repositoryNotFound=Repository not found
fileNotFoundOrDenied=The file was not found. It does not exist or you do not have access.
fileNotLoaded=File not loaded
fileNotSaved=File not saved
fileOpenLocation=How would you like to open these file(s)?
filetypeHtml=.html causes file to save as HTML with redirect to cloud URL
filetypePng=.png causes file to save as PNG with embedded data
filetypeSvg=.svg causes file to save as SVG with embedded data
fileWillBeSavedInAppFolder={1} will be saved in the app folder.
fill=Fill
fillColor=Fill Color
filterCards=Filter Cards
find=Find
fit=Fit
fitContainer=Resize Container
fitIntoContainer=Fit into Container
fitPage=Fit Page
fitPageWidth=Fit Page Width
fitTo=Fit to
fitToSheetsAcross=sheet(s) across
fitToBy=by
fitToSheetsDown=sheet(s) down
fitTwoPages=Two Pages
fitWindow=Fit Window
flip=Flip
flipH=Flip Horizontal
flipV=Flip Vertical
flowchart=Flowchart
folder=Folder
font=Font
fontColor=Font Color
fontFamily=Font Family
fontSize=Font Size
forbidden=You are not authorized to access this file
format=Format
formatPanel=Format Panel
formatted=Formatted
formattedText=Formatted Text
formatPng=PNG
formatGif=GIF
formatJpg=JPEG
formatPdf=PDF
formatSql=SQL
formatSvg=SVG
formatHtmlEmbedded=HTML
formatSvgEmbedded=SVG (with XML)
formatVsdx=VSDX
formatVssx=VSSX
formatWebp=WebP
formatXmlPlain=XML (Plain)
formatXml=XML
forum=Discussion/Help Forums
freehand=Freehand
fromTemplate=From Template
fromTemplateUrl=From Template URL
fromText=From Text
fromUrl=From URL
fromThisPage=From this page
fullscreen=Fullscreen
gap=Gap
gcp=GCP
general=General
getNotionChromeExtension=Get the Notion Chrome Extension
github=GitHub
gitlab=GitLab
gliffy=Gliffy
global=Global
googleDocs=Google Docs
googleDrive=Google Drive
googleGadget=Google Gadget
googleSharingNotAvailable=Sharing is only available via Google Drive. Please click Open below and share from the more actions menu:
googleSlides=Google Slides
googleSites=Google Sites
googleSheets=Google Sheets
gradient=Gradient
gradientColor=Color
grid=Grid
gridColor=Grid Color
gridSize=Grid Size
group=Group
guides=Guides
hateApp=I hate draw.io
heading=Heading
height=Height
help=Help
helpTranslate=Help us translate this application
hide=Hide
hideIt=Hide {1}
hidden=Hidden
home=Home
horizontal=Horizontal
horizontalFlow=Horizontal Flow
horizontalTree=Horizontal Tree
howTranslate=How good is the translation in your language?
html=HTML
htmlText=HTML Text
id=ID
iframe=IFrame
ignore=Ignore
image=Image
imageUrl=Image URL
images=Images
imagePreviewError=This image couldn't be loaded for preview. Please check the URL.
imageTooBig=Image too big
imgur=Imgur
import=Import
importFrom=Import from
improveContrast=Improve Contrast
includeCopyOfMyDiagram=Include a copy of my diagram
increaseIndent=Increase Indent
decreaseIndent=Decrease Indent
insert=Insert
insertColumnBefore=Insert Column Left
insertColumnAfter=Insert Column Right
insertEllipse=Insert Ellipse
insertImage=Insert Image
insertHorizontalRule=Insert Horizontal Rule
insertLink=Insert Link
insertPage=Insert Page
insertRectangle=Insert Rectangle
insertRhombus=Insert Rhombus
insertRowBefore=Insert Row Above
insertRowAfter=Insert Row After
insertText=Insert Text
inserting=Inserting
installApp=Install App
invalidFilename=Diagram names must not contain the following characters: \ / | : ; { } < > & + ? = "
invalidLicenseSeeThisPage=Your license is invalid, please see this <a target="_blank" href="https://www.drawio.com/doc/faq/license-drawio-confluence-jira-cloud">page</a>.
invalidInput=Invalid input
invalidName=Invalid name
invalidOrMissingFile=Invalid or missing file
invalidPublicUrl=Invalid public URL
isometric=Isometric
ios=iOS
italic=Italic
kennedy=Kennedy
keyboardShortcuts=Keyboard Shortcuts
labels=Labels
layers=Layers
landscape=Landscape
language=Language
leanMapping=Lean Mapping
lastChange=Last change {1} ago
lessThanAMinute=less than a minute
licensingError=Licensing Error
licenseHasExpired=The license for {1} has expired on {2}. Click here.
licenseRequired=This feature requires draw.io to be licensed.
licenseWillExpire=The license for {1} will expire on {2}. Click here.
light=Light
lineJumps=Line jumps
linkAccountRequired=If the diagram is not public a Google account is required to view the link.
linkText=Link Text
list=List
minute=minute
minutes=minutes
hours=hours
days=days
months=months
years=years
restartForChangeRequired=Changes will take effect after a restart of the application.
laneColor=Lanecolor
languageCode=Language Code
lastModified=Last modified
layout=Layout
left=Left
leftAlign=Left Align
leftToRight=Left to right
libraryTooltip=Drag and drop shapes here or click + to insert. Double click to edit.
lightbox=Lightbox
line=Line
lineend=Line end
lineheight=Line Height
linestart=Line start
linewidth=Linewidth
link=Link
links=Links
loading=Loading
lockUnlock=Lock/Unlock
loggedOut=Logged Out
logIn=log in
loveIt=I love {1}
lucidchart=Lucidchart
maps=Maps
mathematicalTypesetting=Mathematical Typesetting
makeCopy=Make a Copy
manual=Manual
merge=Merge
mermaid=Mermaid
microsoftOffice=Microsoft Office
microsoftExcel=Microsoft Excel
microsoftPowerPoint=Microsoft PowerPoint
microsoftWord=Microsoft Word
middle=Middle
minimal=Minimal
misc=Misc
mockups=Mockups
modern=Modern
modificationDate=Modification date
modifiedBy=Modified by
more=More
moreResults=More Results
moreShapes=More Shapes
move=Move
moveToFolder=Move to Folder
moving=Moving
moveSelectionTo=Move selection to {1}
myDrive=My Drive
myFiles=My Files
name=Name
navigation=Navigation
network=Network
networking=Networking
new=New
newLibrary=New Library
nextPage=Next Page
no=Ne
noPickFolder=No, pick folder
noAttachments=No attachments found
noColor=No Color
noFiles=No Files
noFileSelected=No file selected
noLibraries=No libraries found
noMoreResults=No more results
none=None
noOtherViewers=No other viewers
noPlugins=No plugins
noPreview=No preview
noResponse=No response from server
noResultsFor=No results for '{1}'
noRevisions=No revisions
noSearchResults=No search results found
noPageContentOrNotSaved=No anchors found on this page or it hasn't been saved yet
normal=Normal
north=North
notADiagramFile=Not a diagram file
notALibraryFile=Not a library file
notAvailable=Not available
notAUtf8File=Not a UTF-8 file
notConnected=Not connected
note=Note
notion=Notion
notSatisfiedWithImport=Not satisfied with the import?
notUsingService=Not using {1}?
numberedList=Numbered list
offline=Offline
ok=OK
oneDrive=OneDrive
online=Online
opacity=Opacity
open=Open
openArrow=Open Arrow
openExistingDiagram=Open Existing Diagram
openFile=Open File
openFrom=Open from
openLibrary=Open Library
openLibraryFrom=Open Library from
openLink=Open Link
openInNewWindow=Open in New Window
openInThisWindow=Open in This Window
openIt=Open {1}
openRecent=Open Recent
openSupported=Supported formats are files saved from this software (.xml), .vsdx and .gliffy
options=Options
organic=Organic
orgChart=Org Chart
orthogonal=Orthogonal
otherViewer=other viewer
otherViewers=other viewers
outline=Outline
oval=Oval
page=Page
pageContent=Page Content
pageNotFound=Page not found
pageWithNumber=Page-{1}
pages=Pages
pageTabs=Page Tabs
pageView=Page View
pageSetup=Page Setup
pageScale=Page Scale
pan=Pan
panTooltip=Space+Drag to pan
paperSize=Paper Size
pattern=Pattern
parallels=Parallels
paste=Paste
pasteData=Paste Data
pasteHere=Paste here
pasteSize=Paste Size
pasteStyle=Paste Style
perimeter=Perimeter
permissionAnyone=Anyone can edit
permissionAuthor=Owner and admins can edit
pickFolder=Pick a folder
pickLibraryDialogTitle=Select Library
publicDiagramUrl=Public URL of the diagram
placeholders=Placeholders
plantUml=PlantUML
plugins=Plugins
pluginUrl=Plugin URL
pluginWarning=The page has requested to load the following plugin(s):\n \n {1}\n \n Would you like to load these plugin(s) now?\n \n NOTE : Only allow plugins to run if you fully understand the security implications of doing so.\n
plusTooltip=Click to connect and clone (ctrl+click to clone, shift+click to connect). Drag to connect (ctrl+drag to clone).
portrait=Portrait
position=Position
posterPrint=Poster Print
preferences=Preferences
preview=Preview
previousPage=Previous Page
presentationMode=Presentation Mode
print=Print
printAllPages=Print All Pages
procEng=Proc. Eng.
project=Project
priority=Priority
processForHiringNewEmployee=Process for hiring a new employee
properties=Properties
publish=Publish
quickStart=Quick Start Video
rack=Rack
radial=Radial
radialTree=Radial Tree
readOnly=Read-only
reconnecting=Reconnecting
recentlyUpdated=Recently Updated
recentlyViewed=Recently Viewed
rectangle=Rectangle
redirectToNewApp=This file was created or modified in a newer version of this app. You will be redirected now.
realtimeTimeout=It looks like you've made a few changes while offline. We're sorry, these changes cannot be saved.
redo=Redo
refresh=Refresh
regularExpression=Regular Expression
relative=Relative
relativeUrlNotAllowed=Relative URL not allowed
rememberMe=Remember me
rememberThisSetting=Remember this setting
removeFormat=Clear Formatting
removeFromGroup=Remove from Group
removeIt=Remove {1}
removeWaypoint=Remove Waypoint
rename=Rename
renamed=Renamed
renameIt=Rename {1}
renaming=Renaming
replace=Replace
replaceIt={1} already exists. Do you want to replace it?
replaceExistingDrawing=Replace existing drawing
required=required
requirementDiagram=Requirement Diagram
reset=Reset
resetView=Reset View
resize=Resize
resizeLargeImages=Do you want to resize large images to make the application run faster?
retina=Retina
responsive=Responsive
restore=Restore
restoring=Restoring
retryingIn=Retrying in {1} second(s)
retryingLoad=Load failed. Retrying...
retryingLogin=Login time out. Retrying...
reverse=Reverse
revision=Revision
revisionHistory=Revision History
rhombus=Rhombus
right=Right
rightAlign=Right Align
rightToLeft=Right to left
rotate=Rotate
rotateTooltip=Click and drag to rotate, click to turn shape only by 90 degrees
rotation=Rotation
rounded=Rounded
save=Save
saveAndExit=Save & Exit
saveAs=Save As
saveAsXmlFile=Save as XML file?
saved=Saved
saveDiagramFirst=Please save the diagram first
saveDiagramsTo=Save diagrams to
saveLibrary403=Insufficient permissions to edit this library
saveLibrary500=There was an error while saving the library
saveLibraryReadOnly=Could not save library while read-only mode is active
saving=Saving
scratchpad=Scratchpad
scrollbars=Scrollbars
search=Search
searchShapes=Search Shapes
selectAll=Select All
selectionOnly=Selection Only
selectCard=Select Card
selectEdges=Select Edges
selectFile=Select File
selectFolder=Select Folder
selectFont=Select Font
selectNone=Select None
selectTemplate=Select Template
selectVertices=Select Vertices
sendBackward=Send Backward
sendMessage=Send
sendYourFeedback=Send your feedback
sequenceDiagram=Sequence Diagram
serviceUnavailableOrBlocked=Service unavailable or blocked
sessionExpired=Your session has expired. Please refresh the browser window.
sessionTimeoutOnSave=Your session has timed out and you have been disconnected from the Google Drive. Press OK to login and save.
setAsDefaultStyle=Set as Default Style
settings=Settings
shadow=Shadow
shape=Shape
shapes=Shapes
share=Share
shareCursor=Share Mouse Cursor
shareLink=Link for shared editing
sharingAvailable=Sharing available for Google Drive and OneDrive files.
saveItToGoogleDriveToCollaborate=You'll need to save "{1}" to Google Drive before you can collaborate.
saveToGoogleDrive=Save to Google Drive
sharp=Sharp
show=Show
showRemoteCursors=Show Remote Mouse Cursors
showStartScreen=Show Start Screen
sidebarTooltip=Click or drag and drop shapes. Shift+click to change selection. Alt+click to insert and connect.
signs=Signs
signOut=Sign out
simple=Simple
simpleArrow=Simple Arrow
simpleViewer=Simple Viewer
size=Size
sketch=Sketch
snapToGrid=Snap to Grid
solid=Solid
sourceSpacing=Source Spacing
south=South
software=Software
space=Space
spacing=Spacing
specialLink=Special Link
stateDiagram=State Diagram
standard=Standard
startDrawing=Start drawing
stopDrawing=Stop drawing
starting=Starting
straight=Straight
strikethrough=Strikethrough
strokeColor=Line Color
style=Style
subscript=Subscript
summary=Summary
superscript=Superscript
support=Support
swap=Swap
swimlaneDiagram=Swimlane Diagram
sysml=SysML
tags=Tags
table=Table
tables=Tables
takeOver=Take Over
targetSpacing=Target Spacing
template=Template
templates=Templates
text=Text
textAlignment=Text Alignment
textOpacity=Text Opacity
theme=Theme
timeout=Timeout
title=Title
to=to
toBack=To Back
toFront=To Front
tooLargeUseDownload=Too large, use download instead.
toolbar=Toolbar
tooltips=Tooltips
top=Top
topAlign=Top Align
topLeft=Top Left
topRight=Top Right
transparent=Transparent
transparentBackground=Transparent Background
trello=Trello
tryAgain=Try again
tryOpeningViaThisPage=Try opening via this page
turn=Rotate shape only by 90°
type=Type
twitter=Twitter
uml=UML
unassigned=Unassigned
underline=Underline
undo=Undo
ungroup=Ungroup
unmerge=Unmerge
unsavedChanges=Unsaved changes
unsavedChangesClickHereToSave=Unsaved changes. Click here to save.
untitled=Untitled
untitledDiagram=Untitled Diagram
untitledLayer=Untitled Layer
untitledLibrary=Untitled Library
unknownError=Unknown error
updateFile=Update {1}
updatingDocument=Updating Document. Please wait...
updatingPreview=Updating Preview. Please wait...
updatingSelection=Updating Selection. Please wait...
upload=Upload
url=URL
useOffline=Use Offline
useRootFolder=Use root folder?
userManual=User Manual
vertical=Vertical
verticalFlow=Vertical Flow
verticalTree=Vertical Tree
view=View
viewerSettings=Viewer Settings
viewUrl=Link to view: {1}
voiceAssistant=Voice Assistant (beta)
warning=Warning
waypoints=Waypoints
west=West
where=Where
width=Width
wiki=Wiki
wordWrap=Word Wrap
writingDirection=Writing Direction
yes=Ja
yourEmailAddress=Your email address
zoom=Zoom
zoomIn=Zoom In
zoomOut=Zoom Out
basic=Basic
businessprocess=Business Processes
charts=Charts
engineering=Engineering
flowcharts=Flowcharts
gmdl=Material Design
mindmaps=Mindmaps
mockups=Mockups
networkdiagrams=Network Diagrams
nothingIsSelected=Nothing is selected
other=Other
softwaredesign=Software Design
venndiagrams=Venn Diagrams
webEmailOrOther=Web, email or any other internet address
webLink=Web Link
wireframes=Wireframes
property=Property
value=Value
showMore=Show More
showLess=Show Less
myDiagrams=My Diagrams
allDiagrams=All Diagrams
recentlyUsed=Recently used
listView=List view
gridView=Grid view
resultsFor=Results for '{1}'
oneDriveCharsNotAllowed=The following characters are not allowed: ~ " # % * : < > ? / \ { | }
oneDriveInvalidDeviceName=The specified device name is invalid
officeNotLoggedOD=You are not logged in to OneDrive. Please open draw.io task pane and login first.
officeSelectSingleDiag=Please select a single draw.io diagram only without other contents.
officeSelectDiag=Please select a draw.io diagram.
officeCannotFindDiagram=Cannot find a draw.io diagram in the selection
noDiagrams=No diagrams found
authFailed=Authentication failed
officeFailedAuthMsg=Unable to successfully authenticate user or authorize application.
convertingDiagramFailed=Converting diagram failed
officeCopyImgErrMsg=Due to some limitations in the host application, the image could not be inserted. Please manually copy the image then paste it to the document.
insertingImageFailed=Inserting image failed
officeCopyImgInst=Instructions: Right-click the image below. Select "Copy image" from the context menu. Then, in the document, right-click and select "Paste" from the context menu.
folderEmpty=Folder is empty
recent=Recent
sharedWithMe=Shared With Me
sharepointSites=Sharepoint Sites
errorFetchingFolder=Error fetching folder items
errorAuthOD=Error authenticating to OneDrive
officeMainHeader=Adds draw.io diagrams to your document.
officeStepsHeader=This add-in performs the following steps:
officeStep1=Connects to Microsoft OneDrive, Google Drive or your device.
officeStep2=Select a draw.io diagram.
officeStep3=Insert the diagram into the document.
officeAuthPopupInfo=Please complete the authentication in the pop-up window.
officeSelDiag=Select draw.io Diagram:
files=Files
shared=Shared
sharepoint=Sharepoint
officeManualUpdateInst=Instructions: Copy draw.io diagram from the document. Then, in the box below, right-click and select "Paste" from the context menu.
officeClickToEdit=Click icon to start editing:
pasteDiagram=Paste draw.io diagram here
connectOD=Connect to OneDrive
selectChildren=Select Children
selectSiblings=Select Siblings
selectParent=Select Parent
selectDescendants=Select Descendants
lastSaved=Last saved {1} ago
resolve=Resolve
reopen=Re-open
showResolved=Show Resolved
reply=Reply
objectNotFound=Object not found
reOpened=Re-opened
markedAsResolved=Marked as resolved
noCommentsFound=No comments found
comments=Comments
timeAgo={1} ago
confluenceCloud=Confluence Cloud
libraries=Libraries
confAnchor=Confluence Page Anchor
confTimeout=The connection has timed out
confSrvTakeTooLong=The server at {1} is taking too long to respond.
confCannotInsertNew=Cannot insert draw.io diagram to a new Confluence page
confSaveTry=Please save the page and try again.
confCannotGetID=Unable to determine page ID
confContactAdmin=Please contact your Confluence administrator.
readErr=Read Error
editingErr=Editing Error
confExtEditNotPossible=This diagram cannot be edited externally. Please try editing it while editing the page
confEditedExt=Diagram/Page edited externally
diagNotFound=Diagram Not Found
confEditedExtRefresh=Diagram/Page is edited externally. Please refresh the page.
confCannotEditDraftDelOrExt=Cannot edit diagrams in a draft page, diagram is deleted from the page, or diagram is edited externally. Please check the page.
retBack=Return back
confDiagNotPublished=The diagram does not belong to a published page
createdByDraw=Created by draw.io
filenameShort=Filename too short
invalidChars=Invalid characters
alreadyExst={1} already exists
draftReadErr=Draft Read Error
diagCantLoad=Diagram cannot be loaded
draftWriteErr=Draft Write Error
draftCantCreate=Draft could not be created
confDuplName=Duplicate diagram name detected. Please pick another name.
confSessionExpired=Looks like your session expired. Log in again to keep working.
login=Login
drawPrev=draw.io preview
drawDiag=draw.io diagram
invalidCallFnNotFound=Invalid Call: {1} not found
invalidCallErrOccured=Invalid Call: An error occurred, {1}
anonymous=Anonymous
confGotoPage=Go to containing page
showComments=Show Comments
confError=Error: {1}
gliffyImport=Gliffy Import
gliffyImportInst1=Click the "Start Import" button to import all Gliffy diagrams to draw.io.
gliffyImportInst2=Please note that the import procedure will take some time and the browser window must remain open until the import is completed.
startImport=Start Import
drawConfig=draw.io Configuration
customLib=Custom Libraries
customTemp=Custom Templates
pageIdsExp=Page IDs Export
drawReindex=draw.io re-indexing (beta)
working=Working
drawConfigNotFoundInst=draw.io Configuration Space (DRAWIOCONFIG) does not exist. This space is needed to store draw.io configuration files and custom libraries/templates.
createConfSp=Create Config Space
unexpErrRefresh=Unexpected error, please refresh the page and try again.
configJSONInst=Write draw.io JSON configuration in the editor below then click save. If you need help, please refer to
thisPage=this page
curCustLib=Current Custom Libraries
libName=Library Name
action=Action
drawConfID=draw.io Config ID
addLibInst=Click the "Add Library" button to upload a new library.
addLib=Add Library
customTempInst1=Custom templates are draw.io diagrams saved in children pages of
customTempInst2=For more details, please refer to
tempsPage=Templates page
pageIdsExpInst1=Select export target, then click the "Start Export" button to export all pages IDs.
pageIdsExpInst2=Please note that the export procedure will take some time and the browser window must remain open until the export is completed.
startExp=Start Export
refreshDrawIndex=Refresh draw.io Diagrams Index
reindexInst1=Click the "Start Indexing" button to refresh draw.io diagrams index.
reindexInst2=Please note that the indexing procedure will take some time and the browser window must remain open until the indexing is completed.
startIndexing=Start Indexing
confAPageFoundFetch=Page "{1}" found. Fetching
confAAllDiagDone=All {1} diagrams processed. Process finished.
confAStartedProcessing=Started processing page "{1}"
confAAllDiagInPageDone=All {1} diagrams in page "{2}" processed successfully.
confAPartialDiagDone={1} out of {2} {3} diagrams in page "{4}" processed successfully.
confAUpdatePageFailed=Updating page "{1}" failed.
confANoDiagFoundInPage=No {1} diagrams found in page "{2}".
confAFetchPageFailed=Fetching the page failed.
confANoDiagFound=No {1} diagrams found. Process finished.
confASearchFailed=Searching for {1} diagrams failed. Please try again later.
confAGliffyDiagFound={2} diagram "{1}" found. Importing
confAGliffyDiagImported={2} diagram "{1}" imported successfully.
confASavingImpGliffyFailed=Saving imported {2} diagram "{1}" failed.
confAImportedFromByDraw=Imported from "{1}" by draw.io
confAImportGliffyFailed=Importing {2} diagram "{1}" failed.
confAFetchGliffyFailed=Fetching {2} diagram "{1}" failed.
confACheckBrokenDiagLnk=Checking for broken diagrams links.
confADelDiagLinkOf=Deleting diagram link of "{1}"
confADupLnk=(duplicate link)
confADelDiagLnkFailed=Deleting diagram link of "{1}" failed.
confAUnexpErrProcessPage=Unexpected error during processing the page with id: {1}
confADiagFoundIndex=Diagram "{1}" found. Indexing
confADiagIndexSucc=Diagram "{1}" indexed successfully.
confAIndexDiagFailed=Indexing diagram "{1}" failed.
confASkipDiagOtherPage=Skipped "{1}" as it belongs to another page!
confADiagUptoDate=Diagram "{1}" is up to date.
confACheckPagesWDraw=Checking pages having draw.io diagrams.
confAErrOccured=An error occurred!
savedSucc=Saved successfully
confASaveFailedErr=Saving Failed (Unexpected Error)
character=Character
confAConfPageDesc=This page contains draw.io configuration file (configuration.json) as attachment
confALibPageDesc=This page contains draw.io custom libraries as attachments
confATempPageDesc=This page contains draw.io custom templates as attachments
working=Working
confAConfSpaceDesc=This space is used to store draw.io configuration files and custom libraries/templates
confANoCustLib=No Custom Libraries
delFailed=Delete failed!
showID=Show ID
confAIncorrectLibFileType=Incorrect file type. Libraries should be XML files.
uploading=Uploading
confALibExist=This library already exists
confAUploadSucc=Uploaded successfully
confAUploadFailErr=Upload Failed (Unexpected Error)
hiResPreview=High Res Preview
officeNotLoggedGD=You are not logged in to Google Drive. Please open draw.io task pane and login first.
officePopupInfo=Please complete the process in the pop-up window.
pickODFile=Pick OneDrive File
createODFile=Create OneDrive File
pickGDriveFile=Pick Google Drive File
createGDriveFile=Create Google Drive File
pickDeviceFile=Pick Device File
vsdNoConfig="vsdurl" is not configured
ruler=Ruler
units=Units
points=Points
inches=Inches
millimeters=Millimeters
confEditDraftDelOrExt=This diagram is in a draft page, is deleted from the page, or is edited externally. It will be saved as a new attachment version and may not be reflected in the page.
confDiagEditedExt=Diagram is edited in another session. It will be saved as a new attachment version but the page will show other session's modifications.
macroNotFound=Macro Not Found
confAInvalidPageIdsFormat=Incorrect Page IDs file format
confACollectingCurPages=Collecting current pages
confABuildingPagesMap=Building pages mapping
confAProcessDrawDiag=Started processing imported draw.io diagrams
confAProcessDrawDiagDone=Finished processing imported draw.io diagrams
confAProcessImpPages=Started processing imported pages
confAErrPrcsDiagInPage=Error processing draw.io diagrams in page "{1}"
confAPrcsDiagInPage=Processing draw.io diagrams in page "{1}"
confAImpDiagram=Importing diagram "{1}"
confAImpDiagramFailed=Importing diagram "{1}" failed. Cannot find its new page ID. Maybe it points to a page that is not imported.
confAImpDiagramError=Error importing diagram "{1}". Cannot fetch or save the diagram. Cannot fix this diagram links.
confAUpdateDgrmCCFailed=Updating link to diagram "{1}" failed.
confImpDiagramSuccess=Updating diagram "{1}" done successfully.
confANoLnksInDrgm=No links to update in: {1}
confAUpdateLnkToPg=Updated link to page: "{1}" in diagram: "{2}"
confAUpdateLBLnkToPg=Updated lightbox link to page: "{1}" in diagram: "{2}"
confAUpdateLnkBase=Updated base URL from: "{1}" to: "{2}" in diagram: "{3}"
confAPageIdsImpDone=Page IDs Import finished
confAPrcsMacrosInPage=Processing draw.io macros in page "{1}"
confAErrFetchPage=Error fetching page "{1}"
confAFixingMacro=Fixing macro of diagram "{1}"
confAErrReadingExpFile=Error reading export file
confAPrcsDiagInPageDone=Processing draw.io diagrams in page "{1}" finished
confAFixingMacroSkipped=Fixing macro of diagram "{1}" failed. Cannot find its new page ID. Maybe it points to a page that is not imported.
pageIdsExpTrg=Export target
confALucidDiagImgImported={2} diagram "{1}" image extracted successfully
confASavingLucidDiagImgFailed=Extracting {2} diagram "{1}" image failed
confGetInfoFailed=Fetching file info from {1} failed.
confCheckCacheFailed=Cannot get cached file info.
confReadFileErr=Cannot read "{1}" file from {2}.
confSaveCacheFailed=Unexpected error. Cannot save cached file
orgChartType=Org Chart Type
linear=Linear
hanger2=Hanger 2
hanger4=Hanger 4
fishbone1=Fishbone 1
fishbone2=Fishbone 2
1ColumnLeft=Single Column Left
1ColumnRight=Single Column Right
smart=Smart
parentChildSpacing=Parent Child Spacing
siblingSpacing=Sibling Spacing
confNoPermErr=Sorry, you don't have enough permissions to view this embedded diagram from page {1}
copyAsImage=Copy as Image
lucidImport=Lucidchart Import
lucidImportInst1=Click the "Start Import" button to import all Lucidchart diagrams.
installFirst=Please install {1} first
drawioChromeExt=draw.io Chrome Extension
loginFirstThen=Please login to {1} first, then {2}
errFetchDocList=Error: Couldn't fetch documents list
builtinPlugins=Built-in Plugins
extPlugins=External Plugins
backupFound=Backup file found
chromeOnly=This feature only works in Google Chrome
msgDeleted=This message has been deleted
confAErrFetchDrawList=Error fetching diagrams list. Some diagrams are skipped.
confAErrCheckDrawDiag=Cannot check diagram {1}
confAErrFetchPageList=Error fetching pages list
confADiagImportIncom={1} diagram "{2}" is imported partially and may have missing shapes
invalidSel=Invalid selection
diagNameEmptyErr=Diagram name cannot be empty
openDiagram=Open Diagram
newDiagram=New diagram
editable=Editable
confAReimportStarted=Re-import {1} diagrams started...
spaceFilter=Filter by spaces
curViewState=Current Viewer State
pageLayers=Page and Layers
customize=Customize
firstPage=First Page (All Layers)
curEditorState=Current Editor State
noAnchorsFound=No anchors found
attachment=Attachment
curDiagram=Current Diagram
recentDiags=Recent Diagrams
csvImport=CSV Import
chooseFile=Choose a file...
choose=Choose
gdriveFname=Google Drive filename
widthOfViewer=Width of the viewer (px)
heightOfViewer=Height of the viewer (px)
autoSetViewerSize=Automatically set the size of the viewer
thumbnail=Thumbnail
prevInDraw=Preview in draw.io
onedriveFname=OneDrive filename
diagFname=Diagram filename
diagUrl=Diagram URL
showDiag=Show Diagram
diagPreview=Diagram Preview
csvFileUrl=CSV File URL
generate=Generate
selectDiag2Insert=Please select a diagram to insert it.
errShowingDiag=Unexpected error. Cannot show diagram
noRecentDiags=No recent diagrams found
fetchingRecentFailed=Failed to fetch recent diagrams
useSrch2FindDiags=Use the search box to find draw.io diagrams
cantReadChckPerms=Cannot read the specified diagram. Please check you have read permission on that file.
cantFetchChckPerms=Cannot fetch diagram info. Please check you have read permission on that file.
searchFailed=Searching failed. Please try again later.
plsTypeStr=Please type a search string.
unsupportedFileChckUrl=Unsupported file. Please check the specified URL
diagNotFoundChckUrl=Diagram not found or cannot be accessed. Please check the specified URL
csvNotFoundChckUrl=CSV file not found or cannot be accessed. Please check the specified URL
cantReadUpload=Cannot read the uploaded diagram
select=Select
errCantGetIdType=Unexpected Error: Cannot get content id or type.
errGAuthWinBlocked=Error: Google Authentication window blocked
authDrawAccess=Authorize draw.io to access {1}
connTimeout=The connection has timed out
errAuthSrvc=Error authenticating to {1}
plsSelectFile=Please select a file
mustBgtZ={1} must be greater than zero
cantLoadPrev=Cannot load file preview.
errAccessFile=Error: Access Denied. You do not have permission to access "{1}".
noPrevAvail=No preview is available.
personalAccNotSup=Personal accounts are not supported.
errSavingTryLater=Error occurred during saving, please try again later.
plsEnterFld=Please enter {1}
invalidDiagUrl=Invalid Diagram URL
unsupportedVsdx=Unsupported vsdx file
unsupportedImg=Unsupported image file
unsupportedFormat=Unsupported file format
plsSelectSingleFile=Please select a single file only
attCorrupt=Attachment file "{1}" is corrupted
loadAttFailed=Failed to load attachment "{1}"
embedDrawDiag=Embed draw.io Diagram
addDiagram=Add Diagram
embedDiagram=Embed Diagram
editOwningPg=Edit owning page
deepIndexing=Deep Indexing (Index diagrams that aren't used in any page also)
confADeepIndexStarted=Deep Indexing Started
confADeepIndexDone=Deep Indexing Done
officeNoDiagramsSelected=No diagrams found in the selection
officeNoDiagramsInDoc=No diagrams found in the document
officeNotSupported=This feature is not supported in this host application
someImagesFailed={1} out of {2} failed due to the following errors
importingNoUsedDiagrams=Importing {1} Diagrams not used in pages
importingDrafts=Importing {1} Diagrams in drafts
processingDrafts=Processing drafts
updatingDrafts=Updating drafts
updateDrafts=Update drafts
notifications=Notifications
drawioImp=draw.io Import
confALibsImp=Importing draw.io Libraries
confALibsImpFailed=Importing {1} library failed
contributors=Contributors
drawDiagrams=draw.io Diagrams
errFileNotFoundOrNoPer=Error: Access Denied. File not found or you do not have permission to access "{1}" on {2}.
confACheckPagesWEmbed=Checking pages having embedded draw.io diagrams.
confADelBrokenEmbedDiagLnk=Removing broken embedded diagram links
replaceWith=Replace with
replaceAll=Replace All
confASkipDiagModified=Skipped "{1}" as it was modified after initial import
replFind=Replace/Find
matchesRepl={1} matches replaced
draftErrDataLoss=An error occurred while reading the draft file. The diagram cannot be edited now to prevent any possible data loss. Please try again later or contact support.
ibm=IBM
linkToDiagramHint=Add a link to this diagram. The diagram can only be edited from the page that owns it.
linkToDiagram=Link to Diagram
changedBy=Changed By
lastModifiedOn=Last modified on
searchResults=Search Results
showAllTemps=Show all templates
notionToken=Notion Token
selectDB=Select Database
noDBs=No Databases
diagramEdited={1} diagram "{2}" edited
confDraftPermissionErr=Draft cannot be written. Do you have attachment write/read permission on this page?
confFileTooBigErr=File size is too large. Pease check "Attachment Maximum Size" of "Attachment Settings" in Confluence Configuration.
owner=Owner
repository=Repository
branch=Branch
meters=Meters
teamsNoEditingMsg=Editor functionality is only available in Desktop environment (in MS Teams App or a web browser)
contactOwner=Contact Owner
viewerOnlyMsg=You cannot edit the diagrams in the mobile platform, please use the desktop client or a web browser.
website=Website
check4Updates=Check for updates
attWriteFailedRetry={1}: Attachment write failed, trying again in {2} seconds...
confPartialPageList=We couldn't fetch all pages due to an error in Confluence. Continuing using {1} pages only.
spellCheck=Spell checker
noChange=No Change
lblToSvg=Convert labels to SVG
txtSettings=Text Settings
LinksLost=Links will be lost
arcSize=Arc Size
editConnectionPoints=Edit Connection Points
notInOffline=Not supported while offline
notInDesktop=Not supported in Desktop App
confConfigSpaceArchived=draw.io Configuration space (DRAWIOCONFIG) is archived. Please restore it first.
confACleanOldVerStarted=Cleaning old diagram draft versions started
confACleanOldVerDone=Cleaning old diagram draft versions finished
confACleaningFile=Cleaning diagram draft "{1}" old versions
confAFileCleaned=Cleaning diagram draft "{1}" done
confAFileCleanFailed=Cleaning diagram draft "{1}" failed
confACleanOnly=Clean Diagram Drafts Only
brush=Brush
openDevTools=Open Developer Tools
autoBkp=Automatic Backup
confAIgnoreCollectErr=Ignore collecting current pages errors
drafts=Drafts
draftSaveInt=Draft save interval [sec] (0 to disable)
pluginsDisabled=External plugins disabled.
extExpNotConfigured=External image service is not configured
pathFilename=Path/Filename
confAHugeInstances=Very Large Instances
confAHugeInstancesDesc=If this instance includes 100,000+ pages, it is faster to request the current instance pages list from Atlassian. Please contact our support for more details.
choosePageIDsFile=Choose current page IDs csv file
chooseDrawioPsgesFile=Choose pages with draw.io diagrams csv file
private=Private
diagramTooLarge=The diagram is too large, please reduce its size and try again.
selectAdminUsers=Select Admin Users
xyzTeam={1} Team
addTeamTitle=Adding a new draw.io Team
addTeamInst1=To create a new draw.io Team, you need to create a new Atlassian group with "drawio-" prefix (e.g, a group named "drawio-marketing").
addTeamInst2=Then, configure which team member can edit/add configuration, templates, and libraries from this page.
drawioTeams=draw.io Teams
members=Members
adminEditors=Admins/Editors
allowAll=Allow all
noTeams=No teams found
errorLoadingTeams=Error Loading Teams
noTeamMembers=No team members found
errLoadTMembers=Error loading team members
errCreateTeamPage=Error creating team "{1}" page in "draw.io Configuration" space, please check you have the required permissions.
gotoConfigPage=Please create the space from draw.io "Configuration" page.
noAdminsSelected=No admins/editors selected
errCreateConfigFile=Error creating "configuration.json" file, please check you have the required permissions.
errSetPageRestr=Error setting page restrictions
notAdmin4Team=You are not an admin for this team
configUpdated=Configuration updated, restart the editor if you want to work with last configuration.
outOfDateRevisionAlert=You are editing a historical revision of the diagram, please review the revision and open it to replace the latest version. Or close and overwrite/merge later.
confAErrFaqs=There are {1} error(s), the following instructions may help fixing most of the cases. (Please download the log for future references)
confA403ErrFaq=There are ({1}) 403 error(s). The current users must have add (write) permissions on all pages and attachments. Even admins sometimes are not allowed to write to some pages via page restrictions
confA404ErrFaq=There are ({1}) 404 error(s). The attachment/page is not found. This is due to improper migration or the diagram file (an attachment of the page) is deleted.
confA500ErrFaq=There are ({1}) 500 error(s). An internal server error in Confluence Cloud. Such errors are due to overloading the server and usually fixed by retrying the process.
confAOtherErrFaq=There are ({1}) other error(s). Please check the error description. If the description is not clear, please contact our support.
confAReplaceBaseUrl=Replace Base URL in diagram links when no page ID mapping is found
drawSvgPrev=draw.io SVG preview
googleFonts=Google Fonts
diagDupl=Duplicate Diagram Detected
diagDuplMsg=This diagram is used in multiple places, which can result in unexpected results when edited. We've created an independent copy. Please open the editor again.
diagDuplNoEditMsg=This diagram is used in multiple places. Please edit it within its own page.
confCloudMigConfirm=Warning: This process will edit many pages and diagrams, so it is recommended to stop the Synchrony service during the process. Do you want to proceed?
confCloudMigNotice=In the Cloud instance, please add linkAdjustments to draw.io configuration as follows {1}. Without this configuation, links in diagrams pointing to Confluence pages will not work.
