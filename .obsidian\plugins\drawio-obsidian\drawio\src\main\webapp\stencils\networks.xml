<shapes name="mxgraph.networks">
<shape aspect="variable" h="100" name="Biometric Reader" strokewidth="2" w="58">
    <connections>
        <constraint name="N1" perimeter="0" x="0.29" y="0"/>
        <constraint name="N2" perimeter="0" x="0.5" y="0.1"/>
        <constraint name="S" perimeter="0" x="0.5" y="1"/>
        <constraint name="NE" perimeter="0" x="0.98" y="0.37"/>
        <constraint name="NW" perimeter="0" x="0.08" y="0.1"/>
        <constraint name="SE" perimeter="0" x="0.94" y="0.94"/>
        <constraint name="SW" perimeter="0" x="0.17" y="0.94"/>
        <constraint name="W" perimeter="0" x="0" y="0.54"/>
        <constraint name="E" perimeter="0" x="1" y="0.5"/>
    </connections>
    <foreground>
        <save/>
        <roundrect arcsize="17.86" h="28" w="28" x="3" y="10"/>
        <fillstroke/>
        <fillcolor color="#ffffff"/>
        <ellipse h="20" w="20" x="7" y="14"/>
        <fill/>
        <restore/>
        <rect/>
        <stroke/>
        <path>
            <move x="17" y="0"/>
            <line x="17" y="10"/>
        </path>
        <fillstroke/>
        <path>
            <move x="0" y="75"/>
            <arc large-arc-flag="0" rx="25" ry="30" sweep-flag="0" x="23" x-axis-rotation="0" y="100"/>
            <line x="48" y="100"/>
            <arc large-arc-flag="0" rx="20" ry="20" sweep-flag="0" x="58" x-axis-rotation="0" y="85"/>
            <line x="58" y="40"/>
            <arc large-arc-flag="1" rx="5" ry="5" sweep-flag="0" x="48" x-axis-rotation="0" y="40"/>
            <line x="48" y="55"/>
            <line x="46" y="55"/>
            <line x="46" y="40"/>
            <arc large-arc-flag="1" rx="5" ry="5" sweep-flag="0" x="36" x-axis-rotation="0" y="40"/>
            <line x="36" y="55"/>
            <line x="34" y="55"/>
            <line x="34" y="40"/>
            <arc large-arc-flag="1" rx="5" ry="5" sweep-flag="0" x="24" x-axis-rotation="0" y="40"/>
            <line x="24" y="55"/>
            <line x="22" y="55"/>
            <line x="22" y="25"/>
            <arc large-arc-flag="1" rx="5" ry="5" sweep-flag="0" x="12" x-axis-rotation="0" y="25"/>
            <line x="12" y="75"/>
            <line x="10" y="75"/>
            <line x="10" y="55"/>
            <arc large-arc-flag="1" rx="5" ry="5" sweep-flag="0" x="0" x-axis-rotation="0" y="55"/>
            <line x="0" y="75"/>
            <close/>
        </path>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="100" name="Business Center" strokewidth="2" w="87">
    <connections>
        <constraint name="N1" perimeter="0" x="0.22" y="0"/>
        <constraint name="N2" perimeter="0" x="0.44" y="0.26"/>
        <constraint name="NE" perimeter="0" x="0.83" y="0.33"/>
        <constraint name="NW" perimeter="0" x="0" y="0.26"/>
        <constraint name="S" perimeter="0" x="0.44" y="1"/>
        <constraint name="SE" perimeter="0" x="1" y="1"/>
        <constraint name="SW" perimeter="0" x="0" y="1"/>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="E" perimeter="0" x="1" y="0.5"/>
    </connections>
    <foreground>
        <save/>
        <path>
            <move x="0" y="100"/>
            <line x="0" y="26"/>
            <line x="7" y="26"/>
            <line x="7" y="20"/>
            <line x="12" y="20"/>
            <line x="12" y="14"/>
            <line x="16" y="14"/>
            <line x="19" y="0"/>
            <line x="22" y="14"/>
            <line x="26" y="14"/>
            <line x="26" y="20"/>
            <line x="31" y="20"/>
            <line x="31" y="26"/>
            <line x="38" y="26"/>
            <line x="38" y="100"/>
            <close/>
            <move x="49" y="100"/>
            <line x="49" y="64"/>
            <line x="61" y="64"/>
            <line x="61" y="41"/>
            <line x="87" y="41"/>
            <line x="87" y="100"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#ffffff"/>
        <rect h="4" w="6" x="77" y="45"/>
        <fill/>
        <rect h="4" w="6" x="77" y="52.5"/>
        <fill/>
        <rect h="4" w="6" x="77" y="60"/>
        <fill/>
        <rect h="4" w="6" x="77" y="67.5"/>
        <fill/>
        <rect h="4" w="6" x="77" y="75"/>
        <fill/>
        <rect h="4" w="6" x="77" y="82.5"/>
        <fill/>
        <rect h="4" w="6" x="77" y="90"/>
        <fill/>
        <rect h="4" w="6" x="65" y="45"/>
        <fill/>
        <rect h="4" w="6" x="65" y="52.5"/>
        <fill/>
        <rect h="4" w="6" x="65" y="60"/>
        <fill/>
        <rect h="4" w="6" x="65" y="67.5"/>
        <fill/>
        <rect h="4" w="6" x="65" y="75"/>
        <fill/>
        <rect h="4" w="6" x="65" y="82.5"/>
        <fill/>
        <rect h="4" w="6" x="65" y="90"/>
        <fill/>
        <rect h="4" w="6" x="53" y="67.5"/>
        <fill/>
        <rect h="4" w="6" x="53" y="75"/>
        <fill/>
        <rect h="4" w="6" x="53" y="82.5"/>
        <fill/>
        <rect h="4" w="6" x="53" y="90"/>
        <fill/>
        <rect h="4" w="6" x="28" y="30"/>
        <fill/>
        <rect h="4" w="6" x="28" y="37.5"/>
        <fill/>
        <rect h="4" w="6" x="28" y="45"/>
        <fill/>
        <rect h="4" w="6" x="28" y="52.5"/>
        <fill/>
        <rect h="4" w="6" x="28" y="60"/>
        <fill/>
        <rect h="4" w="6" x="28" y="67.5"/>
        <fill/>
        <rect h="4" w="6" x="28" y="75"/>
        <fill/>
        <rect h="4" w="6" x="28" y="82.5"/>
        <fill/>
        <rect h="4" w="6" x="16" y="30"/>
        <fill/>
        <rect h="4" w="6" x="16" y="37.5"/>
        <fill/>
        <rect h="4" w="6" x="16" y="45"/>
        <fill/>
        <rect h="4" w="6" x="16" y="52.5"/>
        <fill/>
        <rect h="4" w="6" x="16" y="60"/>
        <fill/>
        <rect h="4" w="6" x="16" y="67.5"/>
        <fill/>
        <rect h="4" w="6" x="16" y="75"/>
        <fill/>
        <rect h="4" w="6" x="16" y="82.5"/>
        <fill/>
        <rect h="4" w="6" x="4" y="30"/>
        <fill/>
        <rect h="4" w="6" x="4" y="37.5"/>
        <fill/>
        <rect h="4" w="6" x="4" y="45"/>
        <fill/>
        <rect h="4" w="6" x="4" y="52.5"/>
        <fill/>
        <rect h="4" w="6" x="4" y="60"/>
        <fill/>
        <rect h="4" w="6" x="4" y="67.5"/>
        <fill/>
        <rect h="4" w="6" x="4" y="75"/>
        <fill/>
        <rect h="4" w="6" x="4" y="82.5"/>
        <fill/>
        <rect h="9" w="6" x="16" y="90"/>
        <fill/>
        <rect h="4" w="6" x="4" y="90"/>
        <fill/>
        <rect h="4" w="6" x="28" y="90"/>
        <fill/>
        <restore/>
        <rect/>
        <stroke/>
        <path>
            <move x="70" y="41"/>
            <line x="74" y="37"/>
            <line x="78" y="41"/>
        </path>
        <stroke/>
        <path>
            <move x="72" y="33"/>
            <arc large-arc-flag="0" rx="4" ry="4" sweep-flag="0" x="78" x-axis-rotation="0" y="37"/>
            <close/>
        </path>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="48.51" name="Cloud" strokewidth="2" w="92.1">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0.08"/>
        <constraint name="S" perimeter="0" x="0.5" y="1"/>
        <constraint name="W" perimeter="0" x="0.06" y="0.5"/>
        <constraint name="E" perimeter="0" x="0.91" y="0.5"/>
        <constraint name="NW" perimeter="0" x="0.26" y="0.22"/>
        <constraint name="SW" perimeter="0" x="0.14" y="1"/>
        <constraint name="NE" perimeter="0" x="0.7" y="0.07"/>
        <constraint name="SE" perimeter="0" x="0.88" y="1"/>
    </connections>
    <foreground>
        <path>
            <move x="80.28" y="48.51"/>
            <arc large-arc-flag="1" rx="10" ry="10" sweep-flag="0" x="77.28" x-axis-rotation="0" y="23.51"/>
            <arc large-arc-flag="0" rx="22" ry="22" sweep-flag="0" x="35.28" x-axis-rotation="0" y="14.51"/>
            <arc large-arc-flag="0" rx="12" ry="12" sweep-flag="0" x="14.28" x-axis-rotation="0" y="21.51"/>
            <arc large-arc-flag="0" rx="13" ry="13" sweep-flag="0" x="13.28" x-axis-rotation="0" y="48.51"/>
            <close/>
        </path>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="85" name="Community" strokewidth="2" w="82">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0.12"/>
        <constraint name="N1" perimeter="0" x="0.23" y="0"/>
        <constraint name="N2" perimeter="0" x="0.77" y="0"/>
        <constraint name="S" perimeter="0" x="0.5" y="1"/>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="E" perimeter="0" x="1" y="0.5"/>
        <constraint name="NW" perimeter="0" x="0" y="0.12"/>
        <constraint name="SW" perimeter="0" x="0" y="1"/>
        <constraint name="NE" perimeter="0" x="1" y="0.12"/>
        <constraint name="SE" perimeter="0" x="1" y="1"/>
    </connections>
    <foreground>
        <path>
            <move x="0" y="85"/>
            <line x="0" y="10"/>
            <line x="19" y="0"/>
            <line x="38" y="10"/>
            <line x="38" y="85"/>
            <close/>
            <move x="44" y="85"/>
            <line x="44" y="10"/>
            <line x="63" y="0"/>
            <line x="82" y="10"/>
            <line x="82" y="85"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#ffffff"/>
        <rect h="4" w="6" x="28" y="15"/>
        <fill/>
        <rect h="4" w="6" x="28" y="22.5"/>
        <fill/>
        <rect h="4" w="6" x="28" y="30"/>
        <fill/>
        <rect h="4" w="6" x="28" y="37.5"/>
        <fill/>
        <rect h="4" w="6" x="28" y="45"/>
        <fill/>
        <rect h="4" w="6" x="28" y="52.5"/>
        <fill/>
        <rect h="4" w="6" x="28" y="60"/>
        <fill/>
        <rect h="4" w="6" x="28" y="67.5"/>
        <fill/>
        <rect h="4" w="6" x="16" y="15"/>
        <fill/>
        <rect h="4" w="6" x="16" y="22.5"/>
        <fill/>
        <rect h="4" w="6" x="16" y="30"/>
        <fill/>
        <rect h="4" w="6" x="16" y="37.5"/>
        <fill/>
        <rect h="4" w="6" x="16" y="45"/>
        <fill/>
        <rect h="4" w="6" x="16" y="52.5"/>
        <fill/>
        <rect h="4" w="6" x="16" y="60"/>
        <fill/>
        <rect h="4" w="6" x="16" y="67.5"/>
        <fill/>
        <rect h="4" w="6" x="4" y="15"/>
        <fill/>
        <rect h="4" w="6" x="4" y="22.5"/>
        <fill/>
        <rect h="4" w="6" x="4" y="30"/>
        <fill/>
        <rect h="4" w="6" x="4" y="37.5"/>
        <fill/>
        <rect h="4" w="6" x="4" y="45"/>
        <fill/>
        <rect h="4" w="6" x="4" y="52.5"/>
        <fill/>
        <rect h="4" w="6" x="4" y="60"/>
        <fill/>
        <rect h="4" w="6" x="4" y="67.5"/>
        <fill/>
        <rect h="9" w="6" x="16" y="75"/>
        <fill/>
        <rect h="4" w="6" x="4" y="75"/>
        <fill/>
        <rect h="4" w="6" x="28" y="75"/>
        <fill/>
        <rect h="4" w="6" x="72" y="15"/>
        <fill/>
        <rect h="4" w="6" x="72" y="22.5"/>
        <fill/>
        <rect h="4" w="6" x="72" y="30"/>
        <fill/>
        <rect h="4" w="6" x="72" y="37.5"/>
        <fill/>
        <rect h="4" w="6" x="72" y="45"/>
        <fill/>
        <rect h="4" w="6" x="72" y="52.5"/>
        <fill/>
        <rect h="4" w="6" x="72" y="60"/>
        <fill/>
        <rect h="4" w="6" x="72" y="67.5"/>
        <fill/>
        <rect h="4" w="6" x="60" y="15"/>
        <fill/>
        <rect h="4" w="6" x="60" y="22.5"/>
        <fill/>
        <rect h="4" w="6" x="60" y="30"/>
        <fill/>
        <rect h="4" w="6" x="60" y="37.5"/>
        <fill/>
        <rect h="4" w="6" x="60" y="45"/>
        <fill/>
        <rect h="4" w="6" x="60" y="52.5"/>
        <fill/>
        <rect h="4" w="6" x="60" y="60"/>
        <fill/>
        <rect h="4" w="6" x="60" y="67.5"/>
        <fill/>
        <rect h="4" w="6" x="48" y="15"/>
        <fill/>
        <rect h="4" w="6" x="48" y="22.5"/>
        <fill/>
        <rect h="4" w="6" x="48" y="30"/>
        <fill/>
        <rect h="4" w="6" x="48" y="37.5"/>
        <fill/>
        <rect h="4" w="6" x="48" y="45"/>
        <fill/>
        <rect h="4" w="6" x="48" y="52.5"/>
        <fill/>
        <rect h="4" w="6" x="48" y="60"/>
        <fill/>
        <rect h="4" w="6" x="48" y="67.5"/>
        <fill/>
        <rect h="9" w="6" x="60" y="75"/>
        <fill/>
        <rect h="4" w="6" x="48" y="75"/>
        <fill/>
        <rect h="4" w="6" x="72" y="75"/>
        <fill/>
    </foreground>
</shape>
<shape aspect="variable" h="100" name="Comm Link" strokewidth="2" w="30">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0"/>
        <constraint name="S" perimeter="0" x="0.5" y="1"/>
        <constraint name="W" perimeter="0" x="0" y="0.42"/>
        <constraint name="E" perimeter="0" x="1" y="0.58"/>
    </connections>
    <foreground>
        <path>
            <move x="15" y="100"/>
            <line x="0" y="42"/>
            <line x="20" y="47"/>
            <line x="15" y="0"/>
            <line x="30" y="58"/>
            <line x="10" y="53"/>
            <close/>
        </path>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="100" name="Copier" strokewidth="2" w="100">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0.04"/>
        <constraint name="S" perimeter="0" x="0.5" y="0.94"/>
        <constraint name="W" perimeter="0" x="0.16" y="0.5"/>
        <constraint name="E" perimeter="0" x="1" y="0.5"/>
        <constraint name="NW" perimeter="0" x="0" y="0.33"/>
        <constraint name="SW" perimeter="0" x="0.17" y="0.93"/>
        <constraint name="NE" perimeter="0" x="0.97" y="0.02"/>
        <constraint name="SE" perimeter="0" x="0.99" y="0.93"/>
    </connections>
    <foreground>
        <path>
            <move x="20" y="94"/>
            <arc large-arc-flag="0" rx="4" ry="4" sweep-flag="1" x="16" x-axis-rotation="0" y="90"/>
            <line x="16" y="46.5"/>
            <line x="0" y="36"/>
            <line x="0" y="33"/>
            <line x="16" y="42"/>
            <line x="16" y="30"/>
            <line x="5" y="23"/>
            <line x="5" y="20"/>
            <line x="16" y="26"/>
            <line x="16" y="18"/>
            <line x="33" y="18"/>
            <line x="37" y="20.5"/>
            <line x="45" y="20.5"/>
            <line x="45" y="6"/>
            <line x="65" y="0"/>
            <line x="65" y="11"/>
            <line x="84" y="4"/>
            <line x="84" y="2"/>
            <line x="97" y="2"/>
            <line x="97" y="5"/>
            <line x="100" y="8"/>
            <line x="100" y="90"/>
            <arc large-arc-flag="0" rx="4" ry="4" sweep-flag="1" x="96" x-axis-rotation="0" y="94"/>
            <close/>
        </path>
        <fillstroke/>
        <ellipse h="6" w="6" x="89" y="94"/>
        <fillstroke/>
        <ellipse h="6" w="6" x="19" y="94"/>
        <fillstroke/>
        <path>
            <move x="45" y="94"/>
            <line x="45" y="20.5"/>
            <move x="45" y="25"/>
            <line x="37" y="20.5"/>
            <move x="45" y="25"/>
            <line x="82" y="25"/>
            <move x="45" y="54"/>
            <line x="100" y="54"/>
            <move x="45" y="64"/>
            <line x="100" y="64"/>
            <move x="63" y="54"/>
            <line x="63" y="64"/>
            <move x="45" y="74"/>
            <line x="100" y="74"/>
            <move x="45" y="84"/>
            <line x="100" y="84"/>
            <move x="82" y="29"/>
            <line x="82" y="17"/>
            <line x="100" y="17"/>
            <move x="45" y="29"/>
            <line x="100" y="29"/>
            <move x="16" y="25"/>
            <line x="16" y="50"/>
        </path>
        <stroke/>
        <rect h="7" w="24" x="53" y="15"/>
        <stroke/>
        <fillcolor color="#ffffff"/>
        <rect h="3" w="10" x="66" y="87"/>
        <fill/>
        <rect h="3" w="10" x="66" y="77"/>
        <fill/>
        <rect h="3" w="10" x="66" y="67"/>
        <fill/>
        <rect h="3" w="10" x="66" y="57"/>
        <fill/>
        <rect h="3" w="12" x="55" y="17"/>
        <fill/>
    </foreground>
</shape>
<shape aspect="variable" h="60" name="Desktop PC" strokewidth="2" w="30">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0"/>
        <constraint name="S" perimeter="0" x="0.5" y="1"/>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="E" perimeter="0" x="1" y="0.5"/>
        <constraint name="NW" perimeter="0" x="0.02" y="0.02"/>
        <constraint name="SW" perimeter="0" x="0.02" y="0.98"/>
        <constraint name="NE" perimeter="0" x="0.98" y="0.02"/>
        <constraint name="SE" perimeter="0" x="0.98" y="0.98"/>
    </connections>
    <foreground>
        <roundrect arcsize="6.67" h="60" w="30" x="0" y="0"/>
        <fillstroke/>
        <path>
            <move x="0" y="10"/>
            <line x="30" y="10"/>
            <move x="0" y="30"/>
            <line x="30" y="30"/>
        </path>
        <stroke/>
        <fillcolor color="#ffffff"/>
        <ellipse h="4" w="4" x="3" y="33"/>
        <fill/>
    </foreground>
</shape>
<shape aspect="variable" h="30" name="DVR" strokewidth="2" w="90">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0"/>
        <constraint name="S" perimeter="0" x="0.5" y="1"/>
        <constraint name="W" perimeter="0" x="0.05" y="0.5"/>
        <constraint name="E" perimeter="0" x="0.95" y="0.5"/>
        <constraint name="NW" perimeter="0" x="0.28" y="0"/>
        <constraint name="SW" perimeter="0" x="0.02" y="0.98"/>
        <constraint name="NE" perimeter="0" x="0.72" y="0"/>
        <constraint name="SE" perimeter="0" x="0.98" y="0.98"/>
    </connections>
    <foreground>
        <path>
            <move x="4" y="13"/>
            <line x="25" y="0"/>
            <line x="65" y="0"/>
            <line x="86" y="13"/>
            <close/>
        </path>
        <fillstroke/>
        <roundrect arcsize="13.33" h="15" w="90" x="0" y="15"/>
        <fillstroke/>
        <fillcolor color="#ffffff"/>
        <ellipse h="8" w="8" x="66" y="18.5"/>
        <fill/>
        <rect h="2" w="4" x="4" y="18"/>
        <fill/>
        <rect h="2" w="4" x="11" y="18"/>
        <fill/>
        <rect h="2" w="4" x="18" y="18"/>
        <fill/>
        <rect h="2" w="4" x="25" y="18"/>
        <fill/>
        <rect h="2" w="4" x="32" y="18"/>
        <fill/>
        <rect h="2" w="4" x="39" y="18"/>
        <fill/>
        <rect h="2" w="4" x="46" y="18"/>
        <fill/>
        <rect h="2" w="4" x="53" y="18"/>
        <fill/>
        <rect h="2" w="4" x="4.12" y="23"/>
        <fill/>
        <rect h="2" w="4" x="11" y="23"/>
        <fill/>
        <rect h="2" w="4" x="18" y="23"/>
        <fill/>
        <rect h="2" w="4" x="25" y="23"/>
        <fill/>
        <rect h="2" w="4" x="32" y="23"/>
        <fill/>
        <rect h="2" w="4" x="39" y="23"/>
        <fill/>
        <rect h="2" w="4" x="46" y="23"/>
        <fill/>
        <rect h="2" w="4" x="53" y="23"/>
        <fill/>
    </foreground>
</shape>
<shape aspect="variable" h="100" name="External Storage" strokewidth="2" w="87">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0"/>
        <constraint name="S" perimeter="0" x="0.5" y="1"/>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="E" perimeter="0" x="1" y="0.5"/>
        <constraint name="NW" perimeter="0" x="0" y="0.25"/>
        <constraint name="SW" perimeter="0" x="0.02" y="0.98"/>
        <constraint name="NE" perimeter="0" x="0.95" y="0.02"/>
    </connections>
    <foreground>
        <save/>
        <roundrect arcsize="8.33" h="80" w="60" x="0" y="20"/>
        <fillstroke/>
        <rect h="7" w="8" x="78" y="54"/>
        <fillstroke/>
        <fillcolor color="#ffffff"/>
        <rect h="3" w="1" x="80" y="55"/>
        <fill/>
        <rect h="3" w="1" x="83" y="55"/>
        <fill/>
        <roundrect arcsize="50" h="4" w="20" x="35" y="92"/>
        <fill/>
        <restore/>
        <rect/>
        <stroke/>
        <path>
            <move x="35" y="19"/>
            <line x="35" y="14"/>
            <line x="34" y="14"/>
            <line x="33" y="11"/>
            <line x="32" y="11"/>
            <line x="32" y="6"/>
            <arc large-arc-flag="0" rx="2" ry="2" sweep-flag="1" x="34" x-axis-rotation="0" y="4"/>
            <line x="78" y="4"/>
            <arc large-arc-flag="0" rx="2" ry="2" sweep-flag="1" x="80" x-axis-rotation="0" y="6"/>
            <line x="80" y="44"/>
            <line x="79" y="44"/>
            <line x="78" y="46"/>
            <line x="77" y="46"/>
            <line x="77" y="53"/>
            <line x="87" y="53"/>
            <line x="87" y="46"/>
            <line x="86" y="46"/>
            <line x="85" y="44"/>
            <line x="84" y="44"/>
            <line x="84" y="6"/>
            <arc large-arc-flag="0" rx="6" ry="6" sweep-flag="0" x="78" x-axis-rotation="0" y="0"/>
            <line x="34" y="0"/>
            <arc large-arc-flag="0" rx="6" ry="6" sweep-flag="0" x="28" x-axis-rotation="0" y="6"/>
            <line x="28" y="11"/>
            <line x="27" y="11"/>
            <line x="26" y="14"/>
            <line x="25" y="14"/>
            <line x="25" y="19"/>
            <close/>
        </path>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="100" name="Firewall" strokewidth="2" w="100">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0"/>
        <constraint name="S" perimeter="0" x="0.5" y="1"/>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="E" perimeter="0" x="1" y="0.5"/>
        <constraint name="NW" perimeter="0" x="0" y="0"/>
        <constraint name="SW" perimeter="0" x="0" y="1"/>
        <constraint name="NE" perimeter="0" x="1" y="0"/>
        <constraint name="SE" perimeter="0" x="1" y="1"/>
    </connections>
    <foreground>
        <rect h="8" w="10.5" x="0" y="0"/>
        <fillstroke/>
        <rect h="8" w="23" x="13.3" y="0"/>
        <fillstroke/>
        <rect h="8" w="23" x="38.5" y="0"/>
        <fillstroke/>
        <rect h="8" w="23" x="64.3" y="0"/>
        <fillstroke/>
        <rect h="8" w="10.5" x="89.5" y="0"/>
        <fillstroke/>
        <rect h="8" w="23" x="0" y="10.25"/>
        <fillstroke/>
        <rect h="8" w="23" x="25.7" y="10.25"/>
        <fillstroke/>
        <rect h="8" w="23" x="51.4" y="10.25"/>
        <fillstroke/>
        <rect h="8" w="23" x="77" y="10.25"/>
        <fillstroke/>
        <rect h="8" w="10.5" x="0" y="20.5"/>
        <fillstroke/>
        <rect h="8" w="23" x="13.3" y="20.5"/>
        <fillstroke/>
        <rect h="8" w="23" x="38.5" y="20.5"/>
        <fillstroke/>
        <rect h="8" w="23" x="64.3" y="20.5"/>
        <fillstroke/>
        <rect h="8" w="10.5" x="89.5" y="20.5"/>
        <fillstroke/>
        <rect h="8" w="23" x="0" y="30.75"/>
        <fillstroke/>
        <rect h="8" w="23" x="25.7" y="30.75"/>
        <fillstroke/>
        <rect h="8" w="23" x="51.4" y="30.75"/>
        <fillstroke/>
        <rect h="8" w="23" x="77" y="30.75"/>
        <fillstroke/>
        <rect h="8" w="10.5" x="0" y="41"/>
        <fillstroke/>
        <rect h="8" w="23" x="13.3" y="41"/>
        <fillstroke/>
        <rect h="8" w="23" x="39" y="41"/>
        <fillstroke/>
        <rect h="8" w="23" x="64.3" y="41"/>
        <fillstroke/>
        <rect h="8" w="10.5" x="89.5" y="41"/>
        <fillstroke/>
        <rect h="8" w="23" x="0" y="51.5"/>
        <fillstroke/>
        <rect h="8" w="23" x="25.7" y="51.5"/>
        <fillstroke/>
        <rect h="8" w="23" x="51.4" y="51.5"/>
        <fillstroke/>
        <rect h="8" w="23" x="77" y="51.5"/>
        <fillstroke/>
        <rect h="8" w="10.5" x="0" y="61.75"/>
        <fillstroke/>
        <rect h="8" w="23" x="13.3" y="61.75"/>
        <fillstroke/>
        <rect h="8" w="23" x="39" y="61.75"/>
        <fillstroke/>
        <rect h="8" w="23" x="64.3" y="61.75"/>
        <fillstroke/>
        <rect h="8" w="10.5" x="89.5" y="61.75"/>
        <fillstroke/>
        <rect h="8" w="23" x="0" y="72"/>
        <fillstroke/>
        <rect h="8" w="23" x="25.7" y="72"/>
        <fillstroke/>
        <rect h="8" w="23" x="51.4" y="72"/>
        <fillstroke/>
        <rect h="8" w="23" x="77" y="72"/>
        <fillstroke/>
        <rect h="8" w="10.5" x="0" y="82"/>
        <fillstroke/>
        <rect h="8" w="23" x="13.3" y="82"/>
        <fillstroke/>
        <rect h="8" w="23" x="38.5" y="82"/>
        <fillstroke/>
        <rect h="8" w="23" x="64.3" y="82"/>
        <fillstroke/>
        <rect h="8" w="10.5" x="89.5" y="82"/>
        <fillstroke/>
        <rect h="8" w="23" x="0" y="92"/>
        <fillstroke/>
        <rect h="8" w="23" x="25.7" y="92"/>
        <fillstroke/>
        <rect h="8" w="23" x="51.4" y="92"/>
        <fillstroke/>
        <rect h="8" w="23" x="77" y="92"/>
        <fillstroke/>
        <fillcolor color="#ffffff"/>
        <path>
            <move x="43.4" y="81.6"/>
            <arc large-arc-flag="0" rx="30" ry="30" sweep-flag="1" x="47.9" x-axis-rotation="0" y="69.4"/>
            <arc large-arc-flag="0" rx="30" ry="30" sweep-flag="0" x="49.9" x-axis-rotation="0" y="65.7"/>
            <arc large-arc-flag="0" rx="5" ry="5" sweep-flag="0" x="50.4" x-axis-rotation="0" y="61.7"/>
            <arc large-arc-flag="0" rx="6" ry="6" sweep-flag="1" x="52.2" x-axis-rotation="0" y="66.2"/>
            <arc large-arc-flag="0" rx="20" ry="20" sweep-flag="0" x="53.4" x-axis-rotation="0" y="76.2"/>
            <arc large-arc-flag="0" rx="15" ry="15" sweep-flag="0" x="58.9" x-axis-rotation="0" y="82.1"/>
            <arc large-arc-flag="0" rx="25" ry="25" sweep-flag="0" x="72.1" x-axis-rotation="0" y="74.4"/>
            <arc large-arc-flag="0" rx="15" ry="15" sweep-flag="0" x="75.1" x-axis-rotation="0" y="60.2"/>
            <arc large-arc-flag="0" rx="65" ry="65" sweep-flag="0" x="68.3" x-axis-rotation="0" y="46.3"/>
            <arc large-arc-flag="0" rx="15" ry="15" sweep-flag="1" x="65.7" x-axis-rotation="0" y="38.2"/>
            <arc large-arc-flag="0" rx="5" ry="5" sweep-flag="1" x="68" x-axis-rotation="0" y="35.2"/>
            <arc large-arc-flag="0" rx="7" ry="7" sweep-flag="0" x="63.4" x-axis-rotation="0" y="36.8"/>
            <arc large-arc-flag="0" rx="15" ry="15" sweep-flag="0" x="59" x-axis-rotation="0" y="46.7"/>
            <arc large-arc-flag="0" rx="15" ry="15" sweep-flag="1" x="58.6" x-axis-rotation="0" y="37.8"/>
            <arc large-arc-flag="0" rx="17" ry="17" sweep-flag="0" x="57.3" x-axis-rotation="0" y="26.1"/>
            <arc large-arc-flag="0" rx="25" ry="25" sweep-flag="0" x="52.9" x-axis-rotation="0" y="21"/>
            <arc large-arc-flag="0" rx="15" ry="15" sweep-flag="1" x="49.4" x-axis-rotation="0" y="17"/>
            <arc large-arc-flag="0" rx="15" ry="15" sweep-flag="0" x="49.9" x-axis-rotation="0" y="22.7"/>
            <arc large-arc-flag="0" rx="12" ry="12" sweep-flag="1" x="48.7" x-axis-rotation="0" y="31.4"/>
            <arc large-arc-flag="0" rx="50" ry="50" sweep-flag="1" x="45.4" x-axis-rotation="0" y="36.2"/>
            <arc large-arc-flag="0" rx="20" ry="20" sweep-flag="0" x="42.2" x-axis-rotation="0" y="44.6"/>
            <arc large-arc-flag="0" rx="30" ry="30" sweep-flag="0" x="39.2" x-axis-rotation="0" y="34.7"/>
            <arc large-arc-flag="0" rx="20" ry="20" sweep-flag="0" x="32.8" x-axis-rotation="0" y="27.2"/>
            <arc large-arc-flag="0" rx="20" ry="20" sweep-flag="1" x="34.2" x-axis-rotation="0" y="36.4"/>
            <arc large-arc-flag="0" rx="35" ry="35" sweep-flag="1" x="32.2" x-axis-rotation="0" y="44.2"/>
            <arc large-arc-flag="0" rx="50" ry="50" sweep-flag="0" x="27.6" x-axis-rotation="0" y="59.8"/>
            <arc large-arc-flag="0" rx="20" ry="20" sweep-flag="0" x="32.4" x-axis-rotation="0" y="74.5"/>
            <arc large-arc-flag="0" rx="22" ry="22" sweep-flag="0" x="43.4" x-axis-rotation="0" y="81.6"/>
            <close/>
        </path>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="71.13" name="Gamepad" strokewidth="2" w="100.2">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0.09"/>
        <constraint name="S" perimeter="0" x="0.5" y="0.7"/>
        <constraint name="W" perimeter="0" x="0.03" y="0.5"/>
        <constraint name="E" perimeter="0" x="0.98" y="0.5"/>
        <constraint name="NW" perimeter="0" x="0.29" y="0.01"/>
        <constraint name="NE" perimeter="0" x="0.71" y="0.01"/>
        <constraint name="SW" perimeter="0" x="0.07" y="0.99"/>
        <constraint name="SE" perimeter="0" x="0.91" y="0.99"/>
    </connections>
    <foreground>
        <path>
            <move x="23" y="2.82"/>
            <arc large-arc-flag="0" rx="12" ry="12" sweep-flag="1" x="35.5" x-axis-rotation="0" y="2.16"/>
            <line x="43.34" y="5.16"/>
            <arc large-arc-flag="0" rx="20" ry="20" sweep-flag="0" x="57.34" x-axis-rotation="0" y="5.36"/>
            <line x="64.5" y="2.16"/>
            <arc large-arc-flag="0" rx="13" ry="13" sweep-flag="1" x="77.34" x-axis-rotation="0" y="2.49"/>
            <arc large-arc-flag="0" rx="30" ry="30" sweep-flag="1" x="89.17" x-axis-rotation="0" y="14.99"/>
            <arc large-arc-flag="0" rx="80" ry="80" sweep-flag="1" x="97.67" x-axis-rotation="0" y="35.16"/>
            <arc large-arc-flag="0" rx="60" ry="60" sweep-flag="1" x="100" x-axis-rotation="0" y="52.99"/>
            <arc large-arc-flag="0" rx="30" ry="30" sweep-flag="1" x="97.34" x-axis-rotation="0" y="66.66"/>
            <arc large-arc-flag="0" rx="8" ry="8" sweep-flag="1" x="87.34" x-axis-rotation="0" y="69.49"/>
            <arc large-arc-flag="0" rx="40" ry="40" sweep-flag="1" x="77.5" x-axis-rotation="0" y="63.56"/>
            <arc large-arc-flag="1" rx="0" ry="0" sweep-flag="0" x="64" x-axis-rotation="0" y="53.82"/>
            <arc large-arc-flag="0" rx="30" ry="30" sweep-flag="0" x="50" x-axis-rotation="0" y="49.96"/>
            <arc large-arc-flag="0" rx="30" ry="30" sweep-flag="0" x="35.34" x-axis-rotation="0" y="53.86"/>
            <arc large-arc-flag="0" rx="50" ry="50" sweep-flag="0" x="26.84" x-axis-rotation="0" y="60.66"/>
            <arc large-arc-flag="0" rx="50" ry="50" sweep-flag="1" x="17.37" x-axis-rotation="0" y="68.46"/>
            <arc large-arc-flag="0" rx="20" ry="20" sweep-flag="1" x="8.17" x-axis-rotation="0" y="70.36"/>
            <arc large-arc-flag="0" rx="8" ry="10" sweep-flag="1" x="1.17" x-axis-rotation="0" y="61.16"/>
            <arc large-arc-flag="0" rx="50" ry="50" sweep-flag="1" x="1.5" x-axis-rotation="0" y="41.66"/>
            <arc large-arc-flag="0" rx="80" ry="80" sweep-flag="1" x="8.67" x-axis-rotation="0" y="19.82"/>
            <arc large-arc-flag="0" rx="35" ry="35" sweep-flag="1" x="23" x-axis-rotation="0" y="2.82"/>
            <close/>
            <move x="29.17" y="12.56"/>
            <arc large-arc-flag="0" rx="14" ry="14" sweep-flag="0" x="15.37" x-axis-rotation="0" y="26.66"/>
            <arc large-arc-flag="0" rx="14" ry="14" sweep-flag="0" x="29.17" x-axis-rotation="0" y="39.96"/>
            <arc large-arc-flag="0" rx="14" ry="14" sweep-flag="0" x="42.87" x-axis-rotation="0" y="26.66"/>
            <arc large-arc-flag="0" rx="14" ry="14" sweep-flag="0" x="29.17" x-axis-rotation="0" y="12.56"/>
            <close/>
            <move x="29.17" y="18.96"/>
            <arc large-arc-flag="0" rx="7.5" ry="7.5" sweep-flag="1" x="36.67" x-axis-rotation="0" y="26.66"/>
            <arc large-arc-flag="0" rx="7.5" ry="7.5" sweep-flag="1" x="29.17" x-axis-rotation="0" y="33.96"/>
            <arc large-arc-flag="0" rx="7.5" ry="7.5" sweep-flag="1" x="21.57" x-axis-rotation="0" y="26.66"/>
            <arc large-arc-flag="0" rx="7.5" ry="7.5" sweep-flag="1" x="29.17" x-axis-rotation="0" y="18.96"/>
            <close/>
            <move x="71.37" y="20.46"/>
            <arc large-arc-flag="0" rx="3" ry="3" sweep-flag="0" x="74.47" x-axis-rotation="0" y="23.46"/>
            <arc large-arc-flag="0" rx="3" ry="3" sweep-flag="0" x="77.67" x-axis-rotation="0" y="20.46"/>
            <arc large-arc-flag="0" rx="3" ry="3" sweep-flag="0" x="74.47" x-axis-rotation="0" y="17.16"/>
            <arc large-arc-flag="0" rx="3" ry="3" sweep-flag="0" x="71.37" x-axis-rotation="0" y="20.46"/>
            <close/>
            <move x="65.07" y="26.46"/>
            <arc large-arc-flag="0" rx="3" ry="3" sweep-flag="0" x="62.07" x-axis-rotation="0" y="29.46"/>
            <arc large-arc-flag="0" rx="3" ry="3" sweep-flag="0" x="65.07" x-axis-rotation="0" y="32.66"/>
            <arc large-arc-flag="0" rx="3" ry="3" sweep-flag="0" x="68.47" x-axis-rotation="0" y="29.46"/>
            <arc large-arc-flag="0" rx="3" ry="3" sweep-flag="0" x="65.07" x-axis-rotation="0" y="26.46"/>
            <close/>
            <move x="74.47" y="35.26"/>
            <arc large-arc-flag="0" rx="3" ry="3" sweep-flag="0" x="71.57" x-axis-rotation="0" y="38.46"/>
            <arc large-arc-flag="0" rx="3" ry="3" sweep-flag="0" x="74.47" x-axis-rotation="0" y="41.46"/>
            <arc large-arc-flag="0" rx="3" ry="3" sweep-flag="0" x="77.47" x-axis-rotation="0" y="38.46"/>
            <arc large-arc-flag="0" rx="3" ry="3" sweep-flag="0" x="74.47" x-axis-rotation="0" y="35.26"/>
            <close/>
            <move x="83.57" y="26.16"/>
            <arc large-arc-flag="0" rx="3" ry="3" sweep-flag="0" x="80.47" x-axis-rotation="0" y="29.46"/>
            <arc large-arc-flag="0" rx="3" ry="3" sweep-flag="0" x="83.57" x-axis-rotation="0" y="32.56"/>
            <arc large-arc-flag="0" rx="3" ry="3" sweep-flag="0" x="86.67" x-axis-rotation="0" y="29.46"/>
            <arc large-arc-flag="0" rx="3" ry="3" sweep-flag="0" x="83.57" x-axis-rotation="0" y="26.16"/>
            <close/>
        </path>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="29" name="Hub" strokewidth="2" w="100">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0"/>
        <constraint name="S" perimeter="0" x="0.5" y="1"/>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="E" perimeter="0" x="1" y="0.5"/>
        <constraint name="NW" perimeter="0" x="0.05" y="0.05"/>
        <constraint name="SW" perimeter="0" x="0.1" y="1"/>
        <constraint name="NE" perimeter="0" x="0.95" y="0.05"/>
        <constraint name="SE" perimeter="0" x="0.9" y="1"/>
    </connections>
    <foreground>
        <save/>
        <roundrect arcsize="20" h="25" w="100" x="0" y="0"/>
        <fillstroke/>
        <rect h="4" w="80" x="10" y="25"/>
        <fillstroke/>
        <fillcolor color="#ffffff"/>
        <path>
            <move x="3" y="7"/>
            <line x="9" y="7"/>
            <line x="9" y="10"/>
            <line x="7" y="10"/>
            <line x="7" y="11"/>
            <line x="5" y="11"/>
            <line x="5" y="10"/>
            <line x="3" y="10"/>
            <close/>
        </path>
        <fill/>
        <ellipse h="16" w="16" x="80" y="4.5"/>
        <fill/>
        <ellipse h="4" w="4" x="73" y="3"/>
        <fill/>
        <ellipse h="4" w="4" x="73" y="18"/>
        <fill/>
        <ellipse h="4" w="4" x="73" y="10.5"/>
        <fill/>
        <path>
            <move x="11.5" y="7"/>
            <line x="17.5" y="7"/>
            <line x="17.5" y="10"/>
            <line x="15.5" y="10"/>
            <line x="15.5" y="11"/>
            <line x="13.5" y="11"/>
            <line x="13.5" y="10"/>
            <line x="11.5" y="10"/>
            <close/>
            <move x="20" y="7"/>
            <line x="26" y="7"/>
            <line x="26" y="10"/>
            <line x="24" y="10"/>
            <line x="24" y="11"/>
            <line x="22" y="11"/>
            <line x="22" y="10"/>
            <line x="20" y="10"/>
            <close/>
            <move x="28.5" y="7"/>
            <line x="34.5" y="7"/>
            <line x="34.5" y="10"/>
            <line x="32.5" y="10"/>
            <line x="32.5" y="11"/>
            <line x="30.5" y="11"/>
            <line x="30.5" y="10"/>
            <line x="28.5" y="10"/>
            <close/>
            <move x="37" y="7"/>
            <line x="43" y="7"/>
            <line x="43" y="10"/>
            <line x="41" y="10"/>
            <line x="41" y="11"/>
            <line x="39" y="11"/>
            <line x="39" y="10"/>
            <line x="37" y="10"/>
            <close/>
            <move x="45.5" y="7"/>
            <line x="51.5" y="7"/>
            <line x="51.5" y="10"/>
            <line x="49.5" y="10"/>
            <line x="49.5" y="11"/>
            <line x="47.5" y="11"/>
            <line x="47.5" y="10"/>
            <line x="45.5" y="10"/>
            <close/>
            <move x="54" y="7"/>
            <line x="60" y="7"/>
            <line x="60" y="10"/>
            <line x="58" y="10"/>
            <line x="58" y="11"/>
            <line x="56" y="11"/>
            <line x="56" y="10"/>
            <line x="54" y="10"/>
            <close/>
            <move x="62.5" y="7"/>
            <line x="68.5" y="7"/>
            <line x="68.5" y="10"/>
            <line x="66.5" y="10"/>
            <line x="66.5" y="11"/>
            <line x="64.5" y="11"/>
            <line x="64.5" y="10"/>
            <line x="62.5" y="10"/>
            <close/>
        </path>
        <fill/>
        <ellipse h="4" w="4" x="63.5" y="15"/>
        <fill/>
        <ellipse h="4" w="4" x="55" y="15"/>
        <fill/>
        <ellipse h="4" w="4" x="46.5" y="15"/>
        <fill/>
        <ellipse h="4" w="4" x="38" y="15"/>
        <fill/>
        <ellipse h="4" w="4" x="29.5" y="15"/>
        <fill/>
        <ellipse h="4" w="4" x="21" y="15"/>
        <fill/>
        <ellipse h="4" w="4" x="12.5" y="15"/>
        <fill/>
        <ellipse h="4" w="4" x="4" y="15"/>
        <fill/>
        <restore/>
        <rect/>
        <stroke/>
        <strokewidth width="1"/>
        <path>
            <move x="83" y="12.5"/>
            <line x="92" y="12.5"/>
        </path>
        <fillstroke/>
        <path>
            <move x="90" y="10.5"/>
            <line x="92" y="12.5"/>
            <line x="90" y="14.5"/>
        </path>
        <stroke/>
    </foreground>
</shape>
<shape aspect="variable" h="57.75" name="Laptop" strokewidth="2" w="101.5">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0"/>
        <constraint name="S" perimeter="0" x="0.5" y="1"/>
        <constraint name="W" perimeter="0" x="0.15" y="0.5"/>
        <constraint name="E" perimeter="0" x="0.85" y="0.5"/>
        <constraint name="NW" perimeter="0" x="0.16" y="0"/>
        <constraint name="SW" perimeter="0" x="0" y="1"/>
        <constraint name="NE" perimeter="0" x="0.83" y="0"/>
        <constraint name="SE" perimeter="0" x="1" y="1"/>
    </connections>
    <foreground>
        <linejoin join="round"/>
        <path>
            <move x="14.16" y="39.45"/>
            <line x="87.1" y="39.45"/>
            <line x="101.5" y="57.75"/>
            <line x="0" y="57.75"/>
            <close/>
            <move x="16.75" y="0"/>
            <line x="84.25" y="0"/>
            <line x="86.25" y="39"/>
            <line x="14.75" y="39"/>
            <close/>
        </path>
        <fillstroke/>
        <path>
            <move x="17.25" y="44"/>
            <line x="85.25" y="44"/>
            <move x="12.75" y="49"/>
            <line x="89.75" y="49"/>
            <move x="32.25" y="53.5"/>
            <line x="67.25" y="53.5"/>
        </path>
        <stroke/>
        <fillcolor color="#ffffff"/>
        <path>
            <move x="20.05" y="3.5"/>
            <line x="81.05" y="3.5"/>
            <line x="82.75" y="35.5"/>
            <line x="18.25" y="35.5"/>
            <close/>
        </path>
        <fill/>
    </foreground>
</shape>
<shape aspect="variable" h="29" name="Load Balancer" strokewidth="2" w="100">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0"/>
        <constraint name="S" perimeter="0" x="0.5" y="1"/>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="E" perimeter="0" x="1" y="0.5"/>
        <constraint name="NW" perimeter="0" x="0.05" y="0.05"/>
        <constraint name="SW" perimeter="0" x="0.1" y="1"/>
        <constraint name="NE" perimeter="0" x="0.95" y="0.05"/>
        <constraint name="SE" perimeter="0" x="0.9" y="1"/>
    </connections>
    <foreground>
        <roundrect arcsize="20" h="25" w="100" x="0" y="0"/>
        <fillstroke/>
        <rect h="4" w="80" x="10" y="25"/>
        <fillstroke/>
        <fillcolor color="#ffffff"/>
        <path>
            <move x="3" y="7"/>
            <line x="9" y="7"/>
            <line x="9" y="10"/>
            <line x="7" y="10"/>
            <line x="7" y="11"/>
            <line x="5" y="11"/>
            <line x="5" y="10"/>
            <line x="3" y="10"/>
            <close/>
        </path>
        <fill/>
        <ellipse h="16" w="16" x="80" y="4.5"/>
        <fill/>
        <ellipse h="4" w="4" x="73" y="18"/>
        <fill/>
        <ellipse h="4" w="4" x="73" y="10.5"/>
        <fill/>
        <path>
            <move x="11.5" y="7"/>
            <line x="17.5" y="7"/>
            <line x="17.5" y="10"/>
            <line x="15.5" y="10"/>
            <line x="15.5" y="11"/>
            <line x="13.5" y="11"/>
            <line x="13.5" y="10"/>
            <line x="11.5" y="10"/>
            <close/>
            <move x="20" y="7"/>
            <line x="26" y="7"/>
            <line x="26" y="10"/>
            <line x="24" y="10"/>
            <line x="24" y="11"/>
            <line x="22" y="11"/>
            <line x="22" y="10"/>
            <line x="20" y="10"/>
            <close/>
            <move x="28.5" y="7"/>
            <line x="34.5" y="7"/>
            <line x="34.5" y="10"/>
            <line x="32.5" y="10"/>
            <line x="32.5" y="11"/>
            <line x="30.5" y="11"/>
            <line x="30.5" y="10"/>
            <line x="28.5" y="10"/>
            <close/>
            <move x="37" y="7"/>
            <line x="43" y="7"/>
            <line x="43" y="10"/>
            <line x="41" y="10"/>
            <line x="41" y="11"/>
            <line x="39" y="11"/>
            <line x="39" y="10"/>
            <line x="37" y="10"/>
            <close/>
            <move x="45.5" y="7"/>
            <line x="51.5" y="7"/>
            <line x="51.5" y="10"/>
            <line x="49.5" y="10"/>
            <line x="49.5" y="11"/>
            <line x="47.5" y="11"/>
            <line x="47.5" y="10"/>
            <line x="45.5" y="10"/>
            <close/>
            <move x="54" y="7"/>
            <line x="60" y="7"/>
            <line x="60" y="10"/>
            <line x="58" y="10"/>
            <line x="58" y="11"/>
            <line x="56" y="11"/>
            <line x="56" y="10"/>
            <line x="54" y="10"/>
            <close/>
            <move x="62.5" y="7"/>
            <line x="68.5" y="7"/>
            <line x="68.5" y="10"/>
            <line x="66.5" y="10"/>
            <line x="66.5" y="11"/>
            <line x="64.5" y="11"/>
            <line x="64.5" y="10"/>
            <line x="62.5" y="10"/>
            <close/>
        </path>
        <fill/>
        <path>
            <move x="88" y="7"/>
            <line x="88" y="18"/>
            <move x="86" y="9"/>
            <line x="88" y="7"/>
            <line x="90" y="9"/>
            <move x="88" y="15"/>
            <line x="83" y="10"/>
            <move x="88" y="15"/>
            <line x="93" y="10"/>
            <move x="83" y="12.5"/>
            <line x="83" y="10"/>
            <line x="85.5" y="10"/>
            <move x="90.5" y="10"/>
            <line x="93" y="10"/>
            <line x="93" y="12.5"/>
        </path>
        <stroke/>
    </foreground>
</shape>
<shape aspect="variable" h="107" name="Mail Server" strokewidth="2" w="103">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0"/>
        <constraint name="S" perimeter="0" x="0.5" y="0.94"/>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="E" perimeter="0" x="0.88" y="0.5"/>
        <constraint name="NW" perimeter="0" x="0" y="0.14"/>
        <constraint name="SW" perimeter="0" x="0" y="0.93"/>
        <constraint name="NE" perimeter="0" x="0.87" y="0.14"/>
        <constraint name="SE" perimeter="0" x="0.95" y="0.95"/>
    </connections>
    <foreground>
        <save/>
        <roundrect arcsize="13.33" h="15" w="90" x="0" y="85"/>
        <fillstroke/>
        <roundrect arcsize="13.33" h="15" w="90" x="0" y="67.5"/>
        <fillstroke/>
        <roundrect arcsize="13.33" h="15" w="90" x="0" y="50"/>
        <fillstroke/>
        <roundrect arcsize="13.33" h="15" w="90" x="0" y="32.5"/>
        <fillstroke/>
        <roundrect arcsize="13.33" h="15" w="90" x="0" y="15"/>
        <fillstroke/>
        <path>
            <move x="4" y="13"/>
            <line x="25" y="0"/>
            <line x="65" y="0"/>
            <line x="86" y="13"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#ffffff"/>
        <ellipse h="8" w="8" x="78" y="88.5"/>
        <fill/>
        <ellipse h="8" w="8" x="77.67" y="71"/>
        <fill/>
        <ellipse h="8" w="8" x="78" y="53.5"/>
        <fill/>
        <ellipse h="8" w="8" x="78" y="36"/>
        <fill/>
        <ellipse h="8" w="8" x="78" y="18.5"/>
        <fill/>
        <path>
            <move x="5" y="98"/>
            <line x="10" y="87"/>
            <line x="15" y="87"/>
            <line x="10" y="98"/>
            <close/>
            <move x="15" y="98"/>
            <line x="20" y="87"/>
            <line x="25" y="87"/>
            <line x="20" y="98"/>
            <close/>
            <move x="25" y="98"/>
            <line x="30" y="87"/>
            <line x="35" y="87"/>
            <line x="30" y="98"/>
            <close/>
            <move x="35" y="98"/>
            <line x="40" y="87"/>
            <line x="45" y="87"/>
            <line x="40" y="98"/>
            <close/>
            <move x="45" y="98"/>
            <line x="50" y="87"/>
            <line x="55" y="87"/>
            <line x="50" y="98"/>
            <close/>
            <move x="55" y="98"/>
            <line x="60" y="87"/>
            <line x="65" y="87"/>
            <line x="60" y="98"/>
            <close/>
            <move x="5" y="80.5"/>
            <line x="10" y="69.5"/>
            <line x="15" y="69.5"/>
            <line x="10" y="80.5"/>
            <close/>
            <move x="15" y="80.5"/>
            <line x="20" y="69.5"/>
            <line x="25" y="69.5"/>
            <line x="20" y="80.5"/>
            <close/>
            <move x="25" y="80.5"/>
            <line x="30" y="69.5"/>
            <line x="35" y="69.5"/>
            <line x="30" y="80.5"/>
            <close/>
            <move x="35" y="80.5"/>
            <line x="40" y="69.5"/>
            <line x="45" y="69.5"/>
            <line x="40" y="80.5"/>
            <close/>
            <move x="45" y="80.5"/>
            <line x="50" y="69.5"/>
            <line x="55" y="69.5"/>
            <line x="50" y="80.5"/>
            <close/>
            <move x="55" y="80.5"/>
            <line x="60" y="69.5"/>
            <line x="65" y="69.5"/>
            <line x="60" y="80.5"/>
            <close/>
            <move x="5" y="63"/>
            <line x="10" y="52"/>
            <line x="15" y="52"/>
            <line x="10" y="63"/>
            <close/>
            <move x="15" y="63"/>
            <line x="20" y="52"/>
            <line x="25" y="52"/>
            <line x="20" y="63"/>
            <close/>
            <move x="25" y="63"/>
            <line x="29.5" y="52"/>
            <line x="34.5" y="52"/>
            <line x="29.5" y="63"/>
            <close/>
            <move x="35" y="63"/>
            <line x="40" y="52"/>
            <line x="45" y="52"/>
            <line x="40" y="63"/>
            <close/>
            <move x="45" y="63"/>
            <line x="50" y="52"/>
            <line x="55" y="52"/>
            <line x="50" y="63"/>
            <close/>
            <move x="55" y="63"/>
            <line x="60" y="52"/>
            <line x="65" y="52"/>
            <line x="60" y="63"/>
            <close/>
            <move x="5" y="45.5"/>
            <line x="10" y="34.5"/>
            <line x="15" y="34.5"/>
            <line x="10" y="45.5"/>
            <close/>
            <move x="15" y="45.5"/>
            <line x="20" y="34.5"/>
            <line x="25" y="34.5"/>
            <line x="20" y="45.5"/>
            <close/>
            <move x="25" y="45.5"/>
            <line x="30" y="34.5"/>
            <line x="35" y="34.5"/>
            <line x="30" y="45.5"/>
            <close/>
            <move x="35" y="45.5"/>
            <line x="40" y="34.5"/>
            <line x="45" y="34.5"/>
            <line x="40" y="45.5"/>
            <close/>
            <move x="45" y="45.5"/>
            <line x="50" y="34.5"/>
            <line x="55" y="34.5"/>
            <line x="50" y="45.5"/>
            <close/>
            <move x="55" y="45.5"/>
            <line x="60" y="34.5"/>
            <line x="65" y="34.5"/>
            <line x="60" y="45.5"/>
            <close/>
            <move x="5" y="28"/>
            <line x="10" y="17"/>
            <line x="15" y="17"/>
            <line x="10" y="28"/>
            <close/>
            <move x="15" y="28"/>
            <line x="20" y="17"/>
            <line x="25" y="17"/>
            <line x="20" y="28"/>
            <close/>
            <move x="25" y="28"/>
            <line x="30" y="17"/>
            <line x="35" y="17"/>
            <line x="30" y="28"/>
            <close/>
            <move x="35" y="28"/>
            <line x="40" y="17"/>
            <line x="45" y="17"/>
            <line x="40" y="28"/>
            <close/>
            <move x="45" y="28"/>
            <line x="50" y="17"/>
            <line x="55" y="17"/>
            <line x="50" y="28"/>
            <close/>
            <move x="55" y="28"/>
            <line x="60" y="17"/>
            <line x="65" y="17"/>
            <line x="60" y="28"/>
            <close/>
        </path>
        <fill/>
        <restore/>
        <rect/>
        <stroke/>
        <ellipse h="36" w="36" x="67" y="71"/>
        <fillstroke/>
        <fillcolor color="#ffffff"/>
        <path>
            <move x="73" y="81"/>
            <line x="97" y="81"/>
            <line x="85.4" y="87"/>
            <close/>
            <move x="73" y="83"/>
            <line x="85" y="89"/>
            <line x="97" y="83"/>
            <line x="97" y="97"/>
            <line x="73" y="97"/>
            <close/>
        </path>
        <fill/>
    </foreground>
</shape>
<shape aspect="variable" h="100" name="Mainframe" strokewidth="2" w="80">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0.05"/>
        <constraint name="S" perimeter="0" x="0.5" y="0.95"/>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="E" perimeter="0" x="1" y="0.5"/>
        <constraint name="NW" perimeter="0" x="0" y="0.05"/>
        <constraint name="SW" perimeter="0" x="0" y="0.95"/>
        <constraint name="NE" perimeter="0" x="1" y="0.05"/>
        <constraint name="SE" perimeter="0" x="1" y="0.95"/>
    </connections>
    <foreground>
        <rect h="90" w="80" x="0" y="5"/>
        <fillstroke/>
        <rect h="100" w="28" x="5" y="0"/>
        <fillstroke/>
        <rect h="100" w="28" x="47" y="0"/>
        <fillstroke/>
        <rect h="20" w="14" x="33" y="40"/>
        <stroke/>
        <path>
            <move x="40" y="5"/>
            <line x="40" y="95"/>
        </path>
        <stroke/>
        <fillcolor color="#ffffff"/>
        <rect h="65" w="4" x="17" y="30"/>
        <fill/>
        <rect h="65" w="4" x="59" y="30"/>
        <fill/>
        <rect h="5" w="3" x="42" y="43"/>
        <fill/>
        <ellipse h="3" w="3" x="35" y="52.5"/>
        <fill/>
        <ellipse h="3" w="3" x="42" y="52.5"/>
        <fill/>
    </foreground>
</shape>
<shape aspect="variable" h="100" name="Mobile" strokewidth="2" w="51.4">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0"/>
        <constraint name="S" perimeter="0" x="0.5" y="1"/>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="E" perimeter="0" x="1" y="0.5"/>
        <constraint name="NW" perimeter="0" x="0.05" y="0.03"/>
        <constraint name="SW" perimeter="0" x="0.05" y="0.97"/>
        <constraint name="NE" perimeter="0" x="0.95" y="0.03"/>
        <constraint name="SE" perimeter="0" x="0.95" y="0.97"/>
    </connections>
    <foreground>
        <path>
            <move x="0" y="9.5"/>
            <arc large-arc-flag="0" rx="9.5" ry="9.5" sweep-flag="1" x="9.5" x-axis-rotation="0" y="0"/>
            <line x="42" y="0"/>
            <arc large-arc-flag="0" rx="9.5" ry="9.5" sweep-flag="1" x="51.4" x-axis-rotation="0" y="9.5"/>
            <line x="51.4" y="90.5"/>
            <arc large-arc-flag="0" rx="9.5" ry="9.5" sweep-flag="1" x="42" x-axis-rotation="0" y="100"/>
            <line x="9.5" y="100"/>
            <arc large-arc-flag="0" rx="9.5" ry="9.5" sweep-flag="1" x="0" x-axis-rotation="0" y="90.5"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#ffffff"/>
        <path>
            <move x="2" y="9"/>
            <line x="2" y="91"/>
            <line x="49.4" y="91.12"/>
            <line x="49.4" y="9"/>
            <close/>
        </path>
        <fill/>
        <roundrect arcsize="50" h="3.6" w="12.3" x="19.5" y="93.8"/>
        <fill/>
    </foreground>
</shape>
<shape aspect="variable" h="29" name="Modem" strokewidth="inherit" w="100">
    <connections/>
    <foreground>
        <save/>
        <save/>
        <roundrect arcsize="20" h="25" w="100" x="0" y="0"/>
        <fillstroke/>
        <rect h="4" w="80" x="10" y="25"/>
        <fillstroke/>
        <strokecolor color="none"/>
        <fillcolor color="#ffffff"/>
        <ellipse h="16" w="16" x="80" y="4.5"/>
        <fill/>
        <ellipse h="4" w="4" x="8" y="8"/>
        <fill/>
        <restore/>
        <rect/>
        <stroke/>
        <path>
            <move x="88" y="7"/>
            <line x="88" y="18"/>
        </path>
        <fillstroke/>
        <fillcolor color="none"/>
        <path>
            <move x="86" y="9"/>
            <line x="88" y="7"/>
            <line x="90" y="9"/>
        </path>
        <stroke/>
        <strokecolor color="none"/>
        <fillcolor color="#ffffff"/>
        <ellipse h="4" w="4" x="18" y="8"/>
        <fill/>
        <ellipse h="4" w="4" x="28" y="8"/>
        <fill/>
        <ellipse h="4" w="4" x="38" y="8"/>
        <fill/>
        <restore/>
        <rect/>
        <stroke/>
        <fillcolor color="none"/>
        <path>
            <move x="86" y="16"/>
            <line x="88" y="18"/>
            <line x="90" y="16"/>
        </path>
        <stroke/>
    </foreground>
</shape>
<shape aspect="variable" h="63.5" name="Monitor" strokewidth="2" w="80">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0"/>
        <constraint name="S1" perimeter="0" x="0.23" y="1"/>
        <constraint name="S2" perimeter="0" x="0.77" y="1"/>
        <constraint name="S3" perimeter="0" x="0.5" y="1"/>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="E" perimeter="0" x="1" y="0.5"/>
        <constraint name="NW" perimeter="0" x="0" y="0"/>
        <constraint name="SW" perimeter="0" x="0" y="0.71"/>
        <constraint name="NE" perimeter="0" x="1" y="0"/>
        <constraint name="SE" perimeter="0" x="1" y="0.71"/>
    </connections>
    <foreground>
        <rect h="45" w="80" x="0" y="0"/>
        <fillstroke/>
        <linejoin join="round"/>
        <path>
            <move x="24.5" y="56.7"/>
            <line x="55.4" y="56.7"/>
            <line x="61.5" y="63.5"/>
            <line x="18.5" y="63.5"/>
            <close/>
        </path>
        <fillstroke/>
        <rect h="15" w="20" x="30" y="45"/>
        <fillstroke/>
        <fillcolor color="#ffffff"/>
        <rect h="39" w="73" x="3" y="3"/>
        <fill/>
    </foreground>
</shape>
<shape aspect="variable" h="34" name="NAS Filer" strokewidth="2" w="100">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0"/>
        <constraint name="S" perimeter="0" x="0.5" y="1"/>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="E" perimeter="0" x="1" y="0.5"/>
        <constraint name="NW" perimeter="0" x="0.25" y="0"/>
        <constraint name="SW" perimeter="0" x="0" y="1"/>
        <constraint name="NE" perimeter="0" x="0.75" y="0"/>
        <constraint name="SE" perimeter="0" x="1" y="1"/>
    </connections>
    <foreground>
        <rect h="18" w="100" x="0" y="16"/>
        <fillstroke/>
        <path>
            <move x="4" y="13"/>
            <line x="25" y="0"/>
            <line x="75" y="0"/>
            <line x="96" y="13"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#ffffff"/>
        <path>
            <move x="4" y="34"/>
            <line x="4" y="16"/>
            <line x="9" y="16"/>
            <line x="9" y="18"/>
            <line x="17" y="20.5"/>
            <line x="21" y="20.5"/>
            <line x="21" y="16"/>
            <line x="23.5" y="16"/>
            <line x="23.5" y="20.5"/>
            <line x="39" y="20.5"/>
            <line x="39" y="16"/>
            <line x="41.5" y="16"/>
            <line x="41.5" y="20.5"/>
            <line x="57.5" y="20.5"/>
            <line x="57.5" y="16"/>
            <line x="60" y="16"/>
            <line x="60" y="20.5"/>
            <line x="76" y="20.5"/>
            <line x="76" y="16"/>
            <line x="78.5" y="16"/>
            <line x="78.5" y="20.5"/>
            <line x="82.5" y="20.5"/>
            <line x="90.5" y="18"/>
            <line x="90.5" y="16"/>
            <line x="95.5" y="16"/>
            <line x="95.5" y="34"/>
            <line x="90.5" y="34"/>
            <line x="90.5" y="32"/>
            <line x="82.5" y="29.5"/>
            <line x="78.5" y="29.5"/>
            <line x="78.5" y="34"/>
            <line x="76" y="34"/>
            <line x="76" y="29.5"/>
            <line x="60" y="29.5"/>
            <line x="60" y="34"/>
            <line x="57.5" y="34"/>
            <line x="57.5" y="29.5"/>
            <line x="41.5" y="29.5"/>
            <line x="41.5" y="34"/>
            <line x="39" y="34"/>
            <line x="39" y="29.5"/>
            <line x="23.5" y="29.5"/>
            <line x="23.5" y="34"/>
            <line x="21" y="34"/>
            <line x="21" y="29.5"/>
            <line x="17" y="29.5"/>
            <line x="9" y="32"/>
            <line x="9" y="34"/>
            <close/>
        </path>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="30" name="Patch Panel" strokewidth="2" w="90">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0"/>
        <constraint name="S" perimeter="0" x="0.5" y="1"/>
        <constraint name="W" perimeter="0" x="0.02" y="0.51"/>
        <constraint name="E" perimeter="0" x="0.98" y="0.51"/>
        <constraint name="NW" perimeter="0" x="0.27" y="0"/>
        <constraint name="SW" perimeter="0" x="0.02" y="0.98"/>
        <constraint name="NE" perimeter="0" x="0.73" y="0"/>
        <constraint name="SE" perimeter="0" x="0.98" y="0.98"/>
    </connections>
    <foreground>
        <path>
            <move x="4" y="13"/>
            <line x="25" y="0"/>
            <line x="65" y="0"/>
            <line x="86" y="13"/>
            <close/>
        </path>
        <fillstroke/>
        <roundrect arcsize="13.33" h="15" w="90" x="0" y="15"/>
        <fillstroke/>
        <fillcolor color="#ffffff"/>
        <rect h="2" w="4" x="4" y="18"/>
        <fill/>
        <rect h="2" w="4" x="11" y="18"/>
        <fill/>
        <rect h="2" w="4" x="18" y="18"/>
        <fill/>
        <rect h="2" w="4" x="25" y="18"/>
        <fill/>
        <rect h="2" w="4" x="32" y="18"/>
        <fill/>
        <rect h="2" w="4" x="39" y="18"/>
        <fill/>
        <rect h="2" w="4" x="46" y="18"/>
        <fill/>
        <rect h="2" w="4" x="53" y="18"/>
        <fill/>
        <rect h="2" w="4" x="4.12" y="23"/>
        <fill/>
        <rect h="2" w="4" x="11" y="23"/>
        <fill/>
        <rect h="2" w="4" x="18" y="23"/>
        <fill/>
        <rect h="2" w="4" x="25" y="23"/>
        <fill/>
        <rect h="2" w="4" x="32" y="23"/>
        <fill/>
        <rect h="2" w="4" x="39" y="23"/>
        <fill/>
        <rect h="2" w="4" x="46" y="23"/>
        <fill/>
        <rect h="2" w="4" x="53" y="23"/>
        <fill/>
        <rect h="2" w="4" x="60" y="18"/>
        <fill/>
        <rect h="2" w="4" x="60" y="23"/>
        <fill/>
        <rect h="2" w="4" x="67" y="18"/>
        <fill/>
        <rect h="2" w="4" x="67" y="23"/>
        <fill/>
        <rect h="2" w="4" x="74" y="18"/>
        <fill/>
        <rect h="2" w="4" x="74" y="23"/>
        <fill/>
        <rect h="2" w="4" x="81" y="18"/>
        <fill/>
        <rect h="2" w="4" x="81" y="23"/>
        <fill/>
    </foreground>
</shape>
<shape aspect="variable" h="70" name="PC" strokewidth="2" w="100">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0.07"/>
        <constraint name="S" perimeter="0" x="0.5" y="1"/>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="E" perimeter="0" x="1" y="0.5"/>
        <constraint name="NW" perimeter="0" x="0" y="0"/>
        <constraint name="SW" perimeter="0" x="0" y="0.85"/>
        <constraint name="NE" perimeter="0" x="1" y="0.07"/>
        <constraint name="SE" perimeter="0" x="0.95" y="1"/>
    </connections>
    <foreground>
        <linejoin join="round"/>
        <roundrect arcsize="6.67" h="60" w="30" x="0" y="0"/>
        <fillstroke/>
        <path>
            <move x="0" y="10"/>
            <line x="30" y="10"/>
            <move x="0" y="30"/>
            <line x="30" y="30"/>
        </path>
        <stroke/>
        <rect h="45" w="80" x="20" y="5"/>
        <fillstroke/>
        <rect h="10" w="20" x="50" y="50"/>
        <fillstroke/>
        <path>
            <move x="35" y="59"/>
            <line x="85" y="59"/>
            <line x="95" y="70"/>
            <line x="25" y="70"/>
            <close/>
        </path>
        <fillstroke/>
        <path>
            <move x="49" y="67.5"/>
            <line x="67" y="67.5"/>
            <move x="36" y="64.5"/>
            <line x="75" y="64.5"/>
            <move x="38" y="61.5"/>
            <line x="74" y="61.5"/>
            <move x="77" y="61.5"/>
            <line x="84" y="61.5"/>
            <move x="79" y="64.5"/>
            <line x="86" y="64.5"/>
            <move x="81" y="67.5"/>
            <line x="88" y="67.5"/>
        </path>
        <stroke/>
        <fillcolor color="#ffffff"/>
        <rect h="39" w="73" x="23" y="8"/>
        <fill/>
        <ellipse h="4" w="4" x="3" y="33"/>
        <fill/>
    </foreground>
</shape>
<shape aspect="variable" h="71.6" name="Phone 1" strokewidth="2" w="101.14">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0"/>
        <constraint name="S" perimeter="0" x="0.5" y="1"/>
        <constraint name="W" perimeter="0" x="0.1" y="0.68"/>
        <constraint name="E" perimeter="0" x="0.9" y="0.68"/>
        <constraint name="NW" perimeter="0" x="0.08" y="0.09"/>
        <constraint name="SW" perimeter="0" x="0.1" y="1"/>
        <constraint name="NE" perimeter="0" x="0.92" y="0.09"/>
        <constraint name="SE" perimeter="0" x="0.9" y="1"/>
    </connections>
    <foreground>
        <path>
            <move x="3.47" y="27.2"/>
            <arc large-arc-flag="0" rx="14" ry="14" sweep-flag="1" x="1.77" x-axis-rotation="0" y="13.8"/>
            <arc large-arc-flag="0" rx="15" ry="15" sweep-flag="1" x="9.87" x-axis-rotation="0" y="5.8"/>
            <arc large-arc-flag="0" rx="200" ry="200" sweep-flag="1" x="50.47" x-axis-rotation="0" y="0"/>
            <arc large-arc-flag="0" rx="200" ry="200" sweep-flag="1" x="91.67" x-axis-rotation="0" y="6"/>
            <arc large-arc-flag="0" rx="14" ry="14" sweep-flag="1" x="99.47" x-axis-rotation="0" y="22.2"/>
            <arc large-arc-flag="0" rx="14" ry="14" sweep-flag="1" x="88.87" x-axis-rotation="0" y="33"/>
            <line x="71.87" y="21.5"/>
            <arc large-arc-flag="0" rx="15" ry="15" sweep-flag="1" x="73.17" x-axis-rotation="0" y="12.7"/>
            <arc large-arc-flag="0" rx="200" ry="200" sweep-flag="0" x="50.47" x-axis-rotation="0" y="11.4"/>
            <arc large-arc-flag="0" rx="200" ry="200" sweep-flag="0" x="27.67" x-axis-rotation="0" y="12.7"/>
            <arc large-arc-flag="0" rx="15" ry="15" sweep-flag="1" x="29.07" x-axis-rotation="0" y="21.4"/>
            <close/>
            <move x="10.27" y="48.6"/>
            <arc large-arc-flag="0" rx="60" ry="60" sweep-flag="0" x="33.37" x-axis-rotation="0" y="13.8"/>
            <line x="39.07" y="13.3"/>
            <line x="39.07" y="19.3"/>
            <line x="61.87" y="19.3"/>
            <line x="61.87" y="13.4"/>
            <line x="67.67" y="13.8"/>
            <arc large-arc-flag="0" rx="60" ry="60" sweep-flag="0" x="90.47" x-axis-rotation="0" y="48.4"/>
            <line x="90.57" y="71.6"/>
            <line x="10.27" y="71.6"/>
            <close/>
            <move x="32.87" y="43.2"/>
            <arc large-arc-flag="0" rx="18" ry="18" sweep-flag="0" x="50.47" x-axis-rotation="0" y="60.2"/>
            <arc large-arc-flag="0" rx="18" ry="18" sweep-flag="0" x="68.07" x-axis-rotation="0" y="43.2"/>
            <arc large-arc-flag="0" rx="18" ry="18" sweep-flag="0" x="50.47" x-axis-rotation="0" y="25"/>
            <arc large-arc-flag="0" rx="18" ry="18" sweep-flag="0" x="32.87" x-axis-rotation="0" y="43.2"/>
            <close/>
            <move x="39.17" y="43.2"/>
            <arc large-arc-flag="0" rx="11.5" ry="11.5" sweep-flag="1" x="50.47" x-axis-rotation="0" y="31.2"/>
            <arc large-arc-flag="0" rx="11.5" ry="11.5" sweep-flag="1" x="61.87" x-axis-rotation="0" y="43.2"/>
            <arc large-arc-flag="0" rx="11.5" ry="11.5" sweep-flag="1" x="50.47" x-axis-rotation="0" y="54"/>
            <arc large-arc-flag="0" rx="11.5" ry="11.5" sweep-flag="1" x="39.17" x-axis-rotation="0" y="43.2"/>
            <close/>
        </path>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="90" name="Phone 2" strokewidth="2" w="100">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0.11"/>
        <constraint name="S" perimeter="0" x="0.5" y="1"/>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="E" perimeter="0" x="1" y="0.5"/>
        <constraint name="NW" perimeter="0" x="0.02" y="0.12"/>
        <constraint name="SW" perimeter="0" x="0.02" y="0.98"/>
        <constraint name="NE" perimeter="0" x="0.98" y="0.12"/>
        <constraint name="SE" perimeter="0" x="0.98" y="0.98"/>
    </connections>
    <foreground>
        <path>
            <move x="91" y="10"/>
            <line x="95" y="10"/>
            <arc large-arc-flag="0" rx="5" ry="5" sweep-flag="1" x="100" x-axis-rotation="0" y="15"/>
            <line x="100" y="85"/>
            <arc large-arc-flag="0" rx="5" ry="5" sweep-flag="1" x="95" x-axis-rotation="0" y="90"/>
            <line x="5" y="90"/>
            <arc large-arc-flag="0" rx="5" ry="5" sweep-flag="1" x="0" x-axis-rotation="0" y="85"/>
            <line x="0" y="15"/>
            <arc large-arc-flag="0" rx="5" ry="5" sweep-flag="1" x="5" x-axis-rotation="0" y="10"/>
            <line x="61" y="10"/>
            <line x="61" y="75"/>
            <arc large-arc-flag="0" rx="6" ry="6" sweep-flag="0" x="67" x-axis-rotation="0" y="81"/>
            <line x="85" y="81"/>
            <arc large-arc-flag="0" rx="6" ry="6" sweep-flag="0" x="91" x-axis-rotation="0" y="75"/>
            <close/>
        </path>
        <fillstroke/>
        <roundrect arcsize="16.67" h="78" w="24" x="64" y="0"/>
        <fillstroke/>
        <fillcolor color="#ffffff"/>
        <rect h="10" w="40" x="12" y="24"/>
        <fillstroke/>
        <rect h="8" w="8" x="12" y="42"/>
        <fillstroke/>
        <rect h="8" w="8" x="28" y="42"/>
        <fillstroke/>
        <rect h="8" w="8" x="44" y="42"/>
        <fillstroke/>
        <rect h="8" w="8" x="12" y="58"/>
        <fillstroke/>
        <rect h="8" w="8" x="28" y="58"/>
        <fillstroke/>
        <rect h="8" w="8" x="44" y="58"/>
        <fillstroke/>
        <rect h="8" w="8" x="12" y="74"/>
        <fillstroke/>
        <rect h="8" w="8" x="28" y="74"/>
        <fillstroke/>
        <rect h="8" w="8" x="44" y="74"/>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="100" name="Printer" strokewidth="2" w="100">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0"/>
        <constraint name="S" perimeter="0" x="0.5" y="1"/>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="E" perimeter="0" x="1" y="0.5"/>
        <constraint name="NW" perimeter="0" x="0.2" y="0"/>
        <constraint name="SW" perimeter="0" x="0.1" y="1"/>
        <constraint name="NE" perimeter="0" x="0.8" y="0"/>
        <constraint name="SE" perimeter="0" x="0.9" y="1"/>
    </connections>
    <foreground>
        <rect h="40" w="100" x="0" y="40"/>
        <fillstroke/>
        <rect h="6" w="84" x="8" y="80"/>
        <fillstroke/>
        <rect h="40" w="60" x="20" y="0"/>
        <fillstroke/>
        <path>
            <move x="80" y="70"/>
            <line x="90" y="100"/>
            <line x="10" y="100"/>
            <line x="20" y="70"/>
            <close/>
            <move x="25" y="78"/>
            <line x="75" y="78"/>
            <move x="22" y="86"/>
            <line x="78" y="86"/>
            <move x="18" y="94"/>
            <line x="82" y="94"/>
        </path>
        <fillstroke/>
        <fillcolor color="#ffffff"/>
        <rect h="3" w="25" x="10" y="45"/>
        <fill/>
        <ellipse h="10" w="10" x="80" y="45"/>
        <fill/>
    </foreground>
</shape>
<shape aspect="variable" h="107" name="Proxy Server" strokewidth="2" w="103">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0"/>
        <constraint name="S" perimeter="0" x="0.5" y="0.94"/>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="E" perimeter="0" x="0.88" y="0.5"/>
        <constraint name="NW" perimeter="0" x="0" y="0.14"/>
        <constraint name="SW" perimeter="0" x="0" y="0.93"/>
        <constraint name="NE" perimeter="0" x="0.88" y="0.14"/>
        <constraint name="SE" perimeter="0" x="0.95" y="0.95"/>
    </connections>
    <foreground>
        <save/>
        <roundrect arcsize="13.33" h="15" w="90" x="0" y="85"/>
        <fillstroke/>
        <roundrect arcsize="13.33" h="15" w="90" x="0" y="67.5"/>
        <fillstroke/>
        <roundrect arcsize="13.33" h="15" w="90" x="0" y="50"/>
        <fillstroke/>
        <roundrect arcsize="13.33" h="15" w="90" x="0" y="32.5"/>
        <fillstroke/>
        <roundrect arcsize="13.33" h="15" w="90" x="0" y="15"/>
        <fillstroke/>
        <path>
            <move x="4" y="13"/>
            <line x="25" y="0"/>
            <line x="65" y="0"/>
            <line x="86" y="13"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#ffffff"/>
        <ellipse h="8" w="8" x="78" y="88.5"/>
        <fill/>
        <ellipse h="8" w="8" x="77.67" y="71"/>
        <fill/>
        <ellipse h="8" w="8" x="78" y="53.5"/>
        <fill/>
        <ellipse h="8" w="8" x="78" y="36"/>
        <fill/>
        <ellipse h="8" w="8" x="78" y="18.5"/>
        <fill/>
        <path>
            <move x="5" y="98"/>
            <line x="10" y="87"/>
            <line x="15" y="87"/>
            <line x="10" y="98"/>
            <close/>
            <move x="15" y="98"/>
            <line x="20" y="87"/>
            <line x="25" y="87"/>
            <line x="20" y="98"/>
            <close/>
            <move x="25" y="98"/>
            <line x="30" y="87"/>
            <line x="35" y="87"/>
            <line x="30" y="98"/>
            <close/>
            <move x="35" y="98"/>
            <line x="40" y="87"/>
            <line x="45" y="87"/>
            <line x="40" y="98"/>
            <close/>
            <move x="45" y="98"/>
            <line x="50" y="87"/>
            <line x="55" y="87"/>
            <line x="50" y="98"/>
            <close/>
            <move x="55" y="98"/>
            <line x="60" y="87"/>
            <line x="65" y="87"/>
            <line x="60" y="98"/>
            <close/>
            <move x="5" y="80.5"/>
            <line x="10" y="69.5"/>
            <line x="15" y="69.5"/>
            <line x="10" y="80.5"/>
            <close/>
            <move x="15" y="80.5"/>
            <line x="20" y="69.5"/>
            <line x="25" y="69.5"/>
            <line x="20" y="80.5"/>
            <close/>
            <move x="25" y="80.5"/>
            <line x="30" y="69.5"/>
            <line x="35" y="69.5"/>
            <line x="30" y="80.5"/>
            <close/>
            <move x="35" y="80.5"/>
            <line x="40" y="69.5"/>
            <line x="45" y="69.5"/>
            <line x="40" y="80.5"/>
            <close/>
            <move x="45" y="80.5"/>
            <line x="50" y="69.5"/>
            <line x="55" y="69.5"/>
            <line x="50" y="80.5"/>
            <close/>
            <move x="55" y="80.5"/>
            <line x="60" y="69.5"/>
            <line x="65" y="69.5"/>
            <line x="60" y="80.5"/>
            <close/>
            <move x="5" y="63"/>
            <line x="10" y="52"/>
            <line x="15" y="52"/>
            <line x="10" y="63"/>
            <close/>
            <move x="15" y="63"/>
            <line x="20" y="52"/>
            <line x="25" y="52"/>
            <line x="20" y="63"/>
            <close/>
            <move x="25" y="63"/>
            <line x="29.5" y="52"/>
            <line x="34.5" y="52"/>
            <line x="29.5" y="63"/>
            <close/>
            <move x="35" y="63"/>
            <line x="40" y="52"/>
            <line x="45" y="52"/>
            <line x="40" y="63"/>
            <close/>
            <move x="45" y="63"/>
            <line x="50" y="52"/>
            <line x="55" y="52"/>
            <line x="50" y="63"/>
            <close/>
            <move x="55" y="63"/>
            <line x="60" y="52"/>
            <line x="65" y="52"/>
            <line x="60" y="63"/>
            <close/>
            <move x="5" y="45.5"/>
            <line x="10" y="34.5"/>
            <line x="15" y="34.5"/>
            <line x="10" y="45.5"/>
            <close/>
            <move x="15" y="45.5"/>
            <line x="20" y="34.5"/>
            <line x="25" y="34.5"/>
            <line x="20" y="45.5"/>
            <close/>
            <move x="25" y="45.5"/>
            <line x="30" y="34.5"/>
            <line x="35" y="34.5"/>
            <line x="30" y="45.5"/>
            <close/>
            <move x="35" y="45.5"/>
            <line x="40" y="34.5"/>
            <line x="45" y="34.5"/>
            <line x="40" y="45.5"/>
            <close/>
            <move x="45" y="45.5"/>
            <line x="50" y="34.5"/>
            <line x="55" y="34.5"/>
            <line x="50" y="45.5"/>
            <close/>
            <move x="55" y="45.5"/>
            <line x="60" y="34.5"/>
            <line x="65" y="34.5"/>
            <line x="60" y="45.5"/>
            <close/>
            <move x="5" y="28"/>
            <line x="10" y="17"/>
            <line x="15" y="17"/>
            <line x="10" y="28"/>
            <close/>
            <move x="15" y="28"/>
            <line x="20" y="17"/>
            <line x="25" y="17"/>
            <line x="20" y="28"/>
            <close/>
            <move x="25" y="28"/>
            <line x="30" y="17"/>
            <line x="35" y="17"/>
            <line x="30" y="28"/>
            <close/>
            <move x="35" y="28"/>
            <line x="40" y="17"/>
            <line x="45" y="17"/>
            <line x="40" y="28"/>
            <close/>
            <move x="45" y="28"/>
            <line x="50" y="17"/>
            <line x="55" y="17"/>
            <line x="50" y="28"/>
            <close/>
            <move x="55" y="28"/>
            <line x="60" y="17"/>
            <line x="65" y="17"/>
            <line x="60" y="28"/>
            <close/>
        </path>
        <fill/>
        <restore/>
        <rect/>
        <stroke/>
        <ellipse h="36" w="36" x="67" y="71"/>
        <fillstroke/>
        <strokecolor color="#ffffff"/>
        <rect h="18" w="23" x="73.5" y="80"/>
        <stroke/>
        <fillcolor color="#ffffff"/>
        <rect h="13.5" w="18" x="76" y="82.5"/>
        <fill/>
    </foreground>
</shape>
<shape aspect="variable" h="100" name="Rack" strokewidth="2" w="50">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0"/>
        <constraint name="S" perimeter="0" x="0.5" y="1"/>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="E" perimeter="0" x="1" y="0.5"/>
        <constraint name="NW" perimeter="0" x="0" y="0"/>
        <constraint name="SW" perimeter="0" x="0" y="1"/>
        <constraint name="NE" perimeter="0" x="1" y="0"/>
        <constraint name="SE" perimeter="0" x="1" y="1"/>
    </connections>
    <foreground>
        <rect h="100" w="50" x="0" y="0"/>
        <fillstroke/>
        <rect h="94" w="44" x="3" y="3"/>
        <stroke/>
        <path>
            <move x="3" y="12"/>
            <line x="47" y="12"/>
            <move x="3" y="21"/>
            <line x="47" y="21"/>
            <move x="3" y="30"/>
            <line x="47" y="30"/>
            <move x="3" y="39"/>
            <line x="47" y="39"/>
            <move x="3" y="48"/>
            <line x="47" y="48"/>
            <move x="3" y="66"/>
            <line x="47" y="66"/>
            <move x="3" y="75"/>
            <line x="47" y="75"/>
            <move x="3" y="93"/>
            <line x="47" y="93"/>
        </path>
        <fillstroke/>
        <fillcolor color="#ffffff"/>
        <rect h="2" w="3" x="7" y="6"/>
        <fill/>
        <rect h="2" w="3" x="12" y="6"/>
        <fill/>
        <rect h="2" w="3" x="17" y="6"/>
        <fill/>
        <ellipse h="3" w="3" x="39.5" y="6"/>
        <fill/>
        <rect h="2" w="3" x="7" y="15"/>
        <fill/>
        <rect h="2" w="3" x="12" y="15"/>
        <fill/>
        <rect h="2" w="3" x="17" y="15"/>
        <fill/>
        <ellipse h="3" w="3" x="39.5" y="15"/>
        <fill/>
        <rect h="2" w="3" x="7" y="24"/>
        <fill/>
        <rect h="2" w="3" x="12" y="24"/>
        <fill/>
        <rect h="2" w="3" x="17" y="24"/>
        <fill/>
        <ellipse h="3" w="3" x="39.5" y="24"/>
        <fill/>
        <rect h="2" w="3" x="7" y="33"/>
        <fill/>
        <rect h="2" w="3" x="12" y="33"/>
        <fill/>
        <rect h="2" w="3" x="17" y="33"/>
        <fill/>
        <ellipse h="3" w="3" x="39.5" y="33"/>
        <fill/>
        <rect h="2" w="3" x="7" y="42"/>
        <fill/>
        <rect h="2" w="3" x="12" y="42"/>
        <fill/>
        <rect h="2" w="3" x="17" y="42"/>
        <fill/>
        <ellipse h="3" w="3" x="39.5" y="42"/>
        <fill/>
        <rect h="12" w="3" x="7" y="51"/>
        <fill/>
        <rect h="12" w="3" x="12" y="51"/>
        <fill/>
        <rect h="12" w="3" x="17" y="51"/>
        <fill/>
        <ellipse h="3" w="3" x="39.5" y="51"/>
        <fill/>
        <rect h="2" w="3" x="7" y="69"/>
        <fill/>
        <rect h="2" w="3" x="12" y="69"/>
        <fill/>
        <rect h="2" w="3" x="17" y="69"/>
        <fill/>
        <ellipse h="3" w="3" x="39.5" y="69"/>
        <fill/>
        <rect h="12" w="3" x="7" y="78"/>
        <fill/>
        <rect h="12" w="3" x="12" y="78"/>
        <fill/>
        <rect h="12" w="3" x="17" y="78"/>
        <fill/>
        <ellipse h="3" w="3" x="39.5" y="78"/>
        <fill/>
        <rect h="12" w="3" x="22" y="51"/>
        <fill/>
        <rect h="12" w="3" x="27" y="51"/>
        <fill/>
        <rect h="12" w="3" x="22" y="78"/>
        <fill/>
        <rect h="12" w="3" x="27" y="78"/>
        <fill/>
    </foreground>
</shape>
<shape aspect="variable" h="98.8" name="Radio Tower" strokewidth="2" w="53.34">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0.15"/>
        <constraint name="S1" perimeter="0" x="0.09" y="1"/>
        <constraint name="S2" perimeter="0" x="0.5" y="1"/>
        <constraint name="S3" perimeter="0" x="0.91" y="1"/>
        <constraint name="W" perimeter="0" x="0.33" y="0.5"/>
        <constraint name="E" perimeter="0" x="0.67" y="0.5"/>
    </connections>
    <foreground>
        <save/>
        <linejoin join="round"/>
        <path>
            <move x="4.67" y="98.8"/>
            <line x="26.67" y="16.3"/>
            <line x="48.67" y="98.8"/>
        </path>
        <stroke/>
        <path>
            <move x="47.67" y="93.3"/>
            <line x="10.67" y="76.8"/>
            <line x="39.67" y="65.8"/>
            <line x="17.17" y="54.8"/>
            <line x="33.67" y="44.3"/>
            <line x="22.17" y="34.8"/>
        </path>
        <stroke/>
        <restore/>
        <rect/>
        <stroke/>
        <ellipse h="8" w="8" x="22.67" y="14.3"/>
        <fillstroke/>
        <path>
            <move x="33.47" y="11.5"/>
            <line x="34.87" y="9.8"/>
            <arc large-arc-flag="0" rx="12" ry="12" sweep-flag="1" x="34.87" x-axis-rotation="0" y="26.6"/>
            <line x="33.47" y="25"/>
            <arc large-arc-flag="0" rx="10" ry="10" sweep-flag="0" x="33.47" x-axis-rotation="0" y="11.5"/>
            <close/>
            <move x="38.47" y="6.5"/>
            <line x="39.87" y="5.1"/>
            <arc large-arc-flag="0" rx="19" ry="19" sweep-flag="1" x="39.87" x-axis-rotation="0" y="31.5"/>
            <line x="38.47" y="30.1"/>
            <arc large-arc-flag="0" rx="17" ry="17" sweep-flag="0" x="38.47" x-axis-rotation="0" y="6.5"/>
            <close/>
            <move x="43.47" y="1.5"/>
            <line x="44.87" y="0"/>
            <arc large-arc-flag="0" rx="24" ry="24" sweep-flag="1" x="44.87" x-axis-rotation="0" y="36.6"/>
            <line x="43.47" y="35.1"/>
            <arc large-arc-flag="0" rx="21.5" ry="21.5" sweep-flag="0" x="43.47" x-axis-rotation="0" y="1.5"/>
            <close/>
            <move x="19.87" y="11.5"/>
            <line x="18.47" y="9.8"/>
            <arc large-arc-flag="0" rx="12" ry="12" sweep-flag="0" x="18.47" x-axis-rotation="0" y="26.6"/>
            <line x="19.87" y="25"/>
            <arc large-arc-flag="0" rx="10" ry="10" sweep-flag="1" x="19.87" x-axis-rotation="0" y="11.5"/>
            <close/>
            <move x="14.87" y="6.5"/>
            <line x="13.47" y="5.1"/>
            <arc large-arc-flag="0" rx="19" ry="19" sweep-flag="0" x="13.47" x-axis-rotation="0" y="31.5"/>
            <line x="14.87" y="30.1"/>
            <arc large-arc-flag="0" rx="17" ry="17" sweep-flag="1" x="14.87" x-axis-rotation="0" y="6.5"/>
            <close/>
            <move x="9.87" y="1.5"/>
            <line x="8.47" y="0"/>
            <arc large-arc-flag="0" rx="24" ry="24" sweep-flag="0" x="8.47" x-axis-rotation="0" y="36.6"/>
            <line x="9.87" y="35.1"/>
            <arc large-arc-flag="0" rx="21.5" ry="21.5" sweep-flag="1" x="9.87" x-axis-rotation="0" y="1.5"/>
            <close/>
        </path>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="29" name="Router" strokewidth="2" w="100">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0"/>
        <constraint name="S" perimeter="0" x="0.5" y="1"/>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="E" perimeter="0" x="1" y="0.5"/>
        <constraint name="NW" perimeter="0" x="0.05" y="0.05"/>
        <constraint name="SW" perimeter="0" x="0.1" y="1"/>
        <constraint name="NE" perimeter="0" x="0.95" y="0.05"/>
        <constraint name="SE" perimeter="0" x="0.9" y="1"/>
    </connections>
    <foreground>
        <roundrect arcsize="20" h="25" w="100" x="0" y="0"/>
        <fillstroke/>
        <rect h="4" w="80" x="10" y="25"/>
        <fillstroke/>
        <fillcolor color="#ffffff"/>
        <path>
            <move x="3" y="7"/>
            <line x="9" y="7"/>
            <line x="9" y="10"/>
            <line x="7" y="10"/>
            <line x="7" y="11"/>
            <line x="5" y="11"/>
            <line x="5" y="10"/>
            <line x="3" y="10"/>
            <close/>
            <move x="11.5" y="7"/>
            <line x="17.5" y="7"/>
            <line x="17.5" y="10"/>
            <line x="15.5" y="10"/>
            <line x="15.5" y="11"/>
            <line x="13.5" y="11"/>
            <line x="13.5" y="10"/>
            <line x="11.5" y="10"/>
            <close/>
            <move x="20" y="7"/>
            <line x="26" y="7"/>
            <line x="26" y="10"/>
            <line x="24" y="10"/>
            <line x="24" y="11"/>
            <line x="22" y="11"/>
            <line x="22" y="10"/>
            <line x="20" y="10"/>
            <close/>
            <move x="28.5" y="7"/>
            <line x="34.5" y="7"/>
            <line x="34.5" y="10"/>
            <line x="32.5" y="10"/>
            <line x="32.5" y="11"/>
            <line x="30.5" y="11"/>
            <line x="30.5" y="10"/>
            <line x="28.5" y="10"/>
            <close/>
            <move x="37" y="7"/>
            <line x="43" y="7"/>
            <line x="43" y="10"/>
            <line x="41" y="10"/>
            <line x="41" y="11"/>
            <line x="39" y="11"/>
            <line x="39" y="10"/>
            <line x="37" y="10"/>
            <close/>
            <move x="45.5" y="7"/>
            <line x="51.5" y="7"/>
            <line x="51.5" y="10"/>
            <line x="49.5" y="10"/>
            <line x="49.5" y="11"/>
            <line x="47.5" y="11"/>
            <line x="47.5" y="10"/>
            <line x="45.5" y="10"/>
            <close/>
            <move x="54" y="7"/>
            <line x="60" y="7"/>
            <line x="60" y="10"/>
            <line x="58" y="10"/>
            <line x="58" y="11"/>
            <line x="56" y="11"/>
            <line x="56" y="10"/>
            <line x="54" y="10"/>
            <close/>
            <move x="62.5" y="7"/>
            <line x="68.5" y="7"/>
            <line x="68.5" y="10"/>
            <line x="66.5" y="10"/>
            <line x="66.5" y="11"/>
            <line x="64.5" y="11"/>
            <line x="64.5" y="10"/>
            <line x="62.5" y="10"/>
            <close/>
        </path>
        <fill/>
        <ellipse h="16" w="16" x="80" y="4.5"/>
        <fill/>
        <ellipse h="4" w="4" x="73" y="3"/>
        <fill/>
        <ellipse h="4" w="4" x="73" y="18"/>
        <fill/>
        <ellipse h="4" w="4" x="73" y="10.5"/>
        <fill/>
        <ellipse h="4" w="4" x="63.5" y="15"/>
        <fill/>
        <ellipse h="4" w="4" x="55" y="15"/>
        <fill/>
        <ellipse h="4" w="4" x="46.5" y="15"/>
        <fill/>
        <ellipse h="4" w="4" x="38" y="15"/>
        <fill/>
        <ellipse h="4" w="4" x="29.5" y="15"/>
        <fill/>
        <ellipse h="4" w="4" x="21" y="15"/>
        <fill/>
        <ellipse h="4" w="4" x="12.5" y="15"/>
        <fill/>
        <ellipse h="4" w="4" x="4" y="15"/>
        <fill/>
        <strokewidth width="1"/>
        <path>
            <move x="88" y="7"/>
            <line x="88" y="18"/>
            <move x="81.5" y="12.5"/>
            <line x="85.5" y="12.5"/>
            <move x="90.5" y="12.5"/>
            <line x="94.5" y="12.5"/>
            <move x="86" y="9"/>
            <line x="88" y="7"/>
            <line x="90" y="9"/>
            <move x="86" y="16"/>
            <line x="88" y="18"/>
            <line x="90" y="16"/>
            <move x="83.5" y="10.5"/>
            <line x="85.5" y="12.5"/>
            <line x="83.5" y="14.5"/>
            <move x="92.5" y="10.5"/>
            <line x="90.5" y="12.5"/>
            <line x="92.5" y="14.5"/>
        </path>
        <stroke/>
    </foreground>
</shape>
<shape aspect="variable" h="100" name="Satellite" strokewidth="2" w="100">
    <connections>
        <constraint name="N" perimeter="0" x="0.58" y="0.3"/>
        <constraint name="S" perimeter="0" x="0.4" y="0.85"/>
        <constraint name="W" perimeter="0" x="0.15" y="0.6"/>
        <constraint name="E" perimeter="0" x="0.7" y="0.42"/>
        <constraint name="NW1" perimeter="0" x="0" y="0.15"/>
        <constraint name="NW2" perimeter="0" x="0.15" y="0"/>
        <constraint name="SW" perimeter="0" x="0.07" y="0.93"/>
        <constraint name="SE1" perimeter="0" x="0.85" y="1"/>
        <constraint name="SE2" perimeter="0" x="1" y="0.85"/>
    </connections>
    <foreground>
        <save/>
        <path>
            <move x="28" y="60"/>
            <line x="58" y="30"/>
            <arc large-arc-flag="0" rx="10" ry="10" sweep-flag="1" x="70" x-axis-rotation="0" y="42"/>
            <line x="40" y="72"/>
            <arc large-arc-flag="0" rx="20" ry="20" sweep-flag="1" x="40" x-axis-rotation="0" y="85"/>
            <arc large-arc-flag="0" rx="30" ry="30" sweep-flag="1" x="15" x-axis-rotation="0" y="60"/>
            <arc large-arc-flag="0" rx="20" ry="20" sweep-flag="1" x="28" x-axis-rotation="0" y="60"/>
            <close/>
            <move x="15" y="0"/>
            <line x="45" y="30"/>
            <line x="30" y="45"/>
            <line x="0" y="15"/>
            <close/>
            <move x="70" y="55"/>
            <line x="100" y="85"/>
            <line x="85" y="100"/>
            <line x="55" y="70"/>
            <close/>
            <move x="92.5" y="77.12"/>
            <line x="77.25" y="92.88"/>
            <move x="85" y="70"/>
            <line x="70" y="85"/>
            <move x="77.5" y="62.5"/>
            <line x="62.5" y="77.5"/>
            <move x="22.5" y="7.5"/>
            <line x="7.5" y="22.5"/>
            <move x="30" y="15"/>
            <line x="15" y="30"/>
            <move x="37.5" y="22.5"/>
            <line x="22.5" y="37.5"/>
        </path>
        <fillstroke/>
        <path>
            <move x="16.7" y="66"/>
            <line x="19.5" y="80.5"/>
            <line x="38.5" y="80"/>
            <move x="7.5" y="7.5"/>
            <line x="37.5" y="37.5"/>
            <move x="62.5" y="62.5"/>
            <line x="92.5" y="92.5"/>
        </path>
        <stroke/>
        <restore/>
        <rect/>
        <stroke/>
        <path>
            <move x="39.5" y="35.5"/>
            <line x="46" y="42"/>
            <line x="42" y="46"/>
            <line x="35.5" y="39.5"/>
            <close/>
            <move x="56" y="52"/>
            <line x="64.5" y="60.5"/>
            <line x="60.5" y="64.5"/>
            <line x="52" y="56"/>
            <arc large-arc-flag="0" rx="4" ry="4" sweep-flag="1" x="56" x-axis-rotation="0" y="52"/>
            <close/>
        </path>
        <fillstroke/>
        <path>
            <move x="51" y="37"/>
            <arc large-arc-flag="0" rx="15" ry="15" sweep-flag="1" x="63" x-axis-rotation="0" y="49"/>
            <move x="38" y="50"/>
            <arc large-arc-flag="0" rx="15" ry="15" sweep-flag="1" x="50" x-axis-rotation="0" y="62"/>
            <move x="36" y="52"/>
            <arc large-arc-flag="0" rx="15" ry="15" sweep-flag="1" x="48" x-axis-rotation="0" y="64"/>
            <move x="28" y="60"/>
            <arc large-arc-flag="0" rx="20" ry="20" sweep-flag="1" x="40" x-axis-rotation="0" y="72"/>
            <move x="15" y="60"/>
            <arc large-arc-flag="0" rx="30" ry="30" sweep-flag="1" x="40" x-axis-rotation="0" y="85"/>
        </path>
        <stroke/>
        <path>
            <move x="54" y="38"/>
            <line x="41" y="51"/>
            <move x="56.5" y="39.5"/>
            <line x="43.5" y="52.5"/>
        </path>
        <stroke/>
        <ellipse h="4" w="4" x="17" y="79"/>
        <fillstroke/>
        <path>
            <move x="14" y="81"/>
            <arc large-arc-flag="0" rx="5" ry="5" sweep-flag="0" x="19" x-axis-rotation="0" y="86"/>
            <line x="19" y="88"/>
            <arc large-arc-flag="0" rx="7" ry="7" sweep-flag="1" x="12" x-axis-rotation="0" y="81"/>
            <close/>
            <move x="9" y="81"/>
            <arc large-arc-flag="0" rx="10" ry="10" sweep-flag="0" x="19" x-axis-rotation="0" y="91"/>
            <line x="19" y="93"/>
            <arc large-arc-flag="0" rx="12" ry="12" sweep-flag="1" x="7" x-axis-rotation="0" y="81"/>
            <close/>
            <move x="4" y="81"/>
            <arc large-arc-flag="0" rx="15" ry="15" sweep-flag="0" x="19" x-axis-rotation="0" y="96"/>
            <line x="19" y="98"/>
            <arc large-arc-flag="0" rx="17" ry="17" sweep-flag="1" x="2" x-axis-rotation="0" y="81"/>
            <close/>
        </path>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="100" name="Satellite Dish" strokewidth="2" w="87.43">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0.35"/>
        <constraint name="S" perimeter="0" x="0.43" y="1"/>
        <constraint name="W" perimeter="0" x="0.07" y="0.5"/>
        <constraint name="E" perimeter="0" x="0.65" y="0.5"/>
        <constraint name="NW" perimeter="0" x="0.08" y="0.15"/>
        <constraint name="SW" perimeter="0" x="0.03" y="1"/>
        <constraint name="NE" perimeter="0" x="0.91" y="0.09"/>
        <constraint name="SE" perimeter="0" x="0.83" y="0.8"/>
    </connections>
    <foreground>
        <path>
            <move x="7.43" y="15"/>
            <arc large-arc-flag="0" rx="100" ry="100" sweep-flag="1" x="72.43" x-axis-rotation="0" y="80"/>
            <arc large-arc-flag="0" rx="50" ry="50" sweep-flag="1" x="7.43" x-axis-rotation="0" y="15"/>
            <close/>
            <move x="2.43" y="100"/>
            <line x="5.43" y="55"/>
            <arc large-arc-flag="0" rx="50" ry="50" sweep-flag="0" x="32.43" x-axis-rotation="0" y="82"/>
            <line x="37.43" y="100"/>
            <close/>
            <move x="57.43" y="15"/>
            <arc large-arc-flag="0" rx="15" ry="15" sweep-flag="1" x="72.43" x-axis-rotation="0" y="30"/>
            <line x="70.43" y="30"/>
            <arc large-arc-flag="0" rx="13" ry="13" sweep-flag="0" x="57.43" x-axis-rotation="0" y="17"/>
            <close/>
            <move x="57.43" y="7"/>
            <arc large-arc-flag="0" rx="23" ry="23" sweep-flag="1" x="80.43" x-axis-rotation="0" y="30"/>
            <line x="78.43" y="30"/>
            <arc large-arc-flag="0" rx="21" ry="21" sweep-flag="0" x="57.43" x-axis-rotation="0" y="9"/>
            <close/>
            <move x="57.43" y="0"/>
            <arc large-arc-flag="0" rx="30" ry="30" sweep-flag="1" x="87.43" x-axis-rotation="0" y="30"/>
            <line x="85.43" y="30"/>
            <arc large-arc-flag="0" rx="28" ry="28" sweep-flag="0" x="57.43" x-axis-rotation="0" y="2"/>
            <close/>
        </path>
        <fillstroke/>
        <path>
            <move x="7.43" y="15"/>
            <arc large-arc-flag="0" rx="100" ry="100" sweep-flag="0" x="72.43" x-axis-rotation="0" y="80.29"/>
        </path>
        <stroke/>
<linejoin join="round"/>
        <path>
            <move x="28.93" y="51.5"/>
            <line x="57.43" y="30"/>
            <line x="35.93" y="58.5"/>
            <close/>
        </path>
        <fillstroke/>
        <ellipse h="6" w="6" x="54.43" y="27"/>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="74.07" name="Scanner" strokewidth="2" w="99.6">
    <connections>
        <constraint name="N" perimeter="0" x="0.7" y="0"/>
        <constraint name="S" perimeter="0" x="0.58" y="1"/>
        <constraint name="W" perimeter="0" x="0" y="0.6"/>
        <constraint name="E" perimeter="0" x="1" y="0.66"/>
    </connections>
    <foreground>
        <path>
            <move x="99.6" y="48.67"/>
            <arc large-arc-flag="0" rx="11.58" ry="11.58" sweep-flag="1" x="96.13" x-axis-rotation="0" y="55.04"/>
            <line x="62.54" y="72.42"/>
            <arc large-arc-flag="0" rx="11.58" ry="11.58" sweep-flag="1" x="52.12" x-axis-rotation="0" y="72.42"/>
            <line x="2.32" y="49.25"/>
            <arc large-arc-flag="0" rx="8.11" ry="8.11" sweep-flag="1" x="0" x-axis-rotation="0" y="44.62"/>
            <arc large-arc-flag="0" rx="11.58" ry="11.58" sweep-flag="1" x="3.47" x-axis-rotation="0" y="37.67"/>
            <line x="34.74" y="11.03"/>
            <arc large-arc-flag="0" rx="23.16" ry="23.16" sweep-flag="1" x="41.69" x-axis-rotation="0" y="7.56"/>
            <line x="67.17" y="0.61"/>
            <arc large-arc-flag="0" rx="6.95" ry="6.95" sweep-flag="1" x="74.12" x-axis-rotation="0" y="2.93"/>
            <line x="39.38" y="28.41"/>
            <line x="99.6" y="48.67"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#ffffff"/>
        <path>
            <move x="39.38" y="31.88"/>
            <line x="91.49" y="49.25"/>
            <line x="56.75" y="66.62"/>
            <line x="8.11" y="44.62"/>
            <close/>
        </path>
        <fill/>
        <path>
            <move x="73" y="2.97"/>
            <line x="39" y="11.97"/>
            <line x="1" y="44.57"/>
            <line x="56.7" y="69.97"/>
            <line x="99" y="48.97"/>
            <move x="40" y="27.97"/>
            <line x="2" y="43.97"/>
        </path>
        <stroke/>
    </foreground>
</shape>
<shape aspect="variable" h="100" name="Secured" strokewidth="2" w="80">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0"/>
        <constraint name="S" perimeter="0" x="0.5" y="1"/>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="E" perimeter="0" x="1" y="0.5"/>
        <constraint name="SW" perimeter="0" x="0" y="1"/>
        <constraint name="SE" perimeter="0" x="1" y="1"/>
    </connections>
    <foreground>
        <path>
            <move x="0" y="45"/>
            <line x="10" y="45"/>
            <line x="10" y="30"/>
            <arc large-arc-flag="0" rx="30" ry="30" sweep-flag="1" x="40" x-axis-rotation="0" y="0"/>
            <arc large-arc-flag="0" rx="30" ry="30" sweep-flag="1" x="70" x-axis-rotation="0" y="30"/>
            <line x="70" y="45"/>
            <line x="80" y="45"/>
            <line x="80" y="100"/>
            <line x="0" y="100"/>
            <close/>
            <close/>
            <move x="20" y="45"/>
            <line x="60" y="45"/>
            <line x="60" y="30"/>
            <arc large-arc-flag="0" rx="20" ry="20" sweep-flag="0" x="40" x-axis-rotation="0" y="10"/>
            <arc large-arc-flag="0" rx="20" ry="20" sweep-flag="0" x="20" x-axis-rotation="0" y="30"/>
            <close/>
        </path>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="71.66" name="Security Camera" strokewidth="2" w="96.5">
    <connections>
        <constraint name="N" perimeter="0" x="0.3" y="0"/>
        <constraint name="S" perimeter="0" x="0.36" y="0.79"/>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="E" perimeter="0" x="0.88" y="0.5"/>
        <constraint name="SW" perimeter="0" x="0" y="1"/>
        <constraint name="SE" perimeter="0" x="0.9" y="0.93"/>
    </connections>
    <foreground>
        <path>
            <move x="0" y="35.66"/>
            <line x="0" y="71.66"/>
            <line x="5" y="71.66"/>
            <arc large-arc-flag="0" rx="5" ry="5" sweep-flag="0" x="10" x-axis-rotation="0" y="66.66"/>
            <line x="10" y="56.66"/>
            <line x="35" y="56.66"/>
            <line x="45" y="41.66"/>
            <line x="75" y="61.66"/>
            <line x="81.8" y="51.66"/>
            <line x="73" y="55.66"/>
            <line x="20" y="20.36"/>
            <line x="17.8" y="23.46"/>
            <line x="40.2" y="38.46"/>
            <line x="32" y="50.66"/>
            <line x="10" y="50.66"/>
            <line x="10" y="40.66"/>
            <arc large-arc-flag="0" rx="5" ry="5" sweep-flag="0" x="5" x-axis-rotation="0" y="35.66"/>
            <close/>
            <move x="18.7" y="13.46"/>
            <line x="73.3" y="50.26"/>
            <line x="93" y="41.66"/>
            <line x="33" y="1.66"/>
            <arc large-arc-flag="0" rx="5" ry="5" sweep-flag="0" x="26" x-axis-rotation="0" y="2.66"/>
            <close/>
            <move x="90.5" y="47.66"/>
            <line x="80.9" y="62.46"/>
            <line x="86.8" y="66.36"/>
            <line x="96.5" y="51.66"/>
            <close/>
        </path>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="100" name="Server" strokewidth="2" w="90">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0"/>
        <constraint name="S" perimeter="0" x="0.5" y="1"/>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="E" perimeter="0" x="1" y="0.5"/>
        <constraint name="NW" perimeter="0" x="0" y="0.15"/>
        <constraint name="SW" perimeter="0" x="0" y="1"/>
        <constraint name="NE" perimeter="0" x="1" y="0.15"/>
        <constraint name="SE" perimeter="0" x="1" y="1"/>
    </connections>
    <foreground>
        <roundrect arcsize="13.33" h="15" w="90" x="0" y="85"/>
        <fillstroke/>
        <roundrect arcsize="13.33" h="15" w="90" x="0" y="67.5"/>
        <fillstroke/>
        <roundrect arcsize="13.33" h="15" w="90" x="0" y="50"/>
        <fillstroke/>
        <roundrect arcsize="13.33" h="15" w="90" x="0" y="32.5"/>
        <fillstroke/>
        <roundrect arcsize="13.33" h="15" w="90" x="0" y="15"/>
        <fillstroke/>
        <path>
            <move x="4" y="13"/>
            <line x="25" y="0"/>
            <line x="65" y="0"/>
            <line x="86" y="13"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#ffffff"/>
        <ellipse h="8" w="8" x="78" y="88.5"/>
        <fill/>
        <ellipse h="8" w="8" x="77.67" y="71"/>
        <fill/>
        <ellipse h="8" w="8" x="78" y="53.5"/>
        <fill/>
        <ellipse h="8" w="8" x="78" y="36"/>
        <fill/>
        <ellipse h="8" w="8" x="78" y="18.5"/>
        <fill/>
        <path>
            <move x="5" y="98"/>
            <line x="10" y="87"/>
            <line x="15" y="87"/>
            <line x="10" y="98"/>
            <close/>
            <move x="15" y="98"/>
            <line x="20" y="87"/>
            <line x="25" y="87"/>
            <line x="20" y="98"/>
            <close/>
            <move x="25" y="98"/>
            <line x="30" y="87"/>
            <line x="35" y="87"/>
            <line x="30" y="98"/>
            <close/>
            <move x="35" y="98"/>
            <line x="40" y="87"/>
            <line x="45" y="87"/>
            <line x="40" y="98"/>
            <close/>
            <move x="45" y="98"/>
            <line x="50" y="87"/>
            <line x="55" y="87"/>
            <line x="50" y="98"/>
            <close/>
            <move x="55" y="98"/>
            <line x="60" y="87"/>
            <line x="65" y="87"/>
            <line x="60" y="98"/>
            <close/>
            <move x="5" y="80.5"/>
            <line x="10" y="69.5"/>
            <line x="15" y="69.5"/>
            <line x="10" y="80.5"/>
            <close/>
            <move x="15" y="80.5"/>
            <line x="20" y="69.5"/>
            <line x="25" y="69.5"/>
            <line x="20" y="80.5"/>
            <close/>
            <move x="25" y="80.5"/>
            <line x="30" y="69.5"/>
            <line x="35" y="69.5"/>
            <line x="30" y="80.5"/>
            <close/>
            <move x="35" y="80.5"/>
            <line x="40" y="69.5"/>
            <line x="45" y="69.5"/>
            <line x="40" y="80.5"/>
            <close/>
            <move x="45" y="80.5"/>
            <line x="50" y="69.5"/>
            <line x="55" y="69.5"/>
            <line x="50" y="80.5"/>
            <close/>
            <move x="55" y="80.5"/>
            <line x="60" y="69.5"/>
            <line x="65" y="69.5"/>
            <line x="60" y="80.5"/>
            <close/>
            <move x="5" y="63"/>
            <line x="10" y="52"/>
            <line x="15" y="52"/>
            <line x="10" y="63"/>
            <close/>
            <move x="15" y="63"/>
            <line x="20" y="52"/>
            <line x="25" y="52"/>
            <line x="20" y="63"/>
            <close/>
            <move x="25" y="63"/>
            <line x="29.5" y="52"/>
            <line x="34.5" y="52"/>
            <line x="29.5" y="63"/>
            <close/>
            <move x="35" y="63"/>
            <line x="40" y="52"/>
            <line x="45" y="52"/>
            <line x="40" y="63"/>
            <close/>
            <move x="45" y="63"/>
            <line x="50" y="52"/>
            <line x="55" y="52"/>
            <line x="50" y="63"/>
            <close/>
            <move x="55" y="63"/>
            <line x="60" y="52"/>
            <line x="65" y="52"/>
            <line x="60" y="63"/>
            <close/>
            <move x="5" y="45.5"/>
            <line x="10" y="34.5"/>
            <line x="15" y="34.5"/>
            <line x="10" y="45.5"/>
            <close/>
            <move x="15" y="45.5"/>
            <line x="20" y="34.5"/>
            <line x="25" y="34.5"/>
            <line x="20" y="45.5"/>
            <close/>
            <move x="25" y="45.5"/>
            <line x="30" y="34.5"/>
            <line x="35" y="34.5"/>
            <line x="30" y="45.5"/>
            <close/>
            <move x="35" y="45.5"/>
            <line x="40" y="34.5"/>
            <line x="45" y="34.5"/>
            <line x="40" y="45.5"/>
            <close/>
            <move x="45" y="45.5"/>
            <line x="50" y="34.5"/>
            <line x="55" y="34.5"/>
            <line x="50" y="45.5"/>
            <close/>
            <move x="55" y="45.5"/>
            <line x="60" y="34.5"/>
            <line x="65" y="34.5"/>
            <line x="60" y="45.5"/>
            <close/>
            <move x="5" y="28"/>
            <line x="10" y="17"/>
            <line x="15" y="17"/>
            <line x="10" y="28"/>
            <close/>
            <move x="15" y="28"/>
            <line x="20" y="17"/>
            <line x="25" y="17"/>
            <line x="20" y="28"/>
            <close/>
            <move x="25" y="28"/>
            <line x="30" y="17"/>
            <line x="35" y="17"/>
            <line x="30" y="28"/>
            <close/>
            <move x="35" y="28"/>
            <line x="40" y="17"/>
            <line x="45" y="17"/>
            <line x="40" y="28"/>
            <close/>
            <move x="45" y="28"/>
            <line x="50" y="17"/>
            <line x="55" y="17"/>
            <line x="50" y="28"/>
            <close/>
            <move x="55" y="28"/>
            <line x="60" y="17"/>
            <line x="65" y="17"/>
            <line x="60" y="28"/>
            <close/>
        </path>
        <fill/>
    </foreground>
</shape>
<shape aspect="variable" h="106.11" name="Server Storage" strokewidth="2" w="103">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0"/>
        <constraint name="S" perimeter="0" x="0.5" y="0.94"/>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="E" perimeter="0" x="0.88" y="0.5"/>
        <constraint name="NW" perimeter="0" x="0" y="0.14"/>
        <constraint name="SW" perimeter="0" x="0" y="0.93"/>
        <constraint name="NE" perimeter="0" x="0.87" y="0.14"/>
        <constraint name="SE" perimeter="0" x="1" y="0.97"/>
    </connections>
    <foreground>
        <save/>
        <roundrect arcsize="13.33" h="15" w="90" x="0" y="85"/>
        <fillstroke/>
        <roundrect arcsize="13.33" h="15" w="90" x="0" y="67.5"/>
        <fillstroke/>
        <roundrect arcsize="13.33" h="15" w="90" x="0" y="50"/>
        <fillstroke/>
        <roundrect arcsize="13.33" h="15" w="90" x="0" y="32.5"/>
        <fillstroke/>
        <roundrect arcsize="13.33" h="15" w="90" x="0" y="15"/>
        <fillstroke/>
        <path>
            <move x="4" y="13"/>
            <line x="25" y="0"/>
            <line x="65" y="0"/>
            <line x="86" y="13"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#ffffff"/>
        <ellipse h="8" w="8" x="78" y="88.5"/>
        <fill/>
        <ellipse h="8" w="8" x="77.67" y="71"/>
        <fill/>
        <ellipse h="8" w="8" x="78" y="53.5"/>
        <fill/>
        <ellipse h="8" w="8" x="78" y="36"/>
        <fill/>
        <ellipse h="8" w="8" x="78" y="18.5"/>
        <fill/>
        <path>
            <move x="5" y="98"/>
            <line x="10" y="87"/>
            <line x="15" y="87"/>
            <line x="10" y="98"/>
            <close/>
            <move x="15" y="98"/>
            <line x="20" y="87"/>
            <line x="25" y="87"/>
            <line x="20" y="98"/>
            <close/>
            <move x="25" y="98"/>
            <line x="30" y="87"/>
            <line x="35" y="87"/>
            <line x="30" y="98"/>
            <close/>
            <move x="35" y="98"/>
            <line x="40" y="87"/>
            <line x="45" y="87"/>
            <line x="40" y="98"/>
            <close/>
            <move x="45" y="98"/>
            <line x="50" y="87"/>
            <line x="55" y="87"/>
            <line x="50" y="98"/>
            <close/>
            <move x="55" y="98"/>
            <line x="60" y="87"/>
            <line x="65" y="87"/>
            <line x="60" y="98"/>
            <close/>
            <move x="5" y="80.5"/>
            <line x="10" y="69.5"/>
            <line x="15" y="69.5"/>
            <line x="10" y="80.5"/>
            <close/>
            <move x="15" y="80.5"/>
            <line x="20" y="69.5"/>
            <line x="25" y="69.5"/>
            <line x="20" y="80.5"/>
            <close/>
            <move x="25" y="80.5"/>
            <line x="30" y="69.5"/>
            <line x="35" y="69.5"/>
            <line x="30" y="80.5"/>
            <close/>
            <move x="35" y="80.5"/>
            <line x="40" y="69.5"/>
            <line x="45" y="69.5"/>
            <line x="40" y="80.5"/>
            <close/>
            <move x="45" y="80.5"/>
            <line x="50" y="69.5"/>
            <line x="55" y="69.5"/>
            <line x="50" y="80.5"/>
            <close/>
            <move x="55" y="80.5"/>
            <line x="60" y="69.5"/>
            <line x="65" y="69.5"/>
            <line x="60" y="80.5"/>
            <close/>
            <move x="5" y="63"/>
            <line x="10" y="52"/>
            <line x="15" y="52"/>
            <line x="10" y="63"/>
            <close/>
            <move x="15" y="63"/>
            <line x="20" y="52"/>
            <line x="25" y="52"/>
            <line x="20" y="63"/>
            <close/>
            <move x="25" y="63"/>
            <line x="29.5" y="52"/>
            <line x="34.5" y="52"/>
            <line x="29.5" y="63"/>
            <close/>
            <move x="35" y="63"/>
            <line x="40" y="52"/>
            <line x="45" y="52"/>
            <line x="40" y="63"/>
            <close/>
            <move x="45" y="63"/>
            <line x="50" y="52"/>
            <line x="55" y="52"/>
            <line x="50" y="63"/>
            <close/>
            <move x="55" y="63"/>
            <line x="60" y="52"/>
            <line x="65" y="52"/>
            <line x="60" y="63"/>
            <close/>
            <move x="5" y="45.5"/>
            <line x="10" y="34.5"/>
            <line x="15" y="34.5"/>
            <line x="10" y="45.5"/>
            <close/>
            <move x="15" y="45.5"/>
            <line x="20" y="34.5"/>
            <line x="25" y="34.5"/>
            <line x="20" y="45.5"/>
            <close/>
            <move x="25" y="45.5"/>
            <line x="30" y="34.5"/>
            <line x="35" y="34.5"/>
            <line x="30" y="45.5"/>
            <close/>
            <move x="35" y="45.5"/>
            <line x="40" y="34.5"/>
            <line x="45" y="34.5"/>
            <line x="40" y="45.5"/>
            <close/>
            <move x="45" y="45.5"/>
            <line x="50" y="34.5"/>
            <line x="55" y="34.5"/>
            <line x="50" y="45.5"/>
            <close/>
            <move x="55" y="45.5"/>
            <line x="60" y="34.5"/>
            <line x="65" y="34.5"/>
            <line x="60" y="45.5"/>
            <close/>
            <move x="5" y="28"/>
            <line x="10" y="17"/>
            <line x="15" y="17"/>
            <line x="10" y="28"/>
            <close/>
            <move x="15" y="28"/>
            <line x="20" y="17"/>
            <line x="25" y="17"/>
            <line x="20" y="28"/>
            <close/>
            <move x="25" y="28"/>
            <line x="30" y="17"/>
            <line x="35" y="17"/>
            <line x="30" y="28"/>
            <close/>
            <move x="35" y="28"/>
            <line x="40" y="17"/>
            <line x="45" y="17"/>
            <line x="40" y="28"/>
            <close/>
            <move x="45" y="28"/>
            <line x="50" y="17"/>
            <line x="55" y="17"/>
            <line x="50" y="28"/>
            <close/>
            <move x="55" y="28"/>
            <line x="60" y="17"/>
            <line x="65" y="17"/>
            <line x="60" y="28"/>
            <close/>
        </path>
        <fill/>
        <restore/>
        <rect/>
        <stroke/>
        <path>
            <move x="66" y="94.5"/>
            <arc large-arc-flag="0" rx="13.5" ry="3" sweep-flag="1" x="103" x-axis-rotation="0" y="94.5"/>
            <line x="103" y="102"/>
            <arc large-arc-flag="1" rx="13.5" ry="3" sweep-flag="1" x="66" x-axis-rotation="0" y="102"/>
            <close/>
        </path>
        <fillstroke/>
        <path>
            <move x="66" y="85"/>
            <arc large-arc-flag="0" rx="13.5" ry="3" sweep-flag="1" x="103" x-axis-rotation="0" y="85"/>
            <line x="103" y="92.5"/>
            <arc large-arc-flag="1" rx="13.5" ry="3" sweep-flag="1" x="66" x-axis-rotation="0" y="92.5"/>
            <close/>
        </path>
        <fillstroke/>
        <path>
            <move x="66" y="76"/>
            <arc large-arc-flag="0" rx="13.5" ry="3" sweep-flag="1" x="103" x-axis-rotation="0" y="76"/>
            <line x="103" y="83"/>
            <arc large-arc-flag="1" rx="13.5" ry="3" sweep-flag="1" x="66" x-axis-rotation="0" y="83"/>
            <close/>
        </path>
        <fillstroke/>
        <path>
            <move x="66" y="94.5"/>
            <arc large-arc-flag="1" rx="13.5" ry="3" sweep-flag="0" x="103" x-axis-rotation="0" y="94.5"/>
            <move x="66" y="85"/>
            <arc large-arc-flag="1" rx="13.5" ry="3" sweep-flag="0" x="103" x-axis-rotation="0" y="85"/>
            <move x="66" y="76"/>
            <arc large-arc-flag="1" rx="13.5" ry="3" sweep-flag="0" x="103" x-axis-rotation="0" y="76"/>
        </path>
        <stroke/>
    </foreground>
</shape>
<shape aspect="variable" h="100" name="Storage" strokewidth="2" w="100">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0"/>
        <constraint name="S" perimeter="0" x="0.5" y="1"/>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="E" perimeter="0" x="1" y="0.5"/>
        <constraint name="NW" perimeter="0" x="0" y="0.15"/>
        <constraint name="SW" perimeter="0" x="0" y="0.86"/>
        <constraint name="NE" perimeter="0" x="1" y="0.15"/>
        <constraint name="SE" perimeter="0" x="1" y="0.86"/>
    </connections>
    <foreground>
        <path>
            <move x="0" y="65"/>
            <arc large-arc-flag="0" rx="50" ry="15" sweep-flag="1" x="100" x-axis-rotation="0" y="65"/>
            <line x="100" y="85"/>
            <arc large-arc-flag="1" rx="50" ry="15" sweep-flag="1" x="0" x-axis-rotation="0" y="85"/>
            <close/>
        </path>
        <fillstroke/>
        <path>
            <move x="0" y="40"/>
            <arc large-arc-flag="0" rx="50" ry="15" sweep-flag="1" x="100" x-axis-rotation="0" y="40"/>
            <line x="100" y="60"/>
            <arc large-arc-flag="1" rx="50" ry="15" sweep-flag="1" x="0" x-axis-rotation="0" y="60"/>
            <close/>
        </path>
        <fillstroke/>
        <path>
            <move x="0" y="15"/>
            <arc large-arc-flag="0" rx="50" ry="15" sweep-flag="1" x="100" x-axis-rotation="0" y="15"/>
            <line x="100" y="35"/>
            <arc large-arc-flag="1" rx="50" ry="15" sweep-flag="1" x="0" x-axis-rotation="0" y="35"/>
            <close/>
        </path>
        <fillstroke/>
        <path>
            <move x="0" y="65"/>
            <arc large-arc-flag="1" rx="50" ry="15" sweep-flag="0" x="100" x-axis-rotation="0" y="65"/>
            <move x="0" y="40"/>
            <arc large-arc-flag="1" rx="50" ry="15" sweep-flag="0" x="100" x-axis-rotation="0" y="40"/>
            <move x="0" y="15"/>
            <arc large-arc-flag="1" rx="50" ry="15" sweep-flag="0" x="100" x-axis-rotation="0" y="15"/>
        </path>
        <stroke/>
    </foreground>
</shape>
<shape aspect="variable" h="100" name="Supercomputer" strokewidth="2" w="100">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0"/>
        <constraint name="S" perimeter="0" x="0.5" y="1"/>
        <constraint name="W1" perimeter="0" x="0.25" y="0.5"/>
        <constraint name="E1" perimeter="0" x="0.75" y="0.5"/>
        <constraint name="W2" perimeter="0" x="0" y="0.65"/>
        <constraint name="E2" perimeter="0" x="1" y="0.65"/>
        <constraint name="NW" perimeter="0" x="0.25" y="0.08"/>
        <constraint name="SW" perimeter="0" x="0" y="0.84"/>
        <constraint name="NE" perimeter="0" x="0.75" y="0.08"/>
        <constraint name="SE" perimeter="0" x="1" y="0.84"/>
    </connections>
    <foreground>
        <path>
            <move x="50" y="100"/>
            <line x="22" y="97"/>
            <line x="0" y="84"/>
            <line x="0" y="65"/>
            <line x="22" y="52"/>
            <line x="25" y="51.5"/>
            <line x="25" y="8"/>
            <line x="37" y="1"/>
            <line x="50" y="0"/>
            <line x="63" y="1"/>
            <line x="75" y="8"/>
            <line x="75" y="51.5"/>
            <line x="78" y="52"/>
            <line x="100" y="65"/>
            <line x="100" y="84"/>
            <line x="78" y="97"/>
            <close/>
        </path>
        <fillstroke/>
        <path>
            <move x="25" y="8"/>
            <line x="25" y="65"/>
            <line x="0" y="65"/>
            <move x="37" y="15"/>
            <line x="37" y="72"/>
            <line x="22" y="78"/>
            <line x="22" y="97"/>
            <move x="50" y="16"/>
            <line x="50" y="100"/>
            <move x="63" y="15"/>
            <line x="63" y="72"/>
            <line x="78" y="78"/>
            <line x="78" y="97"/>
            <move x="75" y="8"/>
            <line x="75" y="65"/>
            <line x="100" y="65"/>
            <move x="25" y="65"/>
            <line x="37" y="72"/>
            <line x="50" y="73"/>
            <line x="63" y="72"/>
            <line x="75" y="65"/>
            <move x="0" y="65"/>
            <line x="22" y="78"/>
            <line x="50" y="81"/>
            <line x="78" y="78"/>
            <line x="100" y="65"/>
            <move x="22" y="52"/>
            <line x="25" y="53.5"/>
            <move x="78" y="52"/>
            <line x="75" y="53.5"/>
            <move x="25" y="8"/>
            <line x="37" y="15"/>
            <line x="50" y="16"/>
            <line x="63" y="15"/>
            <line x="75" y="8"/>
        </path>
        <stroke/>
    </foreground>
</shape>
<shape aspect="variable" h="29" name="Switch" strokewidth="2" w="100">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0"/>
        <constraint name="S" perimeter="0" x="0.5" y="1"/>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="E" perimeter="0" x="1" y="0.5"/>
        <constraint name="NW" perimeter="0" x="0.05" y="0.05"/>
        <constraint name="SW" perimeter="0" x="0.1" y="1"/>
        <constraint name="NE" perimeter="0" x="0.95" y="0.05"/>
        <constraint name="SE" perimeter="0" x="0.9" y="1"/>
    </connections>
    <foreground>
        <roundrect arcsize="20" h="25" w="100" x="0" y="0"/>
        <fillstroke/>
        <rect h="4" w="80" x="10" y="25"/>
        <fillstroke/>
        <fillcolor color="#ffffff"/>
        <ellipse h="16" w="16" x="80" y="4.5"/>
        <fill/>
        <ellipse h="4" w="4" x="73" y="3"/>
        <fill/>
        <ellipse h="4" w="4" x="73" y="18"/>
        <fill/>
        <ellipse h="4" w="4" x="73" y="10.5"/>
        <fill/>
        <ellipse h="4" w="4" x="63.5" y="15"/>
        <fill/>
        <ellipse h="4" w="4" x="55" y="15"/>
        <fill/>
        <ellipse h="4" w="4" x="46.5" y="15"/>
        <fill/>
        <ellipse h="4" w="4" x="38" y="15"/>
        <fill/>
        <ellipse h="4" w="4" x="29.5" y="15"/>
        <fill/>
        <ellipse h="4" w="4" x="21" y="15"/>
        <fill/>
        <ellipse h="4" w="4" x="12.5" y="15"/>
        <fill/>
        <ellipse h="4" w="4" x="4" y="15"/>
        <fill/>
        <path>
            <move x="3" y="7"/>
            <line x="9" y="7"/>
            <line x="9" y="10"/>
            <line x="7" y="10"/>
            <line x="7" y="11"/>
            <line x="5" y="11"/>
            <line x="5" y="10"/>
            <line x="3" y="10"/>
            <close/>
            <move x="11.5" y="7"/>
            <line x="17.5" y="7"/>
            <line x="17.5" y="10"/>
            <line x="15.5" y="10"/>
            <line x="15.5" y="11"/>
            <line x="13.5" y="11"/>
            <line x="13.5" y="10"/>
            <line x="11.5" y="10"/>
            <close/>
            <move x="20" y="7"/>
            <line x="26" y="7"/>
            <line x="26" y="10"/>
            <line x="24" y="10"/>
            <line x="24" y="11"/>
            <line x="22" y="11"/>
            <line x="22" y="10"/>
            <line x="20" y="10"/>
            <close/>
            <move x="28.5" y="7"/>
            <line x="34.5" y="7"/>
            <line x="34.5" y="10"/>
            <line x="32.5" y="10"/>
            <line x="32.5" y="11"/>
            <line x="30.5" y="11"/>
            <line x="30.5" y="10"/>
            <line x="28.5" y="10"/>
            <close/>
            <move x="37" y="7"/>
            <line x="43" y="7"/>
            <line x="43" y="10"/>
            <line x="41" y="10"/>
            <line x="41" y="11"/>
            <line x="39" y="11"/>
            <line x="39" y="10"/>
            <line x="37" y="10"/>
            <close/>
            <move x="45.5" y="7"/>
            <line x="51.5" y="7"/>
            <line x="51.5" y="10"/>
            <line x="49.5" y="10"/>
            <line x="49.5" y="11"/>
            <line x="47.5" y="11"/>
            <line x="47.5" y="10"/>
            <line x="45.5" y="10"/>
            <close/>
            <move x="54" y="7"/>
            <line x="60" y="7"/>
            <line x="60" y="10"/>
            <line x="58" y="10"/>
            <line x="58" y="11"/>
            <line x="56" y="11"/>
            <line x="56" y="10"/>
            <line x="54" y="10"/>
            <close/>
            <move x="62.5" y="7"/>
            <line x="68.5" y="7"/>
            <line x="68.5" y="10"/>
            <line x="66.5" y="10"/>
            <line x="66.5" y="11"/>
            <line x="64.5" y="11"/>
            <line x="64.5" y="10"/>
            <line x="62.5" y="10"/>
            <close/>
        </path>
        <fill/>
        <strokewidth width="1"/>
        <path>
            <move x="82" y="9"/>
            <line x="84" y="7"/>
            <line x="86" y="9"/>
            <move x="82" y="16"/>
            <line x="84" y="18"/>
            <line x="86" y="16"/>
            <move x="90" y="9"/>
            <line x="92" y="7"/>
            <line x="94" y="9"/>
            <move x="90" y="16"/>
            <line x="92" y="18"/>
            <line x="94" y="16"/>
            <move x="92" y="18"/>
            <line x="92" y="15"/>
            <line x="84" y="10"/>
            <line x="84" y="7"/>
            <move x="84" y="18"/>
            <line x="84" y="15"/>
            <line x="92" y="10"/>
            <line x="92" y="7"/>
        </path>
        <stroke/>
    </foreground>
</shape>
<shape aspect="variable" h="68.5" name="Tablet" strokewidth="2" w="100">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0"/>
        <constraint name="S" perimeter="0" x="0.5" y="1"/>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="E" perimeter="0" x="1" y="0.5"/>
        <constraint name="NW" perimeter="0" x="0.01" y="0.02"/>
        <constraint name="SW" perimeter="0" x="0.01" y="0.98"/>
        <constraint name="NE" perimeter="0" x="0.99" y="0.02"/>
        <constraint name="SE" perimeter="0" x="0.99" y="0.98"/>
    </connections>
    <foreground>
        <path>
            <move x="5.5" y="0"/>
            <arc large-arc-flag="0" rx="5.5" ry="5.5" sweep-flag="0" x="0" x-axis-rotation="0" y="5.5"/>
            <line x="0" y="63"/>
            <arc large-arc-flag="0" rx="5.5" ry="5.5" sweep-flag="0" x="5.5" x-axis-rotation="0" y="68.5"/>
            <line x="94.5" y="68.5"/>
            <arc large-arc-flag="0" rx="5.5" ry="5.5" sweep-flag="0" x="100" x-axis-rotation="0" y="63"/>
            <line x="100" y="5.5"/>
            <arc large-arc-flag="0" rx="5.5" ry="5.5" sweep-flag="0" x="94.5" x-axis-rotation="0" y="0"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#ffffff"/>
        <path>
            <move x="7.8" y="7.7"/>
            <line x="92.3" y="7.7"/>
            <line x="92.3" y="60.5"/>
            <line x="7.8" y="60.5"/>
            <close/>
        </path>
        <fill/>
        <ellipse h="1.16" w="1.16" x="49.42" y="3.42"/>
        <fill/>
    </foreground>
</shape>
<shape aspect="variable" h="100" name="Tape Storage" strokewidth="2" w="103">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0"/>
        <constraint name="S" perimeter="0" x="0.5" y="1"/>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="E" perimeter="0" x="0.87" y="0.5"/>
        <constraint name="NW" perimeter="0" x="0" y="0.15"/>
        <constraint name="SW" perimeter="0" x="0" y="0.99"/>
        <constraint name="NE" perimeter="0" x="0.87" y="0.15"/>
        <constraint name="SE" perimeter="0" x="0.97" y="0.94"/>
    </connections>
    <foreground>
        <save/>
        <roundrect arcsize="13.33" h="15" w="90" x="0" y="85"/>
        <fillstroke/>
        <roundrect arcsize="13.33" h="15" w="90" x="0" y="67.5"/>
        <fillstroke/>
        <roundrect arcsize="13.33" h="15" w="90" x="0" y="50"/>
        <fillstroke/>
        <roundrect arcsize="13.33" h="15" w="90" x="0" y="32.5"/>
        <fillstroke/>
        <roundrect arcsize="13.33" h="15" w="90" x="0" y="15"/>
        <fillstroke/>
        <path>
            <move x="4" y="13"/>
            <line x="25" y="0"/>
            <line x="65" y="0"/>
            <line x="86" y="13"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#ffffff"/>
        <ellipse h="8" w="8" x="78" y="88.5"/>
        <fill/>
        <ellipse h="8" w="8" x="77.67" y="71"/>
        <fill/>
        <ellipse h="8" w="8" x="78" y="53.5"/>
        <fill/>
        <ellipse h="8" w="8" x="78" y="36"/>
        <fill/>
        <ellipse h="8" w="8" x="78" y="18.5"/>
        <fill/>
        <path>
            <move x="5" y="98"/>
            <line x="10" y="87"/>
            <line x="15" y="87"/>
            <line x="10" y="98"/>
            <close/>
            <move x="15" y="98"/>
            <line x="20" y="87"/>
            <line x="25" y="87"/>
            <line x="20" y="98"/>
            <close/>
            <move x="25" y="98"/>
            <line x="30" y="87"/>
            <line x="35" y="87"/>
            <line x="30" y="98"/>
            <close/>
            <move x="35" y="98"/>
            <line x="40" y="87"/>
            <line x="45" y="87"/>
            <line x="40" y="98"/>
            <close/>
            <move x="45" y="98"/>
            <line x="50" y="87"/>
            <line x="55" y="87"/>
            <line x="50" y="98"/>
            <close/>
            <move x="55" y="98"/>
            <line x="60" y="87"/>
            <line x="65" y="87"/>
            <line x="60" y="98"/>
            <close/>
            <move x="5" y="80.5"/>
            <line x="10" y="69.5"/>
            <line x="15" y="69.5"/>
            <line x="10" y="80.5"/>
            <close/>
            <move x="15" y="80.5"/>
            <line x="20" y="69.5"/>
            <line x="25" y="69.5"/>
            <line x="20" y="80.5"/>
            <close/>
            <move x="25" y="80.5"/>
            <line x="30" y="69.5"/>
            <line x="35" y="69.5"/>
            <line x="30" y="80.5"/>
            <close/>
            <move x="35" y="80.5"/>
            <line x="40" y="69.5"/>
            <line x="45" y="69.5"/>
            <line x="40" y="80.5"/>
            <close/>
            <move x="45" y="80.5"/>
            <line x="50" y="69.5"/>
            <line x="55" y="69.5"/>
            <line x="50" y="80.5"/>
            <close/>
            <move x="55" y="80.5"/>
            <line x="60" y="69.5"/>
            <line x="65" y="69.5"/>
            <line x="60" y="80.5"/>
            <close/>
            <move x="5" y="63"/>
            <line x="10" y="52"/>
            <line x="15" y="52"/>
            <line x="10" y="63"/>
            <close/>
            <move x="15" y="63"/>
            <line x="20" y="52"/>
            <line x="25" y="52"/>
            <line x="20" y="63"/>
            <close/>
            <move x="25" y="63"/>
            <line x="29.5" y="52"/>
            <line x="34.5" y="52"/>
            <line x="29.5" y="63"/>
            <close/>
            <move x="35" y="63"/>
            <line x="40" y="52"/>
            <line x="45" y="52"/>
            <line x="40" y="63"/>
            <close/>
            <move x="45" y="63"/>
            <line x="50" y="52"/>
            <line x="55" y="52"/>
            <line x="50" y="63"/>
            <close/>
            <move x="55" y="63"/>
            <line x="60" y="52"/>
            <line x="65" y="52"/>
            <line x="60" y="63"/>
            <close/>
            <move x="5" y="45.5"/>
            <line x="10" y="34.5"/>
            <line x="15" y="34.5"/>
            <line x="10" y="45.5"/>
            <close/>
            <move x="15" y="45.5"/>
            <line x="20" y="34.5"/>
            <line x="25" y="34.5"/>
            <line x="20" y="45.5"/>
            <close/>
            <move x="25" y="45.5"/>
            <line x="30" y="34.5"/>
            <line x="35" y="34.5"/>
            <line x="30" y="45.5"/>
            <close/>
            <move x="35" y="45.5"/>
            <line x="40" y="34.5"/>
            <line x="45" y="34.5"/>
            <line x="40" y="45.5"/>
            <close/>
            <move x="45" y="45.5"/>
            <line x="50" y="34.5"/>
            <line x="55" y="34.5"/>
            <line x="50" y="45.5"/>
            <close/>
            <move x="55" y="45.5"/>
            <line x="60" y="34.5"/>
            <line x="65" y="34.5"/>
            <line x="60" y="45.5"/>
            <close/>
            <move x="5" y="28"/>
            <line x="10" y="17"/>
            <line x="15" y="17"/>
            <line x="10" y="28"/>
            <close/>
            <move x="15" y="28"/>
            <line x="20" y="17"/>
            <line x="25" y="17"/>
            <line x="20" y="28"/>
            <close/>
            <move x="25" y="28"/>
            <line x="30" y="17"/>
            <line x="35" y="17"/>
            <line x="30" y="28"/>
            <close/>
            <move x="35" y="28"/>
            <line x="40" y="17"/>
            <line x="45" y="17"/>
            <line x="40" y="28"/>
            <close/>
            <move x="45" y="28"/>
            <line x="50" y="17"/>
            <line x="55" y="17"/>
            <line x="50" y="28"/>
            <close/>
            <move x="55" y="28"/>
            <line x="60" y="17"/>
            <line x="65" y="17"/>
            <line x="60" y="28"/>
            <close/>
        </path>
        <fill/>
        <restore/>
        <rect/>
        <stroke/>
        <ellipse h="16" w="16" x="65" y="79"/>
        <fillstroke/>
        <ellipse h="16" w="16" x="87" y="79"/>
        <fillstroke/>
        <path>
            <move x="73" y="95"/>
            <line x="95" y="95"/>
        </path>
        <stroke/>
    </foreground>
</shape>
<shape aspect="variable" h="65" name="Terminal" strokewidth="2" w="80">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0"/>
        <constraint name="S" perimeter="0" x="0.5" y="1"/>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="E" perimeter="0" x="1" y="0.5"/>
        <constraint name="NW" perimeter="0" x="0" y="0"/>
        <constraint name="SW" perimeter="0" x="0.07" y="1"/>
        <constraint name="NE" perimeter="0" x="1" y="0"/>
        <constraint name="SE" perimeter="0" x="0.93" y="1"/>
    </connections>
    <foreground>
        <rect h="45" w="80" x="0" y="0"/>
        <fillstroke/>
        <rect h="10" w="20" x="30" y="45"/>
        <fillstroke/>
        <linejoin join="round"/>
        <path>
            <move x="15" y="54"/>
            <line x="65" y="54"/>
            <line x="75" y="65"/>
            <line x="5" y="65"/>
            <close/>
            <move x="29" y="62.5"/>
            <line x="47" y="62.5"/>
            <move x="16" y="59.5"/>
            <line x="55" y="59.5"/>
            <move x="18" y="56.5"/>
            <line x="54" y="56.5"/>
            <move x="57" y="56.5"/>
            <line x="64" y="56.5"/>
            <move x="59" y="59.5"/>
            <line x="66" y="59.5"/>
            <move x="61" y="62.5"/>
            <line x="68" y="62.5"/>
        </path>
        <fillstroke/>
        <fillcolor color="#ffffff"/>
        <rect h="39" w="73" x="3" y="3"/>
        <fill/>
    </foreground>
</shape>
<shape aspect="variable" h="100" name="Unsecure" strokewidth="2" w="80">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0"/>
        <constraint name="S" perimeter="0" x="0.5" y="1"/>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="E" perimeter="0" x="1" y="0.5"/>
        <constraint name="SW" perimeter="0" x="0" y="1"/>
        <constraint name="SE" perimeter="0" x="1" y="1"/>
    </connections>
    <foreground>
        <path>
            <move x="0" y="100"/>
            <line x="0" y="45"/>
            <line x="10" y="45"/>
            <line x="10" y="30"/>
            <arc large-arc-flag="0" rx="30" ry="30" sweep-flag="1" x="40" x-axis-rotation="0" y="0"/>
            <arc large-arc-flag="0" rx="30" ry="30" sweep-flag="1" x="70" x-axis-rotation="0" y="30"/>
            <line x="60" y="30"/>
            <arc large-arc-flag="0" rx="20" ry="20" sweep-flag="0" x="40" x-axis-rotation="0" y="10"/>
            <arc large-arc-flag="0" rx="20" ry="20" sweep-flag="0" x="20" x-axis-rotation="0" y="30"/>
            <line x="20" y="45"/>
            <line x="80" y="45"/>
            <line x="80" y="100"/>
            <close/>
        </path>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="100" name="UPS Enterprise" strokewidth="2" w="100">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0"/>
        <constraint name="S" perimeter="0" x="0.5" y="1"/>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="E" perimeter="0" x="1" y="0.5"/>
        <constraint name="NW" perimeter="0" x="0" y="0"/>
        <constraint name="SW" perimeter="0" x="0" y="1"/>
        <constraint name="NE" perimeter="0" x="1" y="0"/>
        <constraint name="SE" perimeter="0" x="1" y="1"/>
    </connections>
    <foreground>
        <rect h="100" w="100" x="0" y="0"/>
        <fillstroke/>
        <path>
            <move x="80" y="0"/>
            <line x="80" y="95"/>
            <move x="60" y="0"/>
            <line x="60" y="95"/>
            <move x="40" y="0"/>
            <line x="40" y="95"/>
            <move x="0" y="95"/>
            <line x="100" y="95"/>
        </path>
        <fillstroke/>
        <fillcolor color="#ffffff"/>
        <rect h="14" w="20" x="10" y="12"/>
        <fill/>
        <rect h="3" w="12" x="4" y="4"/>
        <fill/>
        <rect h="16" w="3" x="34" y="40"/>
        <fill/>
        <rect h="2" w="32" x="4" y="70"/>
        <fill/>
        <rect h="2" w="32" x="4" y="75"/>
        <fill/>
        <rect h="2" w="32" x="4" y="80"/>
        <fill/>
        <rect h="2" w="32" x="4" y="85"/>
        <fill/>
        <rect h="2" w="32" x="4" y="90"/>
        <fill/>
    </foreground>
</shape>
<shape aspect="variable" h="100" name="UPS Small" strokewidth="2" w="70">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0"/>
        <constraint name="S" perimeter="0" x="0.5" y="1"/>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="E" perimeter="0" x="1" y="0.5"/>
        <constraint name="NW" perimeter="0" x="0" y="0"/>
        <constraint name="SW" perimeter="0" x="0" y="1"/>
        <constraint name="NE" perimeter="0" x="1" y="0"/>
        <constraint name="SE" perimeter="0" x="1" y="1"/>
    </connections>
    <foreground>
        <rect h="100" w="70" x="0" y="0"/>
        <fillstroke/>
        <path>
            <move x="5" y="0"/>
            <line x="5" y="100"/>
            <move x="65" y="0"/>
            <line x="65" y="100"/>
            <move x="65" y="15"/>
            <line x="5" y="15"/>
        </path>
        <stroke/>
        <strokecolor color="#ffffff"/>
        <strokewidth width="4"/>
        <path>
            <move x="27" y="30"/>
            <arc large-arc-flag="1" rx="12" ry="12" sweep-flag="0" x="43" x-axis-rotation="0" y="30"/>
            <move x="35" y="25"/>
            <line x="35" y="40"/>
        </path>
        <stroke/>
        <fillcolor color="#ffffff"/>
        <path>
            <move x="12" y="63"/>
            <line x="16" y="63"/>
            <line x="16" y="80"/>
            <arc large-arc-flag="1" rx="3" ry="3" sweep-flag="0" x="22" x-axis-rotation="0" y="80"/>
            <line x="22" y="63"/>
            <line x="26" y="63"/>
            <line x="26" y="80"/>
            <arc large-arc-flag="0" rx="7" ry="7" sweep-flag="1" x="12" x-axis-rotation="0" y="80"/>
            <close/>
            <move x="28" y="63"/>
            <line x="35" y="63"/>
            <arc large-arc-flag="0" rx="7" ry="7" sweep-flag="1" x="35" x-axis-rotation="0" y="77"/>
            <line x="32" y="77"/>
            <line x="32" y="87"/>
            <line x="28" y="87"/>
            <close/>
            <move x="32" y="73"/>
            <line x="35" y="73"/>
            <arc large-arc-flag="1" rx="3" ry="3" sweep-flag="0" x="35" x-axis-rotation="0" y="67"/>
            <line x="32" y="67"/>
            <close/>
            <move x="43" y="87"/>
            <line x="50" y="87"/>
            <arc large-arc-flag="0" rx="7" ry="7" sweep-flag="0" x="50" x-axis-rotation="0" y="73"/>
            <arc large-arc-flag="0" rx="3" ry="3" sweep-flag="1" x="50" x-axis-rotation="0" y="67"/>
            <line x="56" y="67"/>
            <line x="56" y="63"/>
            <line x="50" y="63"/>
            <arc large-arc-flag="0" rx="7" ry="7" sweep-flag="0" x="50" x-axis-rotation="0" y="77"/>
            <arc large-arc-flag="0" rx="3" ry="3" sweep-flag="1" x="50" x-axis-rotation="0" y="83"/>
            <line x="43" y="83"/>
            <close/>
        </path>
        <fill/>
    </foreground>
</shape>
<shape aspect="variable" h="98.51" name="USB Stick" strokewidth="2" w="42.5">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0"/>
        <constraint name="S" perimeter="0" x="0.5" y="1"/>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="E" perimeter="0" x="1" y="0.5"/>
        <constraint name="NW" perimeter="0" x="0.15" y="0.01"/>
        <constraint name="SW" perimeter="0" x="0.08" y="0.96"/>
        <constraint name="NE" perimeter="0" x="0.84" y="0.01"/>
        <constraint name="SE" perimeter="0" x="0.92" y="0.96"/>
    </connections>
    <foreground>
        <path>
            <move x="42.5" y="32.5"/>
            <line x="42.5" y="86"/>
            <arc large-arc-flag="0" rx="13" ry="13" sweep-flag="1" x="29.5" x-axis-rotation="0" y="98.5"/>
            <line x="11.5" y="98.5"/>
            <arc large-arc-flag="0" rx="13" ry="13" sweep-flag="1" x="0" x-axis-rotation="0" y="86"/>
            <line x="0" y="32.5"/>
            <close/>
            <move x="36.5" y="51"/>
            <line x="27" y="51"/>
            <line x="27" y="60"/>
            <line x="29.5" y="60"/>
            <arc large-arc-flag="0" rx="3.5" ry="3.5" sweep-flag="1" x="28" x-axis-rotation="0" y="63"/>
            <line x="23.5" y="65.5"/>
            <line x="23.5" y="47.5"/>
            <line x="27.5" y="47.5"/>
            <line x="21" y="38"/>
            <line x="14" y="47.5"/>
            <line x="18.5" y="47.5"/>
            <line x="18.5" y="71"/>
            <line x="14.5" y="68.5"/>
            <arc large-arc-flag="0" rx="4" ry="4" sweep-flag="1" x="12.5" x-axis-rotation="0" y="65"/>
            <line x="12.5" y="61.5"/>
            <arc large-arc-flag="1" rx="4.4" ry="4.4" sweep-flag="0" x="8" x-axis-rotation="0" y="61.5"/>
            <line x="8" y="66.5"/>
            <arc large-arc-flag="0" rx="8" ry="8" sweep-flag="0" x="11.5" x-axis-rotation="0" y="72.5"/>
            <line x="18.5" y="77"/>
            <line x="18.5" y="79.5"/>
            <arc large-arc-flag="1" rx="6.7" ry="6.7" sweep-flag="0" x="23.5" x-axis-rotation="0" y="79.5"/>
            <line x="23.5" y="70.5"/>
            <line x="29" y="68"/>
            <arc large-arc-flag="0" rx="8" ry="8" sweep-flag="0" x="34" x-axis-rotation="0" y="62.5"/>
            <line x="34" y="60"/>
            <line x="36.5" y="60"/>
            <close/>
            <move x="9" y="29.5"/>
            <arc large-arc-flag="0" rx="3.5" ry="3.5" sweep-flag="1" x="6" x-axis-rotation="0" y="26"/>
            <line x="6" y="2.5"/>
            <arc large-arc-flag="0" rx="3.5" ry="3.5" sweep-flag="1" x="9" x-axis-rotation="0" y="0"/>
            <line x="33" y="0"/>
            <arc large-arc-flag="0" rx="3.5" ry="3.5" sweep-flag="1" x="36" x-axis-rotation="0" y="3.5"/>
            <line x="36" y="26.5"/>
            <arc large-arc-flag="0" rx="3.5" ry="3.5" sweep-flag="1" x="33" x-axis-rotation="0" y="29.5"/>
            <close/>
            <move x="16.5" y="23.5"/>
            <line x="16.5" y="17"/>
            <line x="10.5" y="17"/>
            <line x="10.5" y="23.5"/>
            <close/>
            <move x="25.5" y="23.5"/>
            <line x="31.5" y="23.5"/>
            <line x="31.5" y="17"/>
            <line x="25.5" y="17"/>
            <close/>
        </path>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="99.25" name="Users" strokewidth="2" w="88.5">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0.1"/>
        <constraint name="S" perimeter="0" x="0.5" y="1"/>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="E" perimeter="0" x="1" y="0.5"/>
        <constraint name="NW" perimeter="0" x="0.09" y="0.05"/>
        <constraint name="SW" perimeter="0" x="0.11" y="0.88"/>
        <constraint name="NE" perimeter="0" x="0.91" y="0.05"/>
        <constraint name="SE" perimeter="0" x="0.89" y="0.88"/>
    </connections>
    <foreground>
        <path>
            <move x="17.75" y="0.25"/>
            <arc large-arc-flag="0" rx="13.31" ry="13.31" sweep-flag="1" x="31.06" x-axis-rotation="0" y="13.56"/>
            <arc large-arc-flag="0" rx="13.31" ry="13.31" sweep-flag="1" x="17.75" x-axis-rotation="0" y="26.88"/>
            <arc large-arc-flag="0" rx="13.31" ry="13.31" sweep-flag="1" x="4.44" x-axis-rotation="0" y="13.56"/>
            <arc large-arc-flag="0" rx="14.8" ry="14.8" sweep-flag="1" x="17.75" x-axis-rotation="0" y="0.25"/>
            <close/>
            <move x="26.62" y="31.31"/>
            <arc large-arc-flag="0" rx="8.88" ry="8.88" sweep-flag="1" x="35.5" x-axis-rotation="0" y="40.19"/>
            <line x="35.5" y="53.5"/>
            <arc large-arc-flag="0" rx="8.88" ry="8.88" sweep-flag="1" x="26.62" x-axis-rotation="0" y="62.38"/>
            <line x="26.62" y="84.56"/>
            <arc large-arc-flag="0" rx="4.44" ry="4.44" sweep-flag="1" x="22.19" x-axis-rotation="0" y="89"/>
            <line x="13.31" y="89"/>
            <arc large-arc-flag="0" rx="4.44" ry="4.44" sweep-flag="1" x="8.88" x-axis-rotation="0" y="84.56"/>
            <line x="8.88" y="62.38"/>
            <arc large-arc-flag="0" rx="8.88" ry="8.88" sweep-flag="1" x="0" x-axis-rotation="0" y="53.5"/>
            <line x="0" y="40.19"/>
            <arc large-arc-flag="0" rx="9.64" ry="9.64" sweep-flag="1" x="8.88" x-axis-rotation="0" y="31.31"/>
            <close/>
            <move x="70.75" y="0"/>
            <arc large-arc-flag="0" rx="13.31" ry="13.31" sweep-flag="1" x="84.06" x-axis-rotation="0" y="13.31"/>
            <arc large-arc-flag="0" rx="13.31" ry="13.31" sweep-flag="1" x="70.75" x-axis-rotation="0" y="26.62"/>
            <arc large-arc-flag="0" rx="13.31" ry="13.31" sweep-flag="1" x="57.44" x-axis-rotation="0" y="13.31"/>
            <arc large-arc-flag="0" rx="14.8" ry="14.8" sweep-flag="1" x="70.75" x-axis-rotation="0" y="0"/>
            <close/>
            <move x="79.62" y="31.06"/>
            <arc large-arc-flag="0" rx="8.88" ry="8.88" sweep-flag="1" x="88.5" x-axis-rotation="0" y="39.94"/>
            <line x="88.5" y="53.25"/>
            <arc large-arc-flag="0" rx="8.88" ry="8.88" sweep-flag="1" x="79.62" x-axis-rotation="0" y="62.12"/>
            <line x="79.62" y="84.31"/>
            <arc large-arc-flag="0" rx="4.44" ry="4.44" sweep-flag="1" x="75.19" x-axis-rotation="0" y="88.75"/>
            <line x="66.31" y="88.75"/>
            <arc large-arc-flag="0" rx="4.44" ry="4.44" sweep-flag="1" x="61.88" x-axis-rotation="0" y="84.31"/>
            <line x="61.88" y="62.12"/>
            <arc large-arc-flag="0" rx="8.88" ry="8.88" sweep-flag="1" x="53" x-axis-rotation="0" y="53.25"/>
            <line x="53" y="39.94"/>
            <arc large-arc-flag="0" rx="9.64" ry="9.64" sweep-flag="1" x="61.88" x-axis-rotation="0" y="31.06"/>
            <close/>
        </path>
        <fillstroke/>
        <path>
            <move x="44.25" y="10.5"/>
            <arc large-arc-flag="0" rx="13.31" ry="13.31" sweep-flag="1" x="57.56" x-axis-rotation="0" y="23.81"/>
            <arc large-arc-flag="0" rx="13.31" ry="13.31" sweep-flag="1" x="44.25" x-axis-rotation="0" y="37.12"/>
            <arc large-arc-flag="0" rx="13.31" ry="13.31" sweep-flag="1" x="30.94" x-axis-rotation="0" y="23.81"/>
            <arc large-arc-flag="0" rx="14.8" ry="14.8" sweep-flag="1" x="44.25" x-axis-rotation="0" y="10.5"/>
            <close/>
            <move x="53.12" y="41.56"/>
            <arc large-arc-flag="0" rx="8.88" ry="8.88" sweep-flag="1" x="62" x-axis-rotation="0" y="50.44"/>
            <line x="62" y="63.75"/>
            <arc large-arc-flag="0" rx="8.88" ry="8.88" sweep-flag="1" x="53.12" x-axis-rotation="0" y="72.62"/>
            <line x="53.12" y="94.81"/>
            <arc large-arc-flag="0" rx="4.44" ry="4.44" sweep-flag="1" x="48.69" x-axis-rotation="0" y="99.25"/>
            <line x="39.81" y="99.25"/>
            <arc large-arc-flag="0" rx="4.44" ry="4.44" sweep-flag="1" x="35.38" x-axis-rotation="0" y="94.81"/>
            <line x="35.38" y="72.62"/>
            <arc large-arc-flag="0" rx="8.88" ry="8.88" sweep-flag="1" x="26.5" x-axis-rotation="0" y="63.75"/>
            <line x="26.5" y="50.44"/>
            <arc large-arc-flag="0" rx="9.64" ry="9.64" sweep-flag="1" x="35.38" x-axis-rotation="0" y="41.56"/>
            <close/>
        </path>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="100" name="User Female" strokewidth="2" w="41.05">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0"/>
        <constraint name="S" perimeter="0" x="0.5" y="1"/>
        <constraint name="W" perimeter="0" x="0.01" y="0.5"/>
        <constraint name="E" perimeter="0" x="0.99" y="0.5"/>
        <constraint name="NW" perimeter="0" x="0.18" y="0.07"/>
        <constraint name="SW" perimeter="0" x="0.02" y="0.8"/>
        <constraint name="NE" perimeter="0" x="0.82" y="0.07"/>
        <constraint name="SE" perimeter="0" x="0.98" y="0.8"/>
    </connections>
    <foreground>
        <path>
            <move x="20.53" y="0"/>
            <arc large-arc-flag="0" rx="15" ry="15" sweep-flag="1" x="35.53" x-axis-rotation="0" y="15"/>
            <arc large-arc-flag="0" rx="30" ry="30" sweep-flag="0" x="35.53" x-axis-rotation="0" y="30"/>
            <arc large-arc-flag="0" rx="40" ry="40" sweep-flag="1" x="5.53" x-axis-rotation="0" y="30"/>
            <arc large-arc-flag="0" rx="30" ry="30" sweep-flag="0" x="5.53" x-axis-rotation="0" y="15"/>
            <arc large-arc-flag="0" rx="15" ry="15" sweep-flag="1" x="20.53" x-axis-rotation="0" y="0"/>
            <close/>
            <move x="30.53" y="35"/>
            <arc large-arc-flag="0" rx="10" ry="10" sweep-flag="1" x="40.53" x-axis-rotation="0" y="45"/>
            <line x="40.53" y="60"/>
            <arc large-arc-flag="0" rx="10" ry="10" sweep-flag="1" x="35.53" x-axis-rotation="0" y="70"/>
            <line x="40.53" y="80"/>
            <line x="30.53" y="80"/>
            <line x="30.53" y="95"/>
            <arc large-arc-flag="0" rx="5" ry="5" sweep-flag="1" x="25.53" x-axis-rotation="0" y="100"/>
            <line x="15.53" y="100"/>
            <arc large-arc-flag="0" rx="5" ry="5" sweep-flag="1" x="10.53" x-axis-rotation="0" y="95"/>
            <line x="10.53" y="80"/>
            <line x="0.53" y="80"/>
            <line x="5.53" y="70"/>
            <arc large-arc-flag="0" rx="10" ry="10" sweep-flag="1" x="0.53" x-axis-rotation="0" y="60"/>
            <line x="0.53" y="45"/>
            <arc large-arc-flag="0" rx="10" ry="10" sweep-flag="1" x="10.53" x-axis-rotation="0" y="35"/>
            <line x="12.53" y="35"/>
            <arc large-arc-flag="0" rx="8" ry="8" sweep-flag="0" x="28.53" x-axis-rotation="0" y="35"/>
            <close/>
        </path>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="100" name="User Male" strokewidth="2" w="40">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0"/>
        <constraint name="S" perimeter="0" x="0.5" y="1"/>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="E" perimeter="0" x="1" y="0.5"/>
        <constraint name="NW" perimeter="0" x="0.18" y="0.07"/>
        <constraint name="NE" perimeter="0" x="0.82" y="0.07"/>
    </connections>
    <foreground>
        <path>
            <move x="20" y="0"/>
            <arc large-arc-flag="0" rx="15" ry="15" sweep-flag="1" x="35" x-axis-rotation="0" y="15"/>
            <arc large-arc-flag="0" rx="15" ry="15" sweep-flag="1" x="20" x-axis-rotation="0" y="30"/>
            <arc large-arc-flag="0" rx="15" ry="15" sweep-flag="1" x="5" x-axis-rotation="0" y="15"/>
            <arc large-arc-flag="0" rx="15" ry="15" sweep-flag="1" x="20" x-axis-rotation="0" y="0"/>
            <close/>
            <move x="30" y="35"/>
            <arc large-arc-flag="0" rx="10" ry="10" sweep-flag="1" x="40" x-axis-rotation="0" y="45"/>
            <line x="40" y="60"/>
            <arc large-arc-flag="0" rx="10" ry="10" sweep-flag="1" x="30" x-axis-rotation="0" y="70"/>
            <line x="30" y="95"/>
            <arc large-arc-flag="0" rx="5" ry="5" sweep-flag="1" x="25" x-axis-rotation="0" y="100"/>
            <line x="15" y="100"/>
            <arc large-arc-flag="0" rx="5" ry="5" sweep-flag="1" x="10" x-axis-rotation="0" y="95"/>
            <line x="10" y="70"/>
            <arc large-arc-flag="0" rx="10" ry="10" sweep-flag="1" x="0" x-axis-rotation="0" y="60"/>
            <line x="0" y="45"/>
            <arc large-arc-flag="0" rx="10" ry="10" sweep-flag="1" x="10" x-axis-rotation="0" y="35"/>
            <close/>
        </path>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="34" name="Video Projector" strokewidth="2" w="100">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0.15"/>
        <constraint name="S" perimeter="0" x="0.5" y="0.89"/>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="E" perimeter="0" x="1" y="0.5"/>
        <constraint name="NW" perimeter="0" x="0.03" y="0.18"/>
        <constraint name="SW" perimeter="0" x="0.07" y="1"/>
        <constraint name="NE" perimeter="0" x="0.97" y="0.18"/>
        <constraint name="SE" perimeter="0" x="0.93" y="1"/>
    </connections>
    <foreground>
        <roundrect arcsize="16" h="25" w="100" x="0" y="5"/>
        <fillstroke/>
        <ellipse h="32" w="32" x="54" y="0"/>
        <fillstroke/>
        <rect h="4" w="8" x="7" y="30"/>
        <fillstroke/>
        <rect h="4" w="8" x="85" y="30"/>
        <fillstroke/>
        <fillcolor color="#ffffff"/>
        <ellipse h="4" w="4" x="90" y="16"/>
        <fill/>
        <rect h="3" w="40" x="8" y="8.5"/>
        <fill/>
        <ellipse h="4" w="4" x="90" y="9"/>
        <fill/>
        <rect h="3" w="40" x="8" y="13.5"/>
        <fill/>
        <rect h="3" w="40" x="8" y="18.5"/>
        <fill/>
        <rect h="3" w="40" x="8" y="23.5"/>
        <fill/>
        <ellipse h="26" w="26" x="57" y="3"/>
        <fill/>
    </foreground>
</shape>
<shape aspect="variable" h="100" name="Video Projector Screen" strokewidth="2" w="80">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0"/>
        <constraint name="S" perimeter="0" x="0.5" y="1"/>
        <constraint name="W" perimeter="0" x="0" y="0.56"/>
        <constraint name="E" perimeter="0" x="1" y="0.56"/>
        <constraint name="NW" perimeter="0" x="0" y="0.08"/>
        <constraint name="SW" perimeter="0" x="0.25" y="1"/>
        <constraint name="NE" perimeter="0" x="1" y="0.08"/>
        <constraint name="SE" perimeter="0" x="0.75" y="1"/>
    </connections>
    <foreground>
        <rect h="50" w="76" x="2" y="8"/>
        <fillstroke/>
        <rect h="3" w="80" x="0" y="8"/>
        <fillstroke/>
        <rect h="3" w="80" x="0" y="55"/>
        <fillstroke/>
        <path>
            <move x="40" y="0"/>
            <line x="40" y="8"/>
            <move x="40" y="2"/>
            <line x="25" y="8"/>
            <move x="40" y="2"/>
            <line x="55" y="8"/>
            <move x="40" y="58"/>
            <line x="40" y="100"/>
            <move x="40" y="85"/>
            <line x="20" y="100"/>
            <move x="40" y="85"/>
            <line x="60" y="100"/>
        </path>
        <stroke/>
    </foreground>
</shape>
<shape aspect="variable" h="86" name="Virtual PC" strokewidth="2" w="116">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0"/>
        <constraint name="S" perimeter="0" x="0.5" y="1"/>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="E" perimeter="0" x="1" y="0.5"/>
        <constraint name="NW" perimeter="0" x="0.01" y="0.01"/>
        <constraint name="SW" perimeter="0" x="0.01" y="0.99"/>
        <constraint name="NE" perimeter="0" x="0.99" y="0.01"/>
        <constraint name="SE" perimeter="0" x="0.99" y="0.99"/>
    </connections>
    <foreground>
        <linejoin join="round"/>
        <roundrect arcsize="6.67" h="60" w="30" x="8" y="8"/>
        <fillstroke/>
        <path>
            <move x="8" y="18"/>
            <line x="38" y="18"/>
            <move x="8" y="38"/>
            <line x="38" y="38"/>
        </path>
        <stroke/>
        <rect h="45" w="80" x="28" y="13"/>
        <fillstroke/>
        <rect h="10" w="20" x="58" y="58"/>
        <fillstroke/>
        <path>
            <move x="43" y="67"/>
            <line x="93" y="67"/>
            <line x="103" y="78"/>
            <line x="33" y="78"/>
            <close/>
        </path>
        <fillstroke/>
        <path>
            <move x="57" y="75.5"/>
            <line x="75" y="75.5"/>
            <move x="44" y="72.5"/>
            <line x="83" y="72.5"/>
            <move x="46" y="69.5"/>
            <line x="82" y="69.5"/>
            <move x="85" y="69.5"/>
            <line x="92" y="69.5"/>
            <move x="87" y="72.5"/>
            <line x="94" y="72.5"/>
            <move x="89" y="75.5"/>
            <line x="96" y="75.5"/>
        </path>
        <fillstroke/>
        <fillcolor color="#ffffff"/>
        <rect h="39" w="73" x="31" y="16"/>
        <fill/>
        <ellipse h="4" w="4" x="11" y="41"/>
        <fill/>
        <dashpattern pattern="5 5"/>
        <dashed dashed="1"/>
        <roundrect arcsize="5.81" h="86" w="116" x="0" y="0"/>
        <stroke/>
    </foreground>
</shape>
<shape aspect="variable" h="120" name="Virtual Server" strokewidth="2" w="110">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0"/>
        <constraint name="S" perimeter="0" x="0.5" y="1"/>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="E" perimeter="0" x="1" y="0.5"/>
        <constraint name="NW" perimeter="0" x="0.01" y="0.01"/>
        <constraint name="SW" perimeter="0" x="0.01" y="0.99"/>
        <constraint name="NE" perimeter="0" x="0.99" y="0.01"/>
        <constraint name="SE" perimeter="0" x="0.99" y="0.99"/>
    </connections>
    <foreground>
        <roundrect arcsize="13.33" h="15" w="90" x="10" y="95"/>
        <fillstroke/>
        <roundrect arcsize="13.33" h="15" w="90" x="10" y="77.5"/>
        <fillstroke/>
        <roundrect arcsize="13.33" h="15" w="90" x="10" y="60"/>
        <fillstroke/>
        <roundrect arcsize="13.33" h="15" w="90" x="10" y="42.5"/>
        <fillstroke/>
        <roundrect arcsize="13.33" h="15" w="90" x="10" y="25"/>
        <fillstroke/>
        <path>
            <move x="14" y="23"/>
            <line x="35" y="10"/>
            <line x="75" y="10"/>
            <line x="96" y="23"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#ffffff"/>
        <ellipse h="8" w="8" x="88" y="98.5"/>
        <fill/>
        <ellipse h="8" w="8" x="88" y="81"/>
        <fill/>
        <ellipse h="8" w="8" x="88" y="63.5"/>
        <fill/>
        <ellipse h="8" w="8" x="88" y="46"/>
        <fill/>
        <ellipse h="8" w="8" x="88" y="28.5"/>
        <fill/>
        <path>
            <move x="15" y="108"/>
            <line x="20" y="97"/>
            <line x="25" y="97"/>
            <line x="20" y="108"/>
            <close/>
            <move x="25" y="108"/>
            <line x="30" y="97"/>
            <line x="35" y="97"/>
            <line x="30" y="108"/>
            <close/>
            <move x="35" y="108"/>
            <line x="40" y="97"/>
            <line x="45" y="97"/>
            <line x="40" y="108"/>
            <close/>
            <move x="45" y="108"/>
            <line x="50" y="97"/>
            <line x="55" y="97"/>
            <line x="50" y="108"/>
            <close/>
            <move x="55" y="108"/>
            <line x="60" y="97"/>
            <line x="65" y="97"/>
            <line x="60" y="108"/>
            <close/>
            <move x="65" y="108"/>
            <line x="70" y="97"/>
            <line x="75" y="97"/>
            <line x="70" y="108"/>
            <close/>
            <move x="15" y="90.5"/>
            <line x="20" y="79.5"/>
            <line x="25" y="79.5"/>
            <line x="20" y="90.5"/>
            <close/>
            <move x="25" y="90.5"/>
            <line x="30" y="79.5"/>
            <line x="35" y="79.5"/>
            <line x="30" y="90.5"/>
            <close/>
            <move x="35" y="90.5"/>
            <line x="40" y="79.5"/>
            <line x="45" y="79.5"/>
            <line x="40" y="90.5"/>
            <close/>
            <move x="45" y="90.5"/>
            <line x="50" y="79.5"/>
            <line x="55" y="79.5"/>
            <line x="50" y="90.5"/>
            <close/>
            <move x="55" y="90.5"/>
            <line x="60" y="79.5"/>
            <line x="65" y="79.5"/>
            <line x="60" y="90.5"/>
            <close/>
            <move x="65" y="90.5"/>
            <line x="70" y="79.5"/>
            <line x="75" y="79.5"/>
            <line x="70" y="90.5"/>
            <close/>
            <move x="15" y="73"/>
            <line x="20" y="62"/>
            <line x="25" y="62"/>
            <line x="20" y="73"/>
            <close/>
            <move x="25" y="73"/>
            <line x="30" y="62"/>
            <line x="35" y="62"/>
            <line x="30" y="73"/>
            <close/>
            <move x="35" y="73"/>
            <line x="39.5" y="62"/>
            <line x="44.5" y="62"/>
            <line x="39.5" y="73"/>
            <close/>
            <move x="45" y="73"/>
            <line x="50" y="62"/>
            <line x="55" y="62"/>
            <line x="50" y="73"/>
            <close/>
            <move x="55" y="73"/>
            <line x="60" y="62"/>
            <line x="65" y="62"/>
            <line x="60" y="73"/>
            <close/>
            <move x="65" y="73"/>
            <line x="70" y="62"/>
            <line x="75" y="62"/>
            <line x="70" y="73"/>
            <close/>
            <move x="15" y="55.5"/>
            <line x="20" y="44.5"/>
            <line x="25" y="44.5"/>
            <line x="20" y="55.5"/>
            <close/>
            <move x="25" y="55.5"/>
            <line x="30" y="44.5"/>
            <line x="35" y="44.5"/>
            <line x="30" y="55.5"/>
            <close/>
            <move x="35" y="55.5"/>
            <line x="40" y="44.5"/>
            <line x="45" y="44.5"/>
            <line x="40" y="55.5"/>
            <close/>
            <move x="45" y="55.5"/>
            <line x="50" y="44.5"/>
            <line x="55" y="44.5"/>
            <line x="50" y="55.5"/>
            <close/>
            <move x="55" y="55.5"/>
            <line x="60" y="44.5"/>
            <line x="65" y="44.5"/>
            <line x="60" y="55.5"/>
            <close/>
            <move x="65" y="55.5"/>
            <line x="70" y="44.5"/>
            <line x="75" y="44.5"/>
            <line x="70" y="55.5"/>
            <close/>
            <move x="15" y="38"/>
            <line x="20" y="27"/>
            <line x="25" y="27"/>
            <line x="20" y="38"/>
            <close/>
            <move x="25" y="38"/>
            <line x="30" y="27"/>
            <line x="35" y="27"/>
            <line x="30" y="38"/>
            <close/>
            <move x="35" y="38"/>
            <line x="40" y="27"/>
            <line x="45" y="27"/>
            <line x="40" y="38"/>
            <close/>
            <move x="45" y="38"/>
            <line x="50" y="27"/>
            <line x="55" y="27"/>
            <line x="50" y="38"/>
            <close/>
            <move x="55" y="38"/>
            <line x="60" y="27"/>
            <line x="65" y="27"/>
            <line x="60" y="38"/>
            <close/>
            <move x="65" y="38"/>
            <line x="70" y="27"/>
            <line x="75" y="27"/>
            <line x="70" y="38"/>
            <close/>
        </path>
        <fill/>
        <dashpattern pattern="5 5"/>
        <dashed dashed="1"/>
        <roundrect arcsize="4.55" h="120" w="110" x="0" y="0"/>
        <stroke/>
    </foreground>
</shape>
<shape aspect="variable" h="97.95" name="Virus" strokewidth="2" w="106.43">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0"/>
        <constraint name="S" perimeter="0" x="0.5" y="0.85"/>
        <constraint name="W" perimeter="0" x="0.085" y="0.5"/>
        <constraint name="E" perimeter="0" x="0.915" y="0.5"/>
        <constraint name="NW" perimeter="0" x="0.265" y="0.15"/>
        <constraint name="SW" perimeter="0" x="0.135" y="0.9"/>
        <constraint name="NE" perimeter="0" x="0.73" y="0.15"/>
        <constraint name="SE" perimeter="0" x="0.865" y="0.9"/>
    </connections>
    <foreground>
        <path>
            <move x="54.79" y="0"/>
            <curve x1="65.67" x2="74.24" x3="74.24" y1="0.87" y2="10.07" y3="21.26"/>
            <curve x1="74.24" x2="65.35" x3="54.18" y1="32.66" y2="41.95" y3="42.53"/>
            <line x="54.18" y="46.9"/>
            <curve x1="57.23" x2="59.56" x3="59.56" y1="47.39" y2="50.05" y3="53.26"/>
            <curve x1="59.56" x2="59.42" x3="59.16" y1="54.04" y2="54.8" y3="55.49"/>
            <line x="63.12" y="57.8"/>
            <curve x1="69.24" x2="81.58" x3="91.31" y1="48.48" y2="45.44" y3="51.1"/>
            <curve x1="101.02" x2="104.6" x3="99.71" y1="56.75" y2="69.01" y3="78.99"/>
            <curve x1="106.43" x2="101.76" x3="88.89" y1="65.81" y2="49.48" y3="41.99"/>
            <curve x1="85.67" x2="82.24" x3="78.77" y1="40.12" y2="38.98" y3="38.5"/>
            <curve x1="80.1" x2="80.83" x3="80.83" y1="35.24" y2="31.66" y3="27.92"/>
            <curve x1="80.85" x2="69.32" x3="54.79" y1="13.06" y2="0.89" y3="0"/>
            <line x="54.79" y="0"/>
            <close/>
            <move x="51.25" y="0.02"/>
            <curve x1="36.79" x2="25.34" x3="25.34" y1="0.98" y2="13.12" y3="27.93"/>
            <curve x1="25.34" x2="26.03" x3="27.29" y1="31.57" y2="35.05" y3="38.24"/>
            <curve x1="23.92" x2="20.59" x3="17.47" y1="38.74" y2="39.87" y3="41.69"/>
            <curve x1="4.69" x2="0" x3="6.52" y1="49.12" y2="65.28" y3="78.41"/>
            <curve x1="1.81" x2="5.42" x3="15.04" y1="68.47" y2="56.38" y3="50.78"/>
            <curve x1="24.83" x2="37.26" x3="43.34" y1="45.08" y2="48.2" y3="57.66"/>
            <line x="47.16" y="55.44"/>
            <curve x1="46.92" x2="46.8" x3="46.8" y1="54.76" y2="54.03" y3="53.27"/>
            <curve x1="46.8" x2="48.94" x3="51.79" y1="50.2" y2="47.63" y3="46.99"/>
            <line x="51.79" y="42.53"/>
            <curve x1="40.73" x2="31.95" x3="31.95" y1="41.85" y2="32.59" y3="21.27"/>
            <curve x1="31.95" x2="40.44" x3="51.25" y1="10.12" y2="0.96" y3="0.02"/>
            <line x="51.25" y="0.02"/>
            <close/>
            <move x="52.48" y="26.8"/>
            <curve x1="46.9" x2="41.75" x3="37.52" y1="26.93" y2="28.85" y3="31.96"/>
            <curve x1="37.86" x2="38.22" x3="38.6" y1="32.46" y2="32.95" y3="33.42"/>
            <curve x1="38.98" x2="39.39" x3="39.81" y1="33.89" y2="34.34" y3="34.76"/>
            <curve x1="40.23" x2="40.68" x3="41.14" y1="35.18" y2="35.6" y3="35.98"/>
            <curve x1="41.48" x2="41.85" x3="42.21" y1="36.26" y2="36.52" y3="36.78"/>
            <curve x1="45.33" x2="49.06" x3="53.09" y1="34.71" y2="33.5" y3="33.5"/>
            <curve x1="57.11" x2="60.84" x3="63.96" y1="33.5" y2="34.71" y3="36.78"/>
            <curve x1="64.32" x2="64.69" x3="65.03" y1="36.52" y2="36.27" y3="35.98"/>
            <curve x1="65.49" x2="65.94" x3="66.36" y1="35.59" y2="35.19" y3="34.76"/>
            <curve x1="66.78" x2="67.19" x3="67.57" y1="34.33" y2="33.88" y3="33.42"/>
            <curve x1="67.95" x2="68.32" x3="68.65" y1="32.95" y2="32.46" y3="31.96"/>
            <curve x1="64.27" x2="58.9" x3="53.09" y1="28.73" y2="26.8" y3="26.8"/>
            <curve x1="52.95" x2="52.81" x3="52.68" y1="26.8" y2="26.8" y3="26.8"/>
            <curve x1="52.62" x2="52.55" x3="52.48" y1="26.8" y2="26.8" y3="26.8"/>
            <line x="52.48" y="26.8"/>
            <close/>
            <move x="27.03" y="50.3"/>
            <curve x1="26.44" x2="27.47" x3="30.38" y1="55.74" y2="61.39" y3="66.47"/>
            <curve x1="33.29" x2="37.63" x3="42.6" y1="71.55" y2="75.28" y3="77.48"/>
            <curve x1="42.87" x2="43.1" x3="43.31" y1="76.93" y2="76.37" y3="75.8"/>
            <curve x1="43.52" x2="43.7" x3="43.86" y1="75.23" y2="74.66" y3="74.07"/>
            <curve x1="44.02" x2="44.15" x3="44.25" y1="73.48" y2="72.89" y3="72.29"/>
            <curve x1="44.32" x2="44.36" x3="44.4" y1="71.85" y2="71.41" y3="70.96"/>
            <curve x1="41.07" x2="38.16" x3="36.15" y1="69.27" y2="66.61" y3="63.1"/>
            <curve x1="34.13" x2="33.31" x3="33.52" y1="59.6" y2="55.74" y3="51.98"/>
            <curve x1="33.12" x2="32.71" x3="32.3" y1="51.79" y2="51.61" y3="51.45"/>
            <curve x1="31.74" x2="31.17" x3="30.59" y1="51.24" y2="51.05" y3="50.9"/>
            <curve x1="30.01" x2="29.42" x3="28.83" y1="50.74" y2="50.62" y3="50.52"/>
            <curve x1="28.23" x2="27.63" x3="27.03" y1="50.42" y2="50.34" y3="50.3"/>
            <line x="27.03" y="50.3"/>
            <close/>
            <move x="79.34" y="50.62"/>
            <curve x1="78.74" x2="78.14" x3="77.55" y1="50.66" y2="50.74" y3="50.84"/>
            <curve x1="76.96" x2="76.37" x3="75.79" y1="50.94" y2="51.07" y3="51.22"/>
            <curve x1="75.21" x2="74.63" x3="74.07" y1="51.38" y2="51.56" y3="51.77"/>
            <curve x1="73.66" x2="73.26" x3="72.85" y1="51.93" y2="52.12" y3="52.31"/>
            <curve x1="73.07" x2="72.24" x3="70.22" y1="56.06" y2="59.93" y3="63.44"/>
            <curve x1="68.21" x2="65.3" x3="61.97" y1="66.95" y2="69.6" y3="71.29"/>
            <curve x1="62.01" x2="62.05" x3="62.13" y1="71.73" y2="72.18" y3="72.62"/>
            <curve x1="62.23" x2="62.36" x3="62.51" y1="73.22" y2="73.81" y3="74.4"/>
            <curve x1="62.67" x2="62.85" x3="63.06" y1="74.98" y2="75.57" y3="76.13"/>
            <curve x1="63.27" x2="63.5" x3="63.77" y1="76.7" y2="77.26" y3="77.8"/>
            <curve x1="68.73" x2="73.08" x3="75.98" y1="75.59" y2="71.87" y3="66.79"/>
            <curve x1="78.9" x2="79.93" x3="79.34" y1="61.72" y2="56.05" y3="50.62"/>
            <line x="79.34" y="50.62"/>
            <close/>
            <move x="48.38" y="57.49"/>
            <line x="44.53" y="59.74"/>
            <curve x1="49.47" x2="45.92" x3="36.19" y1="69.74" y2="82.04" y3="87.7"/>
            <curve x1="26.51" x2="14.23" x3="8.08" y1="93.34" y2="90.37" y3="81.16"/>
            <curve x1="16.06" x2="32.38" x3="45.22" y1="93.56" y2="97.62" y3="90.15"/>
            <curve x1="48.33" x2="50.95" x3="53.06" y1="88.34" y2="86.01" y3="83.33"/>
            <curve x1="55.21" x2="57.92" x3="61.16" y1="86.14" y2="88.58" y3="90.47"/>
            <curve x1="74.01" x2="90.35" x3="98.32" y1="97.95" y2="93.87" y3="81.45"/>
            <curve x1="92.18" x2="79.88" x3="70.18" y1="90.7" y2="93.69" y3="88.05"/>
            <curve x1="60.39" x2="56.85" x3="61.94" y1="82.35" y2="69.94" y3="59.9"/>
            <line x="57.94" y="57.57"/>
            <curve x1="56.77" x2="55.07" x3="53.19" y1="58.87" y2="59.7" y3="59.7"/>
            <curve x1="51.27" x2="49.55" x3="48.38" y1="59.7" y2="58.84" y3="57.49"/>
            <line x="48.38" y="57.49"/>
            <close/>
        </path>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="107" name="Web Server" strokewidth="2" w="103">
    <connections>
        <constraint name="N" perimeter="0" x="0.5" y="0"/>
        <constraint name="S" perimeter="0" x="0.5" y="0.94"/>
        <constraint name="W" perimeter="0" x="0" y="0.5"/>
        <constraint name="E" perimeter="0" x="0.88" y="0.5"/>
        <constraint name="NW" perimeter="0" x="0" y="0.14"/>
        <constraint name="SW" perimeter="0" x="0" y="0.93"/>
        <constraint name="NE" perimeter="0" x="0.87" y="0.14"/>
        <constraint name="SE" perimeter="0" x="0.95" y="0.95"/>
    </connections>
    <foreground>
        <save/>
        <roundrect arcsize="13.33" h="15" w="90" x="0" y="85"/>
        <fillstroke/>
        <roundrect arcsize="13.33" h="15" w="90" x="0" y="67.5"/>
        <fillstroke/>
        <roundrect arcsize="13.33" h="15" w="90" x="0" y="50"/>
        <fillstroke/>
        <roundrect arcsize="13.33" h="15" w="90" x="0" y="32.5"/>
        <fillstroke/>
        <roundrect arcsize="13.33" h="15" w="90" x="0" y="15"/>
        <fillstroke/>
        <path>
            <move x="4" y="13"/>
            <line x="25" y="0"/>
            <line x="65" y="0"/>
            <line x="86" y="13"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#ffffff"/>
        <ellipse h="8" w="8" x="78" y="88.5"/>
        <fill/>
        <ellipse h="8" w="8" x="77.67" y="71"/>
        <fill/>
        <ellipse h="8" w="8" x="78" y="53.5"/>
        <fill/>
        <ellipse h="8" w="8" x="78" y="36"/>
        <fill/>
        <ellipse h="8" w="8" x="78" y="18.5"/>
        <fill/>
        <path>
            <move x="5" y="98"/>
            <line x="10" y="87"/>
            <line x="15" y="87"/>
            <line x="10" y="98"/>
            <close/>
            <move x="15" y="98"/>
            <line x="20" y="87"/>
            <line x="25" y="87"/>
            <line x="20" y="98"/>
            <close/>
            <move x="25" y="98"/>
            <line x="30" y="87"/>
            <line x="35" y="87"/>
            <line x="30" y="98"/>
            <close/>
            <move x="35" y="98"/>
            <line x="40" y="87"/>
            <line x="45" y="87"/>
            <line x="40" y="98"/>
            <close/>
            <move x="45" y="98"/>
            <line x="50" y="87"/>
            <line x="55" y="87"/>
            <line x="50" y="98"/>
            <close/>
            <move x="55" y="98"/>
            <line x="60" y="87"/>
            <line x="65" y="87"/>
            <line x="60" y="98"/>
            <close/>
            <move x="5" y="80.5"/>
            <line x="10" y="69.5"/>
            <line x="15" y="69.5"/>
            <line x="10" y="80.5"/>
            <close/>
            <move x="15" y="80.5"/>
            <line x="20" y="69.5"/>
            <line x="25" y="69.5"/>
            <line x="20" y="80.5"/>
            <close/>
            <move x="25" y="80.5"/>
            <line x="30" y="69.5"/>
            <line x="35" y="69.5"/>
            <line x="30" y="80.5"/>
            <close/>
            <move x="35" y="80.5"/>
            <line x="40" y="69.5"/>
            <line x="45" y="69.5"/>
            <line x="40" y="80.5"/>
            <close/>
            <move x="45" y="80.5"/>
            <line x="50" y="69.5"/>
            <line x="55" y="69.5"/>
            <line x="50" y="80.5"/>
            <close/>
            <move x="55" y="80.5"/>
            <line x="60" y="69.5"/>
            <line x="65" y="69.5"/>
            <line x="60" y="80.5"/>
            <close/>
            <move x="5" y="63"/>
            <line x="10" y="52"/>
            <line x="15" y="52"/>
            <line x="10" y="63"/>
            <close/>
            <move x="15" y="63"/>
            <line x="20" y="52"/>
            <line x="25" y="52"/>
            <line x="20" y="63"/>
            <close/>
            <move x="25" y="63"/>
            <line x="29.5" y="52"/>
            <line x="34.5" y="52"/>
            <line x="29.5" y="63"/>
            <close/>
            <move x="35" y="63"/>
            <line x="40" y="52"/>
            <line x="45" y="52"/>
            <line x="40" y="63"/>
            <close/>
            <move x="45" y="63"/>
            <line x="50" y="52"/>
            <line x="55" y="52"/>
            <line x="50" y="63"/>
            <close/>
            <move x="55" y="63"/>
            <line x="60" y="52"/>
            <line x="65" y="52"/>
            <line x="60" y="63"/>
            <close/>
            <move x="5" y="45.5"/>
            <line x="10" y="34.5"/>
            <line x="15" y="34.5"/>
            <line x="10" y="45.5"/>
            <close/>
            <move x="15" y="45.5"/>
            <line x="20" y="34.5"/>
            <line x="25" y="34.5"/>
            <line x="20" y="45.5"/>
            <close/>
            <move x="25" y="45.5"/>
            <line x="30" y="34.5"/>
            <line x="35" y="34.5"/>
            <line x="30" y="45.5"/>
            <close/>
            <move x="35" y="45.5"/>
            <line x="40" y="34.5"/>
            <line x="45" y="34.5"/>
            <line x="40" y="45.5"/>
            <close/>
            <move x="45" y="45.5"/>
            <line x="50" y="34.5"/>
            <line x="55" y="34.5"/>
            <line x="50" y="45.5"/>
            <close/>
            <move x="55" y="45.5"/>
            <line x="60" y="34.5"/>
            <line x="65" y="34.5"/>
            <line x="60" y="45.5"/>
            <close/>
            <move x="5" y="28"/>
            <line x="10" y="17"/>
            <line x="15" y="17"/>
            <line x="10" y="28"/>
            <close/>
            <move x="15" y="28"/>
            <line x="20" y="17"/>
            <line x="25" y="17"/>
            <line x="20" y="28"/>
            <close/>
            <move x="25" y="28"/>
            <line x="30" y="17"/>
            <line x="35" y="17"/>
            <line x="30" y="28"/>
            <close/>
            <move x="35" y="28"/>
            <line x="40" y="17"/>
            <line x="45" y="17"/>
            <line x="40" y="28"/>
            <close/>
            <move x="45" y="28"/>
            <line x="50" y="17"/>
            <line x="55" y="17"/>
            <line x="50" y="28"/>
            <close/>
            <move x="55" y="28"/>
            <line x="60" y="17"/>
            <line x="65" y="17"/>
            <line x="60" y="28"/>
            <close/>
        </path>
        <fill/>
        <restore/>
        <rect/>
        <stroke/>
        <ellipse h="36" w="36" x="67" y="71"/>
        <fillstroke/>
        <path>
            <move x="85" y="71"/>
            <line x="85" y="107"/>
            <move x="67.4" y="89"/>
            <line x="103" y="88.8"/>
            <move x="85" y="71"/>
            <arc large-arc-flag="0" rx="25" ry="25" sweep-flag="1" x="85" x-axis-rotation="0" y="106"/>
            <move x="85" y="71"/>
            <arc large-arc-flag="0" rx="25" ry="25" sweep-flag="0" x="85" x-axis-rotation="0" y="106"/>
            <move x="85" y="71"/>
            <arc large-arc-flag="0" rx="18" ry="18" sweep-flag="1" x="85" x-axis-rotation="0" y="106"/>
            <move x="85" y="71"/>
            <arc large-arc-flag="0" rx="18" ry="18" sweep-flag="0" x="85" x-axis-rotation="0" y="106"/>
            <move x="69.5" y="80"/>
            <line x="100.5" y="80"/>
            <move x="69.4" y="99"/>
            <line x="100.2" y="99.2"/>
        </path>
        <stroke/>
    </foreground>
</shape>
<shape aspect="variable" h="85.6" name="Wireless Hub" strokewidth="2" w="100">
    <connections>
        <constraint name="N" perimeter="0" x="0.55" y="0"/>
        <constraint name="S" perimeter="0" x="0.5" y="1"/>
        <constraint name="W" perimeter="0" x="0" y="0.7"/>
        <constraint name="E" perimeter="0" x="1" y="0.7"/>
        <constraint name="SW" perimeter="0" x="0.02" y="0.94"/>
        <constraint name="NE" perimeter="0" x="0.82" y="0"/>
        <constraint name="SE" perimeter="0" x="0.98" y="0.94"/>
    </connections>
    <foreground>
        <save/>
        <roundrect arcsize="20" h="25" w="100" x="0" y="56.6"/>
        <fillstroke/>
        <rect h="4" w="80" x="10" y="81.6"/>
        <fillstroke/>
        <path>
            <move x="67" y="56.6"/>
            <line x="67.5" y="11.6"/>
            <arc large-arc-flag="0" rx="1.5" ry="1.5" sweep-flag="1" x="70" x-axis-rotation="0" y="11.6"/>
            <line x="70.5" y="56.6"/>
            <close/>
        </path>
        <fillstroke/>
        <fillcolor color="#ffffff"/>
        <ellipse h="16" w="16" x="80" y="61.1"/>
        <fill/>
        <ellipse h="4" w="4" x="73" y="59.6"/>
        <fill/>
        <ellipse h="4" w="4" x="73" y="74.6"/>
        <fill/>
        <ellipse h="4" w="4" x="73" y="67.1"/>
        <fill/>
        <ellipse h="4" w="4" x="63.5" y="71.6"/>
        <fill/>
        <ellipse h="4" w="4" x="55" y="71.6"/>
        <fill/>
        <ellipse h="4" w="4" x="46.5" y="71.6"/>
        <fill/>
        <ellipse h="4" w="4" x="38" y="71.6"/>
        <fill/>
        <ellipse h="4" w="4" x="29.5" y="71.6"/>
        <fill/>
        <ellipse h="4" w="4" x="21" y="71.6"/>
        <fill/>
        <ellipse h="4" w="4" x="12.5" y="71.6"/>
        <fill/>
        <ellipse h="4" w="4" x="4" y="71.6"/>
        <fill/>
        <path>
            <move x="3" y="63.6"/>
            <line x="9" y="63.6"/>
            <line x="9" y="66.6"/>
            <line x="7" y="66.6"/>
            <line x="7" y="67.6"/>
            <line x="5" y="67.6"/>
            <line x="5" y="66.6"/>
            <line x="3" y="66.6"/>
            <close/>
            <move x="11.5" y="63.6"/>
            <line x="17.5" y="63.6"/>
            <line x="17.5" y="66.6"/>
            <line x="15.5" y="66.6"/>
            <line x="15.5" y="67.6"/>
            <line x="13.5" y="67.6"/>
            <line x="13.5" y="66.6"/>
            <line x="11.5" y="66.6"/>
            <close/>
            <move x="20" y="63.6"/>
            <line x="26" y="63.6"/>
            <line x="26" y="66.6"/>
            <line x="24" y="66.6"/>
            <line x="24" y="67.6"/>
            <line x="22" y="67.6"/>
            <line x="22" y="66.6"/>
            <line x="20" y="66.6"/>
            <close/>
            <move x="28.5" y="63.6"/>
            <line x="34.5" y="63.6"/>
            <line x="34.5" y="66.6"/>
            <line x="32.5" y="66.6"/>
            <line x="32.5" y="67.6"/>
            <line x="30.5" y="67.6"/>
            <line x="30.5" y="66.6"/>
            <line x="28.5" y="66.6"/>
            <close/>
            <move x="37" y="63.6"/>
            <line x="43" y="63.6"/>
            <line x="43" y="66.6"/>
            <line x="41" y="66.6"/>
            <line x="41" y="67.6"/>
            <line x="39" y="67.6"/>
            <line x="39" y="66.6"/>
            <line x="37" y="66.6"/>
            <close/>
            <move x="45.5" y="63.6"/>
            <line x="51.5" y="63.6"/>
            <line x="51.5" y="66.6"/>
            <line x="49.5" y="66.6"/>
            <line x="49.5" y="67.6"/>
            <line x="47.5" y="67.6"/>
            <line x="47.5" y="66.6"/>
            <line x="45.5" y="66.6"/>
            <close/>
            <move x="54" y="63.6"/>
            <line x="60" y="63.6"/>
            <line x="60" y="66.6"/>
            <line x="58" y="66.6"/>
            <line x="58" y="67.6"/>
            <line x="56" y="67.6"/>
            <line x="56" y="66.6"/>
            <line x="54" y="66.6"/>
            <close/>
            <move x="62.5" y="63.6"/>
            <line x="68.5" y="63.6"/>
            <line x="68.5" y="66.6"/>
            <line x="66.5" y="66.6"/>
            <line x="66.5" y="67.6"/>
            <line x="64.5" y="67.6"/>
            <line x="64.5" y="66.6"/>
            <line x="62.5" y="66.6"/>
            <close/>
        </path>
        <fill/>
        <restore/>
        <rect/>
        <stroke/>
        <path>
            <move x="90" y="67.1"/>
            <line x="92" y="69.1"/>
            <line x="90" y="71.1"/>
            <move x="83" y="69.1"/>
            <line x="92" y="69.1"/>
        </path>
        <stroke/>
        <path>
            <move x="73.8" y="5.9"/>
            <arc large-arc-flag="0" rx="10" ry="10" sweep-flag="1" x="73.8" x-axis-rotation="0" y="16.9"/>
            <line x="72.3" y="15.9"/>
            <arc large-arc-flag="0" rx="8" ry="8" sweep-flag="0" x="72.3" x-axis-rotation="0" y="7.3"/>
            <close/>
            <move x="77.8" y="2.9"/>
            <arc large-arc-flag="0" rx="14" ry="14" sweep-flag="1" x="77.8" x-axis-rotation="0" y="20.2"/>
            <line x="76.3" y="19"/>
            <arc large-arc-flag="0" rx="12" ry="12" sweep-flag="0" x="76.3" x-axis-rotation="0" y="4.2"/>
            <close/>
            <move x="81.8" y="0"/>
            <arc large-arc-flag="0" rx="17" ry="17" sweep-flag="1" x="81.8" x-axis-rotation="0" y="23.1"/>
            <line x="80.3" y="21.9"/>
            <arc large-arc-flag="0" rx="15" ry="15" sweep-flag="0" x="80.3" x-axis-rotation="0" y="1.2"/>
            <close/>
            <move x="63.8" y="5.9"/>
            <arc large-arc-flag="0" rx="10" ry="10" sweep-flag="0" x="63.8" x-axis-rotation="0" y="16.9"/>
            <line x="65.3" y="15.9"/>
            <arc large-arc-flag="0" rx="8" ry="8" sweep-flag="1" x="65.3" x-axis-rotation="0" y="7.3"/>
            <close/>
            <move x="59.8" y="2.9"/>
            <arc large-arc-flag="0" rx="14" ry="14" sweep-flag="0" x="59.8" x-axis-rotation="0" y="20.3"/>
            <line x="61.3" y="18.9"/>
            <arc large-arc-flag="0" rx="12" ry="12" sweep-flag="1" x="61.3" x-axis-rotation="0" y="4.3"/>
            <close/>
            <move x="55.8" y="0.1"/>
            <arc large-arc-flag="0" rx="17" ry="17" sweep-flag="0" x="55.8" x-axis-rotation="0" y="23.1"/>
            <line x="57.3" y="21.9"/>
            <arc large-arc-flag="0" rx="15" ry="15" sweep-flag="1" x="57.3" x-axis-rotation="0" y="1.3"/>
            <close/>
        </path>
        <fillstroke/>
    </foreground>
</shape>
<shape aspect="variable" h="85.6" name="Wireless Modem" strokewidth="inherit" w="100">
    <connections/>
    <foreground>
        <save/>
        <save/>
        <save/>
        <roundrect arcsize="20" h="25" w="100" x="0" y="56.6"/>
        <fillstroke/>
        <rect h="4" w="80" x="10" y="81.6"/>
        <fillstroke/>
        <strokecolor color="none"/>
        <fillcolor color="#ffffff"/>
        <ellipse h="16" w="16" x="80" y="61.1"/>
        <fill/>
        <ellipse h="4" w="4" x="8" y="64.6"/>
        <fill/>
        <restore/>
        <rect/>
        <stroke/>
        <path>
            <move x="88" y="63.6"/>
            <line x="88" y="74.6"/>
        </path>
        <fillstroke/>
        <fillcolor color="none"/>
        <path>
            <move x="86" y="65.6"/>
            <line x="88" y="63.6"/>
            <line x="90" y="65.6"/>
        </path>
        <stroke/>
        <strokecolor color="none"/>
        <fillcolor color="#ffffff"/>
        <ellipse h="4" w="4" x="18" y="64.6"/>
        <fill/>
        <ellipse h="4" w="4" x="28" y="64.6"/>
        <fill/>
        <ellipse h="4" w="4" x="38" y="64.6"/>
        <fill/>
        <restore/>
        <rect/>
        <stroke/>
        <fillcolor color="none"/>
        <path>
            <move x="86" y="72.6"/>
            <line x="88" y="74.6"/>
            <line x="90" y="72.6"/>
        </path>
        <stroke/>
        <restore/>
        <rect/>
        <stroke/>
        <path>
            <move x="67" y="56.6"/>
            <line x="67.5" y="11.6"/>
            <arc large-arc-flag="0" rx="1.5" ry="1.5" sweep-flag="1" x="70" x-axis-rotation="0" y="11.6"/>
            <line x="70.5" y="56.6"/>
            <close/>
        </path>
        <fillstroke/>
        <path>
            <move x="73.8" y="5.9"/>
            <arc large-arc-flag="0" rx="10" ry="10" sweep-flag="1" x="73.8" x-axis-rotation="0" y="16.9"/>
            <line x="72.3" y="15.9"/>
            <arc large-arc-flag="0" rx="8" ry="8" sweep-flag="0" x="72.3" x-axis-rotation="0" y="7.3"/>
            <close/>
        </path>
        <fillstroke/>
        <path>
            <move x="77.8" y="2.9"/>
            <arc large-arc-flag="0" rx="14" ry="14" sweep-flag="1" x="77.8" x-axis-rotation="0" y="20.2"/>
            <line x="76.3" y="19"/>
            <arc large-arc-flag="0" rx="12" ry="12" sweep-flag="0" x="76.3" x-axis-rotation="0" y="4.2"/>
            <close/>
        </path>
        <fillstroke/>
        <path>
            <move x="81.8" y="0"/>
            <arc large-arc-flag="0" rx="17" ry="17" sweep-flag="1" x="81.8" x-axis-rotation="0" y="23.1"/>
            <line x="80.3" y="21.9"/>
            <arc large-arc-flag="0" rx="15" ry="15" sweep-flag="0" x="80.3" x-axis-rotation="0" y="1.2"/>
            <close/>
        </path>
        <fillstroke/>
        <path>
            <move x="63.8" y="5.9"/>
            <arc large-arc-flag="0" rx="10" ry="10" sweep-flag="0" x="63.8" x-axis-rotation="0" y="16.9"/>
            <line x="65.3" y="15.9"/>
            <arc large-arc-flag="0" rx="8" ry="8" sweep-flag="1" x="65.3" x-axis-rotation="0" y="7.3"/>
            <close/>
        </path>
        <fillstroke/>
        <path>
            <move x="59.8" y="2.9"/>
            <arc large-arc-flag="0" rx="14" ry="14" sweep-flag="0" x="59.8" x-axis-rotation="0" y="20.3"/>
            <line x="61.3" y="18.9"/>
            <arc large-arc-flag="0" rx="12" ry="12" sweep-flag="1" x="61.3" x-axis-rotation="0" y="4.3"/>
            <close/>
        </path>
        <fillstroke/>
        <path>
            <move x="55.8" y="0.1"/>
            <arc large-arc-flag="0" rx="17" ry="17" sweep-flag="0" x="55.8" x-axis-rotation="0" y="23.1"/>
            <line x="57.3" y="21.9"/>
            <arc large-arc-flag="0" rx="15" ry="15" sweep-flag="1" x="57.3" x-axis-rotation="0" y="1.3"/>
            <close/>
        </path>
        <fillstroke/>
    </foreground>
</shape>
</shapes>